#############################################
#  SmaTrendFollower – LIVE TRADING CONFIG  #
#  ⚠️  LIVE TRADING WITH REAL MONEY ⚠️     #
#############################################

# ----- Alpaca Live Trading API -----
# ⚠️  REPLACE WITH YOUR LIVE ALPACA CREDENTIALS ⚠️
APCA_API_KEY_ID=REPLACE_WITH_LIVE_ALPACA_KEY_ID
APCA_API_SECRET_KEY=REPLACE_WITH_LIVE_ALPACA_SECRET_KEY
APCA_API_ENV=live

# ----- Polygon API (Market Data) -----
POLY_API_KEY=********************************

# ----- Discord Notifications -----
DISCORD_BOT_TOKEN=MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo
DISCORD_CHANNEL_ID=1385057459814797383

# ----- AGGRESSIVE LIVE TRADING SAFETY LIMITS -----
# Environment: Live trading only
SAFETY_ALLOWED_ENVIRONMENT=Live

# ----- AGGRESSIVE TRADING CYCLE INTERVALS -----
# Aggressive 3-minute default cycle for live trading
TRADING_CYCLE_INTERVAL_MINUTES=3
# Very fast 1-minute cycles during high volatility
TRADING_CYCLE_HIGH_VIX_MINUTES=1
# Fast 3-minute cycles during normal volatility
TRADING_CYCLE_NORMAL_VIX_MINUTES=3
# Moderate 5-minute cycles during low volatility
TRADING_CYCLE_LOW_VIX_MINUTES=5
# Extended hours: 10 minutes (less liquidity)
TRADING_CYCLE_EXTENDED_HOURS_MINUTES=10
# Overnight: 15 minutes (minimal activity)
TRADING_CYCLE_OVERNIGHT_MINUTES=15
# Lower VIX threshold for more aggressive high-vol detection
TRADING_CYCLE_HIGH_VIX_THRESHOLD=22.0
# Higher VIX threshold for low-vol detection (stay aggressive longer)
TRADING_CYCLE_LOW_VIX_THRESHOLD=18.0
# Enable all dynamic adjustments for maximum responsiveness
TRADING_CYCLE_ENABLE_VIX_ADJUSTMENT=true
TRADING_CYCLE_ENABLE_EXTENDED_HOURS_ADJUSTMENT=true
# Aggressive safety limits
TRADING_CYCLE_MIN_INTERVAL_MINUTES=1
TRADING_CYCLE_MAX_INTERVAL_MINUTES=30

# Risk Limits (Aggressive for Live Trading)
SAFETY_MAX_DAILY_LOSS=500                    # $500 max daily loss (4.2% of $12k account)
SAFETY_MAX_POSITIONS=8                       # Maximum 8 concurrent positions
SAFETY_MAX_SINGLE_TRADE_VALUE=3000          # $3,000 max per trade (25% of account)
SAFETY_MIN_ACCOUNT_EQUITY=5000               # $5,000 minimum account size
SAFETY_MAX_POSITION_SIZE_PERCENT=0.12        # 12% max position size (more aggressive)
SAFETY_MAX_DAILY_TRADES=25                   # Maximum 25 trades per day

# Operational Controls
SAFETY_REQUIRE_CONFIRMATION=false            # No manual confirmation required
SAFETY_DRY_RUN_MODE=false                    # Live trading mode

# ----- Redis Configuration (Recommended) -----
REDIS_URL=localhost:6379
REDIS_DATABASE=0

# ----- Strategy Configuration -----
# Universe and Signal Generation
UNIVERSE_SIZE=500                            # Screen top 500 stocks
TOP_N_SYMBOLS=5                              # Trade only top 5 signals (concentrated)

# Risk Management
VIX_THRESHOLD=25.0                           # Reduce positions when VIX > 25

# Market Regime Filters
ENABLE_REGIME_FILTER=true                    # Block trading in volatile markets
ENABLE_VOLATILITY_FILTER=true               # Filter high volatility stocks

# ----- Options Strategies (Conservative) -----
ENABLE_OPTIONS_OVERLAY=false                 # Disable options for initial live trading
ENABLE_PROTECTIVE_PUTS=false                 # Disable until proven in paper
ENABLE_COVERED_CALLS=false                   # Disable until proven in paper

# ----- Logging and Monitoring -----
# Enhanced logging for live trading
LOG_LEVEL=Information
ENABLE_PERFORMANCE_LOGGING=true
ENABLE_TRADE_LOGGING=true
ENABLE_SAFETY_LOGGING=true

# ----- Timezone -----
BOT_TIMEZONE=America/New_York

#############################################
#  LIVE TRADING DEPLOYMENT CHECKLIST       #
#############################################
# Before using this configuration:
# 1. ✅ Replace APCA_API_KEY_ID with live key
# 2. ✅ Replace APCA_API_SECRET_KEY with live secret
# 3. ✅ Verify account has minimum $5,000 equity
# 4. ✅ Test connectivity: dotnet run -- --dry-run account-status
# 5. ✅ Validate configuration: dotnet run -- validate
# 6. ✅ Run safety tests: dotnet run -- --show-safety
# 7. ✅ Start with: dotnet run -- --confirm run
#############################################
