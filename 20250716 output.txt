='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 8), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:38 INF] Successfully cached 156 bars for CALM Day
[16:00:38 INF] Retrieved 155 stock bars for CALM Day (155 cached, 156 from API)
[16:00:38 WRN] Continuing without caching for CAL Day due to database error
[16:00:38 INF] Successfully cached 157 bars for CAL Day
[16:00:38 INF] Retrieved 156 stock bars for CAL Day (156 cached, 157 from API)
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 3961)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 8)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:38 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 ERR] Database error caching bars for CARS Day: SQLite Error 5: 'database is locked'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 5: 'database is locked'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteConnectionExtensions.ExecuteNonQuery(SqliteConnection connection, String commandText, SqliteParameter[] parameters)
   at Microsoft.Data.Sqlite.SqliteTransaction..ctor(SqliteConnection connection, IsolationLevel isolationLevel, Boolean deferred)
   at System.Data.Common.DbConnection.BeginDbTransactionAsync(IsolationLevel isolationLevel, CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.BeginTransactionAsync(IsolationLevel isolationLevel, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.BeginTransactionAsync(CancellationToken cancellationToken)
   at SmaTrendFollower.Data.StockBarCacheDbContext.AddOrUpdateCachedBarsAsync(String symbol, String timeFrame, IEnumerable`1 bars) in C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\SmaTrendFollower.Console\Data\StockBarCacheDbContext.cs:line 200
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 8), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:39 WRN] Continuing without caching for CARS Day due to database error
[16:00:39 INF] Successfully cached 180 bars for ESNT Day
[16:00:39 INF] Retrieved 180 stock bars for ESNT Day (180 cached, 180 from API)
[16:00:39 WRN] Insufficient bars for ESNT: 180 (need 200+)
[16:00:39 WRN] Slow signal ESNT 40659 ms
[16:00:39 INF] Successfully cached 170 bars for CARS Day
[16:00:39 INF] Retrieved 170 stock bars for CARS Day (170 cached, 170 from API)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 3), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 2509)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 7)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 3), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 7), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:39 INF] Successfully cached 114 bars for GCT Day
[16:00:39 INF] Retrieved 113 stock bars for GCT Day (113 cached, 114 from API)
[16:00:39 WRN] Insufficient bars for GCT: 113 (need 200+)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 4005)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 8)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 WRN] Slow signal GCT 40853 ms
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 8), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:39 INF] Successfully cached 182 bars for FVRR Day
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 3), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 3961)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:39 INF] Retrieved 181 stock bars for FVRR Day (181 cached, 182 from API)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 7)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:39 WRN] Insufficient bars for FVRR: 181 (need 200+)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 3), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 WRN] Slow signal FVRR 41054 ms
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 7), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:39 INF] Successfully cached 180 bars for FXN Day
[16:00:39 INF] Retrieved 179 stock bars for FXN Day (179 cached, 180 from API)
[16:00:39 WRN] Insufficient bars for FXN: 179 (need 200+)
[16:00:39 WRN] Slow signal FXN 41221 ms
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 4137)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 8)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 WRN] Failed to fetch data for CHRD
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at SmaTrendFollower.Services.EnhancedSignalGenerator.<>c__DisplayClass12_0.<<FetchDataInParallelAsync>b__0>d.MoveNext() in C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\SmaTrendFollower.Console\Services\EnhancedSignalGenerator.cs:line 187
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 8), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:39 INF] Successfully cached 188 bars for FOXF Day
[16:00:39 INF] Retrieved 187 stock bars for FOXF Day (187 cached, 188 from API)
[16:00:39 WRN] Insufficient bars for FOXF: 187 (need 200+)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 3037)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 8)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:39 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 WRN] Slow signal CHRD 41467 ms
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 WRN] Slow signal FOXF 41554 ms
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 8), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:40 INF] Successfully cached 138 bars for CMPO Day
[16:00:40 INF] Retrieved 137 stock bars for CMPO Day (137 cached, 138 from API)
[16:00:40 WRN] Insufficient bars for CMPO: 137 (need 200+)
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 3), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 4269)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 7)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 3), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 WRN] Slow signal CMPO 41675 ms
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 7), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:40 ERR] Database error caching bars for CHRD Day: SQLite Error 5: 'database is locked'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 5: 'database is locked'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteConnectionExtensions.ExecuteNonQuery(SqliteConnection connection, String commandText, SqliteParameter[] parameters)
   at Microsoft.Data.Sqlite.SqliteTransaction..ctor(SqliteConnection connection, IsolationLevel isolationLevel, Boolean deferred)
   at System.Data.Common.DbConnection.BeginDbTransactionAsync(IsolationLevel isolationLevel, CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.BeginTransactionAsync(IsolationLevel isolationLevel, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.BeginTransactionAsync(CancellationToken cancellationToken)
   at SmaTrendFollower.Data.StockBarCacheDbContext.AddOrUpdateCachedBarsAsync(String symbol, String timeFrame, IEnumerable`1 bars) in C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\SmaTrendFollower.Console\Data\StockBarCacheDbContext.cs:line 200
[16:00:40 INF] Successfully cached 194 bars for EPC Day
[16:00:40 INF] Retrieved 194 stock bars for EPC Day (194 cached, 194 from API)
[16:00:40 WRN] Insufficient bars for EPC: 194 (need 200+)
[16:00:40 WRN] Slow signal EPC 41862 ms
[16:00:40 WRN] Continuing without caching for CHRD Day due to database error
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 3), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 4115)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:40 INF] Successfully cached 166 bars for CHRD Day
[16:00:40 INF] Retrieved 165 stock bars for CHRD Day (165 cached, 166 from API)
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 7)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 3), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 7), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:40 INF] Successfully cached 187 bars for CNO Day
[16:00:40 INF] Retrieved 186 stock bars for CNO Day (186 cached, 187 from API)
[16:00:40 WRN] Insufficient bars for CNO: 186 (need 200+)
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 4027)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 8)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 WRN] Slow signal CNO 42288 ms
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 WRN] Failed to fetch data for CLMT
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at SmaTrendFollower.Services.EnhancedSignalGenerator.<>c__DisplayClass12_0.<<FetchDataInParallelAsync>b__0>d.MoveNext() in C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\SmaTrendFollower.Console\Services\EnhancedSignalGenerator.cs:line 187
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 WRN] Slow signal CLMT 42445 ms
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:40 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 8), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:40 INF] Successfully cached 183 bars for CRUS Day
[16:00:40 INF] Retrieved 182 stock bars for CRUS Day (182 cached, 183 from API)
[16:00:40 WRN] Insufficient bars for CRUS: 182 (need 200+)
[16:00:41 WRN] Slow signal CRUS 42530 ms
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3), @__timeStamps_2='?' (Size = 2575)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."CompressedData", "c"."High", "c"."IsCompressed", "c"."Low", "c"."Open", "c"."OriginalDataSize", "c"."Symbol", "c"."TimeFrame", "c"."TimeUtc", "c"."TradeCount", "c"."Volume", "c"."Vwap"
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1 AND "c"."TimeUtc" IN (
    SELECT "t"."value"
    FROM json_each(@__timeStamps_2) AS "t"
)
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 8)], CommandType='Text', CommandTimeout='30']
SELECT "s"."CacheKey", "s"."BarCount", "s"."EarliestDataDate", "s"."LastUpdated", "s"."LatestDataDate", "s"."Symbol", "s"."TimeFrame"
FROM "StockCacheMetadata" AS "s"
WHERE "s"."CacheKey" = @__p_0
LIMIT 1
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 4), @__timeFrame_1='?' (Size = 3)], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM "CachedStockBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeFrame" = @__timeFrame_1
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Fetched data for 7/198 symbols
[16:00:41 WRN] Excluded 191 symbols due to insufficient data: AAPD, AAT, ABM, ACIW, ACMR, ACVA, ADEA, ADNT, AGCO, AGIO, AIRO, AKRO, ALGN, ALKT, ALNY, ALSN, ALV, AMBA, AMN, AMRC, AMSC, ANGI, ANSS, APAM, APGE, APPN, ARES, ARIS, ARQQ, ASC, ATEN, ATMU, ATRO, AUB, AVDL, AVNT, AVPT, AVT, AVUV, AVXL, AVY, AXGN, AXON, AXS, AXSM, AZTA, BABX, BC, BEPC, BFH, BHF, BIPC, BIZD, BKKT, BKU, BL, BLCO, BLFS, BOOT, BOTZ, BRKL, BSGM, BURL, BV, BWIN, BWXT, BXMT, BYD, BYRN, CAE, CAI, CAL, CALM, CALX, CARG, CARS, CBRL, CDNA, CDTX, CDW, CEP, CGON, CHKP, CHRD, CHRW, CHTR, CINF, CLDX, CLMT, CMP, CMPO, CNO, CNR, CNXC, COCO, COKE, COLM, COOP, COR, CORT, CRC, CRL, CRNX, CROX, CRS, CRUS, CSIQ, CTRI, CVBF, CVI, CWEN, CXW, CYBR, DAKT, DAVE, DCTH, DIOD, DOMO, DPST, DPZ, DRVN, DTM, DUOL, DXD, ECL, EDV, EMBC, ENR, EPAM, EPC, ERO, ESNT, ETHD, ETHT, EUAD, EVR, EWBC, EWTX, EXPD, EXPI, EYPT, EZPW, FAF, FAS, FBP, FBRT, FETH, FFIV, FIBK, FIHL, FN, FORM, FOUR, FOX, FOXF, FROG, FSK, FSS, FTDR, FUL, FVRR, FWONK, FWRG, FXN, GBCI, GBX, GCT, GDOT, GDYN, GES, GIII, GKOS, GL, GLNG, GLOB, GNK, GNRC, GOOS, GPC, GRAL, GRMN, GRPN, GSAT, GSBD, GTLS, GUSH, GWRE, H, HAE, HCC, HCSG
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "CachedStockBars" SET "CachedAt" = @p0
WHERE "Id" = @p1
RETURNING 1;
[16:00:41 INF] Executed DbCommand (0ms) [Parameters=[@p1='?' (Size = 8), @p0='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
UPDATE "StockCacheMetadata" SET "LastUpdated" = @p0
WHERE "CacheKey" = @p1
RETURNING 1;
[16:00:41 INF] Successfully processed symbols: CENTA, ELVN, DSGX, ASGN, EPAC, AMR, ARKF
[16:00:41 INF] Using Alpaca credentials from configuration for live environment
[16:00:41 INF] Creating Alpaca trading client for live environment
[16:00:41 INF] Using API Key ID: AKGBPW5HD8LVI5C6NJUJ
[16:00:41 INF] Successfully cached 117 bars for CLMT Day
[16:00:41 INF] Retrieved 116 stock bars for CLMT Day (116 cached, 117 from API)
[16:00:41 INF] Enhanced signal generation completed: 0 signals from 198 symbols in 43737ms
[16:00:41 INF] Generated 0 trading signals for Normal regime
[16:00:41 INF] Completed equity trading cycle: 0 trades executed, total value: 0.00
[16:00:41 INF] Equity trading cycle completed successfully: 0 trades executed
[16:00:41 INF] Starting options overlay execution
[16:00:41 INF] Using Alpaca credentials from configuration for live environment
[16:00:41 INF] Creating Alpaca trading client for live environment
[16:00:41 INF] Using API Key ID: AKGBPW5HD8LVI5C6NJUJ
[16:00:41 INF] Using Alpaca credentials from configuration for live environment
[16:00:41 INF] Creating Alpaca trading client for live environment
[16:00:41 INF] Using API Key ID: AKGBPW5HD8LVI5C6NJUJ
[16:00:41 INF] Using Alpaca credentials from configuration for live environment
[16:00:41 INF] Creating Alpaca trading client for live environment
[16:00:41 INF] Using API Key ID: AKGBPW5HD8LVI5C6NJUJ
[16:00:41 INF] Using Alpaca credentials from configuration for live environment
[16:00:41 INF] Creating Alpaca trading client for live environment
[16:00:41 INF] Using API Key ID: AKGBPW5HD8LVI5C6NJUJ
[16:00:41 INF] Completed options overlay: 0 covered calls, 0 protective puts, 0 delta-efficient evaluated
[16:00:41 INF] Options overlay completed successfully: 0 covered calls, 0 protective puts evaluated
[16:00:41 INF] Starting portfolio management execution
[16:00:41 INF] Using Alpaca credentials from configuration for live environment
[16:00:41 INF] Creating Alpaca trading client for live environment
[16:00:41 INF] Using API Key ID: AKGBPW5HD8LVI5C6NJUJ
[16:00:42 INF] Monitoring 1 positions
[16:00:42 INF] Sending portfolio snapshot
[16:00:42 INF] Using Alpaca credentials from configuration for live environment
[16:00:42 INF] Creating Alpaca trading client for live environment
[16:00:42 INF] Using API Key ID: AKGBPW5HD8LVI5C6NJUJ
[16:00:42 INF] Using Alpaca credentials from configuration for live environment
[16:00:42 INF] Creating Alpaca trading client for live environment
[16:00:42 INF] Using API Key ID: AKGBPW5HD8LVI5C6NJUJ
[16:00:42 INF] Start processing HTTP request POST https://discord.com/api/v10/channels/1385057459814797383/messages
[16:00:42 INF] Sending HTTP request POST https://discord.com/api/v10/channels/1385057459814797383/messages
[16:00:42 INF] Received HTTP response headers after 142.1458ms - 401
[16:00:42 INF] End processing HTTP request after 143.821ms - 401
[16:00:42 WRN] Discord API returned Unauthorized: Unauthorized. Response: {"message": "401: Unauthorized", "code": 0}
[16:00:42 INF] Portfolio snapshot sent - Equity: 17,061.87, Day P&L: 0.00, Positions: 1
[16:00:42 INF] Completed portfolio management: 1 positions monitored, 1 notifications sent
[16:00:42 INF] Portfolio management completed successfully: 1 positions monitored
[16:00:42 INF] Orchestrated trading cycle completed: Trading cycle completed successfully, Total time: 44854ms
[16:00:42 INF] ? Trading cycle #77 completed
[16:00:42 INF] Start processing HTTP request GET https://finance.yahoo.com/quote/%5EVIX
[16:00:42 INF] Sending HTTP request GET https://finance.yahoo.com/quote/%5EVIX
[16:00:43 INF] Received HTTP response headers after 738.3933ms - 200
[16:00:43 INF] End processing HTTP request after 739.9501ms - 200
[16:00:43 INF] Start processing HTTP request GET https://www.google.com/finance/quote/VIX:INDEXCBOE
[16:00:43 INF] Sending HTTP request GET https://www.google.com/finance/quote/VIX:INDEXCBOE
[16:00:43 INF] Received HTTP response headers after 209.5035ms - 200
[16:00:43 INF] End processing HTTP request after 211.8269ms - 200
[16:00:43 INF] VIX retrieved from Google Finance: 17.04
[16:00:43 INF] VIX retrieved from web scraping: 17.04
[16:00:43 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 5), @__startDate_1='?' (DbType = DateTime), @__endDate_2='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."High", "c"."Low", "c"."Open", "c"."Symbol", "c"."TimeUtc", "c"."Volume"
FROM "CachedIndexBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeUtc" >= @__startDate_1 AND "c"."TimeUtc" <= @__endDate_2
ORDER BY "c"."TimeUtc"
[16:00:43 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 5)], CommandType='Text', CommandTimeout='30']
SELECT MAX("c"."TimeUtc")
FROM "CachedIndexBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0
[16:00:43 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:00:43 ET
[16:00:43 INF] Start processing HTTP request GET https://finance.yahoo.com/quote/%5EVIX
[16:00:43 INF] Sending HTTP request GET https://finance.yahoo.com/quote/%5EVIX
[16:00:44 INF] Received HTTP response headers after 929.4207ms - 200
[16:00:44 INF] End processing HTTP request after 931.3784ms - 200
[16:00:44 INF] Start processing HTTP request GET https://www.google.com/finance/quote/VIX:INDEXCBOE
[16:00:44 INF] Sending HTTP request GET https://www.google.com/finance/quote/VIX:INDEXCBOE
[16:00:45 INF] Received HTTP response headers after 168.6825ms - 200
[16:00:45 INF] End processing HTTP request after 171.0815ms - 200
[16:00:45 INF] VIX retrieved from Google Finance: 17.04
[16:00:45 INF] VIX retrieved from web scraping: 17.04
[16:00:45 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 5), @__startDate_1='?' (DbType = DateTime), @__endDate_2='?' (DbType = DateTime)], CommandType='Text', CommandTimeout='30']
SELECT "c"."Id", "c"."CachedAt", "c"."Close", "c"."High", "c"."Low", "c"."Open", "c"."Symbol", "c"."TimeUtc", "c"."Volume"
FROM "CachedIndexBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0 AND "c"."TimeUtc" >= @__startDate_1 AND "c"."TimeUtc" <= @__endDate_2
ORDER BY "c"."TimeUtc"
[16:00:45 INF] Executed DbCommand (0ms) [Parameters=[@__symbol_0='?' (Size = 5)], CommandType='Text', CommandTimeout='30']
SELECT MAX("c"."TimeUtc")
FROM "CachedIndexBars" AS "c"
WHERE "c"."Symbol" = @__symbol_0
[16:00:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:00:45 ET
[16:00:45 INF] ?? Next cycle in 30 minutes (Market closed: 30 minutes)
[16:30:45 INF] ?? Starting trading cycle #78
[16:30:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:30:45 ET
[16:30:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:30:45 ET
[16:35:45 INF] ?? Starting trading cycle #79
[16:35:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:35:45 ET
[16:35:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:35:45 ET
[16:40:45 INF] ?? Starting trading cycle #80
[16:40:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:40:45 ET
[16:40:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:40:45 ET
[16:45:45 INF] ?? Starting trading cycle #81
[16:45:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:45:45 ET
[16:45:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:45:45 ET
[16:50:45 INF] ?? Starting trading cycle #82
[16:50:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:50:45 ET
[16:50:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:50:45 ET
[16:55:45 INF] ?? Starting trading cycle #83
[16:55:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:55:45 ET
[16:55:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 16:55:45 ET
[17:00:45 INF] ?? Starting trading cycle #84
[17:00:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:00:45 ET
[17:00:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:00:45 ET
[17:05:45 INF] ?? Starting trading cycle #85
[17:05:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:05:45 ET
[17:05:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:05:45 ET
[17:10:45 INF] ?? Starting trading cycle #86
[17:10:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:10:45 ET
[17:10:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:10:45 ET
[17:15:45 INF] ?? Starting trading cycle #87
[17:15:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:15:45 ET
[17:15:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:15:45 ET
[17:20:45 INF] ?? Starting trading cycle #88
[17:20:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:20:45 ET
[17:20:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:20:45 ET
[17:25:45 INF] ?? Starting trading cycle #89
[17:25:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:25:45 ET
[17:25:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:25:45 ET
[17:30:45 INF] ?? Starting trading cycle #90
[17:30:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:30:45 ET
[17:30:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:30:45 ET
[17:35:45 INF] ?? Starting trading cycle #91
[17:35:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:35:45 ET
[17:35:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:35:45 ET
[17:40:45 INF] ?? Starting trading cycle #92
[17:40:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:40:45 ET
[17:40:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:40:45 ET
[17:45:45 INF] ?? Starting trading cycle #93
[17:45:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:45:45 ET
[17:45:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:45:45 ET
[17:50:45 INF] ?? Starting trading cycle #94
[17:50:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:50:45 ET
[17:50:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:50:45 ET
[17:55:45 INF] ?? Starting trading cycle #95
[17:55:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:55:45 ET
[17:55:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 17:55:45 ET
[18:00:45 INF] ?? Starting trading cycle #96
[18:00:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:00:45 ET
[18:00:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:00:45 ET
[18:05:45 INF] ?? Starting trading cycle #97
[18:05:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:05:45 ET
[18:05:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:05:45 ET
[18:10:45 INF] ?? Starting trading cycle #98
[18:10:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:10:45 ET
[18:10:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:10:45 ET
[18:15:45 INF] ?? Starting trading cycle #99
[18:15:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:15:45 ET
[18:15:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:15:45 ET
[18:20:45 INF] ?? Starting trading cycle #100
[18:20:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:20:45 ET
[18:20:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:20:45 ET
[18:25:45 INF] ?? Starting trading cycle #101
[18:25:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:25:45 ET
[18:25:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:25:45 ET
[18:30:45 INF] ?? Starting trading cycle #102
[18:30:45 INF] Live trading blocked: Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:30:45 ET
[18:30:45 INF] ?? Trading paused - Outside market hours (9:30 AM - 4:00 PM ET) - current time: 18:30:45 ET
[18:35:32 INF] ?? Shutdown requested - completing current cycle...
[18:35:32 INF] ?? Continuous trading stopped after 102 cycles
[18:35:32 INF] QuoteVolatilityGuard disposed
[18:35:32 INF] OptimizedRedisConnectionService disposed

C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\SmaTrendFollower.Console>dotnet run --project SmaTrendFollower.Console --configuration Release -- metrics
MSBUILD : error MSB1009: Project file does not exist.
Switch: SmaTrendFollower.Console

The build failed. Fix the build errors and run again.

C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\SmaTrendFollower.Console>dotnet run --project SmaTrendFollower.Console --configuration Release -- --check-account
MSBUILD : error MSB1009: Project file does not exist.
Switch: SmaTrendFollower.Console

The build failed. Fix the build errors and run again.

C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\SmaTrendFollower.Console>

