# ARKW Database Caching Fix - Temporary Exclusion Solution

## Issue Description

The Discord warning message:
```
[Warning] Continuing without caching for "ARKW" "Day" due to database error
```

This error indicates that the ARKW symbol was causing database caching issues, similar to other problematic symbols like BELFB and BATRA.

## Root Cause

The error occurs when the PostgreSQL database encounters issues while caching stock bar data for the ARKW symbol. This could be due to:
- Binary format incompatibilities
- Network timeouts
- Connection issues
- Data format problems

## Solution: Temporary Cache Exclusion System

Instead of permanent blacklisting, implemented a Redis-based temporary exclusion system that automatically retries problematic symbols after a configurable period.

### New Service: TemporaryCacheExclusionService

```csharp
public interface ITemporaryCacheExclusionService
{
    Task<bool> IsExcludedAsync(string symbol);
    Task AddExclusionAsync(string symbol, string reason, TimeSpan? duration = null);
    Task RemoveExclusionAsync(string symbol);
    Task<CacheExclusionInfo?> GetExclusionInfoAsync(string symbol);
    Task<IReadOnlyList<CacheExclusionInfo>> GetAllExclusionsAsync();
}
```

### Key Features
- **Automatic TTL**: Symbols are excluded for 24 hours by default, then automatically retry
- **Redis Storage**: Uses Redis with TTL for automatic cleanup
- **Configurable Duration**: Can override default exclusion period
- **Monitoring**: Track exclusion reasons and expiration times
- **Case Insensitive**: Handles symbol normalization

### Database Context Integration

Updated `StockBarCacheDbContext.cs` to automatically add temporary exclusions when database errors occur:

```csharp
catch (Exception ex)
{
    _logger?.LogError(ex, "Database error caching bars for {Symbol} {TimeFrame}: {ErrorMessage}", symbol, timeFrame, ex.Message);

    var exclusionService = _serviceProvider?.GetService<ITemporaryCacheExclusionService>();
    if (exclusionService != null)
    {
        await exclusionService.AddExclusionAsync(symbol,
            $"Database caching error: {ex.GetType().Name}",
            TimeSpan.FromHours(24));
    }

    _logger?.LogWarning("Continuing without caching for {Symbol} {TimeFrame} due to database error", symbol, timeFrame);
}
```

### Cache Service Updates

Updated both `StockBarCacheService.cs` and `ThreadSafeStockBarCacheService.cs` to check temporary exclusions:

```csharp
// Check for temporarily excluded symbols that cause database issues
var exclusionService = _serviceProvider?.GetService<ITemporaryCacheExclusionService>();
if (exclusionService != null && await exclusionService.IsExcludedAsync(symbol))
{
    _logger.LogDebug("Skipping cache retrieval for temporarily excluded symbol: {Symbol}", symbol);
    return new List<IBar>(); // Return empty to force API fetch
}
```

## Advantages Over Permanent Blacklisting

1. **Automatic Recovery**: Symbols are automatically retried after 24 hours
2. **Self-Healing**: Temporary issues resolve themselves without manual intervention
3. **Reduced Maintenance**: No need to manually manage permanent blacklists
4. **Better Monitoring**: Track exclusion reasons and durations
5. **Flexible Duration**: Can adjust exclusion periods based on error type

## Impact

### Positive Effects:
- **Eliminates repeated Discord warnings** for problematic symbols
- **Automatic retry mechanism** prevents permanent loss of caching capability
- **Self-healing system** reduces manual maintenance
- **Better monitoring** of database caching issues
- **Maintains trading functionality** - symbols can still be traded without caching

### Performance Impact:
- **Minimal impact** - Excluded symbols fetch data from APIs instead of cache
- **No trading disruption** - all trading functionality remains intact
- **Automatic recovery** - caching resumes after exclusion period expires

## Files Modified

1. `SmaTrendFollower.Console/Services/TemporaryCacheExclusionService.cs` - New temporary exclusion service
2. `SmaTrendFollower.Console/Data/StockBarCacheDbContext.cs` - Auto-exclusion on database errors
3. `SmaTrendFollower.Console/Services/StockBarCacheService.cs` - Check temporary exclusions
4. `SmaTrendFollower.Console/Services/ThreadSafeStockBarCacheService.cs` - Check temporary exclusions
5. `SmaTrendFollower.Console/Configuration/ServiceConfiguration.cs` - Register new service
6. `SmaTrendFollower.Console/Configuration/ProblematicSymbolsConfig.cs` - Removed ARKW from permanent list
7. `SmaTrendFollower.Tests.Core/Unit/ProblematicSymbolsConfigTests.cs` - Updated for ARKW removal
8. `SmaTrendFollower.Tests.Core/Unit/TemporaryCacheExclusionServiceTests.cs` - New test coverage

## Verification

- [x] Temporary exclusion service implemented
- [x] Database context auto-exclusion on errors
- [x] Cache services check temporary exclusions
- [x] Service registered in DI container
- [x] ARKW removed from permanent blacklist
- [x] Tests updated for new behavior
- [x] System builds successfully

## Monitoring

Monitor Discord for:
1. **Elimination of repeated ARKW database caching warnings**
2. **Automatic retry attempts** after 24-hour exclusion periods
3. **Successful caching resumption** for previously problematic symbols

The system should now handle database caching errors gracefully with automatic recovery.
