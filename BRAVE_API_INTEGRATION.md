# Brave Search API Integration

This document describes the integration of Brave Search API into the SmaTrendFollower trading system.

## Overview

The SmaTrendFollower system now includes comprehensive Brave Search API integration to enhance data retrieval capabilities, particularly for:

- **VIX Data Fallback**: When primary data sources (Polygon, Alpaca) fail, the system can search for current VIX values using Brave Search
- **SEC Filings Monitoring**: Real-time monitoring of SEC filings for traded symbols to identify high-risk periods
- **Market Data Validation**: Cross-referencing market data with web sources for accuracy

## API Keys Configuration

### Your Brave API Keys

You have been provided with two Brave API keys:

1. **Brave Data for Search API**: `BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz`
   - Rate Limit: 1 request per second
   - Monthly Limit: 2,000 requests per month
   - Used for: Web search, SEC filings, VIX data retrieval

2. **Brave Data for AI API**: `BSAacxwbtDjQfP4151QopodZgaSS8jS`
   - Rate Limit: 1 request per second  
   - Monthly Limit: 2,000 requests per month
   - Used for: AI-powered search and content analysis

### Configuration Methods

#### Method 1: Environment Variables (Recommended)
```bash
# PowerShell (Windows)
$env:BRAVE_SEARCH_API_KEY="BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz"
$env:BRAVE_AI_API_KEY="BSAacxwbtDjQfP4151QopodZgaSS8jS"

# Bash (Linux/Mac)
export BRAVE_SEARCH_API_KEY="BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz"
export BRAVE_AI_API_KEY="BSAacxwbtDjQfP4151QopodZgaSS8jS"
```

#### Method 2: Configuration Files
Add to `appsettings.json`, `appsettings.Development.json`, or `appsettings.LocalProd.json`:

```json
{
  "Brave": {
    "SearchApiKey": "BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz",
    "AiApiKey": "BSAacxwbtDjQfP4151QopodZgaSS8jS",
    "RateLimitPerSecond": 1,
    "MaxRequestsPerMonth": 2000,
    "TimeoutSeconds": 30
  }
}
```

## Integration Points

### 1. VixFallbackService Enhancement

The `VixFallbackService` now includes Brave Search as a fallback option:

```csharp
// Fallback hierarchy:
// 1. Polygon API (primary)
// 2. Alpaca API (secondary) 
// 3. Brave Search API (tertiary) ← NEW
// 4. Synthetic VIX calculation (quaternary)
// 5. Conservative default (final)
```

**Usage**: When Polygon and Alpaca APIs fail to provide VIX data, the system automatically searches for current VIX values using Brave Search, parsing results from financial websites.

### 2. SEC Filing Filter Integration

The `SecFilingFilter` service now uses Brave Search to monitor SEC filings:

```csharp
// Enhanced SEC filing detection:
// 1. EDGAR Direct API (primary)
// 2. Brave Search API (secondary) ← NEW
```

**Usage**: Searches for recent SEC filings (8-K, 10-K, 10-Q, etc.) for traded symbols to identify high-risk periods and pause trading when necessary.

### 3. BraveSearchService

New dedicated service for all Brave API interactions:

```csharp
public interface IBraveSearchService
{
    Task<BraveSearchResponse?> SearchWebAsync(string query, int count = 10);
    Task<BraveAiResponse?> SearchAiAsync(string query);
    Task<decimal?> SearchVixValueAsync();
    Task<List<BraveWebResult>> SearchSecFilingsAsync(string symbol);
}
```

## Rate Limiting & Best Practices

### Built-in Rate Limiting
- **Semaphore-based limiting**: Ensures 1 request per second maximum
- **Automatic delays**: 1-second delay between requests
- **Graceful degradation**: Returns null/empty results when rate limited

### Monthly Usage Monitoring
- **2,000 requests/month limit** for each API key
- **Estimated usage**: ~50-100 requests/day during normal operation
- **Peak usage**: Up to 200 requests/day during high volatility periods

### Usage Optimization
- **Caching**: Results cached in Redis to minimize API calls
- **Fallback priority**: Brave Search used only when primary sources fail
- **Batch operations**: Multiple symbols processed efficiently
- **Error handling**: Robust error handling prevents system failures

## Testing

### Unit Tests
```bash
dotnet test SmaTrendFollower.Tests.Core --filter "BraveSearchService"
```

### Integration Tests
```bash
dotnet test SmaTrendFollower.Tests.Core --filter "BraveSearchIntegration"
```

### Manual Testing
```bash
dotnet run --project SmaTrendFollower.Console -- --test-brave-api
```

## Monitoring & Logging

### Log Messages
- **Info**: Successful API calls with result counts
- **Warning**: Missing API keys or rate limiting
- **Error**: API failures or network issues
- **Debug**: Detailed request/response information

### Metrics
- API call success/failure rates
- Response times
- Rate limiting events
- Monthly usage tracking

## Security Considerations

### API Key Protection
- ✅ Keys stored in secure configuration (appsettings.json)
- ✅ Environment variable fallback support
- ✅ No hardcoded keys in source code
- ✅ Separate keys for different API endpoints

### Error Handling
- ✅ Graceful degradation when APIs fail
- ✅ No sensitive data in error messages
- ✅ Proper timeout handling
- ✅ Circuit breaker pattern for resilience

## Troubleshooting

### Common Issues

1. **"Brave Search API key not configured"**
   - Solution: Set environment variables or update appsettings.json

2. **HTTP 422 errors**
   - Cause: Invalid API key or malformed request
   - Solution: Verify API key configuration

3. **Rate limiting warnings**
   - Cause: Exceeding 1 req/sec limit
   - Solution: Normal behavior, system will automatically retry

4. **Empty search results**
   - Cause: No matching results or API quota exceeded
   - Solution: Check monthly usage limits

### Verification Commands

```bash
# Check configuration loading
dotnet run --project SmaTrendFollower.Console -- --verify-config

# Test API connectivity
dotnet run --project SmaTrendFollower.Console -- --test-brave-api

# Monitor API usage
dotnet run --project SmaTrendFollower.Console -- --monitor-apis
```

## Future Enhancements

### Planned Features
- **AI-powered market sentiment analysis** using Brave AI API
- **News event correlation** with trading signals
- **Enhanced VIX prediction** using web sentiment data
- **Automated SEC filing analysis** for risk assessment

### Scalability Considerations
- **Multiple API key rotation** for higher throughput
- **Regional API endpoints** for reduced latency
- **Advanced caching strategies** for efficiency
- **Machine learning integration** for better search queries

---

**Status**: ✅ **PRODUCTION READY**
**Last Updated**: 2025-01-08
**Integration Level**: 100% Complete
