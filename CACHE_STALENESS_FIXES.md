# Cache Staleness Fixes Implementation

## Overview

This document outlines the comprehensive fixes implemented to address cache staleness issues in the SmaTrendFollower system. The solution provides centralized, market-hours-aware staleness validation with configurable thresholds.

## Problem Statement

The system had several cache staleness issues:

1. **Inconsistent Staleness Thresholds**: Different services used hardcoded or inconsistent staleness thresholds
2. **Missing Market Hours Awareness**: Some services didn't differentiate between market hours (18-minute threshold) and after-hours (8-hour threshold)
3. **Incomplete Data Age Validation**: Some cache services didn't validate data timestamps before use
4. **Lack of Centralized Configuration**: Staleness settings were scattered across the codebase

## Solution Architecture

### 1. Centralized Configuration

**File**: `SmaTrendFollower.Console/Configuration/DataStalenessConfiguration.cs`

- `DataStalenessConfiguration`: Main configuration class
- `MarketHoursStalenessThresholds`: Strict thresholds for trading hours
- `AfterHoursStalenessThresholds`: Relaxed thresholds for after-hours
- `DataType` enum: Categorizes different types of cached data

**Configuration in appsettings.LocalProd.json**:
```json
{
  "DataStaleness": {
    "MarketHours": {
      "HistoricalBars": "00:18:00",
      "RealTimeQuotes": "00:02:00", 
      "IndexData": "00:15:00",
      "VixData": "00:15:00",
      "UniverseData": "04:00:00",
      "SignalData": "00:30:00",
      "IndicatorData": "00:20:00"
    },
    "AfterHours": {
      "HistoricalBars": "08:00:00",
      "RealTimeQuotes": "08:00:00",
      "IndexData": "08:00:00", 
      "VixData": "08:00:00",
      "UniverseData": "24:00:00",
      "SignalData": "08:00:00",
      "IndicatorData": "08:00:00"
    },
    "EnableStrictStalenessChecks": true,
    "RejectStaleData": true,
    "LogStalenessWarnings": true
  }
}
```

### 2. Validation Service

**File**: `SmaTrendFollower.Console/Services/DataStalenessValidationService.cs`

- `IDataStalenessValidationService`: Interface for staleness validation
- `DataStalenessValidationService`: Implementation with market-hours awareness
- `DataStalenessValidationResult`: Detailed validation result
- `DataStalenessException`: Exception for rejected stale data

**Key Features**:
- Market hours detection (9:30 AM - 4:00 PM ET, Monday-Friday)
- Configurable rejection of stale data
- Detailed logging of staleness warnings
- Extension methods for easy integration

### 3. Updated Cache Services

The following cache services were updated with staleness validation:

#### StockBarCacheService
- Added market-hours-aware staleness checking
- 18-minute threshold during market hours
- 8-hour threshold after hours
- Enhanced logging with staleness details

#### ThreadSafeStockBarCacheService
- Same staleness logic as StockBarCacheService
- Thread-safe implementation maintained
- Consistent logging format

#### IndexCacheService
- 15-minute threshold during market hours for index data
- 8-hour threshold after hours
- Specialized for index data requirements

### 4. Service Registration

**File**: `SmaTrendFollower.Console/Configuration/ServiceConfiguration.cs`

Added to `AddCoreInfrastructure()`:
```csharp
// Data staleness validation services
services.AddOptions<DataStalenessConfiguration>()
    .Configure<IConfiguration>((settings, configuration) =>
    {
        configuration.GetSection("DataStaleness").Bind(settings);
    });
services.AddScoped<IDataStalenessValidationService, DataStalenessValidationService>();
```

## Implementation Details

### Market Hours Detection

```csharp
private bool IsMarketHours()
{
    var easternTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow,
        TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));

    // Check if it's a weekday
    if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
        return false;

    // Check if it's during market hours (9:30 AM - 4:00 PM ET)
    var marketOpen = new TimeSpan(9, 30, 0);
    var marketClose = new TimeSpan(16, 0, 0);

    return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
}
```

### Staleness Validation Logic

```csharp
// First check: Does cache have data up to the requested end date
var hasRequiredData = latestCachedDate.Value >= requestedEndDate;

if (!hasRequiredData)
    return false;

// Second check: Is the latest cached data within staleness threshold
var dataAge = DateTime.UtcNow - latestCachedDate.Value;
var isMarketHours = IsMarketHours();

// Apply market-hours-aware staleness threshold
var stalenessThreshold = isMarketHours 
    ? TimeSpan.FromMinutes(18)  // Market hours: 18 minutes
    : TimeSpan.FromHours(8);    // After hours: 8 hours

var isDataFresh = dataAge <= stalenessThreshold;
```

## Testing

### Unit Tests

**File**: `SmaTrendFollower.Tests/Services/DataStalenessValidationServiceTests.cs`

Comprehensive test coverage including:
- Fresh data validation
- Stale data detection and rejection
- Market hours vs after-hours thresholds
- Configuration option testing
- Extension method validation
- Exception handling

### Test Scenarios

1. **Fresh Data**: Data within threshold should pass validation
2. **Stale Data**: Data exceeding threshold should be marked stale
3. **Configuration Toggles**: Respect EnableStrictStalenessChecks and RejectStaleData settings
4. **Market Hours Awareness**: Different thresholds for market vs after-hours
5. **Exception Handling**: Proper exception throwing for rejected stale data

## Benefits

### 1. Consistency
- Centralized staleness configuration
- Uniform validation logic across all cache services
- Consistent logging format

### 2. Market Awareness
- Different thresholds for market hours vs after-hours
- Prevents unnecessary data refreshes during off-hours
- Maintains strict requirements during trading hours

### 3. Configurability
- Easy adjustment of staleness thresholds
- Toggle strict checking on/off
- Control rejection behavior

### 4. Observability
- Detailed logging of staleness events
- Validation results with timestamps
- Performance metrics for staleness checks

### 5. Reliability
- Prevents use of stale data in trading decisions
- Graceful handling of cache misses
- Fallback mechanisms for data retrieval

## Migration Guide

### For Existing Services

1. **Inject IDataStalenessValidationService**:
```csharp
public MyService(IDataStalenessValidationService stalenessValidator)
{
    _stalenessValidator = stalenessValidator;
}
```

2. **Replace Custom Staleness Logic**:
```csharp
// Old way
var dataAge = DateTime.UtcNow - dataTimestamp;
var isStale = dataAge > TimeSpan.FromMinutes(30);

// New way
var result = await _stalenessValidator.ValidateDataFreshnessAsync(
    dataTimestamp, DataType.HistoricalBars, "MyService");
var isStale = result.IsStale;
```

3. **Use Extension Methods for Validation**:
```csharp
var validatedData = await _stalenessValidator.ValidateAndReturnAsync(
    data, dataTimestamp, DataType.HistoricalBars, "MyService");
```

## Configuration Reference

### Data Types and Default Thresholds

| Data Type | Market Hours | After Hours | Description |
|-----------|--------------|-------------|-------------|
| HistoricalBars | 18 minutes | 8 hours | Stock price bars |
| RealTimeQuotes | 2 minutes | 8 hours | Live price quotes |
| IndexData | 15 minutes | 8 hours | Index values (SPX, VIX) |
| VixData | 15 minutes | 8 hours | VIX volatility data |
| UniverseData | 4 hours | 24 hours | Stock universe lists |
| SignalData | 30 minutes | 8 hours | Trading signals |
| IndicatorData | 20 minutes | 8 hours | Technical indicators |

### Configuration Options

- `EnableStrictStalenessChecks`: Enable/disable staleness validation
- `RejectStaleData`: Throw exceptions for stale data
- `LogStalenessWarnings`: Log staleness events

## Monitoring and Alerts

The system now provides comprehensive staleness monitoring:

1. **Staleness Warnings**: Logged when data exceeds thresholds
2. **Rejection Events**: Logged when stale data is rejected
3. **Validation Metrics**: Performance tracking for staleness checks
4. **Configuration Validation**: Startup validation of staleness settings

## Future Enhancements

1. **Dynamic Thresholds**: Adjust thresholds based on market volatility
2. **Data Source Prioritization**: Different thresholds per data source
3. **Historical Staleness Tracking**: Track staleness patterns over time
4. **Automated Threshold Optimization**: ML-based threshold tuning
