using Microsoft.ML;
using Microsoft.ML.Data;

// Quick script to create the missing position sizing model
public class PositionSizingInput
{
    public float RankProb { get; set; }
    public float ATR_Pct { get; set; }
    public float AvgSpreadPct { get; set; }
}

public class PositionSizingOutput
{
    public float Score { get; set; }
}

public class PositionSizingTrainingData
{
    public float RankProb { get; set; }
    public float ATR_Pct { get; set; }
    public float AvgSpreadPct { get; set; }
    public float EquityPctRisk { get; set; }
}

class Program
{
    static void Main()
    {
        Console.WriteLine("🚀 Creating Position Sizing Model...");
        
        var mlContext = new MLContext(seed: 42);
        
        // Generate synthetic training data
        var trainingData = GenerateTrainingData();
        
        Console.WriteLine($"📊 Generated {trainingData.Count} training samples");
        
        // Convert to IDataView
        var dataView = mlContext.Data.LoadFromEnumerable(trainingData);
        
        Console.WriteLine("🔧 Building training pipeline...");
        
        // Create training pipeline
        var pipeline = mlContext.Transforms.Concatenate("Features", 
                nameof(PositionSizingTrainingData.RankProb),
                nameof(PositionSizingTrainingData.ATR_Pct),
                nameof(PositionSizingTrainingData.AvgSpreadPct))
            .Append(mlContext.Regression.Trainers.LightGbm(
                labelColumnName: nameof(PositionSizingTrainingData.EquityPctRisk),
                featureColumnName: "Features",
                numberOfLeaves: 31,
                minimumExampleCountPerLeaf: 20,
                learningRate: 0.1,
                numberOfIterations: 100));
        
        Console.WriteLine("🎯 Training model...");
        
        // Train the model
        var model = pipeline.Fit(dataView);
        
        Console.WriteLine("✅ Training completed");
        
        // Test the model
        var predictionEngine = mlContext.Model.CreatePredictionEngine<PositionSizingInput, PositionSizingOutput>(model);
        
        // Test with sample data
        var testInput = new PositionSizingInput
        {
            RankProb = 0.8f,
            ATR_Pct = 0.02f,
            AvgSpreadPct = 0.001f
        };
        
        var prediction = predictionEngine.Predict(testInput);
        Console.WriteLine($"🧪 Test prediction: {prediction.Score:P3} equity risk");
        
        // Save the model
        var modelPath = @"..\SmaTrendFollower.Console\Model\position_model.zip";
        Console.WriteLine($"💾 Saving model to {modelPath}...");

        // Ensure directory exists
        var directory = Path.GetDirectoryName(modelPath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        mlContext.Model.Save(model, dataView.Schema, modelPath);
        
        Console.WriteLine("✅ Position sizing model created successfully!");
        Console.WriteLine($"📁 Model saved to: {Path.GetFullPath(modelPath)}");
    }
    
    static List<PositionSizingTrainingData> GenerateTrainingData()
    {
        var random = new Random(42);
        var data = new List<PositionSizingTrainingData>();
        
        // Generate 1000 synthetic training samples
        for (int i = 0; i < 1000; i++)
        {
            var rankProb = (float)random.NextDouble(); // 0.0 to 1.0
            var atrPct = (float)(random.NextDouble() * 0.08 + 0.005); // 0.5% to 8.5%
            var spreadPct = (float)(random.NextDouble() * 0.005 + 0.0001); // 0.01% to 0.51%
            
            // Calculate target equity risk percentage using heuristics
            var baseRisk = 0.01f; // 1% base risk
            
            // Higher rank probability = higher position size
            var rankMultiplier = 0.5f + rankProb * 1.5f; // 0.5x to 2.0x
            
            // Higher volatility = lower position size
            var volatilityMultiplier = Math.Max(0.3f, 1.0f / (1.0f + atrPct * 20)); // 0.3x to 1.0x
            
            // Higher spread = slightly lower position size
            var spreadMultiplier = Math.Max(0.8f, 1.0f - spreadPct * 100); // 0.8x to 1.0x
            
            var equityPctRisk = baseRisk * rankMultiplier * volatilityMultiplier * spreadMultiplier;
            
            // Clamp to reasonable range
            equityPctRisk = Math.Clamp(equityPctRisk, 0.002f, 0.05f); // 0.2% to 5%
            
            data.Add(new PositionSizingTrainingData
            {
                RankProb = rankProb,
                ATR_Pct = atrPct,
                AvgSpreadPct = spreadPct,
                EquityPctRisk = equityPctRisk
            });
        }
        
        return data;
    }
}
