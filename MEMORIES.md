# SmaTrendFollower Project Memories

This file contains the accumulated knowledge and preferences from previous interactions between the AI assistant and the user for the SmaTrendFollower project.

## General Preferences

- User prefers .NET 8 solutions with console + xUnit test projects.
- User prefers DI architecture.
- User has extremely high risk tolerance and prefers immediate live trading deployment even when critical safety issues are identified, rejecting multi-week validation timelines.
- User is ready to proceed with live trading deployment with real money, confirming high risk tolerance for production deployment.
- User prefers to proceed with live trading deployment immediately even when safety concerns are identified, indicating high risk tolerance for production deployment.
- User wants only one project, not two separate projects like SmaTrendFollower and AlpacaMomentumBot.
- User prefers implementing safety guardrails for trading systems before production deployment.
- User prefers using Augment remote agent with setup scripts to prepare remote environments for better code changes, testing, and building.
- User prefers to fix CS1998 compiler warnings (async methods without await operators) rather than leaving them as non-critical warnings.
- User requires 100% production readiness (47/47 services), 100% test coverage, and 100% documentation coverage as deployment criteria for the trading system, with enhanced services upgraded from Complex to Production status.
- User prefers fixing core trading functionality test failures over experimental/advanced features like NLP sentiment analysis, enhanced signal filtering, complex options logic, regime detection edge cases, and system health event handling.

## Libraries

- User prefers Alpaca.Markets, Skender.Stock.Indicators, Serilog for logging, DotNetEnv, and FluentAssertions/Moq for testing.

## Architecture

- User prefers scoped services for trade cycles with SignalGenerator, RiskManager, PortfolioGate, TradeExecutor components.
- User prefers AlpacaClientFactory and PolygonClientFactory.
- User prefers single-shot execution flow (guard → ExecuteCycleAsync → exit) over scheduled execution, removing ScheduleService and Cronos package while ensuring ILogger and SystemTimeProvider are registered.
- Current SmaTrendFollower codebase has namespace inconsistencies (SmaTrendFollower.Services vs AlpacaMomentumBot), incomplete service implementations, and needs to be reindexed to align with user's preferred architecture: single-shot execution, universe screening with SPY+top-500 tickers, PortfolioGate SPY SMA200 check, RiskManager 10bps per $100k cap, and TradeExecutor Limit-on-Open pattern.
- User prefers real-time streaming architecture: Alpaca websocket for equities trading/exit, Polygon websocket for index/volatility triggers like VIX spikes.

## Market Data

- User prefers unified market data service combining Alpaca.Markets SDK for stocks/ETFs and HttpClient for Polygon API for index data, using PolygonClientFactory pattern with IHttpClientFactory and Bearer token authentication.
- User has Alpaca Markets levels 1-3 options + AlgoTrader Plus market data, and Polygon Indices Starter + Options Starter subscriptions.
- User provided Polygon API key: ******************************** for the SmaTrendFollower project.
- For SmaTrendFollower data integration: use Alpaca for account/positions/fills and equity/ETF bars with Polygon fallback for throttles/history, use Polygon Indices for index data (SPX/VIX), and Polygon Options for Greeks/IV/OI.
- Always convert Polygon timestamps (milliseconds since epoch, UTC) to DateTimeOffset before mixing with Alpaca bars for proper clock alignment.
- Alpaca API has 200 requests/minute limit, Polygon Starter has 5 requests/second limit, and both should be wrapped in exponential back-off retry policies.
- For SmaTrendFollower: all order placement must go through Alpaca only, Polygon is strictly read-only for market data.
- PolygonClientFactory should use a single .env file and inject the API key into request headers or query parameters.
- User prefers caching index bars nightly in SQLite to avoid re-downloading unchanged history.
- User prefers SQLite bar cache implementation to store last 1-year of bars and refresh only the differential data to minimize API calls.
- User prefers cache performance optimizations including bulk insert optimization, connection pooling, compression, cache warming, and metrics for SQLite bar cache systems.

## Signal Generation

- User prefers DynamicUniverseProvider service with Redis caching (universe:today key), filtering by price >$10, volume >1M shares, volatility >2% daily stddev, and integration with SignalGenerator/TradingService replacing static symbol lists.
- SignalGenerator should implement universe screening: pull 250 daily bars for SPY + top-500 tickers, filter by close>sma50 && close>sma200 && atr14/close<0.03, rank by sixMonthReturn descending, return top N symbols with synthetic data testing for filtering/ranking validation.
- User prefers ranking strategy that concentrates capital in leaders by selecting top N symbols (default 10) ranked by 6-month total return for position rotation.
- For SmaTrendFollower strategy: primary trend filter requires Close > 200-day SMA AND Close > 50-day SMA to stay in broad uptrends.
- For SmaTrendFollower strategy: add volatility throttle filter where 14-day ATR / Close < 3% to avoid signals during whipsaw regimes.

## Portfolio Management

- PortfolioGate.ShouldTradeAsync() should fetch SPY bars and return false if SPY close < SPY sma200, else true, with both paths tested via Moq on data client.

## Risk Management

- RiskManager.CalculateQuantity() should use riskDollars = min(account.Equity * 0.01m, 1000m) for 10bps per $100k cap, calculate qty = riskDollars / (atr14 * price), return decimal for fractional shares, and test qty <= riskDollars / price tolerance.
- User prefers dynamic risk tolerance calculation that automatically adjusts safety parameters based on current account size as funds are added or withdrawn.

## Trade Execution

- User prefers trade execution pattern: cancel existing orders for symbol, submit Limit-on-Open Buy at lastClose * 1.002m, place GTC stop-loss Sell at entry - 2×ATR.
- User prefers 2x ATR trailing stop-loss strategy updated daily for capital preservation.
- Fixed SmaTrendFollower compilation errors related to Moq expression trees with optional CancellationToken parameters by explicitly including It.IsAny<CancellationToken>() in Verify calls and updating TradeExecutor implementation.

## Notifications

- User prefers Discord bot token + channel ID approach over webhook URL for Discord notifications in SmaTrendFollower.
- User prefers Discord channel ID 1385057459814797383 for SmaTrendFollower notifications.
- User prefers Discord messages to be sent using bot token MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo for channel 1385057459814797383.

## Caching

- User prefers Redis cache warming service pattern with specific key naming conventions (stop:<symbol>, signal:<symbol>:<yyyymmdd>, block:<symbol>:<yyyymmdd>) and pre-market cache loading from SQLite for trailing stops and signal flags to optimize trading system performance.
- User's Redis server for SmaTrendFollower is running at *************:6379 with no password authentication required.

## Market Regime

- User prefers MarketRegimeService with SPY 100-day analysis using 200SMA slope, ATR volatility, and return-to-drawdown ratio to detect TrendingUp/TrendingDown/Sideways/Volatile regimes, cached in Redis with 24h TTL, and integrated into TradingService to skip trades during Volatile/TrendingDown regimes.

## Credentials

- User provided Alpaca paper trading credentials: key PK0AM3WB1CES3YBQPGR0, secret 2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf for testing purposes.

## Testing Preferences (Added 2025-01-21)

- User prefers optimized test performance with reduced data sizes and minimal delays
- User prefers proper integration test configuration that skips tests when API credentials are unavailable
- User prefers test timeout configurations to prevent hanging tests:
  - Unit tests: 5 seconds
  - Integration tests: 30 seconds  
  - Performance tests: 60 seconds
  - Network/API tests: 45 seconds
  - Streaming tests: 90 seconds
- User prefers eliminating blocking operations (.Wait() calls) in favor of async/await patterns
- User prefers reduced concurrent operations in tests for better performance (10→3-5 tasks)
- User prefers test categorization with proper traits for filtering (Unit, Integration, Performance, etc.)
- User prefers PowerShell test runner scripts for different execution modes
