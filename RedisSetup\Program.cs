using StackExchange.Redis;
using System.Text.Json;

namespace RedisSetup;

class Program
{
    private static readonly string[] UniverseSymbols = {
        "SPY", "QQQ", "IWM", "VTI", "VEA", "VWO", "AGG", "TLT", "GLD", "VNQ",
        "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "BRK.B", "UNH", "JNJ",
        "V", "PG", "JPM", "HD", "MA", "DIS", "PYPL", "ADBE", "NFLX", "CRM",
        "CMCSA", "PEP", "ABT", "COST", "TMO", "AVGO", "ACN", "NKE", "MRK", "LLY",
        "DHR", "NEE", "VZ", "ABBV", "KO", "PFE", "WMT", "INTC", "CIS", "MDT"
    };

    static async Task Main(string[] args)
    {
        var redisHost = args.Length > 0 ? args[0] : "*************:6379";
        var force = args.Contains("--force");

        Console.WriteLine("🚀 SmaTrendFollower Redis Cache Setup");
        Console.WriteLine($"Target: {redisHost}");
        Console.WriteLine();

        try
        {
            // Connect to Redis
            Console.WriteLine("🔌 Connecting to Redis...");
            var redis = ConnectionMultiplexer.Connect(redisHost);
            var db = redis.GetDatabase();

            // Test connection
            await db.StringSetAsync("test:connection", "ok", TimeSpan.FromSeconds(10));
            await db.KeyDeleteAsync("test:connection");
            Console.WriteLine("✅ Redis connection successful!");
            Console.WriteLine();

            // Check existing data
            var existingUniverse = await db.StringGetAsync("universe:candidates");
            if (existingUniverse.HasValue && !force)
            {
                Console.WriteLine("⚠️ Redis cache already contains data. Use --force to overwrite.");
                Console.Write("Continue anyway? (y/N): ");
                var response = Console.ReadLine();
                if (response?.ToLower() != "y")
                {
                    Console.WriteLine("❌ Setup cancelled by user");
                    return;
                }
            }

            Console.WriteLine("📋 Setting up Redis cache structures...");
            Console.WriteLine();

            // Set up all data structures
            await SetUniverseData(db);
            await SetSignalFlags(db, UniverseSymbols);
            await SetThrottleFlags(db, UniverseSymbols);
            await SetVixData(db);
            await SetRegimeData(db);
            await SetHealthChecks(db);

            Console.WriteLine();
            Console.WriteLine("🎉 Redis cache setup completed successfully!");
            Console.WriteLine();

            // Show summary
            Console.WriteLine("📊 Cache Summary:");
            var server = redis.GetServer(redisHost);
            var keys = server.Keys(pattern: "*").ToArray();
            Console.WriteLine($"   Total keys created: {keys.Length}");
            Console.WriteLine($"   Universe symbols: {UniverseSymbols.Length}");
            Console.WriteLine($"   Signal flags: {UniverseSymbols.Length}");
            Console.WriteLine($"   Throttle flags: {UniverseSymbols.Length}");
            Console.WriteLine("   VIX data: Initialized");
            Console.WriteLine("   Regime data: Normal");
            Console.WriteLine("   Health checks: Healthy");

            redis.Dispose();
            Console.WriteLine();
            Console.WriteLine("✅ SmaTrendFollower Redis cache is ready for trading!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Environment.Exit(1);
        }
    }

    static async Task SetUniverseData(IDatabase db)
    {
        Console.WriteLine("📊 Setting up universe data...");

        var universeData = new
        {
            symbols = UniverseSymbols,
            generatedAt = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
            candidateCount = UniverseSymbols.Length,
            qualifiedCount = UniverseSymbols.Length,
            filterCriteria = new
            {
                minPrice = 10.0,
                minVolume = 1000000,
                minMarketCap = **********
            }
        };

        var json = JsonSerializer.Serialize(universeData);
        var ttl = TimeSpan.FromHours(24);

        var tasks = new[]
        {
            db.StringSetAsync("universe:candidates", json, ttl),
            db.StringSetAsync("universe:today", json, ttl),
            db.StringSetAsync("universe:filtered", json, ttl)
        };

        var results = await Task.WhenAll(tasks);
        if (results.All(r => r))
        {
            Console.WriteLine($"✅ Universe data created with {UniverseSymbols.Length} symbols");
        }
        else
        {
            Console.WriteLine("❌ Failed to set universe data");
        }
    }

    static async Task SetSignalFlags(IDatabase db, string[] symbols)
    {
        Console.WriteLine("🚦 Setting up signal flags...");
        var today = DateTime.UtcNow.ToString("yyyy-MM-dd");
        var todayKey = DateTime.UtcNow.ToString("yyyyMMdd");

        var tasks = symbols.Select(async symbol =>
        {
            var signalFlag = new
            {
                symbol = symbol,
                tradingDate = today,
                signalTriggered = false,
                triggeredAt = (DateTime?)null,
                signalStrength = (decimal?)null,
                metadata = (string?)null
            };

            var key = $"signal:{symbol}:{todayKey}";
            return await db.StringSetAsync(key, JsonSerializer.Serialize(signalFlag), TimeSpan.FromHours(24));
        });

        var results = await Task.WhenAll(tasks);
        var count = results.Count(r => r);
        Console.WriteLine($"✅ Created {count} signal flags for {today}");
    }

    static async Task SetThrottleFlags(IDatabase db, string[] symbols)
    {
        Console.WriteLine("🛑 Setting up throttle flags...");
        var today = DateTime.UtcNow.ToString("yyyy-MM-dd");
        var todayKey = DateTime.UtcNow.ToString("yyyyMMdd");

        var tasks = symbols.Select(async symbol =>
        {
            var throttleFlag = new
            {
                symbol = symbol,
                tradingDate = today,
                isBlocked = false,
                blockReason = (string?)null,
                blockedAt = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                blockedBy = "system"
            };

            var key = $"throttle:{symbol}:{todayKey}";
            return await db.StringSetAsync(key, JsonSerializer.Serialize(throttleFlag), TimeSpan.FromHours(24));
        });

        var results = await Task.WhenAll(tasks);
        var count = results.Count(r => r);
        Console.WriteLine($"✅ Created {count} throttle flags for {today}");
    }

    static async Task SetVixData(IDatabase db)
    {
        Console.WriteLine("📈 Setting up VIX data...");

        var vixData = new
        {
            value = 16.73m,
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
            source = "synthetic",
            quality = "good"
        };

        var json = JsonSerializer.Serialize(vixData);
        var ttl = TimeSpan.FromMinutes(10);

        var tasks = new[]
        {
            db.StringSetAsync("vix:current", json, ttl),
            db.StringSetAsync("vix:source:primary", json, ttl)
        };

        var results = await Task.WhenAll(tasks);
        if (results.All(r => r))
        {
            Console.WriteLine("✅ VIX data initialized (16.73)");
        }
        else
        {
            Console.WriteLine("❌ Failed to set VIX data");
        }
    }

    static async Task SetRegimeData(IDatabase db)
    {
        Console.WriteLine("🎯 Setting up regime data...");

        var regimeData = new
        {
            regime = "Normal",
            detectedAt = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
            confidence = 0.85m,
            vixLevel = 16.73m,
            spyTrend = "Bullish",
            metadata = "Auto-initialized"
        };

        var json = JsonSerializer.Serialize(regimeData);
        var ttl = TimeSpan.FromHours(4);

        var tasks = new[]
        {
            db.StringSetAsync("regime:current", json, ttl),
            db.StringSetAsync("index:regime:SPY", json, ttl)
        };

        var results = await Task.WhenAll(tasks);
        if (results.All(r => r))
        {
            Console.WriteLine("✅ Regime data initialized (Normal)");
        }
        else
        {
            Console.WriteLine("❌ Failed to set regime data");
        }
    }

    static async Task SetHealthChecks(IDatabase db)
    {
        Console.WriteLine("🏥 Setting up health check data...");

        var healthData = new
        {
            status = "Healthy",
            lastCheck = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
            services = new
            {
                redis = "Healthy",
                alpaca = "Healthy",
                polygon = "Healthy",
                database = "Healthy"
            }
        };

        var result = await db.StringSetAsync("health_check_system", JsonSerializer.Serialize(healthData), TimeSpan.FromMinutes(5));

        if (result)
        {
            Console.WriteLine("✅ Health check data initialized");
        }
        else
        {
            Console.WriteLine("❌ Failed to set health check data");
        }
    }
}
