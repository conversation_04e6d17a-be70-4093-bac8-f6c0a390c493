# SQLite Connection Pooling with Write Serialization

## Overview

The SQLite Connection Pooling system eliminates database lock contention through intelligent connection management and write serialization. This solution provides a robust alternative or complement to the Redis-first caching approach.

## Problem Solved

### SQLite Concurrency Challenges
- **Database Lock Errors**: `SQLite Error 5: 'database is locked'`
- **Connection Overhead**: Creating new connections for each operation
- **Write Contention**: Multiple threads attempting simultaneous writes
- **Resource Waste**: Inefficient connection lifecycle management

### Solution Benefits
- **Write Serialization**: Single-writer pattern eliminates lock contention
- **Connection Reuse**: Pool management reduces connection overhead
- **Concurrent Reads**: Multiple simultaneous read operations supported
- **WAL Mode**: Write-Ahead Logging for better concurrency
- **Automatic Recovery**: Health checks and connection recreation

## Architecture

```mermaid
graph TD
    A[Application] --> B[SqliteConnectionPool]
    B --> C[Read Connection Pool]
    B --> D[Write Connection Pool]
    
    C --> E[Connection 1]
    C --> F[Connection 2]
    C --> G[Connection N]
    
    D --> H[Dedicated Write Connection]
    
    I[Write Semaphore] --> H
    
    J[Maintenance Timer] --> B
    J --> K[Health Checks]
    J --> L[Pool Cleanup]
```

## Key Components

### 1. SqliteConnectionPool
- **Connection Management**: Separate pools for read/write operations
- **Write Serialization**: Semaphore ensures single writer
- **Health Monitoring**: Automatic connection health checks
- **WAL Configuration**: Optimized SQLite settings

### 2. PooledSqliteConnection
- **Transparent Wrapper**: Drop-in replacement for SqliteConnection
- **Automatic Return**: Connections returned to pool on disposal
- **Transaction Support**: Full transaction capabilities
- **Statistics**: Connection usage tracking

### 3. SqliteConnectionPoolFactory
- **Pool Management**: One pool per database file
- **Configuration**: Centralized pool configuration
- **Monitoring**: Pool statistics and health metrics

### 4. PooledStockBarCacheService
- **Optimized Implementation**: Uses connection pooling
- **Batch Operations**: Efficient bulk data operations
- **Metrics Integration**: Prometheus monitoring

## Configuration

### appsettings.json
```json
{
  "SqlitePool": {
    "MaxReadConnections": 10,
    "InitialReadConnections": 3,
    "MaxWriteConnections": 1,
    "CommandTimeoutSeconds": 30,
    "BusyTimeoutMs": 5000
  }
}
```

### Configuration Options
- **MaxReadConnections**: Maximum concurrent read connections
- **InitialReadConnections**: Pre-created read connections
- **MaxWriteConnections**: Always 1 for serialization
- **CommandTimeoutSeconds**: SQL command timeout
- **BusyTimeoutMs**: SQLite busy timeout

## Performance Optimizations

### SQLite Settings
```sql
PRAGMA journal_mode = WAL;        -- Write-Ahead Logging
PRAGMA synchronous = NORMAL;      -- Balanced durability/performance
PRAGMA cache_size = -64000;       -- 64MB cache
PRAGMA temp_store = MEMORY;       -- In-memory temp tables
PRAGMA mmap_size = 268435456;     -- 256MB memory mapping
PRAGMA busy_timeout = 5000;       -- 5-second busy timeout
```

### Connection Pool Benefits
- **Reduced Latency**: Pre-warmed connections
- **Memory Efficiency**: Controlled connection count
- **Resource Sharing**: Connection reuse across operations
- **Automatic Scaling**: Dynamic pool management

## Usage Examples

### Basic Usage
```csharp
// Get read connection (concurrent)
using var readConnection = await pool.GetReadConnectionAsync();
var data = await readConnection.ExecuteReaderAsync("SELECT * FROM table");

// Get write connection (serialized)
using var writeConnection = await pool.GetWriteConnectionAsync();
await writeConnection.ExecuteNonQueryAsync("INSERT INTO table VALUES (...)");
```

### Transaction Support
```csharp
using var connection = await pool.GetWriteConnectionAsync();
await connection.ExecuteInTransactionAsync(async transaction =>
{
    await connection.ExecuteNonQueryAsync("INSERT INTO table1 VALUES (...)");
    await connection.ExecuteNonQueryAsync("INSERT INTO table2 VALUES (...)");
});
```

### Bulk Operations
```csharp
var cacheService = serviceProvider.GetService<PooledStockBarCacheService>();
await cacheService.CacheBarsAsync(symbolTimeFrameBars);
```

## Monitoring & Metrics

### Prometheus Metrics
- `cache_operations_total{operation="sqlite_write", result="success"}`
- `cache_latency_ms{operation="sqlite_write"}`
- `sqlite_lock_contention`: Lock contention incidents
- `cache_hit_miss_total{cache_type="sqlite", result="hit"}`

### Pool Statistics
- Active connections per pool
- Total connections created
- Connection health status
- Pool utilization metrics

## Service Integration

### Priority Selection
1. **Redis-First Cache**: If Redis available and enabled
2. **Pooled SQLite Cache**: Connection pooling for better concurrency
3. **Thread-Safe Cache**: Fallback to original implementation

### Registration
```csharp
services.AddSqliteConnectionPooling(config =>
{
    config.MaxReadConnections = 10;
    config.InitialReadConnections = 3;
    config.CommandTimeoutSeconds = 30;
});
```

## Performance Comparison

### Before (Thread-Safe Cache)
- **Concurrency**: Limited by SQLite locks
- **Performance**: 3,200ms+ per symbol during universe building
- **Errors**: Frequent `database is locked` errors
- **Resource Usage**: New connection per operation

### After (Pooled Cache)
- **Concurrency**: 10+ concurrent reads, serialized writes
- **Performance**: Expected 50-80% improvement
- **Errors**: Eliminated lock contention
- **Resource Usage**: Efficient connection reuse

## Best Practices

### Connection Management
1. **Always Use Using Statements**: Ensure connections return to pool
2. **Separate Read/Write Operations**: Use appropriate connection type
3. **Batch Write Operations**: Minimize write connection usage
4. **Monitor Pool Health**: Watch for connection leaks

### Error Handling
1. **Retry Logic**: Implement exponential backoff for transient errors
2. **Circuit Breaker**: Fail fast on persistent issues
3. **Fallback Strategy**: Graceful degradation options
4. **Logging**: Comprehensive error logging

### Performance Tuning
1. **Pool Sizing**: Adjust based on workload
2. **Timeout Settings**: Balance responsiveness vs. reliability
3. **WAL Checkpointing**: Manage WAL file growth
4. **Cache Size**: Optimize for available memory

## Troubleshooting

### High Connection Usage
- **Symptom**: Pool exhaustion warnings
- **Cause**: Connection leaks or high concurrency
- **Solution**: Review using statements, adjust pool size

### Slow Write Operations
- **Symptom**: Write operations taking too long
- **Cause**: Write serialization bottleneck
- **Solution**: Batch operations, optimize queries

### Connection Health Issues
- **Symptom**: Frequent connection recreation
- **Cause**: Database corruption or network issues
- **Solution**: Check database integrity, review connection string

## Testing

### Unit Tests
- Connection pool lifecycle
- Write serialization verification
- Concurrent read operations
- Transaction support
- Error handling scenarios

### Integration Tests
- End-to-end cache operations
- Performance benchmarking
- Stress testing with high concurrency
- Failover scenarios

### Load Testing
- Universe building with pooled connections
- Sustained high-throughput operations
- Memory usage under load
- Connection pool exhaustion scenarios

## Migration Strategy

### Phase 1: Parallel Deployment
1. Deploy with pooled cache as secondary option
2. Monitor baseline performance
3. Compare metrics with existing implementation

### Phase 2: Gradual Rollout
1. Enable pooled cache for non-critical operations
2. Monitor error rates and performance
3. Validate connection pool behavior

### Phase 3: Full Production
1. Make pooled cache the primary SQLite implementation
2. Optimize pool configuration based on observed usage
3. Set up monitoring and alerting

## Future Enhancements

1. **Dynamic Pool Sizing**: Auto-adjust based on load
2. **Connection Affinity**: Sticky connections for better cache locality
3. **Read Replicas**: Multiple read-only database files
4. **Sharding**: Distribute data across multiple databases
5. **Async Everywhere**: Full async/await throughout the stack
