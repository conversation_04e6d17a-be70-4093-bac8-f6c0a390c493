# SmaTrendFollower System Configuration Reference

## Account Information
- **Alpaca Live Account**: $17,066.39
- **Alpaca Live Credentials**: AKGBPW5HD8LVI5C6NJUJ/MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM
- **Alpaca Paper Credentials**: PK0AM3WB1CES3YBQPGR0/2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf
- **Trading Universe**: 3,736 qualified symbols
- **Strategy**: SMA Trend Following (Close > 200-day SMA AND Close > 50-day SMA)

## API Credentials & Subscriptions

### Polygon.io
- **API Key**: ********************************
- **Subscriptions**: Stocks Advanced, Indices Advanced, Options Starter
- **Rate Limit**: 100 req/sec budget

### Brave Search
- **Search API Key**: BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz
- **AI API Key**: BSAacxwbtDjQfP4151QopodZgaSS8jS
- **Rate Limits**: 1 req/sec, 2000 req/month each

### Finnhub
- **API Key**: d1hm14hr01qsvr2b0o8gd1hm14hr01qsvr2b0o90
- **Subscription**: Free API

### Discord
- **Bot Token**: MTM4NTA1OTI3MDMzNjMxNTQ1NA.GtTKUd.fuCC2ZI-H-tTLZl41YF3gZj-w3gPbv_Xep8NoE
- **Channel ID**: 1385057459814797383

### OpenAI
- **Subscription**: ChatGPT Plus + API tokens

### Gemini
- **Subscription**: Free API

## Infrastructure Configuration

### Redis
- **Connection**: *************:6379 (LocalProd)
- **Authentication**: No password
- **TTL Settings**:
  - Signals/Universe: 24 hours
  - Stops: 7 days
  - Synthetic VIX: 10 minutes

### PostgreSQL
- **Connection**: *************:5432
- **Status**: Fresh installation, needs schema creation

### Data Freshness Thresholds
- **Market Hours**: 18 minutes staleness threshold (9:30 AM - 4:00 PM ET)
- **After Hours**: 8 hours staleness threshold
- **Emergency Mode**: 1 hour staleness threshold

## Trading Configuration

### Risk Management
- **Max Daily Loss**: 4% of account ($682.68)
- **Max Positions**: 8
- **Position Sizing**: ML model with rule-based fallback
- **Anomaly Detection**: 3-sigma halt thresholds, 200-window z-score

### Universe Filtering
- **Min Price**: $2.00
- **Min Volume**: 500,000 (preferred), 50,000 (minimum)
- **Min Volatility**: 1% (preferred), 0.3% (minimum)
- **Analysis Period**: 60 days

### Signal Generation
- **Parallel Processing**: 20 concurrent calls (≈ 80-90 req/s)
- **Rate Limiting**: SemaphoreSlim with adaptive adjustment
- **Confidence Threshold**: 0.5 minimum

## Market Data Configuration

### Index Symbols
- **SPX**: S&P 500 Index
- **VIX**: Volatility Index
- **NDX**: NASDAQ 100 Index

### VIX Thresholds
- **Volatile**: 25.0
- **Panic**: 35.0
- **Euphoric**: 12.0

### SPX Momentum Thresholds
- **Bull**: +0.5%
- **Bear**: -0.5%

## Execution Settings

### Order Preferences
- **Prefer Limit Orders**: true
- **Max Spread**: 0.5%
- **VWAP Validation**: Required
- **Liquidity Threshold**: 5,000

### Monitoring
- **Spread Spike Threshold**: 200% of normal
- **Max Concurrent Executions**: 10
- **Throttle Duration**: 5 minutes (poor conditions)

## Development Environment
- **Configuration**: LocalProd
- **Build**: Release (optimized)
- **Logging**: Serilog with Discord sink
- **Testing**: xUnit with FluentAssertions/Moq

## Quick Start Commands

### Start Live Trading
```cmd
cd "C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower"
dotnet run --project SmaTrendFollower.Console --configuration Release
```

### Emergency Stop
```powershell
.\emergency-stop.ps1 -ClosePositions -Force
```

### Monitor Trading
```powershell
.\monitor-trading.ps1
```

## Key File Locations
- **Configuration**: appsettings.json, appsettings.Phase6.json
- **Logs**: Logs/ directory
- **Models**: SmaTrendFollower.Console/Model/
- **Data**: Data/ directory
- **Scripts**: *.ps1, *.bat files in root

## System Status Indicators
- **✅ Ready**: All services initialized, universe loaded, credentials validated
- **⏸️ Paused**: Outside market hours or system maintenance
- **🔄 Active**: Live trading in progress
- **❌ Error**: System failure requiring intervention
- **🛑 Halted**: Anomaly detection or emergency stop triggered
