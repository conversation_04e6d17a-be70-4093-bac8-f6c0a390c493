# SmaTrendFollower System Memories

## General Preferences & Requirements
- User prefers .NET 8 solutions with console + xUnit test projects, DI architecture, and standardized namespaces to SmaTrendFollower.
- User requires 100% production readiness, test coverage, and documentation coverage as deployment criteria.
- User requires data freshness validation with 18-minute staleness threshold during market hours (9:30 AM - 4:00 PM ET), 8-hour threshold after hours.
- User prefers appsettings.json files for configuration with fallback to Environment.GetEnvironmentVariable, using LocalProd configuration in Development environment.
- User has extremely high risk tolerance, is ready for immediate live trading deployment with real money, and prefers RequireConfirmation = false for fully automated trading.
- User prefers AI to admit uncertainty rather than make confident false claims, wants consistent behavior without contradictions, and values honesty about limitations over false confidence.
- User has experienced repeated false claims from AI about fixing ArgumentException date range errors and system readiness, leading to failed trading days with real financial consequences.
- User prefers systematic root cause analysis and comprehensive fixes addressing all instances of similar issues across the entire codebase rather than partial solutions.
- User prefers exact counts and precise data rather than estimates when reporting system metrics and results.
- User strongly prefers AI to execute tasks directly without asking confirmatory questions when the correct action is already established and clear.
- User strongly prefers fixing production systems directly rather than creating pared-down test versions when production processes hang or fail.
- User prefers live trading deployment with continuous trade executions and is ready for production release builds for real-money automated trading.
- User strongly prefers fixing underlying data issues rather than filtering error messages, and expects SPY data to always be available without 'insufficient bar data' errors.

## Architecture & Implementation
- User prefers scoped services for trade cycles with SignalGenerator, RiskManager, PortfolioGate, TradeExecutor components.
- User prefers real-time streaming architecture: Alpaca websocket for equities trading/exit, Polygon websocket for index/volatility triggers.
- User prefers libraries: Alpaca.Markets, Skender.Stock.Indicators, Serilog, DotNetEnv, FluentAssertions/Moq for testing.
- User prefers Polly resilience wrapper with 3-attempt exponential backoff retry policy, 30-second circuit breaker after 5 failures.
- User prefers PolygonWebSocketManager architecture with batch subscribe in ≤500-symbol chunks, Redis subscription persistence.
- User prefers parallel signal generation with SemaphoreSlim rate limiting (20 concurrent calls ≈ 80-90 req/s), ConcurrentBag for thread-safe collections.
- User prefers StartupDataBootstrapper IHostedService pattern with 6-day full universe and 4-hour daily universe staleness thresholds.
- User prefers PeriodicTimer over Task.Delay loops in BackgroundService implementations with proper cancellation handling.
- User prefers thread-safe patterns using Volatile.Read/Write and Interlocked operations for shared state access.
- User prefers avoiding constructor over-injection and following Single Responsibility Principle by breaking large services into smaller focused services.

## Infrastructure & Services
- User's Redis server runs at *************:6379 (LocalProd) with no password authentication.
- User prefers Discord notifications with bot token MTM4NTA1OTI3MDMzNjMxNTQ1NA.GtTKUd.fuCC2ZI-H-tTLZl41YF3gZj-w3gPbv_Xep8NoE for channel 1385057459814797383.
- User prefers Redis hygiene with standardized TTLs (24h for signals/universe, 7d for stops, 10min for synthetic VIX), RedisCleanupService as IJob.
- User prefers AnomalyDetectorService with RollingStats (200-window z-score), 3-sigma halt thresholds, and QuoteVolatilityGuard with 120-second rolling window.
- User prefers Docker Compose stack with HashiCorp Vault, Redis, Prometheus, Grafana, Gitea, GitHub Actions runner, and bot service.
- User prefers Prometheus metrics with TimedHttpHandler, MetricsRegistry, latency tracking, and operation counters throughout the system.
- User prefers Redis for high-frequency caching operations with 5-minute TTL, circuit breaker patterns for database operations with Redis fallback.
- User has fresh PostgreSQL installation at *************:5432 that needs database schema creation and connectivity testing before use.

## Market Data & API Integration
- User has Alpaca Markets levels 1-3 options + AlgoTrader Plus market data, and Polygon.io Stocks Advanced & Indices Advanced subscription.
- User has API credentials: Polygon (********************************), Alpaca Live (AKGBPW5HD8LVI5C6NJUJ/MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM), Alpaca Paper (PK0AM3WB1CES3YBQPGR0/2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf).
- User prefers unified market data service combining Alpaca for account/positions/fills and equity/ETF bars with Polygon fallback.
- User prefers VIX data fallback strategy: Polygon primary → web scraping → synthetic VIX from ETFs → halt trading.
- User has Finnhub API key: d1hm14hr01qsvr2b0o8gd1hm14hr01qsvr2b0o90 for earnings data access.
- User has Brave Search API keys: BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz (Search) and BSAacxwbtDjQfP4151QopodZgaSS8jS (AI), both with 1 req/sec and 2000 req/month limits.
- User prefers distributed rate limiting for external APIs with 100 req/sec budget for Polygon API.
- User prefers pagination and parallelization techniques for API requests in universe construction to improve data retrieval speed.
- User prefers date validation logic that prevents future dates in market data requests.

## Trading Strategy & Risk Management
- SmaTrendFollower strategy requires Close > 200-day SMA AND Close > 50-day SMA to stay in broad uptrends.
- User prefers DynamicUniverseProvider: weekly fetch from Polygon, daily filtering to top ~200 tickers cached in Redis.
- User prefers screening thousands of stocks to find the top 100-200 trading candidates with MinAverageVolume of 500,000 and MinVolatilityPercent of 1%.
- User prefers ML implementation with Microsoft.ML and Microsoft.ML.AutoML for signal ranking with weekly retraining workflow.
- User prefers NewsSentimentService with Alpaca news websocket integration and sentiment analysis using FinBERT, OpenAI, and Gemini API as a third fallback option.
- User's SmaTrendFollower system has 1,851 symbols with good data, live trading configured with $17,066.39 account balance, and signal generation working with 40% hit rate.
- User's SmaTrendFollower system has active anomaly detection that halts trading for 5 minutes when z-scores exceed -3.76 threshold.
- User prefers anomaly detection to disable trading on individual affected symbols only, not halt the entire trading process.
- User expects the trading universe to contain thousands of symbols, not just hundreds like the current 982 symbols.
- ML position sizing model requires real trade data to function properly, so trading system must be working first before ML model can be trained or used.

## Issues & Errors
- Critical test failures found: ISlippageForecaster service not registered causing TradeExecutor dependency injection failure.
- Alpaca paper trading account restriction: can only buy during market hours (9:30 AM - 4:00 PM ET), after hours only liquidation is allowed - this is normal behavior, not a system error.
- Paper trading account shows "account is restricted to liquidation only" error with $0 buying power despite $79,770 portfolio value, preventing order submission during tests.
- ArgumentException date range errors have comprehensive guardrails: all signal generators use endDate = DateTime.UtcNow.Date.AddDays(-1) and startDate = endDate.AddDays(-250/-300), ensuring startDate is always before endDate. MarketDataService has validation that adjusts invalid ranges and cache services prevent startDate > endDate scenarios.
- User confirmed SmaTrendFollower system was running during market hours but failing with critical errors: invalid date ranges (start > end), slow signal processing (3844+ seconds), missing position sizing model, database query issues, and bar caching failures - these are systemic technical issues preventing successful trading execution.

## System Readiness
- System reindexed and verified ready for live trading: 1,851 symbols available, live credentials configured ($17,066.39 account), all services initialize successfully, safety limits calculated dynamically, ArgumentException guardrails comprehensive, only missing ML position sizing model (falls back to rule-based).
- Despite previous readiness verification, the system experienced critical errors during live trading, including invalid date ranges, slow signal processing, missing position sizing model, database query issues, and bar caching failures.
- User confirmed readiness to proceed with live automated trading using real money despite previous system failures, indicating high risk tolerance and commitment to live trading deployment.
