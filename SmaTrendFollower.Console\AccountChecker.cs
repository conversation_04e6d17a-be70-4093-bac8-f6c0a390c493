using SmaTrendFollower.Services;
using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using DotNetEnv;

namespace SmaTrendFollower.Console;

public static class AccountChecker
{
    public static async Task CheckAccountAsync()
    {
        System.Console.WriteLine("=== ALPACA ACCOUNT CHECKER ===");
        System.Console.WriteLine("Checking account status using configuration...");
        
        // Create a simple logger
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<AlpacaClientFactory>();
        var rateLimitLogger = loggerFactory.CreateLogger<AlpacaRateLimitHelper>();

        try
        {
            // Create configuration using the same logic as the main application
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
            var actualEnvironment = environment == "Development" ? "LocalProd" : environment;

            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile($"appsettings.{actualEnvironment}.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            System.Console.WriteLine($"Using configuration environment: {actualEnvironment}");

            // Create client factory
            var clientFactory = new AlpacaClientFactory(logger, rateLimitLogger, configuration);

            // Check what environment is configured
            var configuredEnvironment = configuration["Alpaca:Environment"] ?? "paper";
            System.Console.WriteLine($"Configured Alpaca environment: {configuredEnvironment}");

            // Debug: Show all Alpaca configuration values
            System.Console.WriteLine($"Debug - Alpaca:KeyId: {configuration["Alpaca:KeyId"] ?? "null"}");
            System.Console.WriteLine($"Debug - Alpaca:SecretKey: {configuration["Alpaca:SecretKey"]?.Substring(0, 8) ?? "null"}...");
            System.Console.WriteLine($"Debug - Alpaca:Environment: {configuration["Alpaca:Environment"] ?? "null"}");
            System.Console.WriteLine($"Debug - APCA_API_ENV env var: {Environment.GetEnvironmentVariable("APCA_API_ENV") ?? "null"}");

            // FORCE LIVE TRADING FOR TESTING
            System.Console.WriteLine("🔴 FORCING LIVE TRADING MODE FOR TESTING");
            Environment.SetEnvironmentVariable("APCA_API_ENV", "live");
            Environment.SetEnvironmentVariable("APCA_API_KEY_ID_LIVE", "AKGBPW5HD8LVI5C6NJUJ");
            Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY_LIVE", "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM");

            IAccount? account = null;
            string workingEnvironment = configuredEnvironment;

            try
            {
                using var client = clientFactory.CreateTradingClient();
                account = await client.GetAccountAsync();
                System.Console.WriteLine($"✓ {configuredEnvironment} trading environment works!");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"✗ {configuredEnvironment} trading failed: {ex.Message}");
                throw new InvalidOperationException($"Could not connect to {configuredEnvironment} trading environment");
            }

            if (account == null)
            {
                throw new InvalidOperationException("Could not retrieve account information");
            }
            
            System.Console.WriteLine($"=== ALPACA ACCOUNT STATUS ({workingEnvironment.ToUpper()}) ===");
            System.Console.WriteLine($"Account ID: {account.AccountId}");
            System.Console.WriteLine($"Status: {account.Status}");
            System.Console.WriteLine();

            System.Console.WriteLine("=== ACCOUNT EQUITY ===");
            System.Console.WriteLine($"Equity: {account.Equity:C}");
            System.Console.WriteLine($"Cash: {account.TradableCash:C}");
            System.Console.WriteLine($"Buying Power: {account.BuyingPower:C}");
            System.Console.WriteLine();
            
            System.Console.WriteLine("=== RISK CALCULATIONS ===");
            var equity = account.Equity ?? 0m;
            var riskDollars = Math.Min(equity * 0.01m, 1000m);
            System.Console.WriteLine($"Current Risk Capital (1% of equity, max $1000): {riskDollars:C}");
            System.Console.WriteLine($"Risk as % of equity: {(riskDollars / equity * 100):F2}%");
            System.Console.WriteLine();
            
            // Get current positions using the working environment
            using var workingClient = clientFactory.CreateTradingClient();
            var positions = await workingClient.ListPositionsAsync();
            var positionList = positions.ToList();
            
            System.Console.WriteLine("=== CURRENT POSITIONS ===");
            if (positionList.Any())
            {
                foreach (var position in positionList)
                {
                    var marketValue = position.MarketValue ?? 0m;
                    var unrealizedPnl = position.UnrealizedProfitLoss ?? 0m;
                    var unrealizedPnlPercent = position.UnrealizedProfitLossPercent ?? 0m;
                    
                    System.Console.WriteLine($"{position.Symbol}: {position.Quantity} shares");
                    System.Console.WriteLine($"  Market Value: {marketValue:C}");
                    System.Console.WriteLine($"  Unrealized P&L: {unrealizedPnl:C} ({unrealizedPnlPercent:P2})");
                    System.Console.WriteLine($"  Avg Cost: {position.AverageEntryPrice:C}");
                    System.Console.WriteLine();
                }
            }
            else
            {
                System.Console.WriteLine("No current positions");
            }
            
            // Get recent orders
            var orders = await workingClient.ListOrdersAsync(new ListOrdersRequest
            {
                OrderStatusFilter = OrderStatusFilter.All,
                LimitOrderNumber = 10
            });
            var orderList = orders.ToList();
            
            System.Console.WriteLine("=== RECENT ORDERS (Last 10) ===");
            if (orderList.Any())
            {
                foreach (var order in orderList.Take(10))
                {
                    System.Console.WriteLine($"{order.CreatedAtUtc:yyyy-MM-dd HH:mm} - {order.Symbol}");
                    System.Console.WriteLine($"  {order.OrderSide} {order.Quantity} @ {order.LimitPrice:C}");
                    System.Console.WriteLine($"  Status: {order.OrderStatus}");
                    System.Console.WriteLine();
                }
            }
            else
            {
                System.Console.WriteLine("No recent orders");
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"Error checking account: {ex.Message}");
            System.Console.WriteLine($"Details: {ex}");
        }
    }
}
