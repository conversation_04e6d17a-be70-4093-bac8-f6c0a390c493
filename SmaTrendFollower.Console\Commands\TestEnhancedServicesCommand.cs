using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Console.Commands;

/// <summary>
/// Console command to test enhanced services integration
/// Provides a quick way to validate that all enhanced components are working
/// </summary>
public sealed class TestEnhancedServicesCommand
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<TestEnhancedServicesCommand> _logger;

    public TestEnhancedServicesCommand(IServiceProvider serviceProvider, ILogger<TestEnhancedServicesCommand> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// Executes the enhanced services test
    /// </summary>
    public async Task<bool> ExecuteAsync()
    {
        _logger.LogInformation("🚀 Starting Enhanced Services Integration Test");
        
        try
        {
            var allTestsPassed = true;

            // Test 1: Service Resolution
            allTestsPassed &= await TestServiceResolutionAsync();

            // Test 2: Migration Service
            allTestsPassed &= await TestMigrationServiceAsync();

            // Test 3: Staleness Service
            allTestsPassed &= await TestStalenessServiceAsync();

            // Test 4: Rate Limiting Service
            allTestsPassed &= await TestRateLimitingServiceAsync();

            // Test 5: Enhanced Signal Generation (if available)
            allTestsPassed &= await TestEnhancedSignalGenerationAsync();

            if (allTestsPassed)
            {
                _logger.LogInformation("✅ All Enhanced Services Integration Tests PASSED");
                return true;
            }
            else
            {
                _logger.LogError("❌ Some Enhanced Services Integration Tests FAILED");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "💥 Enhanced Services Integration Test failed with exception");
            return false;
        }
    }

    private Task<bool> TestServiceResolutionAsync()
    {
        _logger.LogInformation("🔍 Testing Service Resolution...");

        try
        {
            // Test enhanced data retrieval service
            var enhancedDataRetrieval = _serviceProvider.GetService<IEnhancedDataRetrievalService>();
            if (enhancedDataRetrieval == null)
            {
                _logger.LogError("❌ IEnhancedDataRetrievalService not resolved");
                return Task.FromResult(false);
            }

            // Test migration service
            var migrationService = _serviceProvider.GetService<IEnhancedServicesMigrationService>();
            if (migrationService == null)
            {
                _logger.LogError("❌ IEnhancedServicesMigrationService not resolved");
                return Task.FromResult(false);
            }

            // Test staleness service
            var stalenessService = _serviceProvider.GetService<IFlexibleDataStalenessService>();
            if (stalenessService == null)
            {
                _logger.LogError("❌ IFlexibleDataStalenessService not resolved");
                return Task.FromResult(false);
            }

            // Test rate limiting service
            var rateLimitService = _serviceProvider.GetService<IAdaptiveRateLimitingService>();
            if (rateLimitService == null)
            {
                _logger.LogError("❌ IAdaptiveRateLimitingService not resolved");
                return Task.FromResult(false);
            }

            // Test enhanced signal generator (optional)
            var enhancedSignalGenerator = _serviceProvider.GetService<IEnhancedSignalGenerator>();
            if (enhancedSignalGenerator != null)
            {
                _logger.LogInformation("✅ IEnhancedSignalGenerator resolved (decorator pattern working)");
            }

            _logger.LogInformation("✅ Service Resolution Test PASSED");
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Service Resolution Test FAILED");
            return Task.FromResult(false);
        }
    }

    private async Task<bool> TestMigrationServiceAsync()
    {
        _logger.LogInformation("🔄 Testing Migration Service...");

        try
        {
            var migrationService = _serviceProvider.GetRequiredService<IEnhancedServicesMigrationService>();

            // Test feature flags
            var useEnhancedData = migrationService.ShouldUseEnhancedDataRetrieval();
            var useAdaptiveRate = migrationService.ShouldUseAdaptiveRateLimit();
            var useAdaptiveSignal = migrationService.ShouldUseAdaptiveSignalGeneration();

            _logger.LogInformation("📊 Feature Flags - Enhanced Data: {EnhancedData}, Adaptive Rate: {AdaptiveRate}, Adaptive Signal: {AdaptiveSignal}",
                useEnhancedData, useAdaptiveRate, useAdaptiveSignal);

            // Test health check
            var healthResult = await migrationService.PerformHealthCheckAsync();
            _logger.LogInformation("🏥 Health Check - Overall: {Health}, Services: {ServiceCount}",
                healthResult.OverallHealth, healthResult.ServiceChecks.Count);

            // Test migration report
            var report = migrationService.GetMigrationReport();
            _logger.LogInformation("📋 Migration Report - Status: {Status}, Services: {ServiceCount}",
                report.OverallStatus, report.ServiceStatuses.Count);

            _logger.LogInformation("✅ Migration Service Test PASSED");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Migration Service Test FAILED");
            return false;
        }
    }

    private async Task<bool> TestStalenessServiceAsync()
    {
        _logger.LogInformation("⏰ Testing Staleness Service...");

        try
        {
            var stalenessService = _serviceProvider.GetRequiredService<IFlexibleDataStalenessService>();

            // Test current status
            var status = stalenessService.GetCurrentStatus();
            _logger.LogInformation("📈 Staleness Status - Policy: {Policy}, Emergency: {Emergency}",
                status.CurrentPolicy, status.EmergencyModeActive);

            // Test data validation with different staleness levels
            var testData = new[]
            {
                new { Age = TimeSpan.FromMinutes(5), Name = "Fresh" },
                new { Age = TimeSpan.FromMinutes(30), Name = "Acceptable" },
                new { Age = TimeSpan.FromHours(2), Name = "Stale" }
            };

            foreach (var test in testData)
            {
                var dataTimestamp = DateTime.UtcNow - test.Age;
                var result = await stalenessService.ValidateDataFreshnessAsync(
                    dataTimestamp, DataType.HistoricalBars, "TestSource");

                _logger.LogInformation("🔍 {Name} Data ({Age}) - Level: {Level}, Acceptable: {Acceptable}, Quality: {Quality:F2}",
                    test.Name, test.Age, result.StalenessLevel, result.IsAcceptable, result.QualityScore);
            }

            _logger.LogInformation("✅ Staleness Service Test PASSED");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Staleness Service Test FAILED");
            return false;
        }
    }

    private async Task<bool> TestRateLimitingServiceAsync()
    {
        _logger.LogInformation("🚦 Testing Rate Limiting Service...");

        try
        {
            var rateLimitService = _serviceProvider.GetRequiredService<IAdaptiveRateLimitingService>();

            // Test acquiring permits
            var results = new List<RateLimitResult>();
            for (int i = 0; i < 5; i++)
            {
                var result = await rateLimitService.TryAcquireAsync("TestProvider", $"operation_{i}");
                results.Add(result);
                
                if (result.IsSuccess)
                {
                    // Simulate operation and release
                    await Task.Delay(10);
                    rateLimitService.Release("TestProvider", $"operation_{i}", true, TimeSpan.FromMilliseconds(10));
                }
            }

            var successCount = results.Count(r => r.IsSuccess);
            _logger.LogInformation("🎯 Rate Limiting - {Success}/{Total} permits acquired successfully",
                successCount, results.Count);

            // Test statistics
            var stats = rateLimitService.GetStats("TestProvider");
            _logger.LogInformation("📊 Rate Limiting Stats - Current Limit: {Limit}, Available: {Available}, Total Requests: {Total}",
                stats.CurrentLimit, stats.AvailablePermits, stats.TotalRequests);

            _logger.LogInformation("✅ Rate Limiting Service Test PASSED");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Rate Limiting Service Test FAILED");
            return false;
        }
    }

    private async Task<bool> TestEnhancedSignalGenerationAsync()
    {
        _logger.LogInformation("📡 Testing Enhanced Signal Generation...");

        try
        {
            var enhancedSignalGenerator = _serviceProvider.GetService<IEnhancedSignalGenerator>();
            if (enhancedSignalGenerator == null)
            {
                _logger.LogInformation("⚠️ Enhanced Signal Generator not available (decorator not applied)");
                return true; // Not a failure, just not available
            }

            // Test error statistics
            var errorStats = enhancedSignalGenerator.GetErrorStatistics();
            _logger.LogInformation("📈 Signal Generation Error Stats - Total Symbols: {Total}, Errors: {Errors}",
                errorStats.TotalSymbolsTracked, errorStats.TotalErrors);

            // Test robust signal generation (if robust service is available)
            var robustService = _serviceProvider.GetService<IRobustSignalGenerationService>();
            if (robustService != null)
            {
                _logger.LogInformation("🔧 Testing robust signal generation...");
                
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                var robustResult = await robustService.GenerateSignalsRobustlyAsync(3, cts.Token);
                
                _logger.LogInformation("🎯 Robust Signal Generation - Success: {Success}, Signals: {Count}, Methods: {Methods}",
                    robustResult.IsSuccess, robustResult.Signals.Count, string.Join(", ", robustResult.GenerationMethods));
            }

            _logger.LogInformation("✅ Enhanced Signal Generation Test PASSED");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Enhanced Signal Generation Test FAILED");
            return false;
        }
    }
}
