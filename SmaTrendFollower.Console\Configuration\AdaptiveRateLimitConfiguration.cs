namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration for adaptive rate limiting system
/// </summary>
public sealed class AdaptiveRateLimitConfiguration
{
    /// <summary>
    /// Provider-specific rate limit configurations
    /// </summary>
    public Dictionary<string, ProviderRateLimitConfiguration> Providers { get; set; } = new()
    {
        ["Alpaca"] = new ProviderRateLimitConfiguration
        {
            InitialLimit = 50,
            MinLimit = 10,
            MaxLimit = 100,
            CircuitBreakerConfig = new CircuitBreakerConfiguration
            {
                FailureThreshold = 5,
                OpenTimeout = TimeSpan.FromMinutes(2)
            }
        },
        ["Polygon"] = new ProviderRateLimitConfiguration
        {
            InitialLimit = 80,
            MinLimit = 20,
            MaxLimit = 150,
            CircuitBreakerConfig = new CircuitBreakerConfiguration
            {
                FailureThreshold = 3,
                OpenTimeout = TimeSpan.FromMinutes(1)
            }
        }
    };

    /// <summary>
    /// How often to adjust rate limits based on performance
    /// </summary>
    public TimeSpan AdjustmentInterval { get; set; } = TimeSpan.FromMinutes(2);

    /// <summary>
    /// Time window for calculating recent performance metrics
    /// </summary>
    public TimeSpan AdjustmentWindow { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Success rate threshold for increasing rate limits
    /// </summary>
    public double SuccessRateThreshold { get; set; } = 0.95;

    /// <summary>
    /// Timeout for acquiring rate limit permits
    /// </summary>
    public TimeSpan AcquisitionTimeout { get; set; } = TimeSpan.FromSeconds(30);
}

/// <summary>
/// Rate limit configuration for a specific API provider
/// </summary>
public sealed class ProviderRateLimitConfiguration
{
    /// <summary>
    /// Initial rate limit when starting
    /// </summary>
    public int InitialLimit { get; set; } = 50;

    /// <summary>
    /// Minimum rate limit (safety floor)
    /// </summary>
    public int MinLimit { get; set; } = 10;

    /// <summary>
    /// Maximum rate limit (safety ceiling)
    /// </summary>
    public int MaxLimit { get; set; } = 200;

    /// <summary>
    /// Circuit breaker configuration
    /// </summary>
    public CircuitBreakerConfiguration CircuitBreakerConfig { get; set; } = new();
}

/// <summary>
/// Circuit breaker configuration
/// </summary>
public sealed class CircuitBreakerConfiguration
{
    /// <summary>
    /// Number of failures before opening the circuit
    /// </summary>
    public int FailureThreshold { get; set; } = 5;

    /// <summary>
    /// How long to keep the circuit open before allowing test requests
    /// </summary>
    public TimeSpan OpenTimeout { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Success threshold for closing the circuit from half-open state
    /// </summary>
    public int SuccessThreshold { get; set; } = 1;
}

/// <summary>
/// Configuration for batch processing operations
/// </summary>
public sealed class BatchProcessorOptions
{
    /// <summary>
    /// Initial batch size when starting
    /// </summary>
    public int InitialBatchSize { get; set; } = 50;

    /// <summary>
    /// Minimum batch size (safety floor)
    /// </summary>
    public int MinBatchSize { get; set; } = 10;

    /// <summary>
    /// Maximum batch size (safety ceiling)
    /// </summary>
    public int MaxBatchSize { get; set; } = 200;

    /// <summary>
    /// Maximum number of concurrent batches
    /// </summary>
    public int MaxConcurrentBatches { get; set; } = 5;

    /// <summary>
    /// Minimum success rate threshold for maintaining batch size
    /// </summary>
    public double MinSuccessRateThreshold { get; set; } = 0.8;

    /// <summary>
    /// Target success rate threshold for increasing batch size
    /// </summary>
    public double TargetSuccessRateThreshold { get; set; } = 0.95;

    /// <summary>
    /// Target duration per batch in seconds
    /// </summary>
    public double TargetBatchDurationSeconds { get; set; } = 2.0;

    /// <summary>
    /// Timeout for individual batch operations
    /// </summary>
    public TimeSpan BatchTimeout { get; set; } = TimeSpan.FromMinutes(1);
}
