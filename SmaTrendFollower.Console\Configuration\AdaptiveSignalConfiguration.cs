namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration for adaptive signal generation
/// </summary>
public sealed class AdaptiveSignalConfiguration
{
    /// <summary>
    /// Maximum number of symbols to process in one run
    /// </summary>
    public int MaxSymbolsToProcess { get; set; } = 500;

    /// <summary>
    /// Maximum number of concurrent symbol processing tasks
    /// </summary>
    public int MaxConcurrentSymbols { get; set; } = 20;

    /// <summary>
    /// Maximum number of days to look back for historical data
    /// </summary>
    public int MaxLookbackDays { get; set; } = 300;

    /// <summary>
    /// Minimum number of bars required for any signal generation
    /// </summary>
    public int MinimumBarsRequired { get; set; } = 10;

    /// <summary>
    /// Minimum confidence score required for a signal to be considered valid
    /// </summary>
    public double MinimumConfidenceScore { get; set; } = 0.5;

    /// <summary>
    /// Whether to enable synthetic data for signal generation
    /// </summary>
    public bool EnableSyntheticData { get; set; } = true;

    /// <summary>
    /// Whether to enable fallback strategies for limited data
    /// </summary>
    public bool EnableFallbackStrategies { get; set; } = true;

    /// <summary>
    /// Strategy preferences by data availability
    /// </summary>
    public StrategyPreferences StrategyPreferences { get; set; } = new();
}

/// <summary>
/// Strategy preferences based on available data
/// </summary>
public sealed class StrategyPreferences
{
    /// <summary>
    /// Minimum bars required for full SMA strategy
    /// </summary>
    public int FullSMAMinBars { get; set; } = 200;

    /// <summary>
    /// Minimum bars required for adapted SMA strategy
    /// </summary>
    public int AdaptedSMAMinBars { get; set; } = 100;

    /// <summary>
    /// Minimum bars required for momentum-based strategy
    /// </summary>
    public int MomentumBasedMinBars { get; set; } = 50;

    /// <summary>
    /// Minimum bars required for short-term trend strategy
    /// </summary>
    public int ShortTermTrendMinBars { get; set; } = 20;

    /// <summary>
    /// Minimum bars required for price action strategy
    /// </summary>
    public int PriceActionMinBars { get; set; } = 10;

    /// <summary>
    /// Confidence multipliers for different strategies
    /// </summary>
    public Dictionary<string, double> ConfidenceMultipliers { get; set; } = new()
    {
        ["FullSMA"] = 1.0,
        ["AdaptedSMA"] = 0.9,
        ["MomentumBased"] = 0.8,
        ["ShortTermTrend"] = 0.7,
        ["PriceAction"] = 0.6
    };
}
