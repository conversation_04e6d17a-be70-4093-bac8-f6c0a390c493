namespace SmaTrendFollower.Console.Configuration;

/// <summary>
/// Configuration options for caching behavior
/// </summary>
public class CacheConfiguration
{
    public const string SectionName = "Cache";

    /// <summary>
    /// Enable Redis-first caching to eliminate SQLite concurrency issues
    /// When true, bars are written to Redis immediately and persisted to SQLite in background
    /// When false, uses traditional SQLite-only caching
    /// </summary>
    public bool EnableRedisFirstCaching { get; set; } = true;

    /// <summary>
    /// Background persistence settings
    /// </summary>
    public BackgroundPersistenceSettings BackgroundPersistence { get; set; } = new();

    /// <summary>
    /// Redis cache TTL settings
    /// </summary>
    public RedisTtlSettings RedisTtl { get; set; } = new();
}

/// <summary>
/// Background persistence configuration
/// </summary>
public class BackgroundPersistenceSettings
{
    /// <summary>
    /// Interval between persistence runs during market hours (minutes)
    /// </summary>
    public int MarketHoursIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// Interval between persistence runs during off-hours (minutes)
    /// </summary>
    public int OffHoursIntervalMinutes { get; set; } = 1;

    /// <summary>
    /// Maximum number of symbols to process in a single batch
    /// </summary>
    public int MaxBatchSize { get; set; } = 50;

    /// <summary>
    /// Maximum number of concurrent batches to process
    /// </summary>
    public int MaxConcurrentBatches { get; set; } = 3;

    /// <summary>
    /// Delay between individual symbol processing within a batch (milliseconds)
    /// </summary>
    public int BatchProcessingDelayMs { get; set; } = 100;

    /// <summary>
    /// Maximum retry attempts for failed SQLite writes
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;
}

/// <summary>
/// Redis TTL configuration
/// </summary>
public class RedisTtlSettings
{
    /// <summary>
    /// TTL for pending bars awaiting SQLite persistence (hours)
    /// </summary>
    public int PendingBarsHours { get; set; } = 24;

    /// <summary>
    /// TTL for cached bars in Redis (hours)
    /// </summary>
    public int CachedBarsHours { get; set; } = 6;

    /// <summary>
    /// TTL for bar snapshots in high-frequency cache (minutes)
    /// </summary>
    public int BarSnapshotsMinutes { get; set; } = 30;
}
