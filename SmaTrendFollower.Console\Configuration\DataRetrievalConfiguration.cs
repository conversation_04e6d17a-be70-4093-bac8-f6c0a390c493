namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration for enhanced data retrieval with fallback strategies
/// </summary>
public sealed class DataRetrievalConfiguration
{
    /// <summary>
    /// Maximum number of concurrent requests for batch operations
    /// </summary>
    public int MaxConcurrentRequests { get; set; } = 20;

    /// <summary>
    /// Timeout for primary API calls
    /// </summary>
    public TimeSpan PrimaryApiTimeout { get; set; } = TimeSpan.FromSeconds(45);

    /// <summary>
    /// Timeout for batch operations
    /// </summary>
    public TimeSpan BatchTimeout { get; set; } = TimeSpan.FromMinutes(3);

    /// <summary>
    /// Relaxed staleness threshold for cached data
    /// </summary>
    public TimeSpan RelaxedStalenessThreshold { get; set; } = TimeSpan.FromHours(2);

    /// <summary>
    /// Maximum staleness allowed in emergency mode
    /// </summary>
    public TimeSpan EmergencyModeMaxStaleness { get; set; } = TimeSpan.FromDays(1);

    /// <summary>
    /// How long emergency mode stays active
    /// </summary>
    public TimeSpan EmergencyModeTimeout { get; set; } = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Whether to enable synthetic data generation
    /// </summary>
    public bool EnableSyntheticData { get; set; } = true;

    /// <summary>
    /// Minimum success rate for batch operations before triggering emergency mode
    /// </summary>
    public double MinimumBatchSuccessRate { get; set; } = 0.7;

    /// <summary>
    /// Maximum number of failed attempts before giving up
    /// </summary>
    public int MaxFailedAttempts { get; set; } = 3;
}

/// <summary>
/// Configuration for synthetic data generation
/// </summary>
public sealed class SyntheticDataConfiguration
{
    /// <summary>
    /// Random seed for reproducible synthetic data (null for random)
    /// </summary>
    public int? RandomSeed { get; set; } = null;

    /// <summary>
    /// Default starting price for symbols without historical data
    /// </summary>
    public decimal DefaultStartPrice { get; set; } = 100.0m;

    /// <summary>
    /// Volume multiplier relative to reference data
    /// </summary>
    public decimal VolumeMultiplier { get; set; } = 0.5m;

    /// <summary>
    /// Maximum correlation with market indices
    /// </summary>
    public double MaxCorrelation { get; set; } = 0.95;

    /// <summary>
    /// Minimum correlation with market indices
    /// </summary>
    public double MinCorrelation { get; set; } = 0.1;

    /// <summary>
    /// Default volatility for unknown symbols
    /// </summary>
    public double DefaultVolatility { get; set; } = 0.02;

    /// <summary>
    /// Whether to use sector-based correlations
    /// </summary>
    public bool UseSectorCorrelations { get; set; } = true;
}
