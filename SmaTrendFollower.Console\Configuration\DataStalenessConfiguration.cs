using System;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Centralized configuration for data staleness thresholds across the system.
/// Implements market-hours-aware staleness checking with different thresholds for
/// trading hours vs after-hours periods.
/// </summary>
public class DataStalenessConfiguration
{
    /// <summary>
    /// Staleness thresholds during market hours (9:30 AM - 4:00 PM ET)
    /// </summary>
    public MarketHoursStalenessThresholds MarketHours { get; set; } = new();
    
    /// <summary>
    /// Staleness thresholds during after-hours periods
    /// </summary>
    public AfterHoursStalenessThresholds AfterHours { get; set; } = new();
    
    /// <summary>
    /// Whether to enforce strict staleness checks (reject stale data)
    /// </summary>
    public bool EnableStrictStalenessChecks { get; set; } = true;
    
    /// <summary>
    /// Whether to reject stale data or allow with warnings
    /// </summary>
    public bool RejectStaleData { get; set; } = true;
    
    /// <summary>
    /// Whether to log staleness warnings
    /// </summary>
    public bool LogStalenessWarnings { get; set; } = true;
}

/// <summary>
/// Common interface for staleness thresholds
/// </summary>
public interface IStalenessThresholds
{
    TimeSpan HistoricalBars { get; set; }
    TimeSpan RealTimeQuotes { get; set; }
    TimeSpan IndexData { get; set; }
    TimeSpan VixData { get; set; }
    TimeSpan UniverseData { get; set; }
    TimeSpan SignalData { get; set; }
    TimeSpan IndicatorData { get; set; }
}

/// <summary>
/// Staleness thresholds for market hours (9:30 AM - 4:00 PM ET)
/// These are stricter thresholds for live trading decisions
/// </summary>
public class MarketHoursStalenessThresholds : IStalenessThresholds
{
    /// <summary>
    /// Historical bar data staleness threshold (18 minutes)
    /// </summary>
    public TimeSpan HistoricalBars { get; set; } = TimeSpan.FromMinutes(18);
    
    /// <summary>
    /// Real-time quote data staleness threshold (2 minutes)
    /// </summary>
    public TimeSpan RealTimeQuotes { get; set; } = TimeSpan.FromMinutes(2);
    
    /// <summary>
    /// Index data (SPX, VIX, etc.) staleness threshold (15 minutes)
    /// </summary>
    public TimeSpan IndexData { get; set; } = TimeSpan.FromMinutes(15);
    
    /// <summary>
    /// VIX data staleness threshold (15 minutes)
    /// </summary>
    public TimeSpan VixData { get; set; } = TimeSpan.FromMinutes(15);
    
    /// <summary>
    /// Universe data staleness threshold (4 hours)
    /// </summary>
    public TimeSpan UniverseData { get; set; } = TimeSpan.FromHours(4);
    
    /// <summary>
    /// Signal data staleness threshold (30 minutes)
    /// </summary>
    public TimeSpan SignalData { get; set; } = TimeSpan.FromMinutes(30);
    
    /// <summary>
    /// Technical indicator data staleness threshold (20 minutes)
    /// </summary>
    public TimeSpan IndicatorData { get; set; } = TimeSpan.FromMinutes(20);
}

/// <summary>
/// Staleness thresholds for after-hours periods
/// These are more relaxed thresholds when markets are closed
/// </summary>
public class AfterHoursStalenessThresholds : IStalenessThresholds
{
    /// <summary>
    /// Historical bar data staleness threshold (8 hours)
    /// </summary>
    public TimeSpan HistoricalBars { get; set; } = TimeSpan.FromHours(8);
    
    /// <summary>
    /// Real-time quote data staleness threshold (8 hours)
    /// </summary>
    public TimeSpan RealTimeQuotes { get; set; } = TimeSpan.FromHours(8);
    
    /// <summary>
    /// Index data staleness threshold (8 hours)
    /// </summary>
    public TimeSpan IndexData { get; set; } = TimeSpan.FromHours(8);
    
    /// <summary>
    /// VIX data staleness threshold (8 hours)
    /// </summary>
    public TimeSpan VixData { get; set; } = TimeSpan.FromHours(8);
    
    /// <summary>
    /// Universe data staleness threshold (24 hours)
    /// </summary>
    public TimeSpan UniverseData { get; set; } = TimeSpan.FromHours(24);
    
    /// <summary>
    /// Signal data staleness threshold (8 hours)
    /// </summary>
    public TimeSpan SignalData { get; set; } = TimeSpan.FromHours(8);
    
    /// <summary>
    /// Technical indicator data staleness threshold (8 hours)
    /// </summary>
    public TimeSpan IndicatorData { get; set; } = TimeSpan.FromHours(8);
}

/// <summary>
/// Data type enumeration for staleness checking
/// </summary>
public enum DataType
{
    HistoricalBars,
    RealTimeQuotes,
    IndexData,
    VixData,
    UniverseData,
    SignalData,
    IndicatorData
}

/// <summary>
/// Extension methods for data staleness configuration
/// </summary>
public static class DataStalenessExtensions
{
    /// <summary>
    /// Gets the appropriate staleness threshold for a data type based on market hours
    /// </summary>
    /// <param name="config">Data staleness configuration</param>
    /// <param name="dataType">Type of data to check</param>
    /// <param name="isMarketHours">Whether it's currently market hours</param>
    /// <returns>Staleness threshold for the data type</returns>
    public static TimeSpan GetThreshold(this DataStalenessConfiguration config, DataType dataType, bool isMarketHours)
    {
        IStalenessThresholds thresholds = isMarketHours ? config.MarketHours : config.AfterHours;
        
        return dataType switch
        {
            DataType.HistoricalBars => thresholds.HistoricalBars,
            DataType.RealTimeQuotes => thresholds.RealTimeQuotes,
            DataType.IndexData => thresholds.IndexData,
            DataType.VixData => thresholds.VixData,
            DataType.UniverseData => thresholds.UniverseData,
            DataType.SignalData => thresholds.SignalData,
            DataType.IndicatorData => thresholds.IndicatorData,
            _ => throw new ArgumentOutOfRangeException(nameof(dataType), dataType, null)
        };
    }
    
    /// <summary>
    /// Checks if data is stale based on its timestamp and type
    /// </summary>
    /// <param name="config">Data staleness configuration</param>
    /// <param name="dataTimestamp">Timestamp of the data</param>
    /// <param name="dataType">Type of data</param>
    /// <param name="isMarketHours">Whether it's currently market hours</param>
    /// <returns>True if data is stale, false otherwise</returns>
    public static bool IsDataStale(this DataStalenessConfiguration config, DateTime dataTimestamp, DataType dataType, bool isMarketHours)
    {
        if (!config.EnableStrictStalenessChecks)
            return false;
            
        var threshold = config.GetThreshold(dataType, isMarketHours);
        var dataAge = DateTime.UtcNow - dataTimestamp;
        
        return dataAge > threshold;
    }
}
