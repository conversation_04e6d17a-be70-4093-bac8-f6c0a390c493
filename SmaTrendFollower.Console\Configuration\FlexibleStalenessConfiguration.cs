using SmaTrendFollower.Models;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration for flexible data staleness handling
/// </summary>
public sealed class FlexibleStalenessConfiguration
{
    /// <summary>
    /// Market hours staleness thresholds
    /// </summary>
    public MarketHoursStalenessThresholds MarketHours { get; set; } = new();

    /// <summary>
    /// After hours staleness thresholds
    /// </summary>
    public AfterHoursStalenessThresholds AfterHours { get; set; } = new();

    /// <summary>
    /// How long emergency mode stays active by default
    /// </summary>
    public TimeSpan EmergencyModeDuration { get; set; } = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Whether to allow critical operation overrides
    /// </summary>
    public bool AllowCriticalOverrides { get; set; } = true;

    /// <summary>
    /// Whether to allow fallback data usage when primary data is stale
    /// </summary>
    public bool AllowFallbackDataUsage { get; set; } = true;

    /// <summary>
    /// Whether to log staleness warnings
    /// </summary>
    public bool LogStalenessWarnings { get; set; } = true;

    /// <summary>
    /// Gets staleness thresholds for a specific data type and market hours
    /// </summary>
    public StalenessThresholds GetThresholds(DataType dataType, bool isMarketHours)
    {
        if (isMarketHours)
        {
            return dataType switch
            {
                DataType.HistoricalBars => new StalenessThresholds
                {
                    Fresh = MarketHours.HistoricalBars * 0.5,
                    Acceptable = MarketHours.HistoricalBars,
                    Stale = MarketHours.HistoricalBars * 2.0,
                    VeryStale = MarketHours.HistoricalBars * 4.0
                },
                DataType.RealTimeQuotes => new StalenessThresholds
                {
                    Fresh = MarketHours.RealTimeQuotes * 0.5,
                    Acceptable = MarketHours.RealTimeQuotes,
                    Stale = MarketHours.RealTimeQuotes * 2.0,
                    VeryStale = MarketHours.RealTimeQuotes * 4.0
                },
                DataType.IndexData => new StalenessThresholds
                {
                    Fresh = MarketHours.IndexData * 0.5,
                    Acceptable = MarketHours.IndexData,
                    Stale = MarketHours.IndexData * 2.0,
                    VeryStale = MarketHours.IndexData * 4.0
                },
                DataType.VixData => new StalenessThresholds
                {
                    Fresh = MarketHours.VixData * 0.5,
                    Acceptable = MarketHours.VixData,
                    Stale = MarketHours.VixData * 2.0,
                    VeryStale = MarketHours.VixData * 4.0
                },
                _ => new StalenessThresholds
                {
                    Fresh = TimeSpan.FromMinutes(5),
                    Acceptable = TimeSpan.FromMinutes(15),
                    Stale = TimeSpan.FromMinutes(30),
                    VeryStale = TimeSpan.FromHours(1)
                }
            };
        }
        else
        {
            return dataType switch
            {
                DataType.HistoricalBars => new StalenessThresholds
                {
                    Fresh = AfterHours.HistoricalBars * 0.5,
                    Acceptable = AfterHours.HistoricalBars,
                    Stale = AfterHours.HistoricalBars * 2.0,
                    VeryStale = AfterHours.HistoricalBars * 4.0
                },
                DataType.RealTimeQuotes => new StalenessThresholds
                {
                    Fresh = AfterHours.RealTimeQuotes * 0.5,
                    Acceptable = AfterHours.RealTimeQuotes,
                    Stale = AfterHours.RealTimeQuotes * 2.0,
                    VeryStale = AfterHours.RealTimeQuotes * 4.0
                },
                DataType.IndexData => new StalenessThresholds
                {
                    Fresh = AfterHours.IndexData * 0.5,
                    Acceptable = AfterHours.IndexData,
                    Stale = AfterHours.IndexData * 2.0,
                    VeryStale = AfterHours.IndexData * 4.0
                },
                DataType.VixData => new StalenessThresholds
                {
                    Fresh = AfterHours.VixData * 0.5,
                    Acceptable = AfterHours.VixData,
                    Stale = AfterHours.VixData * 2.0,
                    VeryStale = AfterHours.VixData * 4.0
                },
                _ => new StalenessThresholds
                {
                    Fresh = TimeSpan.FromMinutes(20),
                    Acceptable = TimeSpan.FromHours(2),
                    Stale = TimeSpan.FromHours(4),
                    VeryStale = TimeSpan.FromHours(8)
                }
            };
        }
    }
}


