namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration for robust signal generation with error recovery
/// </summary>
public sealed class RobustSignalConfiguration
{
    /// <summary>
    /// Maximum number of concurrent signal generation operations
    /// </summary>
    public int MaxConcurrentGenerations { get; set; } = 5;

    /// <summary>
    /// Minimum number of signals that must be generated for success
    /// </summary>
    public int MinimumAcceptableSignals { get; set; } = 3;

    /// <summary>
    /// Minimum confidence score required for signals
    /// </summary>
    public double MinimumConfidenceScore { get; set; } = 0.5;

    /// <summary>
    /// Whether to enable fallback generation when adaptive fails
    /// </summary>
    public bool EnableFallbackGeneration { get; set; } = true;

    /// <summary>
    /// Whether to enable emergency generation as last resort
    /// </summary>
    public bool EnableEmergencyGeneration { get; set; } = true;

    /// <summary>
    /// Timeout for adaptive signal generation
    /// </summary>
    public TimeSpan AdaptiveGenerationTimeout { get; set; } = TimeSpan.FromMinutes(2);

    /// <summary>
    /// Timeout for fallback signal generation
    /// </summary>
    public TimeSpan FallbackGenerationTimeout { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// How long emergency mode stays active
    /// </summary>
    public TimeSpan EmergencyModeDuration { get; set; } = TimeSpan.FromMinutes(10);

    /// <summary>
    /// Maximum number of errors per symbol before skipping
    /// </summary>
    public int MaxErrorsPerSymbol { get; set; } = 5;

    /// <summary>
    /// Cooldown period after errors before retrying a symbol
    /// </summary>
    public TimeSpan ErrorCooldownPeriod { get; set; } = TimeSpan.FromMinutes(30);

    /// <summary>
    /// Core symbols to use for emergency generation
    /// </summary>
    public List<string> CoreSymbols { get; set; } = new()
    {
        "SPY", "QQQ", "IWM", "VTI", "VOO", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"
    };

    /// <summary>
    /// Retry configuration for failed operations
    /// </summary>
    public RetryConfiguration RetryConfig { get; set; } = new();
}

/// <summary>
/// Configuration for retry logic
/// </summary>
public sealed class RetryConfiguration
{
    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Base delay between retries
    /// </summary>
    public TimeSpan BaseDelay { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Maximum delay between retries
    /// </summary>
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Whether to use exponential backoff
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;

    /// <summary>
    /// Whether to add jitter to retry delays
    /// </summary>
    public bool UseJitter { get; set; } = true;
}
