using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Models;
using EFCore.BulkExtensions;

namespace SmaTrendFollower.Data;

/// <summary>
/// Entity Framework DbContext for SQLite index bar caching.
/// Manages cached index data to reduce API calls to Polygon.
/// </summary>
public class IndexCacheDbContext : DbContext
{
    public IndexCacheDbContext(DbContextOptions<IndexCacheDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Cached index bars from Polygon API
    /// </summary>
    public DbSet<CachedIndexBar> CachedIndexBars { get; set; } = null!;

    /// <summary>
    /// Metadata about cached data for each symbol
    /// </summary>
    public DbSet<CacheMetadata> CacheMetadata { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure CachedIndexBar
        modelBuilder.Entity<CachedIndexBar>(entity =>
        {
            // Composite index for efficient queries
            entity.HasIndex(e => new { e.Symbol, e.TimeUtc })
                  .IsUnique()
                  .HasDatabaseName("IX_CachedIndexBars_Symbol_TimeUtc");

            // Additional index for date range queries
            entity.HasIndex(e => e.TimeUtc)
                  .HasDatabaseName("IX_CachedIndexBars_TimeUtc");

            // Symbol index for metadata queries
            entity.HasIndex(e => e.Symbol)
                  .HasDatabaseName("IX_CachedIndexBars_Symbol");

            // Financial data precision: 18 digits total, 4 decimal places for accurate calculations
            entity.Property(e => e.Open).HasPrecision(18, 4);
            entity.Property(e => e.High).HasPrecision(18, 4);
            entity.Property(e => e.Low).HasPrecision(18, 4);
            entity.Property(e => e.Close).HasPrecision(18, 4);

            // Volume precision: 18 digits total, 0 decimal places (whole shares)
            entity.Property(e => e.Volume).HasPrecision(18, 0);
        });

        // Configure CacheMetadata
        modelBuilder.Entity<CacheMetadata>(entity =>
        {
            entity.HasKey(e => e.Symbol);
        });
    }

    /// <summary>
    /// Ensures the database is created and migrations are applied
    /// </summary>
    public async Task EnsureDatabaseCreatedAsync()
    {
        await Database.EnsureCreatedAsync();
    }

    /// <summary>
    /// Gets cached bars for a symbol within a date range
    /// </summary>
    public async Task<List<CachedIndexBar>> GetCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        return await CachedIndexBars
            .Where(b => b.Symbol == symbol && b.TimeUtc >= startDate && b.TimeUtc <= endDate)
            .OrderBy(b => b.TimeUtc)
            .ToListAsync();
    }

    /// <summary>
    /// Gets the latest cached date for a symbol
    /// </summary>
    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol)
    {
        var metadata = await CacheMetadata.FindAsync(symbol);
        return metadata?.LatestDataDate;
    }

    /// <summary>
    /// Adds or updates cached bars for a symbol
    /// </summary>
    public async Task AddOrUpdateCachedBarsAsync(string symbol, IEnumerable<Services.IndexBar> indexBars)
    {
        var cachedBars = indexBars.Select(bar => CachedIndexBar.FromIndexBar(symbol, bar)).ToList();
        
        if (!cachedBars.Any())
            return;

        // Bulk check for existing bars to minimize database round trips
        var timeStamps = cachedBars.Select(b => b.TimeUtc).ToList();
        var existingBars = await CachedIndexBars
            .Where(b => b.Symbol == symbol && timeStamps.Contains(b.TimeUtc))
            .Select(b => b.TimeUtc)
            .ToHashSetAsync();

        // Filter out existing bars and bulk insert new ones
        var newBars = cachedBars.Where(b => !existingBars.Contains(b.TimeUtc)).ToList();
        if (newBars.Any())
        {
            await this.BulkInsertAsync(newBars);
        }

        // Update metadata
        var latestDate = cachedBars.Max(b => b.TimeUtc);
        var metadata = await CacheMetadata.FindAsync(symbol);
        
        if (metadata == null)
        {
            metadata = new CacheMetadata
            {
                Symbol = symbol,
                LatestDataDate = latestDate,
                LastUpdated = DateTime.UtcNow,
                BarCount = cachedBars.Count
            };
            CacheMetadata.Add(metadata);
        }
        else
        {
            if (latestDate > metadata.LatestDataDate)
            {
                metadata.LatestDataDate = latestDate;
            }
            metadata.LastUpdated = DateTime.UtcNow;
            metadata.BarCount = await CachedIndexBars.CountAsync(b => b.Symbol == symbol);
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// Cleans up old cached data (older than specified days)
    /// </summary>
    public async Task CleanupOldDataAsync(int retainDays = 365)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);
        
        var oldBars = CachedIndexBars.Where(b => b.TimeUtc < cutoffDate);
        CachedIndexBars.RemoveRange(oldBars);
        
        await SaveChangesAsync();
    }
}
