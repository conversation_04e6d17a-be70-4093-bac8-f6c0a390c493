using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Console.Extensions;
using Alpaca.Markets;

namespace SmaTrendFollower.Diagnostics;

/// <summary>
/// Diagnostic tool to analyze why signal generation is producing zero signals
/// </summary>
public class SignalGenerationDiagnostics
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<SignalGenerationDiagnostics> _logger;

    public SignalGenerationDiagnostics(
        IMarketDataService marketDataService,
        ILogger<SignalGenerationDiagnostics> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
    }

    /// <summary>
    /// Analyzes signal generation for a specific symbol with detailed logging
    /// </summary>
    public async Task<SignalDiagnosticResult> AnalyzeSymbolAsync(string symbol)
    {
        _logger.LogInformation("🔍 Starting signal generation diagnostics for {Symbol}", symbol);

        var result = new SignalDiagnosticResult { Symbol = symbol };

        try
        {
            // Step 1: Get historical data
            var endDate = DateTime.UtcNow.Date.AddDays(-1); // Use yesterday to avoid weekend/holiday issues
            var startDate = endDate.AddDays(-300); // Get more days to account for weekends/holidays
            var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var barsList = barsPage.Items.ToList();

            result.BarsCount = barsList.Count;
            _logger.LogInformation("📊 {Symbol}: Retrieved {Count} bars", symbol, barsList.Count);

            if (barsList.Count < 200)
            {
                result.FailureReason = $"Insufficient bars: {barsList.Count} < 200";
                _logger.LogWarning("❌ {Symbol}: {Reason}", symbol, result.FailureReason);
                return result;
            }

            // Step 2: Calculate technical indicators
            var currentPrice = barsList.Last().Close;
            var sma50 = (decimal)barsList.GetSma50();
            var sma200 = (decimal)barsList.GetSma200();
            var atr14 = (decimal)barsList.GetAtr14();
            var sixMonthReturn = (decimal)barsList.GetTotalReturn(126);

            result.CurrentPrice = currentPrice;
            result.Sma50 = sma50;
            result.Sma200 = sma200;
            result.Atr14 = atr14;
            result.SixMonthReturn = sixMonthReturn;

            _logger.LogInformation("📈 {Symbol}: Price=${Price:F2}, SMA50=${SMA50:F2}, SMA200=${SMA200:F2}, ATR=${ATR:F2}, 6M Return={Return:P2}",
                symbol, currentPrice, sma50, sma200, atr14, sixMonthReturn);

            // Step 3: Check basic validation
            if (currentPrice <= 0 || sma50 <= 0 || sma200 <= 0 || atr14 <= 0)
            {
                result.FailureReason = "Invalid calculated values";
                _logger.LogWarning("❌ {Symbol}: {Reason}", symbol, result.FailureReason);
                return result;
            }

            // Step 4: Check trend filter
            result.TrendFilterPassed = currentPrice > sma50 && currentPrice > sma200;
            _logger.LogInformation("🔄 {Symbol}: Trend Filter = {Passed} (Price > SMA50: {PriceSma50}, Price > SMA200: {PriceSma200})",
                symbol, result.TrendFilterPassed, currentPrice > sma50, currentPrice > sma200);

            // Step 5: Check volatility filter
            var atrPercent = atr14 / currentPrice;
            result.AtrPercent = atrPercent;
            result.VolatilityFilterPassed = atrPercent < 0.05m;
            _logger.LogInformation("📊 {Symbol}: Volatility Filter = {Passed} (ATR%: {AtrPercent:P2} < 5%)",
                symbol, result.VolatilityFilterPassed, atrPercent);

            // Step 6: Calculate RSI and MACD for enhanced signals
            try
            {
                var rsi = barsList.GetRsi14();
                var macd = barsList.GetMacd();

                result.Rsi = rsi;
                result.MacdHistogram = macd.Histogram;

                // Step 7: Check RSI filter
                result.RsiFilterPassed = rsi > 55;
                _logger.LogInformation("📊 {Symbol}: RSI Filter = {Passed} (RSI: {Rsi:F2} > 55)",
                    symbol, result.RsiFilterPassed, rsi);

                // Step 8: Check MACD filter
                result.MacdFilterPassed = macd.Histogram > 0;
                _logger.LogInformation("📊 {Symbol}: MACD Filter = {Passed} (Histogram: {Histogram:F4} > 0)",
                    symbol, result.MacdFilterPassed, macd.Histogram);

                // Step 9: Overall signal evaluation
                result.WouldGenerateSignal = result.TrendFilterPassed && result.VolatilityFilterPassed && 
                                           result.RsiFilterPassed && result.MacdFilterPassed;

                _logger.LogInformation("🎯 {Symbol}: Final Signal = {Signal} (Trend: {Trend}, Vol: {Vol}, RSI: {Rsi}, MACD: {Macd})",
                    symbol, result.WouldGenerateSignal, result.TrendFilterPassed, result.VolatilityFilterPassed,
                    result.RsiFilterPassed, result.MacdFilterPassed);
            }
            catch (Exception ex)
            {
                result.FailureReason = $"RSI/MACD calculation failed: {ex.Message}";
                _logger.LogWarning("❌ {Symbol}: {Reason}", symbol, result.FailureReason);
            }
        }
        catch (Exception ex)
        {
            result.FailureReason = $"Analysis failed: {ex.Message}";
            _logger.LogError(ex, "❌ {Symbol}: Analysis failed", symbol);
        }

        return result;
    }

    /// <summary>
    /// Analyzes signal generation for multiple symbols
    /// </summary>
    public async Task<List<SignalDiagnosticResult>> AnalyzeUniverseAsync(IEnumerable<string> symbols)
    {
        var results = new List<SignalDiagnosticResult>();

        foreach (var symbol in symbols)
        {
            var result = await AnalyzeSymbolAsync(symbol);
            results.Add(result);
        }

        // Summary
        var totalSymbols = results.Count;
        var passedTrend = results.Count(r => r.TrendFilterPassed);
        var passedVolatility = results.Count(r => r.VolatilityFilterPassed);
        var passedRsi = results.Count(r => r.RsiFilterPassed);
        var passedMacd = results.Count(r => r.MacdFilterPassed);
        var wouldSignal = results.Count(r => r.WouldGenerateSignal);

        _logger.LogInformation("📋 SUMMARY: {Total} symbols analyzed", totalSymbols);
        _logger.LogInformation("📋 Trend Filter: {Passed}/{Total} ({Percent:P1})", passedTrend, totalSymbols, (decimal)passedTrend / totalSymbols);
        _logger.LogInformation("📋 Volatility Filter: {Passed}/{Total} ({Percent:P1})", passedVolatility, totalSymbols, (decimal)passedVolatility / totalSymbols);
        _logger.LogInformation("📋 RSI Filter: {Passed}/{Total} ({Percent:P1})", passedRsi, totalSymbols, (decimal)passedRsi / totalSymbols);
        _logger.LogInformation("📋 MACD Filter: {Passed}/{Total} ({Percent:P1})", passedMacd, totalSymbols, (decimal)passedMacd / totalSymbols);
        _logger.LogInformation("📋 Final Signals: {Signals}/{Total} ({Percent:P1})", wouldSignal, totalSymbols, (decimal)wouldSignal / totalSymbols);

        return results;
    }
}

/// <summary>
/// Result of signal generation diagnostics for a symbol
/// </summary>
public class SignalDiagnosticResult
{
    public string Symbol { get; set; } = string.Empty;
    public int BarsCount { get; set; }
    public decimal CurrentPrice { get; set; }
    public decimal Sma50 { get; set; }
    public decimal Sma200 { get; set; }
    public decimal Atr14 { get; set; }
    public decimal AtrPercent { get; set; }
    public decimal SixMonthReturn { get; set; }
    public double Rsi { get; set; }
    public double MacdHistogram { get; set; }
    
    public bool TrendFilterPassed { get; set; }
    public bool VolatilityFilterPassed { get; set; }
    public bool RsiFilterPassed { get; set; }
    public bool MacdFilterPassed { get; set; }
    public bool WouldGenerateSignal { get; set; }
    
    public string? FailureReason { get; set; }
}
