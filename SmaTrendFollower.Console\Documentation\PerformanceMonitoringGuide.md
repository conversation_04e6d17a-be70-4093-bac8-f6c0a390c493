# 🚀 SmaTrendFollower Performance Monitoring System

## Overview

The SmaTrendFollower trading system includes a comprehensive performance monitoring infrastructure designed to ensure optimal performance under real-time trading loads. This system provides real-time monitoring, intelligent alerting, bottleneck detection, and automatic optimization capabilities.

## 🏗️ Architecture

### Core Components

1. **Enhanced System Resource Monitor** - Monitors CPU, memory, disk I/O, network, and thread usage
2. **Database Performance Monitor** - Tracks SQLite and Redis performance metrics
3. **WebSocket Performance Monitor** - Monitors connection stability and message throughput
4. **Trading Pipeline Performance Monitor** - Tracks end-to-end trading pipeline latency
5. **Real-Time Performance Dashboard** - Aggregates all performance data
6. **Performance Alerting Service** - Intelligent alerting with Discord notifications
7. **API Rate Limit Monitor** - Comprehensive API rate limiting and performance tracking
8. **Load Testing Service** - Validates system performance under stress
9. **Performance Optimization Service** - Automatic bottleneck resolution

### Data Flow

```
System Resources → Resource Monitor → Prometheus Metrics
Database Ops → Database Monitor → Performance Dashboard
WebSocket Events → WebSocket Monitor → Alert Service
Trading Pipeline → Pipeline Monitor → Optimization Service
```

## 📊 Monitoring Capabilities

### System Resource Monitoring

- **CPU Usage**: Real-time CPU utilization with configurable thresholds
- **Memory Usage**: Memory consumption tracking with leak detection
- **Thread Count**: Active thread monitoring with contention detection
- **Disk I/O**: Read/write operations per second
- **Network**: Bytes sent/received tracking
- **Process Uptime**: System availability monitoring

### Database Performance Monitoring

- **Query Latency**: P50, P95, P99 latency percentiles
- **Operation Throughput**: Operations per second by type
- **Connection Pool Usage**: Pool utilization and exhaustion detection
- **Cache Hit Rates**: Redis cache effectiveness metrics
- **Error Rates**: Database operation failure tracking

### WebSocket Connection Monitoring

- **Connection Status**: Real-time connection state tracking
- **Message Throughput**: Messages per second by service
- **Reconnection Patterns**: Automatic reconnection frequency
- **Message Latency**: Processing time for incoming messages
- **Error Tracking**: Connection and message processing errors

### Trading Pipeline Monitoring

- **End-to-End Latency**: Complete pipeline execution time
- **Stage Performance**: Individual stage latency tracking
- **Bottleneck Detection**: Automatic slow stage identification
- **Execution Tracking**: Active pipeline execution monitoring
- **Success Rates**: Pipeline completion success tracking

### API Rate Limit Monitoring

- **Rate Limit Tracking**: Real-time monitoring of API rate limits across all services
- **Request Performance**: Latency and throughput metrics for all API calls
- **Automatic Header Parsing**: Extracts rate limit info from HTTP response headers
- **Predictive Analysis**: Forecasts when rate limits might be exceeded
- **Service Integration**: Automatic monitoring for Alpaca, Polygon, Discord, OpenAI, Gemini, Finnhub
- **Error Rate Tracking**: Monitors API failures and rate limit exceeded events
- **Utilization Metrics**: Calculates percentage utilization of rate limits

## 🚨 Alerting System

### Alert Types

1. **Critical Alerts** (5-minute cooldown)
   - CPU usage > 95%
   - Memory usage > 95%
   - Health score < 50%
   - Database latency > 5 seconds

2. **Warning Alerts** (15-minute cooldown)
   - CPU usage > 80%
   - Memory usage > 85%
   - Health score < 70%
   - High bottleneck frequency

3. **Info Alerts** (30-minute cooldown)
   - Performance optimizations applied
   - Load test results
   - Configuration changes

### Discord Integration

Alerts are automatically sent to Discord with:
- Severity indicators (🚨 Critical, ⚠️ Warning, ℹ️ Info)
- Detailed alert information
- Current vs threshold values
- Timestamp and source identification

## 🔧 Configuration

### appsettings.json Configuration

```json
{
  "PerformanceMonitoring": {
    "SystemResources": {
      "MonitoringIntervalSeconds": 5,
      "CpuWarningThreshold": 80.0,
      "CpuCriticalThreshold": 95.0,
      "MemoryWarningThreshold": 85.0,
      "MemoryCriticalThreshold": 95.0
    },
    "Database": {
      "QueryLatencyWarningMs": 1000,
      "QueryLatencyCriticalMs": 5000,
      "RedisLatencyWarningMs": 100,
      "RedisLatencyCriticalMs": 500
    },
    "TradingPipeline": {
      "MaxActiveExecutions": 50,
      "MaxBottlenecksPer5Min": 10,
      "SignalLatencyWarningMs": 5000,
      "TradeExecutionLatencyWarningMs": 2000
    },
    "Alerting": {
      "EnableDiscordNotifications": true,
      "CriticalAlertCooldownMinutes": 5,
      "WarningAlertCooldownMinutes": 15,
      "HealthScoreWarning": 70.0,
      "HealthScoreCritical": 50.0
    }
  }
}
```

## 📈 Prometheus Metrics

### System Metrics

- `system_cpu_usage_percent` - Current CPU usage
- `system_memory_usage_bytes` - Memory consumption
- `system_thread_count` - Active thread count
- `system_process_uptime_seconds` - Process uptime

### Database Metrics

- `database_query_duration_ms` - Query execution time histogram
- `redis_operation_duration_ms` - Redis operation latency
- `redis_cache_operations_total` - Cache hit/miss counters
- `database_connection_pool_usage` - Connection pool metrics

### WebSocket Metrics

- `websocket_connection_status` - Connection state gauge
- `websocket_messages_received_total` - Message throughput
- `websocket_message_latency_ms` - Message processing time
- `websocket_errors_total` - Error counters

### Trading Pipeline Metrics

- `signal_latency_ms` - Signal generation time
- `trades_total` - Trade execution counters
- `pipeline_bottlenecks_total` - Bottleneck detection

### API Rate Limit Metrics

- `api_request_duration_ms` - API request latency histogram
- `api_requests_total` - Total API requests by service/endpoint/status
- `api_rate_limit_remaining` - Current rate limit remaining by service
- `api_rate_limit_exceeded_total` - Rate limit exceeded events by service

## 🧪 Load Testing

### Test Types

1. **Signal Generation Load Test**
   - Concurrent signal generation
   - Configurable duration and concurrency
   - Latency and throughput measurement

2. **WebSocket Stress Test**
   - Multiple connection simulation
   - Message throughput testing
   - Connection stability validation

3. **Database Load Test**
   - Concurrent operation testing
   - Query performance validation
   - Connection pool stress testing

4. **API Stress Test**
   - Rate limit validation
   - Response time measurement
   - Error rate tracking

### Running Load Tests

```bash
# Via API
curl -X POST "http://localhost:8080/api/PerformanceMonitoring/load-test" \
  -H "Content-Type: application/json" \
  -d '{
    "DurationMinutes": 5,
    "ConcurrentSignals": 10,
    "WebSocketConnections": 5,
    "MessagesPerSecond": 100
  }'

# Via CLI (if implemented)
dotnet run -- load-test --duration 5 --signals 10 --websockets 5
```

## 🔄 Automatic Optimization

### Optimization Types

1. **CPU Optimization**
   - Reduce parallel operation concurrency
   - Optimize algorithm efficiency
   - Thread pool tuning

2. **Memory Optimization**
   - Force garbage collection
   - Object pooling implementation
   - Cache size reduction

3. **Database Optimization**
   - Query optimization
   - Connection pool tuning
   - Index recommendations

4. **Pipeline Optimization**
   - Stage parallelization
   - Bottleneck elimination
   - Execution throttling

### Enabling Automatic Optimization

```csharp
// Via service injection
var optimizationService = serviceProvider.GetService<IPerformanceOptimizationService>();
optimizationService.SetAutomaticOptimization(true);

// Via API
curl -X POST "http://localhost:8080/api/PerformanceMonitoring/optimization/auto/true"
```

## 📊 Grafana Dashboard

### Dashboard Panels

1. **System Overview** - CPU, Memory, Threads, Uptime
2. **Trading Activity** - Trades/min, Signals/min, Portfolio value
3. **Signal Generation Latency** - P50, P95, P99 percentiles
4. **WebSocket Connections** - Connection status by service
5. **Database Performance** - Query latency by operation
6. **API Rate Limits** - Remaining limits and exceeded events
7. **Error Rates** - Application and system errors
8. **Market Data Freshness** - Data staleness monitoring

### Accessing the Dashboard

1. Import `SmaTrendFollower.Console/Monitoring/GrafanaDashboard.json`
2. Configure Prometheus data source
3. Set refresh interval to 5 seconds for real-time monitoring

## 🚀 API Endpoints

### Performance Data

- `GET /api/PerformanceMonitoring/dashboard` - Complete dashboard data
- `GET /api/PerformanceMonitoring/system-metrics` - System resource metrics
- `GET /api/PerformanceMonitoring/database-metrics` - Database performance
- `GET /api/PerformanceMonitoring/websocket-metrics` - WebSocket performance
- `GET /api/PerformanceMonitoring/pipeline-metrics` - Trading pipeline metrics

### Alerting

- `GET /api/PerformanceMonitoring/alerts` - Active alerts
- `GET /api/PerformanceMonitoring/alerts/statistics` - Alert statistics
- `POST /api/PerformanceMonitoring/alerts/trigger` - Manual alert trigger

### Optimization

- `GET /api/PerformanceMonitoring/optimization/recommendations` - Get recommendations
- `POST /api/PerformanceMonitoring/optimization/optimize` - Trigger optimization
- `POST /api/PerformanceMonitoring/optimization/auto/{enabled}` - Enable/disable auto-optimization

### Load Testing

- `POST /api/PerformanceMonitoring/load-test` - Comprehensive load test
- `POST /api/PerformanceMonitoring/load-test/signals` - Signal generation test
- `POST /api/PerformanceMonitoring/load-test/websockets` - WebSocket stress test
- `POST /api/PerformanceMonitoring/load-test/database` - Database load test

### API Rate Limit Monitoring

- `GET /api/PerformanceMonitoring/api-rate-limits` - Current rate limit status
- `GET /api/PerformanceMonitoring/api-performance` - API performance metrics
- `GET /api/PerformanceMonitoring/api-rate-limits/analysis` - Rate limit analysis and predictions
- `POST /api/PerformanceMonitoring/api-rate-limits/record` - Manual API request recording

## 🎯 Best Practices

### Production Deployment

1. **Enable all monitoring services** in production
2. **Configure appropriate thresholds** for your environment
3. **Set up Discord notifications** for critical alerts
4. **Schedule regular load tests** during off-hours
5. **Monitor Grafana dashboards** continuously
6. **Enable automatic optimization** for non-critical optimizations

### Performance Tuning

1. **Monitor baseline performance** before optimization
2. **Apply optimizations incrementally** and measure impact
3. **Use load testing** to validate improvements
4. **Set conservative thresholds** initially and adjust based on data
5. **Review alert patterns** to identify systemic issues

### Troubleshooting

1. **Check system resource usage** first
2. **Review recent alerts** for patterns
3. **Analyze bottleneck reports** for pipeline issues
4. **Validate WebSocket connections** for data flow problems
5. **Run targeted load tests** to reproduce issues

## 📚 Additional Resources

- [Prometheus Observability Guide](./PrometheusObservabilityGuide.md)
- [Trading Strategy Documentation](../TRADING_STRATEGY.md)
- [Architecture Overview](../ARCHITECTURE.md)
- [Service Integration Guide](../SERVICE_INTEGRATION_TEST_SUMMARY.md)
