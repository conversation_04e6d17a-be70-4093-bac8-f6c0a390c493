using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using StackExchange.Redis;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Demonstrates the optimized Redis connection service with database caching
/// </summary>
public static class OptimizedRedisConnectionDemo
{
    public static async Task RunAsync()
    {
        System.Console.WriteLine("🔧 OptimizedRedisConnectionService Database Caching Demo");
        System.Console.WriteLine(new string('=', 60));

        var host = CreateHost();
        using var scope = host.Services.CreateScope();

        try
        {
            var redisService = scope.ServiceProvider.GetRequiredService<IOptimizedRedisConnectionService>();
            
            System.Console.WriteLine("📊 Testing database caching optimization...");

            // Test 1: Multiple calls to default database (should use cached instance)
            System.Console.WriteLine("\n🔍 Test 1: Multiple calls to default database (db=0)");
            var db1 = redisService.GetDatabase(0);
            var db2 = redisService.GetDatabase(0);
            var db3 = redisService.GetDatabase();  // Default parameter

            System.Console.WriteLine($"   Database instance 1: {db1.GetHashCode()}");
            System.Console.WriteLine($"   Database instance 2: {db2.GetHashCode()}");
            System.Console.WriteLine($"   Database instance 3: {db3.GetHashCode()}");
            System.Console.WriteLine($"   ✅ All instances are the same: {ReferenceEquals(db1, db2) && ReferenceEquals(db2, db3)}");

            // Test 2: Call to non-default database (should create new instance)
            System.Console.WriteLine("\n🔍 Test 2: Call to non-default database (db=1)");
            var db4 = redisService.GetDatabase(1);
            System.Console.WriteLine($"   Database instance for db=1: {db4.GetHashCode()}");
            System.Console.WriteLine($"   ✅ Different from default db: {!ReferenceEquals(db1, db4)}");
            
            // Test 3: Performance comparison (simulated)
            System.Console.WriteLine("\n⚡ Test 3: Performance benefit simulation");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Simulate multiple rapid calls to default database
            for (int i = 0; i < 1000; i++)
            {
                var db = redisService.GetDatabase(0);
                // In real usage, this would be used for Redis operations
            }

            stopwatch.Stop();
            System.Console.WriteLine($"   1000 calls to GetDatabase(0): {stopwatch.ElapsedMilliseconds}ms");
            System.Console.WriteLine($"   ✅ Cached access provides consistent O(1) performance");

            // Test 4: Async method still works
            System.Console.WriteLine("\n🔄 Test 4: Async method compatibility");
            var asyncDb = await redisService.GetDatabaseAsync(0);
            System.Console.WriteLine($"   Async database instance: {asyncDb.GetHashCode()}");
            System.Console.WriteLine($"   ✅ Async method works alongside sync optimization");
            
            System.Console.WriteLine("\n🎉 All tests completed successfully!");
            System.Console.WriteLine("\n📋 Summary of optimizations:");
            System.Console.WriteLine("   • Default database (db=0) is cached in constructor");
            System.Console.WriteLine("   • Synchronous GetDatabase(0) returns cached instance");
            System.Console.WriteLine("   • Non-default databases use direct multiplexer access");
            System.Console.WriteLine("   • Async methods remain available for complex scenarios");

        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Demo failed: {ex.Message}");
            System.Console.WriteLine("💡 This is expected if Redis is not configured or available");
            System.Console.WriteLine("   The optimization still works with mock/test scenarios");
        }
    }

    private static IHost CreateHost()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Redis:ConnectionString"] = "localhost:6379",
                ["Logging:LogLevel:Default"] = "Information"
            })
            .Build();

        return Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                services.AddSingleton<IConfiguration>(configuration);
                services.AddLogging(builder => 
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // Register Redis connection (will be null if Redis not available)
                services.AddSingleton<IConnectionMultiplexer>(provider =>
                {
                    try
                    {
                        var connectionString = configuration["Redis:ConnectionString"] ?? "localhost:6379";
                        return ConnectionMultiplexer.Connect(connectionString);
                    }
                    catch
                    {
                        // Return null if Redis is not available - this is handled gracefully
                        return null!;
                    }
                });

                // Register OptimizedRedisConnectionService
                services.AddSingleton<IOptimizedRedisConnectionService>(provider =>
                {
                    var muxer = provider.GetService<IConnectionMultiplexer>();
                    var config = provider.GetRequiredService<IConfiguration>();
                    var logger = provider.GetRequiredService<ILogger<OptimizedRedisConnectionService>>();
                    
                    if (muxer == null)
                    {
                        throw new InvalidOperationException("Redis connection not available for demo");
                    }
                    
                    return new OptimizedRedisConnectionService(muxer, config, logger);
                });
            })
            .Build();
    }
}
