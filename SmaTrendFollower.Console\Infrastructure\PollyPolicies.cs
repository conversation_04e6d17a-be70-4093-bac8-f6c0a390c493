using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.Extensions.Http;
using System.Net;

namespace SmaTrendFollower.Infrastructure;

/// <summary>
/// Centralized Polly resilience policies for HTTP clients
/// Provides robust retry and circuit-breaker patterns for external API calls
/// </summary>
public static class PollyPolicies
{
    /// <summary>
    /// Retry policy with exponential back-off for transient failures
    /// - 5 attempts with exponential back-off (1s, 2s, 4s, 8s, 16s)
    /// - Handles 5xx errors, network failures, timeouts, and 429 rate limiting
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> RetryPolicy =>
        HttpPolicyExtensions
            .HandleTransientHttpError()               // 5xx + network failures
            .Or<TaskCanceledException>()              // Timeout exceptions
            .Or<TimeoutException>()                   // Explicit timeout exceptions
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429) // Rate limiting
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)503) // Service unavailable
            .WaitAndRetryAsync(5, retryAttempt =>
                TimeSpan.FromSeconds(Math.Pow(2, retryAttempt - 1))); // 1s, 2s, 4s, 8s, 16s

    /// <summary>
    /// Circuit breaker policy to prevent cascading failures
    /// - Opens after 8 consecutive failures (increased tolerance)
    /// - Stays open for 60 seconds before allowing test requests
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> CircuitBreakerPolicy =>
        HttpPolicyExtensions
            .HandleTransientHttpError()
            .Or<TaskCanceledException>()
            .Or<TimeoutException>()
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)503)
            .CircuitBreakerAsync(8, TimeSpan.FromSeconds(60));

    /// <summary>
    /// Enhanced retry policy with comprehensive logging and socket error handling
    /// - 6 attempts with exponential back-off and jitter
    /// - Handles timeouts, network failures, socket errors, and rate limiting
    /// - Detailed logging of retry attempts with URL and failure reasons
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetRetryPolicyWithLogging(ILogger logger, string apiName)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .Or<TaskCanceledException>()
            .Or<TimeoutException>()
            .Or<System.Net.Sockets.SocketException>()     // Socket-level network errors
            .Or<System.IO.IOException>()                  // I/O errors including connection drops
            .Or<HttpRequestException>()                   // HTTP request failures
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)  // Rate limiting
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)503)  // Service unavailable
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)502)  // Bad gateway
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)504)  // Gateway timeout
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)408)  // Request timeout
            .WaitAndRetryAsync(
                retryCount: 6,
                sleepDurationProvider: retryAttempt =>
                    TimeSpan.FromSeconds(Math.Pow(2, retryAttempt - 1)) +
                    TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)), // Add jitter
                onRetry: (outcome, timespan, retry, ctx) =>
                {
                    var url = outcome.Result?.RequestMessage?.RequestUri?.ToString() ?? "Unknown URL";
                    var reason = outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString() ?? "Unknown";
                    var exceptionType = outcome.Exception?.GetType().Name ?? "HTTP Error";

                    logger.LogWarning("Retry #{Retry}/6 for {ApiName} {Url} due to {ExceptionType}: {Reason}. Waiting {Delay}ms",
                        retry, apiName, url, exceptionType, reason, timespan.TotalMilliseconds);
                });
    }

    /// <summary>
    /// Enhanced circuit breaker policy with comprehensive logging and socket error handling
    /// - Opens after 10 consecutive failures for 90 seconds (increased tolerance for socket errors)
    /// - Handles timeouts, network failures, and socket errors
    /// - Logs circuit breaker state changes for monitoring
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicyWithLogging(ILogger logger, string apiName)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .Or<TaskCanceledException>()
            .Or<TimeoutException>()
            .Or<System.Net.Sockets.SocketException>()     // Socket-level network errors
            .Or<System.IO.IOException>()                  // I/O errors including connection drops
            .Or<HttpRequestException>()                   // HTTP request failures
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)  // Rate limiting
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)503)  // Service unavailable
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)502)  // Bad gateway
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)504)  // Gateway timeout
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)408)  // Request timeout
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 10,
                durationOfBreak: TimeSpan.FromSeconds(90),
                onBreak: (result, duration) =>
                {
                    var reason = result.Exception?.Message ?? result.Result?.StatusCode.ToString() ?? "Unknown";
                    var exceptionType = result.Exception?.GetType().Name ?? "HTTP Error";
                    logger.LogError("{ApiName} circuit breaker OPENED for {Duration}s. Last error: {ExceptionType}: {Reason}",
                        apiName, duration.TotalSeconds, exceptionType, reason);
                },
                onReset: () =>
                {
                    logger.LogInformation("{ApiName} circuit breaker RESET - service recovered", apiName);
                },
                onHalfOpen: () =>
                {
                    logger.LogInformation("{ApiName} circuit breaker HALF-OPEN - testing service", apiName);
                });
    }

    /// <summary>
    /// Timeout policy to prevent hanging requests
    /// - 25 second timeout (slightly less than HttpClient default)
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> TimeoutPolicy =>
        Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(25));

    /// <summary>
    /// Combined policy wrapper for complete resilience
    /// - Combines timeout, retry, and circuit breaker policies
    /// - Applies policies in the correct order for optimal behavior
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetCombinedPolicy(ILogger logger, string apiName)
    {
        var timeout = TimeoutPolicy;
        var retry = GetRetryPolicyWithLogging(logger, apiName);
        var circuitBreaker = GetCircuitBreakerPolicyWithLogging(logger, apiName);

        // Apply policies in order: Timeout -> Retry -> Circuit Breaker
        return Policy.WrapAsync(timeout, retry, circuitBreaker);
    }
}
