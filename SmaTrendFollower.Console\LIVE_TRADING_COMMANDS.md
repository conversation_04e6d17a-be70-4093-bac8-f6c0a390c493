# 🚀 LIVE TRADING QUICK REFERENCE

## **🎯 ESSENTIAL COMMANDS**

### **Start Live Trading**
```powershell
# Full startup with checks
.\start-live-trading.ps1

# Skip pre-flight checks (faster startup)
.\start-live-trading.ps1 -SkipChecks

# Limit to top symbols only
.\start-live-trading.ps1 -MaxSymbols 500

# Verbose logging
.\start-live-trading.ps1 -Verbose
```

### **Emergency Stop**
```powershell
# Stop trading system only
.\emergency-stop.ps1

# Stop trading + close all positions
.\emergency-stop.ps1 -ClosePositions

# Force stop without confirmation
.\emergency-stop.ps1 -ClosePositions -Force
```

### **Real-time Monitoring**
```powershell
# Start monitoring dashboard
.\monitor-trading.ps1

# Faster refresh (15 seconds)
.\monitor-trading.ps1 -RefreshSeconds 15

# Compact view
.\monitor-trading.ps1 -Compact
```

---

## **📊 MANUAL COMMANDS**

### **Account & Positions**
```powershell
# Check account status
.\SmaTrendFollower.Console.exe check-account

# View current positions
.\SmaTrendFollower.Console.exe get-positions

# Check day P&L
.\SmaTrendFollower.Console.exe get-pnl

# View recent trades
.\SmaTrendFollower.Console.exe get-recent-trades
```

### **System Health**
```powershell
# Full system health check
.\SmaTrendFollower.Console.exe check-system-health

# Database performance test
.\SmaTrendFollower.Console.exe test-postgres

# Market data connectivity
.\SmaTrendFollower.Console.exe test-market-data

# Signal generation speed test
.\SmaTrendFollower.Console.exe test-signal-speed
```

### **Trading Controls**
```powershell
# Pause trading (keep positions)
.\SmaTrendFollower.Console.exe pause-trading

# Resume trading
.\SmaTrendFollower.Console.exe resume-trading

# Close specific position
.\SmaTrendFollower.Console.exe close-position --symbol AAPL

# Close all positions
.\SmaTrendFollower.Console.exe close-all-positions
```

---

## **🔍 TROUBLESHOOTING**

### **Signal Generation Issues**
```powershell
# Test signal generation speed
.\SmaTrendFollower.Console.exe test-signal-speed --symbols 100

# Generate signals manually
.\SmaTrendFollower.Console.exe generate-signals --dry-run

# Check universe size
.\SmaTrendFollower.Console.exe check-universe
```

### **Database Issues**
```powershell
# Test database connection
.\SmaTrendFollower.Console.exe test-postgres

# Check database performance
.\SmaTrendFollower.Console.exe check-db-performance

# Clear cache if needed
.\SmaTrendFollower.Console.exe clear-cache
```

### **Market Data Issues**
```powershell
# Test Polygon connection
.\SmaTrendFollower.Console.exe test-market-data

# Check data freshness
.\SmaTrendFollower.Console.exe check-data-freshness

# Restart market data feed
.\SmaTrendFollower.Console.exe restart-market-data
```

---

## **📋 LOG FILES**

### **Key Log Locations**
```
logs/trading.log          - Main trading activity
logs/errors.log           - Error messages
logs/signals.log          - Signal generation
logs/trades.log           - Trade executions
logs/system.log           - System health
```

### **View Logs**
```powershell
# Live trading log
Get-Content logs\trading.log -Tail 50 -Wait

# Recent errors
Get-Content logs\errors.log -Tail 20

# Latest trades
Get-Content logs\trades.log -Tail 10

# System health
Get-Content logs\system.log -Tail 30
```

---

## **🚨 EMERGENCY PROCEDURES**

### **System Crash Recovery**
1. Check process status: `Get-Process SmaTrendFollower*`
2. Check positions: `.\SmaTrendFollower.Console.exe get-positions`
3. Restart system: `.\start-live-trading.ps1`

### **Excessive Losses**
1. Emergency stop: `.\emergency-stop.ps1`
2. Close positions: `.\emergency-stop.ps1 -ClosePositions`
3. Review logs: `Get-Content logs\trading.log -Tail 100`

### **Signal Generation Hanging**
1. Check system health: `.\SmaTrendFollower.Console.exe check-system-health`
2. Test database: `.\SmaTrendFollower.Console.exe test-postgres`
3. Restart with fewer symbols: `.\start-live-trading.ps1 -MaxSymbols 200`

---

## **📞 SUPPORT CONTACTS**

### **Alpaca Support**
- Dashboard: https://app.alpaca.markets/
- Support: <EMAIL>
- Phone: 1-844-ALPACA-1

### **System Information**
- Account: $17,066.39 balance
- Max Daily Loss: $682.68 (4%)
- Max Positions: 8
- Environment: Live Trading

---

## **⚡ KEYBOARD SHORTCUTS**

### **In Trading System**
- `Ctrl+C` - Graceful shutdown
- `Ctrl+Z` - Force stop (Windows)
- `q` + Enter - Quit (if supported)

### **In Monitor**
- `Ctrl+C` - Stop monitoring
- `r` + Enter - Force refresh (if supported)

---

## **🎯 SUCCESS METRICS**

### **Daily Targets**
- **Minimum**: No system crashes, controlled losses
- **Good**: Profitable day, 40%+ win rate
- **Excellent**: >$500 profit, >50% win rate

### **System Performance**
- Signal generation: <10 minutes
- Memory usage: <2GB
- Database queries: <1 second
- Trade execution: <30 seconds
