﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace SmaTrendFollower.Migrations
{
    /// <inheritdoc />
    public partial class AddApiCallLogsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ApiCallLogs",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Provider = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Operation = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Symbol = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    RequestData = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    ResponseData = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Success = table.Column<bool>(type: "boolean", nullable: false),
                    StatusCode = table.Column<int>(type: "integer", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DurationMs = table.Column<int>(type: "integer", nullable: false),
                    TokensUsed = table.Column<int>(type: "integer", nullable: true),
                    Cost = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    Metadata = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApiCallLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Features",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Symbol = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SmaGap = table.Column<float>(type: "real", nullable: false),
                    Volatility = table.Column<float>(type: "real", nullable: false),
                    Rsi = table.Column<float>(type: "real", nullable: false),
                    BreadthScore = table.Column<float>(type: "real", nullable: false),
                    VixLevel = table.Column<float>(type: "real", nullable: false),
                    SixMonthReturn = table.Column<float>(type: "real", precision: 18, scale: 4, nullable: false),
                    RelativeVolume = table.Column<float>(type: "real", nullable: false),
                    MarketRegime = table.Column<float>(type: "real", nullable: false),
                    Forward3DReturn = table.Column<float>(type: "real", precision: 18, scale: 4, nullable: false),
                    Forward5DReturn = table.Column<float>(type: "real", precision: 18, scale: 4, nullable: false),
                    Forward10DReturn = table.Column<float>(type: "real", precision: 18, scale: 4, nullable: false),
                    MaxFavorableExcursion = table.Column<float>(type: "real", nullable: false),
                    MaxAdverseExcursion = table.Column<float>(type: "real", nullable: false),
                    RankProb = table.Column<float>(type: "real", nullable: false),
                    ATR_Pct = table.Column<float>(type: "real", nullable: false),
                    AvgSpreadPct = table.Column<float>(type: "real", nullable: false),
                    EquityPctRisk = table.Column<float>(type: "real", precision: 18, scale: 4, nullable: false),
                    Sentiment = table.Column<float>(type: "real", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Features", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FillsLog",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TimeUtc = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Symbol = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Side = table.Column<int>(type: "integer", nullable: false),
                    Qty = table.Column<decimal>(type: "numeric(18,8)", precision: 18, scale: 8, nullable: false),
                    FillPrice = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    MidPrice = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    SpreadPct = table.Column<float>(type: "real", nullable: false),
                    RankProb = table.Column<float>(type: "real", nullable: false),
                    Regime = table.Column<float>(type: "real", nullable: false),
                    ATR_Pct = table.Column<float>(type: "real", nullable: false),
                    VolumePct10d = table.Column<float>(type: "real", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FillsLog", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ApiCallLogs_CreatedAt",
                table: "ApiCallLogs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ApiCallLogs_Provider_Cost_Timestamp",
                table: "ApiCallLogs",
                columns: new[] { "Provider", "Cost", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_ApiCallLogs_Provider_Timestamp",
                table: "ApiCallLogs",
                columns: new[] { "Provider", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_ApiCallLogs_Success_Timestamp",
                table: "ApiCallLogs",
                columns: new[] { "Success", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_ApiCallLogs_Symbol_Timestamp",
                table: "ApiCallLogs",
                columns: new[] { "Symbol", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_ApiCallLogs_Timestamp",
                table: "ApiCallLogs",
                column: "Timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_Features_CreatedAt",
                table: "Features",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Features_Date",
                table: "Features",
                column: "Date");

            migrationBuilder.CreateIndex(
                name: "IX_Features_Forward3DReturn",
                table: "Features",
                column: "Forward3DReturn");

            migrationBuilder.CreateIndex(
                name: "IX_Features_Symbol",
                table: "Features",
                column: "Symbol");

            migrationBuilder.CreateIndex(
                name: "IX_Features_Symbol_Date",
                table: "Features",
                columns: new[] { "Symbol", "Date" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Features_Symbol_Date_Forward3DReturn",
                table: "Features",
                columns: new[] { "Symbol", "Date", "Forward3DReturn" });

            migrationBuilder.CreateIndex(
                name: "IX_FillsLog_CreatedAt",
                table: "FillsLog",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FillsLog_Symbol",
                table: "FillsLog",
                column: "Symbol");

            migrationBuilder.CreateIndex(
                name: "IX_FillsLog_Symbol_Side_TimeUtc",
                table: "FillsLog",
                columns: new[] { "Symbol", "Side", "TimeUtc" });

            migrationBuilder.CreateIndex(
                name: "IX_FillsLog_Symbol_TimeUtc",
                table: "FillsLog",
                columns: new[] { "Symbol", "TimeUtc" });

            migrationBuilder.CreateIndex(
                name: "IX_FillsLog_TimeUtc",
                table: "FillsLog",
                column: "TimeUtc");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ApiCallLogs");

            migrationBuilder.DropTable(
                name: "Features");

            migrationBuilder.DropTable(
                name: "FillsLog");
        }
    }
}
