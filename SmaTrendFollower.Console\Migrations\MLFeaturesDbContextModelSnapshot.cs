﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using SmaTrendFollower.Data;

#nullable disable

namespace SmaTrendFollower.Migrations
{
    [DbContext(typeof(MLFeaturesDbContext))]
    partial class MLFeaturesDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("SmaTrendFollower.Models.ApiCallLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<decimal?>("Cost")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DurationMs")
                        .HasColumnType("integer");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Metadata")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Operation")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("RequestData")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ResponseData")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int?>("StatusCode")
                        .HasColumnType("integer");

                    b.Property<bool>("Success")
                        .HasColumnType("boolean");

                    b.Property<string>("Symbol")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("TokensUsed")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_ApiCallLogs_CreatedAt");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("IX_ApiCallLogs_Timestamp");

                    b.HasIndex("Provider", "Timestamp")
                        .HasDatabaseName("IX_ApiCallLogs_Provider_Timestamp");

                    b.HasIndex("Success", "Timestamp")
                        .HasDatabaseName("IX_ApiCallLogs_Success_Timestamp");

                    b.HasIndex("Symbol", "Timestamp")
                        .HasDatabaseName("IX_ApiCallLogs_Symbol_Timestamp");

                    b.HasIndex("Provider", "Cost", "Timestamp")
                        .HasDatabaseName("IX_ApiCallLogs_Provider_Cost_Timestamp");

                    b.ToTable("ApiCallLogs");
                });

            modelBuilder.Entity("SmaTrendFollower.Models.FillLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<float>("ATR_Pct")
                        .HasColumnType("real");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("FillPrice")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<decimal>("MidPrice")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<decimal>("Qty")
                        .HasPrecision(18, 8)
                        .HasColumnType("numeric(18,8)");

                    b.Property<float>("RankProb")
                        .HasColumnType("real");

                    b.Property<float>("Regime")
                        .HasColumnType("real");

                    b.Property<int>("Side")
                        .HasColumnType("integer");

                    b.Property<float>("SpreadPct")
                        .HasColumnType("real");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("TimeUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<float>("VolumePct10d")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_FillsLog_CreatedAt");

                    b.HasIndex("Symbol")
                        .HasDatabaseName("IX_FillsLog_Symbol");

                    b.HasIndex("TimeUtc")
                        .HasDatabaseName("IX_FillsLog_TimeUtc");

                    b.HasIndex("Symbol", "TimeUtc")
                        .HasDatabaseName("IX_FillsLog_Symbol_TimeUtc");

                    b.HasIndex("Symbol", "Side", "TimeUtc")
                        .HasDatabaseName("IX_FillsLog_Symbol_Side_TimeUtc");

                    b.ToTable("FillsLog");
                });

            modelBuilder.Entity("SmaTrendFollower.Models.SignalFeature", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<float>("ATR_Pct")
                        .HasColumnType("real");

                    b.Property<float>("AvgSpreadPct")
                        .HasColumnType("real");

                    b.Property<float>("BreadthScore")
                        .HasColumnType("real");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<float>("EquityPctRisk")
                        .HasPrecision(18, 4)
                        .HasColumnType("real");

                    b.Property<float>("Forward10DReturn")
                        .HasPrecision(18, 4)
                        .HasColumnType("real");

                    b.Property<float>("Forward3DReturn")
                        .HasPrecision(18, 4)
                        .HasColumnType("real");

                    b.Property<float>("Forward5DReturn")
                        .HasPrecision(18, 4)
                        .HasColumnType("real");

                    b.Property<float>("MarketRegime")
                        .HasColumnType("real");

                    b.Property<float>("MaxAdverseExcursion")
                        .HasColumnType("real");

                    b.Property<float>("MaxFavorableExcursion")
                        .HasColumnType("real");

                    b.Property<float>("RankProb")
                        .HasColumnType("real");

                    b.Property<float>("RelativeVolume")
                        .HasColumnType("real");

                    b.Property<float>("Rsi")
                        .HasColumnType("real");

                    b.Property<float>("Sentiment")
                        .HasColumnType("real");

                    b.Property<float>("SixMonthReturn")
                        .HasPrecision(18, 4)
                        .HasColumnType("real");

                    b.Property<float>("SmaGap")
                        .HasColumnType("real");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<float>("VixLevel")
                        .HasColumnType("real");

                    b.Property<float>("Volatility")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Features_CreatedAt");

                    b.HasIndex("Date")
                        .HasDatabaseName("IX_Features_Date");

                    b.HasIndex("Forward3DReturn")
                        .HasDatabaseName("IX_Features_Forward3DReturn");

                    b.HasIndex("Symbol")
                        .HasDatabaseName("IX_Features_Symbol");

                    b.HasIndex("Symbol", "Date")
                        .IsUnique()
                        .HasDatabaseName("IX_Features_Symbol_Date");

                    b.HasIndex("Symbol", "Date", "Forward3DReturn")
                        .HasDatabaseName("IX_Features_Symbol_Date_Forward3DReturn");

                    b.ToTable("Features");
                });
#pragma warning restore 612, 618
        }
    }
}
