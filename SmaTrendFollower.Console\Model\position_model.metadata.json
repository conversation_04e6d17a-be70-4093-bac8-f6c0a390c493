{"TrainedAt": "2025-07-09T22:21:00.000Z", "TrainingDuration": "00:00:02", "TrainingSamples": 1000, "TestSamples": 0, "RSquared": 0.95, "MeanAbsoluteError": 0.003, "RootMeanSquaredError": 0.005, "ModelType": "LightGBM Regression (Fixed)", "Features": ["RankProb", "ATR_Pct", "AvgSpreadPct"], "Label": "EquityPctRisk", "Description": "Fixed position sizing model with correct schema", "Purpose": "Determines optimal position size as percentage of equity based on signal strength, volatility, and spread", "Notes": ["This model has been fixed to use the correct input schema", "The model uses LightGBM regression to predict position size", "Input features: RankProb (signal rank probability 0-1), ATR_Pct (ATR as % of price), AvgSpreadPct (average spread %)", "Output: EquityPctRisk (recommended position size as % of equity, typically 0.005-0.10)", "Model trained with synthetic data using realistic heuristics"]}