using SmaTrendFollower.Services;

namespace SmaTrendFollower.Models;

/// <summary>
/// Enhanced trading signal with adaptive capabilities and confidence scoring
/// </summary>
public sealed class AdaptiveTradingSignal
{
    /// <summary>
    /// Stock symbol
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Current price
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// Whether the signal is valid for trading
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Confidence score (0.0 to 1.0) based on data quality and signal strength
    /// </summary>
    public double ConfidenceScore { get; set; }

    /// <summary>
    /// Adapted return calculation based on available data
    /// </summary>
    public decimal AdaptedReturn { get; set; }

    /// <summary>
    /// Average True Range for volatility measurement
    /// </summary>
    public decimal Atr { get; set; }

    /// <summary>
    /// Relative Strength Index
    /// </summary>
    public double Rsi { get; set; }

    /// <summary>
    /// Data source used for signal generation
    /// </summary>
    public string DataSource { get; set; } = string.Empty;

    /// <summary>
    /// Quality of the underlying data
    /// </summary>
    public DataQuality DataQuality { get; set; }

    /// <summary>
    /// Strategy used to generate this signal
    /// </summary>
    public SignalStrategy Strategy { get; set; }

    /// <summary>
    /// Number of bars used in the calculation
    /// </summary>
    public int BarsUsed { get; set; }

    /// <summary>
    /// When the signal was generated
    /// </summary>
    public DateTime ValidationTimestamp { get; set; }

    /// <summary>
    /// Additional metadata about the signal
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Converts to standard TradingSignal for compatibility
    /// </summary>
    public TradingSignal ToTradingSignal()
    {
        return new TradingSignal(
            Symbol,
            Price,
            Atr,
            AdaptedReturn);
    }

    /// <summary>
    /// Gets a human-readable description of the signal
    /// </summary>
    public string GetDescription()
    {
        return $"{Symbol}: {Strategy} strategy, {ConfidenceScore:P1} confidence, " +
               $"{AdaptedReturn:P2} return, {DataQuality} data quality";
    }

    /// <summary>
    /// Determines if this signal should be prioritized over another
    /// </summary>
    public bool ShouldPrioritizeOver(AdaptiveTradingSignal other)
    {
        // First compare confidence scores
        if (Math.Abs(ConfidenceScore - other.ConfidenceScore) > 0.1)
            return ConfidenceScore > other.ConfidenceScore;

        // Then compare data quality
        if (DataQuality != other.DataQuality)
            return DataQuality > other.DataQuality;

        // Finally compare returns
        return AdaptedReturn > other.AdaptedReturn;
    }
}
