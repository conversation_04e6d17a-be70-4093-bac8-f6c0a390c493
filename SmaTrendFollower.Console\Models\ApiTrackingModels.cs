using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace SmaTrendFollower.Models;

/// <summary>
/// Entity representing an API call log for tracking usage, outcomes, and performance
/// of all external APIs used in the trading system (FinBERT, OpenAI, Gemini, Brave Search, etc.)
/// </summary>
[Table("ApiCallLogs")]
[Index(nameof(Provider), nameof(Timestamp), IsUnique = false)]
[Index(nameof(Symbol), nameof(Timestamp), IsUnique = false)]
[Index(nameof(Success), nameof(Timestamp), IsUnique = false)]
public class ApiCallLog
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    /// <summary>
    /// UTC timestamp when the API call was made
    /// </summary>
    [Required]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// API provider name (e.g., "FinBERT", "OpenAI", "Gemini", "BraveSearch", "Alpaca", "Polygon")
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Provider { get; set; } = string.Empty;

    /// <summary>
    /// Operation type (e.g., "sentiment_analysis", "news_search", "market_data", "trade_execution")
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Operation { get; set; } = string.Empty;

    /// <summary>
    /// Associated stock symbol (if applicable, e.g., "AAPL", "SPY")
    /// </summary>
    [MaxLength(20)]
    public string? Symbol { get; set; }

    /// <summary>
    /// Input data sent to the API (truncated for storage efficiency)
    /// </summary>
    [MaxLength(2000)]
    public string? RequestData { get; set; }

    /// <summary>
    /// Response data from the API (truncated for storage efficiency)
    /// </summary>
    [MaxLength(2000)]
    public string? ResponseData { get; set; }

    /// <summary>
    /// Whether the API call was successful
    /// </summary>
    [Required]
    public bool Success { get; set; }

    /// <summary>
    /// HTTP status code returned by the API
    /// </summary>
    public int? StatusCode { get; set; }

    /// <summary>
    /// Error message if the call failed
    /// </summary>
    [MaxLength(500)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// API call duration in milliseconds
    /// </summary>
    [Required]
    public int DurationMs { get; set; }

    /// <summary>
    /// Number of tokens consumed (for LLM APIs like OpenAI, Gemini)
    /// </summary>
    public int? TokensUsed { get; set; }

    /// <summary>
    /// Estimated cost of the API call in USD
    /// </summary>
    [Column(TypeName = "decimal(10,6)")]
    public decimal? Cost { get; set; }

    /// <summary>
    /// Additional metadata as JSON (e.g., model version, confidence scores)
    /// </summary>
    [MaxLength(1000)]
    public string? Metadata { get; set; }

    /// <summary>
    /// When this record was created in the database
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// API usage statistics for a specific provider and time period
/// </summary>
public record ApiUsageStats(
    string Provider,
    DateTime StartDate,
    DateTime EndDate,
    int TotalCalls,
    int SuccessfulCalls,
    int FailedCalls,
    double SuccessRate,
    double AverageResponseTimeMs,
    int? TotalTokensUsed,
    decimal? TotalCost,
    Dictionary<string, int> OperationBreakdown,
    Dictionary<int, int> StatusCodeBreakdown
);

/// <summary>
/// API performance metrics for monitoring and alerting
/// </summary>
public record ApiPerformanceMetrics(
    string Provider,
    DateTime CalculatedAt,
    double SuccessRateLastHour,
    double SuccessRateLastDay,
    double AverageResponseTimeMs,
    int CallsLastHour,
    int CallsLastDay,
    decimal? CostLastDay,
    List<string> RecentErrors
);

/// <summary>
/// API call request for tracking
/// </summary>
public record ApiCallRequest(
    string Provider,
    string Operation,
    string? Symbol = null,
    string? RequestData = null,
    Dictionary<string, object>? Metadata = null
);

/// <summary>
/// API call result for tracking
/// </summary>
public record ApiCallResult(
    bool Success,
    int? StatusCode = null,
    string? ResponseData = null,
    string? ErrorMessage = null,
    int? TokensUsed = null,
    decimal? Cost = null,
    Dictionary<string, object>? Metadata = null
);

/// <summary>
/// Enum for common API providers in the system
/// </summary>
public enum ApiProvider
{
    FinBERT,
    OpenAI,
    Gemini,
    BraveSearch,
    Alpaca,
    Polygon,
    Discord,
    Finnhub,
    Redis,
    PostgreSQL
}

/// <summary>
/// Enum for common API operations
/// </summary>
public enum ApiOperation
{
    SentimentAnalysis,
    NewsSearch,
    MarketData,
    TradeExecution,
    AccountInfo,
    PositionData,
    BarData,
    QuoteData,
    Notification,
    CacheOperation,
    DatabaseQuery
}
