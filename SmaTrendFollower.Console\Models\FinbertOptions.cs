namespace SmaTrendFollower.Models;

/// <summary>
/// Configuration options for FinBERT sentiment analysis pipeline.
/// Controls HTTP endpoint, concurrency, caching, and polling behavior.
/// </summary>
public sealed record FinbertOptions
{
    /// <summary>
    /// Base URL for FinBERT HTTP endpoint (default: local LAN endpoint)
    /// </summary>
    public string BaseUrl { get; init; } = "http://*************:5000/predict_sentiment";
    
    /// <summary>
    /// Maximum number of concurrent FinBERT HTTP calls (default: 4)
    /// </summary>
    public int Parallelism { get; init; } = 4;
    
    /// <summary>
    /// Time-to-live for sentiment data in Redis (default: 3 days)
    /// </summary>
    public int TtlDays { get; init; } = 3;
    
    /// <summary>
    /// Polling interval for fetching fresh news from Alpaca (default: 15 minutes)
    /// </summary>
    public int LookbackMins { get; init; } = 15;

    /// <summary>
    /// Whether to enable FinBERT HTTP endpoint calls (default: true)
    /// Set to false to use only LLM sentiment analysis when FinBERT service is unavailable
    /// </summary>
    public bool EnableFinbertEndpoint { get; init; } = true;
}
