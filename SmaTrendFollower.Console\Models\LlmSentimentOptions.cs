namespace SmaTrendFollower.Models;

/// <summary>
/// Configuration options for LLM sentiment overlay on top of FinBERT.
/// Controls which LLM provider to use, blending weights, and confidence thresholds.
/// </summary>
public sealed record LlmSentimentOptions
{
    /// <summary>
    /// LLM provider to use for sentiment analysis ("OpenAI" or "Gemini")
    /// </summary>
    public string Provider { get; init; } = "OpenAI";
    
    /// <summary>
    /// Model name to use for the selected provider (e.g., "gpt-4o-mini", "gemini-1.5-flash")
    /// </summary>
    public string Model { get; init; } = "gpt-4o-mini";
    
    /// <summary>
    /// Weight for blending LLM sentiment with FinBERT (0.0 = pure FinBERT, 1.0 = pure LLM)
    /// </summary>
    public double BlendWeight { get; init; } = 0.30;
    
    /// <summary>
    /// Confidence cutoff threshold - if |FinBERT score| < cutoff, trigger LLM call
    /// </summary>
    public double ConfidenceCutoff { get; init; } = 0.25;
    
    /// <summary>
    /// Timeout in seconds for LLM API calls
    /// </summary>
    public int TimeoutSeconds { get; init; } = 10;
}
