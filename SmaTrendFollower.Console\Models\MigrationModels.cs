namespace SmaTrendFollower.Models;

/// <summary>
/// Status of a service migration
/// </summary>
public sealed class MigrationStatus
{
    public string ServiceName { get; set; } = string.Empty;
    public bool IsEnabled { get; set; }
    public bool IsRolledBack { get; set; }
    public DateTime LastChangeTime { get; set; }
    public string LastChangeReason { get; set; } = string.Empty;
    public List<MigrationChange> ChangeHistory { get; set; } = new();
}

/// <summary>
/// Record of a migration change
/// </summary>
public sealed class MigrationChange
{
    public DateTime Timestamp { get; set; }
    public string Action { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// Overall migration report
/// </summary>
public sealed class MigrationReport
{
    public DateTime GeneratedAt { get; set; }
    public string OverallStatus { get; set; } = string.Empty;
    public Dictionary<string, MigrationStatus> ServiceStatuses { get; set; } = new();
    public ConfigurationStatus ConfigurationStatus { get; set; } = new();
}

/// <summary>
/// Configuration status for enhanced services
/// </summary>
public sealed class ConfigurationStatus
{
    public bool EnableEnhancedDataRetrieval { get; set; }
    public bool EnableAdaptiveRateLimit { get; set; }
    public bool EnableAdaptiveSignalGeneration { get; set; }
    public bool EnableEnhancedMetrics { get; set; }
    public bool EnableSyntheticData { get; set; }
    public bool EnableEmergencyMode { get; set; }
}

/// <summary>
/// Health check result for enhanced services
/// </summary>
public sealed class HealthCheckResult
{
    public DateTime CheckTime { get; set; }
    public HealthStatus OverallHealth { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, ServiceHealthCheck> ServiceChecks { get; set; } = new();
}

/// <summary>
/// Health check for individual service
/// </summary>
public sealed class ServiceHealthCheck
{
    public string ServiceName { get; set; } = string.Empty;
    public HealthStatus Status { get; set; }
    public DateTime CheckTime { get; set; }
    public string Details { get; set; } = string.Empty;
}

/// <summary>
/// Health status enumeration
/// </summary>
public enum HealthStatus
{
    Healthy,
    Degraded,
    Unhealthy
}
