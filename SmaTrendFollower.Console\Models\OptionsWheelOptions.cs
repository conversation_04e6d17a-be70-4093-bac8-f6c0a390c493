namespace SmaTrendFollower.Models;

/// <summary>
/// Configuration options for the Options Wheel Strategy Engine
/// Controls strike band filtering to optimize option chain fetching
/// </summary>
public sealed record OptionsWheelOptions
{
    /// <summary>
    /// Strike band expressed as percent of underlying spot price (e.g., 15 → ±15%)
    /// Used to filter option chains to only include strikes within this range of the current price
    /// This reduces data transfer and improves performance by filtering at the API level
    /// </summary>
    public double StrikeBandPercent { get; init; } = 15.0;
}
