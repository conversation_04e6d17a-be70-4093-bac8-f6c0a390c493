namespace SmaTrendFollower.Models;

/// <summary>
/// Result of robust signal generation with comprehensive error tracking
/// </summary>
public sealed class RobustSignalResult
{
    /// <summary>
    /// Number of signals requested
    /// </summary>
    public int RequestedCount { get; set; }

    /// <summary>
    /// Successfully generated signals
    /// </summary>
    public List<TradingSignal> Signals { get; set; } = new();

    /// <summary>
    /// Whether the generation was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Overall success rate (successful symbols / attempted symbols)
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// Number of symbols attempted
    /// </summary>
    public int AttemptedSymbols { get; set; }

    /// <summary>
    /// Number of symbols that generated valid signals
    /// </summary>
    public int SuccessfulSymbols { get; set; }

    /// <summary>
    /// Generation methods used
    /// </summary>
    public List<string> GenerationMethods { get; set; } = new();

    /// <summary>
    /// Start time of generation
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// End time of generation
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// Total duration of generation
    /// </summary>
    public TimeSpan TotalDuration { get; set; }

    /// <summary>
    /// Primary error message if generation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Warning messages during generation
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Additional metadata about the generation process
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Gets a summary of the generation result
    /// </summary>
    public string GetSummary()
    {
        var methods = string.Join(", ", GenerationMethods);
        return $"Generated {Signals.Count}/{RequestedCount} signals using {methods} " +
               $"({SuccessRate:P2} success rate, {TotalDuration.TotalSeconds:F1}s)";
    }
}

/// <summary>
/// Tracks error history for individual symbols
/// </summary>
public sealed class SymbolErrorHistory
{
    private readonly List<ErrorRecord> _errors = new();
    private readonly object _lock = new();

    /// <summary>
    /// Total number of errors for this symbol
    /// </summary>
    public int ErrorCount
    {
        get
        {
            lock (_lock)
            {
                return _errors.Count;
            }
        }
    }

    /// <summary>
    /// Last error time
    /// </summary>
    public DateTime? LastErrorTime
    {
        get
        {
            lock (_lock)
            {
                return _errors.LastOrDefault()?.Timestamp;
            }
        }
    }

    /// <summary>
    /// Records a new error for this symbol
    /// </summary>
    public void RecordError(Exception exception)
    {
        lock (_lock)
        {
            _errors.Add(new ErrorRecord(DateTime.UtcNow, exception.GetType().Name, exception.Message));
            
            // Keep only recent errors (last 24 hours)
            var cutoff = DateTime.UtcNow.AddHours(-24);
            _errors.RemoveAll(e => e.Timestamp < cutoff);
        }
    }

    /// <summary>
    /// Determines if this symbol should be skipped due to error history
    /// </summary>
    public bool ShouldSkip(int maxErrors, TimeSpan cooldownPeriod)
    {
        lock (_lock)
        {
            if (_errors.Count < maxErrors)
                return false;

            var lastError = _errors.LastOrDefault();
            if (lastError == null)
                return false;

            return DateTime.UtcNow - lastError.Timestamp < cooldownPeriod;
        }
    }

    /// <summary>
    /// Gets recent error types and their counts
    /// </summary>
    public Dictionary<string, int> GetRecentErrorTypes(TimeSpan window)
    {
        lock (_lock)
        {
            var cutoff = DateTime.UtcNow - window;
            return _errors
                .Where(e => e.Timestamp >= cutoff)
                .GroupBy(e => e.ErrorType)
                .ToDictionary(g => g.Key, g => g.Count());
        }
    }
}

/// <summary>
/// Record of a single error occurrence
/// </summary>
public sealed record ErrorRecord(DateTime Timestamp, string ErrorType, string Message);

/// <summary>
/// Statistics about symbol errors across the system
/// </summary>
public sealed class SymbolErrorStatistics
{
    /// <summary>
    /// Total number of symbols being tracked
    /// </summary>
    public int TotalSymbolsTracked { get; set; }

    /// <summary>
    /// Number of symbols that have had errors
    /// </summary>
    public int SymbolsWithErrors { get; set; }

    /// <summary>
    /// Total number of errors across all symbols
    /// </summary>
    public int TotalErrors { get; set; }

    /// <summary>
    /// Average number of errors per symbol
    /// </summary>
    public double AverageErrorsPerSymbol { get; set; }

    /// <summary>
    /// Top symbols by error count
    /// </summary>
    public Dictionary<string, int> TopErrorSymbols { get; set; } = new();

    /// <summary>
    /// Error rate (symbols with errors / total symbols)
    /// </summary>
    public double ErrorRate => TotalSymbolsTracked > 0 ? (double)SymbolsWithErrors / TotalSymbolsTracked : 0.0;
}
