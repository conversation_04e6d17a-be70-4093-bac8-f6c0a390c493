namespace SmaTrendFollower.Models;

/// <summary>
/// Configuration options for the slippage forecaster service
/// </summary>
public sealed record SlippageOptions
{
    /// <summary>Path to compressed model file (zip of JSON weights)</summary>
    public string ModelPath { get; init; } = "Model/slippage_model.zip";
    
    /// <summary>Fallback slippage (¢/share) if model missing</summary>
    public double DefaultCents { get; init; } = 0.3;
}
