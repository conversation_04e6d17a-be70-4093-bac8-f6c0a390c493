namespace SmaTrendFollower.Models;

/// <summary>
/// Configuration options for universe cache timing and refresh behavior
/// Controls when and how often the universe data is refreshed from external sources
/// </summary>
public sealed record UniverseCacheOptions
{
    /// <summary>
    /// Interval between refresh attempts *inside* allowed window.
    /// Default is 10 minutes to balance freshness with API rate limits.
    /// </summary>
    public TimeSpan RefreshInterval { get; init; } = TimeSpan.FromMinutes(10);
}
