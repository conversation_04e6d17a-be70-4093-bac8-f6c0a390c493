namespace SmaTrendFollower.Models;

/// <summary>
/// Configuration options for VWAP monitoring service timing
/// </summary>
public sealed record VwapOptions
{
    /// <summary>
    /// Hour (0-23) in Eastern Time when VWAP monitoring should start
    /// Default: 9 (9:00 AM ET)
    /// </summary>
    public int StartHour { get; init; } = 9;

    /// <summary>
    /// Minute (0-59) in Eastern Time when VWAP monitoring should start
    /// Default: 40 (40 minutes past the hour)
    /// </summary>
    public int StartMinute { get; init; } = 40;
}
