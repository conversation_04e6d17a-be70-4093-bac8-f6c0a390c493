using Prometheus;

namespace SmaTrendFollower.Console.Monitoring;

/// <summary>
/// Prometheus metrics for Redis-first caching performance monitoring
/// </summary>
public static class CacheMetrics
{
    /// <summary>
    /// Counter for cache operations by type and result
    /// Labels: operation (redis_write, redis_read, sqlite_write, sqlite_read), result (success, failure)
    /// </summary>
    public static readonly Counter CacheOperations = Metrics
        .CreateCounter("cache_operations_total", "Total cache operations by type and result",
            new CounterConfiguration { LabelNames = new[] { "operation", "result" } });

    /// <summary>
    /// Histogram for cache operation latency in milliseconds
    /// Labels: operation (redis_write, redis_read, sqlite_write, sqlite_read)
    /// </summary>
    public static readonly Histogram CacheLatency = Metrics
        .CreateHistogram("cache_latency_ms", "Cache operation latency in milliseconds",
            new HistogramConfiguration 
            { 
                LabelNames = new[] { "operation" },
                Buckets = Histogram.ExponentialBuckets(0.1, 2, 15) // 0.1ms to ~3.2s
            });

    /// <summary>
    /// Gauge for pending bars awaiting SQLite persistence
    /// </summary>
    public static readonly Gauge PendingBarsCount = Metrics
        .CreateGauge("pending_bars_count", "Number of bar entries pending SQLite persistence");

    /// <summary>
    /// Counter for background persistence operations
    /// Labels: result (success, failure)
    /// </summary>
    public static readonly Counter BackgroundPersistenceOperations = Metrics
        .CreateCounter("background_persistence_operations_total", "Background persistence operations",
            new CounterConfiguration { LabelNames = new[] { "result" } });

    /// <summary>
    /// Histogram for background persistence batch processing time
    /// </summary>
    public static readonly Histogram BackgroundPersistenceBatchTime = Metrics
        .CreateHistogram("background_persistence_batch_time_ms", "Background persistence batch processing time in milliseconds",
            new HistogramConfiguration 
            { 
                Buckets = Histogram.ExponentialBuckets(10, 2, 12) // 10ms to ~40s
            });

    /// <summary>
    /// Gauge for Redis memory usage by cache type
    /// Labels: cache_type (bars, pending, snapshots)
    /// </summary>
    public static readonly Gauge RedisCacheMemoryUsage = Metrics
        .CreateGauge("redis_cache_memory_bytes", "Redis memory usage by cache type",
            new GaugeConfiguration { LabelNames = new[] { "cache_type" } });

    /// <summary>
    /// Counter for cache hit/miss ratios
    /// Labels: cache_type (redis, sqlite), result (hit, miss)
    /// </summary>
    public static readonly Counter CacheHitMiss = Metrics
        .CreateCounter("cache_hit_miss_total", "Cache hit/miss ratios",
            new CounterConfiguration { LabelNames = new[] { "cache_type", "result" } });

    /// <summary>
    /// Gauge for SQLite database lock contention
    /// </summary>
    public static readonly Gauge SqliteLockContention = Metrics
        .CreateGauge("sqlite_lock_contention", "SQLite database lock contention incidents");

    /// <summary>
    /// Counter for fallback operations when Redis is unavailable
    /// </summary>
    public static readonly Counter RedisFallbackOperations = Metrics
        .CreateCounter("redis_fallback_operations_total", "Operations that fell back to SQLite due to Redis issues");

    // Helper methods for common metric recording patterns

    /// <summary>
    /// Record a successful Redis write operation
    /// </summary>
    public static void RecordRedisWrite(double latencyMs)
    {
        CacheOperations.WithLabels("redis_write", "success").Inc();
        CacheLatency.WithLabels("redis_write").Observe(latencyMs);
    }

    /// <summary>
    /// Record a failed Redis write operation
    /// </summary>
    public static void RecordRedisWriteFailure(double latencyMs)
    {
        CacheOperations.WithLabels("redis_write", "failure").Inc();
        CacheLatency.WithLabels("redis_write").Observe(latencyMs);
    }

    /// <summary>
    /// Record a successful Redis read operation
    /// </summary>
    public static void RecordRedisRead(double latencyMs, bool hit)
    {
        CacheOperations.WithLabels("redis_read", "success").Inc();
        CacheLatency.WithLabels("redis_read").Observe(latencyMs);
        CacheHitMiss.WithLabels("redis", hit ? "hit" : "miss").Inc();
    }

    /// <summary>
    /// Record a successful SQLite write operation
    /// </summary>
    public static void RecordSqliteWrite(double latencyMs)
    {
        CacheOperations.WithLabels("sqlite_write", "success").Inc();
        CacheLatency.WithLabels("sqlite_write").Observe(latencyMs);
    }

    /// <summary>
    /// Record a failed SQLite write operation (likely due to lock contention)
    /// </summary>
    public static void RecordSqliteWriteFailure(double latencyMs)
    {
        CacheOperations.WithLabels("sqlite_write", "failure").Inc();
        CacheLatency.WithLabels("sqlite_write").Observe(latencyMs);
        SqliteLockContention.Inc();
    }

    /// <summary>
    /// Record a successful SQLite read operation
    /// </summary>
    public static void RecordSqliteRead(double latencyMs, bool hit)
    {
        CacheOperations.WithLabels("sqlite_read", "success").Inc();
        CacheLatency.WithLabels("sqlite_read").Observe(latencyMs);
        CacheHitMiss.WithLabels("sqlite", hit ? "hit" : "miss").Inc();
    }

    /// <summary>
    /// Record a successful background persistence batch
    /// </summary>
    public static void RecordBackgroundPersistenceSuccess(double batchTimeMs, int itemsProcessed)
    {
        BackgroundPersistenceOperations.WithLabels("success").Inc();
        BackgroundPersistenceBatchTime.Observe(batchTimeMs);
    }

    /// <summary>
    /// Record a failed background persistence batch
    /// </summary>
    public static void RecordBackgroundPersistenceFailure(double batchTimeMs)
    {
        BackgroundPersistenceOperations.WithLabels("failure").Inc();
        BackgroundPersistenceBatchTime.Observe(batchTimeMs);
    }

    /// <summary>
    /// Update the pending bars count
    /// </summary>
    public static void UpdatePendingBarsCount(int count)
    {
        PendingBarsCount.Set(count);
    }

    /// <summary>
    /// Record a fallback operation to SQLite due to Redis issues
    /// </summary>
    public static void RecordRedisFallback()
    {
        RedisFallbackOperations.Inc();
    }
}
