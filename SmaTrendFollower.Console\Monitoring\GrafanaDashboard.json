{"dashboard": {"id": null, "title": "SmaTrendFollower - Real-Time Trading Performance", "tags": ["trading", "performance", "sma-trend-follower"], "timezone": "browser", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "system_cpu_usage_percent", "legendFormat": "CPU %"}, {"expr": "system_memory_usage_bytes / 1024 / 1024", "legendFormat": "Memory MB"}, {"expr": "system_thread_count", "legendFormat": "Threads"}, {"expr": "system_process_uptime_seconds / 3600", "legendFormat": "Uptime Hours"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Trading Activity", "type": "stat", "targets": [{"expr": "rate(trades_total[5m]) * 60", "legendFormat": "Trades/min"}, {"expr": "rate(signals_total[5m]) * 60", "legendFormat": "Signals/min"}, {"expr": "portfolio_value_usd", "legendFormat": "Portfolio $"}, {"expr": "current_positions", "legendFormat": "Positions"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Signal Generation Latency", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, rate(signal_latency_ms_bucket[5m]))", "legendFormat": "P50"}, {"expr": "histogram_quantile(0.95, rate(signal_latency_ms_bucket[5m]))", "legendFormat": "P95"}, {"expr": "histogram_quantile(0.99, rate(signal_latency_ms_bucket[5m]))", "legendFormat": "P99"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "WebSocket Connections", "type": "graph", "targets": [{"expr": "websocket_connection_status", "legendFormat": "{{service}}-{{channel}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Database Performance", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(database_query_duration_ms_bucket[5m]))", "legendFormat": "{{database}}-{{operation}} P95"}, {"expr": "histogram_quantile(0.95, rate(redis_operation_duration_ms_bucket[5m]))", "legendFormat": "Redis-{{operation}} P95"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "API Rate Limits", "type": "graph", "targets": [{"expr": "api_rate_limit_remaining", "legendFormat": "{{service}} Remaining"}, {"expr": "rate(api_rate_limit_exceeded_total[5m]) * 60", "legendFormat": "{{service}} Exceeded/min"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Error Rates", "type": "graph", "targets": [{"expr": "rate(application_errors_total[5m]) * 60", "legendFormat": "{{component}} Errors/min"}, {"expr": "rate(websocket_errors_total[5m]) * 60", "legendFormat": "{{service}} WS Errors/min"}, {"expr": "rate(order_failures_total[5m]) * 60", "legendFormat": "Order Failures/min"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "Market Data Freshness", "type": "graph", "targets": [{"expr": "data_staleness_minutes", "legendFormat": "{{data_type}}-{{symbol}} Age (min)"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "Resource Alerts", "type": "table", "targets": [{"expr": "increase(system_resource_alerts_total[1h])", "legendFormat": "{{resource_type}}-{{severity}}"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}, {"id": 10, "title": "Trading Pipeline Bottlenecks", "type": "heatmap", "targets": [{"expr": "rate(signal_latency_ms_bucket[5m])", "legendFormat": "Signal Generation"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}}, {"id": 11, "title": "API Rate Limit Utilization", "type": "graph", "targets": [{"expr": "(api_requests_total / ignoring(status_code) group_left() (api_rate_limit_remaining + api_requests_total)) * 100", "legendFormat": "{{service}} Utilization %"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 48}}, {"id": 12, "title": "API Request Latency by Service", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(api_request_duration_ms_bucket[5m]))", "legendFormat": "{{service}} P95"}, {"expr": "histogram_quantile(0.50, rate(api_request_duration_ms_bucket[5m]))", "legendFormat": "{{service}} P50"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 30, "version": 1, "annotations": {"list": [{"name": "Trading Events", "datasource": "Prometheus", "expr": "changes(trades_total[1m])", "titleFormat": "Trade Executed", "textFormat": "{{symbol}} {{side}}"}, {"name": "System Alerts", "datasource": "Prometheus", "expr": "changes(system_resource_alerts_total[1m])", "titleFormat": "Resource Alert", "textFormat": "{{resource_type}} {{severity}}"}]}, "templating": {"list": [{"name": "symbol", "type": "query", "query": "label_values(trades_total, symbol)", "refresh": 1}, {"name": "service", "type": "query", "query": "label_values(websocket_connection_status, service)", "refresh": 1}]}}}