using System.Diagnostics;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace SmaTrendFollower.Monitoring;

/// <summary>
/// HTTP message handler that automatically measures and records latency metrics for outbound HTTP calls.
/// Integrates with Prometheus metrics to provide observability for API performance.
/// </summary>
internal sealed class TimedHttpHandler : DelegatingHandler
{
    private readonly string _label;

    /// <summary>
    /// Initializes a new instance of the TimedHttpHandler with the specified client label.
    /// </summary>
    /// <param name="label">The client label for metrics (e.g., "alpaca", "polygon", "finbert")</param>
    public TimedHttpHandler(string label)
    {
        _label = label ?? throw new ArgumentNullException(nameof(label));
    }

    /// <summary>
    /// Sends an HTTP request and measures the latency, recording it to Prometheus metrics.
    /// </summary>
    /// <param name="request">The HTTP request message</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The HTTP response message</returns>
    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request, 
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            return await base.SendAsync(request, cancellationToken);
        }
        finally
        {
            stopwatch.Stop();
            MetricsRegistry.HttpLatency
                .WithLabels(_label)
                .Observe(stopwatch.Elapsed.TotalMilliseconds);
        }
    }
}
