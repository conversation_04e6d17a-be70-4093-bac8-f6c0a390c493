<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmaTrendFollower - Trading Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-offline { background-color: #F44336; }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #fff;
            font-size: 1.2rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 500;
        }
        
        .metric-value {
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .positive { color: #4CAF50; }
        .negative { color: #F44336; }
        .neutral { color: #FFC107; }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }
        
        .alert {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .alert h4 {
            margin-bottom: 10px;
            color: #F44336;
        }
        
        .refresh-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .timestamp {
            text-align: center;
            margin-top: 20px;
            opacity: 0.7;
            font-size: 0.9rem;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 SmaTrendFollower Dashboard</h1>
            <p><span class="status-indicator status-warning"></span>System Status: Data Collection Mode</p>
        </div>
        
        <div class="alert">
            <h4>🚨 Critical Issue Detected</h4>
            <p><strong>Root Cause:</strong> Insufficient historical data (14 bars available, 200+ required for SMA200)</p>
            <p><strong>Impact:</strong> Zero trading signals generated</p>
            <p><strong>Action Required:</strong> Populate historical data cache or adjust signal generation criteria</p>
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <h3>📊 Signal Generation</h3>
                <div class="metric">
                    <span class="metric-label">Signals Generated</span>
                    <span class="metric-value negative">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Symbols Processed</span>
                    <span class="metric-value">15</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Success Rate</span>
                    <span class="metric-value negative">0%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Processing Time</span>
                    <span class="metric-value">1,736ms</span>
                </div>
            </div>
            
            <div class="card">
                <h3>💰 Portfolio Status</h3>
                <div class="metric">
                    <span class="metric-label">Account Equity</span>
                    <span class="metric-value positive">$12,061.38</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Day P&L</span>
                    <span class="metric-value neutral">$0.00</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Open Positions</span>
                    <span class="metric-value">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Max Daily Loss</span>
                    <span class="metric-value">$482.46</span>
                </div>
            </div>
            
            <div class="card">
                <h3>📈 Market Conditions</h3>
                <div class="metric">
                    <span class="metric-label">VIX Level</span>
                    <span class="metric-value positive">16.83</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Volatility Regime</span>
                    <span class="metric-value positive">Normal</span>
                </div>
                <div class="metric">
                    <span class="metric-label">SPY vs SMA200</span>
                    <span class="metric-value positive">Above</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Trading Allowed</span>
                    <span class="metric-value positive">Yes</span>
                </div>
            </div>
            
            <div class="card">
                <h3>🤖 ML Models</h3>
                <div class="metric">
                    <span class="metric-label">Signal Model</span>
                    <span class="metric-value negative">Missing</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Position Sizer</span>
                    <span class="metric-value negative">Missing</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Slippage Model</span>
                    <span class="metric-value negative">Missing</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Regime Model</span>
                    <span class="metric-value negative">Missing</span>
                </div>
            </div>
            
            <div class="card">
                <h3>📊 Data Quality</h3>
                <div class="metric">
                    <span class="metric-label">Historical Bars (SPY)</span>
                    <span class="metric-value negative">14/200</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 7%"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">Cache Status</span>
                    <span class="metric-value negative">Insufficient</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Data Freshness</span>
                    <span class="metric-value neutral">6h 35m old</span>
                </div>
            </div>
            
            <div class="card">
                <h3>⚡ Performance</h3>
                <div class="metric">
                    <span class="metric-label">Expected Return</span>
                    <span class="metric-value negative">-85.01%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Sharpe Ratio</span>
                    <span class="metric-value neutral">0.56</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Max Drawdown</span>
                    <span class="metric-value positive">0.00%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">VaR (95%)</span>
                    <span class="metric-value negative">-99.38%</span>
                </div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh Dashboard</button>
        </div>
        
        <div class="timestamp">
            Last Updated: <span id="timestamp"></span>
        </div>
    </div>
    
    <script>
        // Update timestamp
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        // Auto-refresh every 30 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
