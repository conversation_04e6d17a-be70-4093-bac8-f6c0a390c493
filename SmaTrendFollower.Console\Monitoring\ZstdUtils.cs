using System.IO.Compression;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Monitoring;

/// <summary>
/// Utility class for Zstd compression with gzip fallback
/// </summary>
internal static class ZstdUtils
{
    private static readonly ILogger _logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger(typeof(ZstdUtils));

    /// <summary>
    /// Compresses a file using Zstd compression with gzip fallback
    /// Enhanced with better error handling and file validation
    /// </summary>
    /// <param name="src">Source file path</param>
    /// <param name="dstZst">Destination compressed file path</param>
    public static void CompressAndReplace(string src, string dstZst)
    {
        // Validate inputs
        if (string.IsNullOrEmpty(src) || !File.Exists(src))
        {
            _logger.LogError("Source file does not exist: {Source}", src);
            throw new FileNotFoundException($"Source file not found: {src}");
        }

        if (string.IsNullOrEmpty(dstZst))
        {
            _logger.LogError("Destination path is null or empty");
            throw new ArgumentException("Destination path cannot be null or empty", nameof(dstZst));
        }

        // Check available disk space (basic check)
        var sourceInfo = new FileInfo(src);
        var destDir = Path.GetDirectoryName(dstZst);
        if (!string.IsNullOrEmpty(destDir) && !Directory.Exists(destDir))
        {
            Directory.CreateDirectory(destDir);
            _logger.LogDebug("Created destination directory: {Directory}", destDir);
        }

        try
        {
            // Try Zstd compression first
            _logger.LogDebug("Attempting Zstd compression: {Source} ({Size:N0} bytes) -> {Destination}",
                src, sourceInfo.Length, dstZst);

            using var fin = File.OpenRead(src);
            using var fout = File.Create(dstZst);
            using var z = new ZstdNet.CompressionStream(fout, 3); // Level 3 for good balance of speed/compression
            fin.CopyTo(z);

            var compressedInfo = new FileInfo(dstZst);
            var ratio = sourceInfo.Length > 0 ? (double)compressedInfo.Length / sourceInfo.Length : 0;

            _logger.LogDebug("Successfully compressed {Source} to {Destination} using Zstd. " +
                           "Size: {OriginalSize:N0} -> {CompressedSize:N0} bytes (ratio: {Ratio:P1})",
                src, dstZst, sourceInfo.Length, compressedInfo.Length, ratio);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Zstd compression failed for {Source} ({Size:N0} bytes): {Error}. Falling back to gzip",
                src, sourceInfo.Length, ex.Message);

            // Clean up failed Zstd file
            try
            {
                if (File.Exists(dstZst))
                {
                    File.Delete(dstZst);
                }
            }
            catch (Exception cleanupEx)
            {
                _logger.LogWarning(cleanupEx, "Failed to clean up partial Zstd file: {File}", dstZst);
            }

            // Fallback to gzip if Zstd not available
            var gzipPath = dstZst + ".gz";
            try
            {
                _logger.LogDebug("Attempting gzip compression: {Source} -> {Destination}", src, gzipPath);

                using var fin = File.OpenRead(src);
                using var gz = new GZipStream(File.Create(gzipPath), CompressionLevel.Optimal);
                fin.CopyTo(gz);

                var compressedInfo = new FileInfo(gzipPath);
                var ratio = sourceInfo.Length > 0 ? (double)compressedInfo.Length / sourceInfo.Length : 0;

                _logger.LogDebug("Successfully compressed {Source} to {Destination} using gzip fallback. " +
                               "Size: {OriginalSize:N0} -> {CompressedSize:N0} bytes (ratio: {Ratio:P1})",
                    src, gzipPath, sourceInfo.Length, compressedInfo.Length, ratio);
            }
            catch (Exception gzipEx)
            {
                _logger.LogError(gzipEx, "Both Zstd and gzip compression failed for {Source} ({Size:N0} bytes). " +
                                       "Zstd error: {ZstdError}, Gzip error: {GzipError}",
                    src, sourceInfo.Length, ex.Message, gzipEx.Message);

                // Clean up failed gzip file
                try
                {
                    if (File.Exists(gzipPath))
                    {
                        File.Delete(gzipPath);
                    }
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogWarning(cleanupEx, "Failed to clean up partial gzip file: {File}", gzipPath);
                }

                throw new InvalidOperationException($"All compression methods failed for {src}. " +
                                                  $"Zstd: {ex.Message}, Gzip: {gzipEx.Message}", gzipEx);
            }
        }
    }
}
