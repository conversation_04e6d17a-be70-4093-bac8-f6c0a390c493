-- Database Optimization Script for SmaTrendFollower
-- Fixes performance issues with universe refresh hanging on UPDATE operations
-- Run this script to add missing indexes and optimize PostgreSQL settings

-- ============================================================================
-- CRITICAL MISSING INDEXES (Fixes the hanging UPDATE operations)
-- ============================================================================

-- 1. Primary key index on CachedStockBars.Id (CRITICAL - fixes 7+ second UPDATEs)
-- This is the most important fix - without this, UPDATE operations scan the entire table
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_CachedStockBars_Id" 
ON "CachedStockBars" ("Id");

-- 2. Primary key index on CachedIndexBars.Id
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_CachedIndexBars_Id" 
ON "CachedIndexBars" ("Id");

-- ============================================================================
-- ADDITIONAL PERFORMANCE INDEXES
-- ============================================================================

-- 3. Composite index for cache timestamp updates (improves batch operations)
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_CachedStockBars_Symbol_TimeFrame_CachedAt" 
ON "CachedStockBars" ("Symbol", "TimeFrame", "CachedAt");

-- 4. Index for cache cleanup operations (removes old data efficiently)
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_CachedStockBars_CachedAt_Symbol" 
ON "CachedStockBars" ("CachedAt", "Symbol");

-- 5. Index for metadata lookups during universe refresh
CREATE INDEX CONCURRENTLY IF NOT EXISTS "IX_StockCacheMetadata_LastUpdated" 
ON "StockCacheMetadata" ("LastUpdated");

-- ============================================================================
-- POSTGRESQL PERFORMANCE OPTIMIZATIONS
-- ============================================================================

-- 6. Increase work memory for complex queries (universe refresh uses large sorts)
ALTER SYSTEM SET work_mem = '256MB';

-- 7. Increase shared buffers for better caching (25% of available RAM)
ALTER SYSTEM SET shared_buffers = '512MB';

-- 8. Enable parallel query execution for large operations
ALTER SYSTEM SET max_parallel_workers_per_gather = 4;
ALTER SYSTEM SET max_parallel_workers = 8;

-- 9. Optimize for read-heavy workloads (universe refresh is mostly reads)
ALTER SYSTEM SET random_page_cost = 1.1;

-- 10. Increase checkpoint segments for better write performance
ALTER SYSTEM SET max_wal_size = '2GB';
ALTER SYSTEM SET min_wal_size = '512MB';

-- 11. Optimize autovacuum for high-frequency updates
ALTER SYSTEM SET autovacuum_vacuum_scale_factor = 0.1;
ALTER SYSTEM SET autovacuum_analyze_scale_factor = 0.05;

-- ============================================================================
-- TABLE-SPECIFIC OPTIMIZATIONS
-- ============================================================================

-- 12. Set fill factor for CachedStockBars to allow for updates without page splits
ALTER TABLE "CachedStockBars" SET (fillfactor = 90);

-- 13. Set fill factor for StockCacheMetadata (frequently updated)
ALTER TABLE "StockCacheMetadata" SET (fillfactor = 90);

-- ============================================================================
-- STATISTICS UPDATES (Helps query planner choose better execution plans)
-- ============================================================================

-- 14. Update table statistics for better query planning
ANALYZE "CachedStockBars";
ANALYZE "StockCacheMetadata";
ANALYZE "CachedIndexBars";
ANALYZE "CacheMetadata";

-- ============================================================================
-- RELOAD CONFIGURATION
-- ============================================================================

-- 15. Reload PostgreSQL configuration to apply system changes
-- Note: This requires superuser privileges
SELECT pg_reload_conf();

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Check if indexes were created successfully
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('CachedStockBars', 'StockCacheMetadata', 'CachedIndexBars')
ORDER BY tablename, indexname;

-- Check current PostgreSQL settings
SELECT name, setting, unit, context 
FROM pg_settings 
WHERE name IN (
    'work_mem', 
    'shared_buffers', 
    'max_parallel_workers_per_gather',
    'random_page_cost',
    'max_wal_size'
)
ORDER BY name;

-- Check table sizes and row counts
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes
FROM pg_stat_user_tables 
WHERE tablename IN ('CachedStockBars', 'StockCacheMetadata')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ============================================================================
-- NOTES
-- ============================================================================

/*
CRITICAL FIXES:
1. IX_CachedStockBars_Id - This is the most important fix. Without this index,
   UPDATE operations on the Id column scan the entire table, causing 7+ second
   delays that make universe refresh hang.

2. CONCURRENTLY keyword - Creates indexes without blocking table access,
   allowing the system to continue running during optimization.

PERFORMANCE IMPACT:
- Before: UPDATE operations take 7+ seconds (487ms, 555ms, 7624ms observed)
- After: UPDATE operations should take <50ms
- Universe refresh should complete in minutes instead of hours

MEMORY REQUIREMENTS:
- work_mem: 256MB per connection (for sorting/hashing operations)
- shared_buffers: 512MB total (for caching frequently accessed data)
- Total additional memory usage: ~1GB

MAINTENANCE:
- Run ANALYZE periodically to keep statistics current
- Monitor pg_stat_user_tables for update patterns
- Consider VACUUM FULL if table bloat becomes excessive
*/
