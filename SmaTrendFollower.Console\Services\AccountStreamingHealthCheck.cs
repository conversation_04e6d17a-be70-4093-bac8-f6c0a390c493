using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace SmaTrendFollower.Services;

/// <summary>
/// Health check for the account streaming service to monitor cache freshness
/// </summary>
public sealed class AccountStreamingHealthCheck : IHealthCheck
{
    private readonly IAccountSnapshotService _accountSnapshot;
    private readonly TimeSpan _maxStaleTime;

    public AccountStreamingHealthCheck(
        IAccountSnapshotService accountSnapshot,
        TimeSpan? maxStaleTime = null)
    {
        _accountSnapshot = accountSnapshot;
        _maxStaleTime = maxStaleTime ?? TimeSpan.FromMinutes(5); // Default 5 minutes
    }

    public Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var latestAccount = _accountSnapshot.Latest;

            if (latestAccount == null)
            {
                return Task.FromResult(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Degraded(
                    "No cached account data available - service may be starting up"));
            }

            // For this implementation, we can't check timestamp since IAccount doesn't have one
            // In a real implementation, you might want to track the last update time separately
            return Task.FromResult(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                $"Account cache available - Equity: {latestAccount.Equity:C}, BuyingPower: {latestAccount.BuyingPower:C}"));
        }
        catch (Exception ex)
        {
            return Task.FromResult(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "Error accessing account snapshot service", ex));
        }
    }
}
