using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Diagnostics.Metrics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that maintains a cached snapshot of account information
/// by periodically polling the Alpaca API to reduce REST calls from RiskManager
/// </summary>
public sealed class AccountStreamingService : BackgroundService, IAccountSnapshotService
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<AccountStreamingService> _logger;
    private readonly bool _isLive;
    private IAccount? _latest;
    private readonly TimeSpan _refreshInterval;

    // Metrics
    private static readonly Meter _meter = new("SmaTrendFollower.AccountStreaming");
    private static readonly Counter<long> _refreshCounter = _meter.CreateCounter<long>("account_refresh_total", "count", "Total number of account data refreshes");
    private static readonly Counter<long> _errorCounter = _meter.CreateCounter<long>("account_refresh_errors_total", "count", "Total number of account refresh errors");
    private static readonly Histogram<double> _refreshDuration = _meter.CreateHistogram<double>("account_refresh_duration_seconds", "seconds", "Duration of account refresh operations");

    public AccountStreamingService(
        IAlpacaClientFactory factory,
        ITradingEnvironmentProvider envProvider,
        IConfiguration configuration,
        ILogger<AccountStreamingService> logger)
    {
        _clientFactory = factory;
        _isLive = envProvider.IsLive;
        _logger = logger;

        // Get refresh interval from configuration, default to 30 seconds
        var refreshSeconds = configuration.GetValue<int?>("AccountStreaming:RefreshIntervalSeconds") ?? 30;
        _refreshInterval = TimeSpan.FromSeconds(refreshSeconds);
    }

    /// <inheritdoc />
    public IAccount? Latest => Volatile.Read(ref _latest);

    protected override async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting account caching service for {Environment} environment (refresh interval: {Interval})",
                _isLive ? "live" : "paper", _refreshInterval);

            using var timer = new PeriodicTimer(_refreshInterval);

            // Initial fetch
            await RefreshAccountDataAsync(cancellationToken);

            // Periodic refresh
            while (!cancellationToken.IsCancellationRequested)
            {
                await timer.WaitForNextTickAsync(cancellationToken);
                await RefreshAccountDataAsync(cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Account caching service cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in account caching service");
            throw;
        }
    }

    private async Task RefreshAccountDataAsync(CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            var rateLimitHelper = _clientFactory.GetRateLimitHelper();

            var account = await rateLimitHelper.ExecuteAsync(async () =>
            {
                using var tradingClient = _clientFactory.CreateTradingClient();
                return await tradingClient.GetAccountAsync(cancellationToken);
            });

            // Store the latest account data using thread-safe write
            Interlocked.Exchange(ref _latest, account);

            // Record successful refresh
            _refreshCounter.Add(1, new KeyValuePair<string, object?>("status", "success"));

            var duration = (DateTime.UtcNow - startTime).TotalSeconds;
            _refreshDuration.Record(duration);

            _logger.LogDebug("Account data refreshed: Equity={Equity:C}, BuyingPower={BuyingPower:C} (took {Duration:F2}s)",
                account.Equity, account.BuyingPower, duration);
        }
        catch (Exception ex)
        {
            // Record error metrics
            _refreshCounter.Add(1, new KeyValuePair<string, object?>("status", "error"));
            _errorCounter.Add(1);

            _logger.LogError(ex, "Error refreshing account data");
            // Don't throw - keep the service running with stale data
        }
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping account caching service");
        return base.StopAsync(cancellationToken);
    }
}
