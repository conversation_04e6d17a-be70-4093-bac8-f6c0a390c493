using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class AlpacaClientFactory : IAlpacaClientFactory
{
    private readonly ILogger<AlpacaClientFactory> _logger;
    private readonly AlpacaRateLimitHelper _rateLimitHelper;
    private readonly IConfiguration _configuration;

    public AlpacaClientFactory(ILogger<AlpacaClientFactory> logger, ILogger<AlpacaRateLimitHelper> rateLimitLogger, IConfiguration configuration)
    {
        _logger = logger;
        _rateLimitHelper = new AlpacaRateLimitHelper(rateLimitLogger);
        _configuration = configuration;
    }

    public IAlpacaTradingClient CreateTradingClient()
    {
        var (keyId, secretKey, isPaper) = GetEnvironmentSpecificKeys();

        _logger.LogInformation("Creating Alpaca trading client for {Environment} environment",
            isPaper ? "paper" : "live");
        _logger.LogInformation("Using API Key ID: {KeyId}", keyId);

        // Use the correct environment based on the setting
        var alpacaEnvironment = isPaper ? Environments.Paper : Environments.Live;
        return alpacaEnvironment.GetAlpacaTradingClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaDataClient CreateDataClient()
    {
        var (keyId, secretKey, isPaper) = GetEnvironmentSpecificKeys();

        // Use the correct environment based on the setting
        var alpacaEnvironment = isPaper ? Environments.Paper : Environments.Live;
        return alpacaEnvironment.GetAlpacaDataClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaStreamingClient CreateStreamingClient()
    {
        var (keyId, secretKey, isPaper) = GetEnvironmentSpecificKeys();

        _logger.LogInformation("Creating Alpaca streaming client for {Environment} environment",
            isPaper ? "paper" : "live");

        // Use the correct environment based on the setting
        var alpacaEnvironment = isPaper ? Environments.Paper : Environments.Live;
        return alpacaEnvironment.GetAlpacaStreamingClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaDataStreamingClient CreateDataStreamingClient()
    {
        var (keyId, secretKey, isPaper) = GetEnvironmentSpecificKeys();

        _logger.LogInformation("Creating Alpaca data streaming client for {Environment} environment",
            isPaper ? "paper" : "live");

        // Use the correct environment based on the setting
        var alpacaEnvironment = isPaper ? Environments.Paper : Environments.Live;
        return alpacaEnvironment.GetAlpacaDataStreamingClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaRateLimitHelper GetRateLimitHelper() => _rateLimitHelper;

    public IAlpacaStreamingClient CreateStreamingClient(bool live)
    {
        var env = live ? Environments.Live : Environments.Paper;
        var (keyId, secretKey) = GetEnvironmentSpecificKeys(live);

        _logger.LogInformation("Creating Alpaca streaming client for {Environment} environment",
            live ? "live" : "paper");

        return env.GetAlpacaStreamingClient(new SecretKey(keyId, secretKey));
    }

    private (string keyId, string secretKey, bool isPaper) GetEnvironmentSpecificKeys()
    {
        // First try to get from configuration (appsettings.json)
        var keyId = _configuration["Alpaca:KeyId"];
        var secretKey = _configuration["Alpaca:SecretKey"];
        var environment = _configuration["Alpaca:Environment"];

        if (!string.IsNullOrEmpty(keyId) && !string.IsNullOrEmpty(secretKey))
        {
            var isPaper = string.Equals(environment, "paper", StringComparison.OrdinalIgnoreCase);
            _logger.LogInformation("Using Alpaca credentials from configuration for {Environment} environment",
                isPaper ? "paper" : "live");
            return (keyId, secretKey, isPaper);
        }

        // Fallback to environment variables for backward compatibility
        _logger.LogWarning("Alpaca credentials not found in configuration, falling back to environment variables");

        var envEnvironment = Environment.GetEnvironmentVariable("APCA_API_ENV");
        var isPaperEnv = string.Equals(envEnvironment, "paper", StringComparison.OrdinalIgnoreCase);

        if (isPaperEnv)
        {
            keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID")
                ?? Environment.GetEnvironmentVariable("APCA_API_KEY_ID_PAPER")
                ?? throw new InvalidOperationException("APCA_API_KEY_ID or APCA_API_KEY_ID_PAPER environment variable not set");

            secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY")
                ?? Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY_PAPER")
                ?? throw new InvalidOperationException("APCA_API_SECRET_KEY or APCA_API_SECRET_KEY_PAPER environment variable not set");
        }
        else
        {
            keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID_LIVE")
                ?? throw new InvalidOperationException("APCA_API_KEY_ID_LIVE environment variable not set for live trading");

            secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY_LIVE")
                ?? throw new InvalidOperationException("APCA_API_SECRET_KEY_LIVE environment variable not set for live trading");
        }

        return (keyId, secretKey, isPaperEnv);
    }

    private (string keyId, string secretKey) GetEnvironmentSpecificKeys(bool live)
    {
        // First try to get from configuration (appsettings.json)
        var keyId = _configuration["Alpaca:KeyId"];
        var secretKey = _configuration["Alpaca:SecretKey"];

        if (!string.IsNullOrEmpty(keyId) && !string.IsNullOrEmpty(secretKey))
        {
            _logger.LogInformation("Using Alpaca credentials from configuration for {Environment} environment",
                live ? "live" : "paper");
            return (keyId, secretKey);
        }

        // Fallback to environment variables for backward compatibility
        _logger.LogWarning("Alpaca credentials not found in configuration, falling back to environment variables");

        if (!live) // Paper trading
        {
            keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID")
                ?? Environment.GetEnvironmentVariable("APCA_API_KEY_ID_PAPER")
                ?? throw new InvalidOperationException("APCA_API_KEY_ID or APCA_API_KEY_ID_PAPER environment variable not set");

            secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY")
                ?? Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY_PAPER")
                ?? throw new InvalidOperationException("APCA_API_SECRET_KEY or APCA_API_SECRET_KEY_PAPER environment variable not set");
        }
        else // Live trading
        {
            keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID_LIVE")
                ?? throw new InvalidOperationException("APCA_API_KEY_ID_LIVE environment variable not set for live trading");

            secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY_LIVE")
                ?? throw new InvalidOperationException("APCA_API_SECRET_KEY_LIVE environment variable not set for live trading");
        }

        return (keyId, secretKey);
    }

    public void Dispose()
    {
        _rateLimitHelper?.Dispose();
    }
}
