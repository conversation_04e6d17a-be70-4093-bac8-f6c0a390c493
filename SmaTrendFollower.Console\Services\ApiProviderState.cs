using SmaTrendFollower.Configuration;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Maintains state for an API provider including rate limiting and circuit breaker
/// </summary>
public sealed class ApiProviderState : IDisposable
{
    private readonly ProviderRateLimitConfiguration _config;
    private SemaphoreSlim _semaphore;
    private volatile int _currentLimit;

    public ApiProviderState(ProviderRateLimitConfiguration config)
    {
        _config = config;
        _currentLimit = config.InitialLimit;
        _semaphore = new SemaphoreSlim(_currentLimit, _currentLimit);
        CircuitBreaker = new CircuitBreaker(config.CircuitBreakerConfig);
        Statistics = new ApiStatistics();
    }

    public SemaphoreSlim Semaphore => _semaphore;
    public int CurrentLimit => _currentLimit;
    public CircuitBreaker CircuitBreaker { get; }
    public ApiStatistics Statistics { get; }

    public void RecordRequest(string operation, int priority)
    {
        Statistics.RecordRequest(operation, priority);
    }

    public void RecordResult(string operation, bool success, TimeSpan duration, string? errorCode)
    {
        Statistics.RecordResult(operation, success, duration, errorCode);
    }

    public void RecordRateLimitHit()
    {
        Statistics.RecordRateLimitHit();
    }

    public async Task UpdateLimitAsync(int newLimit)
    {
        if (newLimit == _currentLimit)
            return;

        var oldSemaphore = _semaphore;
        var newSemaphore = new SemaphoreSlim(newLimit, newLimit);

        // Transfer available permits
        var availablePermits = Math.Min(oldSemaphore.CurrentCount, newLimit);
        for (int i = 0; i < newLimit - availablePermits; i++)
        {
            await newSemaphore.WaitAsync();
        }

        _semaphore = newSemaphore;
        _currentLimit = newLimit;

        // Dispose old semaphore after a delay to allow in-flight operations
        _ = Task.Run(async () =>
        {
            await Task.Delay(TimeSpan.FromSeconds(5));
            oldSemaphore.Dispose();
        });
    }

    public void Dispose()
    {
        _semaphore?.Dispose();
        CircuitBreaker?.Dispose();
    }
}

/// <summary>
/// Circuit breaker implementation for API calls
/// </summary>
public sealed class CircuitBreaker : IDisposable
{
    private readonly CircuitBreakerConfiguration _config;
    private readonly object _lock = new();
    private CircuitBreakerState _state = CircuitBreakerState.Closed;
    private int _failureCount = 0;
    private DateTime _lastFailureTime = DateTime.MinValue;
    private DateTime _nextAttemptTime = DateTime.MinValue;

    public CircuitBreaker(CircuitBreakerConfiguration config)
    {
        _config = config;
    }

    public bool IsOpen
    {
        get
        {
            lock (_lock)
            {
                if (_state == CircuitBreakerState.Open && DateTime.UtcNow >= _nextAttemptTime)
                {
                    _state = CircuitBreakerState.HalfOpen;
                    return false;
                }
                return _state == CircuitBreakerState.Open;
            }
        }
    }

    public TimeSpan TimeUntilReset
    {
        get
        {
            lock (_lock)
            {
                if (_state != CircuitBreakerState.Open)
                    return TimeSpan.Zero;
                
                var timeUntilReset = _nextAttemptTime - DateTime.UtcNow;
                return timeUntilReset > TimeSpan.Zero ? timeUntilReset : TimeSpan.Zero;
            }
        }
    }

    public void RecordSuccess()
    {
        lock (_lock)
        {
            _failureCount = 0;
            _state = CircuitBreakerState.Closed;
        }
    }

    public void RecordFailure()
    {
        lock (_lock)
        {
            _failureCount++;
            _lastFailureTime = DateTime.UtcNow;

            if (_failureCount >= _config.FailureThreshold)
            {
                _state = CircuitBreakerState.Open;
                _nextAttemptTime = DateTime.UtcNow.Add(_config.OpenTimeout);
            }
        }
    }

    public void Dispose()
    {
        // Nothing to dispose
    }
}

public enum CircuitBreakerState
{
    Closed,
    Open,
    HalfOpen
}

/// <summary>
/// Statistics tracking for API operations
/// </summary>
public sealed class ApiStatistics
{
    private readonly ConcurrentQueue<RequestRecord> _requests = new();
    private readonly ConcurrentQueue<ResultRecord> _results = new();
    private readonly ConcurrentQueue<DateTime> _rateLimitHits = new();
    private long _totalRequests = 0;

    public long TotalRequests => _totalRequests;

    public void RecordRequest(string operation, int priority)
    {
        Interlocked.Increment(ref _totalRequests);
        _requests.Enqueue(new RequestRecord(DateTime.UtcNow, operation, priority));
        
        // Clean old records
        CleanOldRecords();
    }

    public void RecordResult(string operation, bool success, TimeSpan duration, string? errorCode)
    {
        _results.Enqueue(new ResultRecord(DateTime.UtcNow, operation, success, duration, errorCode));
        
        // Clean old records
        CleanOldRecords();
    }

    public void RecordRateLimitHit()
    {
        _rateLimitHits.Enqueue(DateTime.UtcNow);
        
        // Clean old records
        CleanOldRecords();
    }

    public double GetRecentSuccessRate(TimeSpan window)
    {
        var cutoff = DateTime.UtcNow - window;
        var recentResults = _results.Where(r => r.Timestamp >= cutoff).ToList();
        
        if (!recentResults.Any())
            return 1.0; // Assume success if no recent data
        
        var successCount = recentResults.Count(r => r.Success);
        return (double)successCount / recentResults.Count;
    }

    public int GetRecentRateLimitHits(TimeSpan window)
    {
        var cutoff = DateTime.UtcNow - window;
        return _rateLimitHits.Count(hit => hit >= cutoff);
    }

    public double GetAverageResponseTime(TimeSpan window)
    {
        var cutoff = DateTime.UtcNow - window;
        var recentResults = _results.Where(r => r.Timestamp >= cutoff && r.Success).ToList();
        
        if (!recentResults.Any())
            return 0.0;
        
        return recentResults.Average(r => r.Duration.TotalMilliseconds);
    }

    private void CleanOldRecords()
    {
        var cutoff = DateTime.UtcNow - TimeSpan.FromHours(1); // Keep 1 hour of data
        
        // Clean requests
        while (_requests.TryPeek(out var request) && request.Timestamp < cutoff)
        {
            _requests.TryDequeue(out _);
        }
        
        // Clean results
        while (_results.TryPeek(out var result) && result.Timestamp < cutoff)
        {
            _results.TryDequeue(out _);
        }
        
        // Clean rate limit hits
        while (_rateLimitHits.TryPeek(out var hit) && hit < cutoff)
        {
            _rateLimitHits.TryDequeue(out _);
        }
    }
}

public sealed record RequestRecord(DateTime Timestamp, string Operation, int Priority);
public sealed record ResultRecord(DateTime Timestamp, string Operation, bool Success, TimeSpan Duration, string? ErrorCode);
