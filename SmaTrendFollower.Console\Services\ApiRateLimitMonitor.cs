using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Monitoring;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Comprehensive API rate limit monitoring service
/// Tracks rate limits, request latencies, and failure rates across all external services
/// </summary>
public interface IApiRateLimitMonitor : IDisposable
{
    /// <summary>
    /// Records an API request with timing and rate limit information
    /// </summary>
    void RecordApiRequest(string service, string endpoint, string method, int statusCode, 
        TimeSpan duration, int? rateLimitRemaining = null, DateTime? rateLimitReset = null);
    
    /// <summary>
    /// Records a rate limit exceeded event
    /// </summary>
    void RecordRateLimitExceeded(string service, string endpoint, int? retryAfterSeconds = null);
    
    /// <summary>
    /// Gets current API rate limit status for all services
    /// </summary>
    Task<ApiRateLimitStatus> GetRateLimitStatusAsync();
    
    /// <summary>
    /// Gets detailed API performance metrics
    /// </summary>
    Task<ApiPerformanceMetrics> GetApiPerformanceMetricsAsync();
    
    /// <summary>
    /// Gets rate limit predictions and recommendations
    /// </summary>
    Task<RateLimitAnalysis> AnalyzeRateLimitsAsync();
    
    /// <summary>
    /// Starts monitoring
    /// </summary>
    Task StartMonitoringAsync();
    
    /// <summary>
    /// Stops monitoring
    /// </summary>
    Task StopMonitoringAsync();
}

/// <summary>
/// API rate limit monitoring service implementation
/// </summary>
public sealed class ApiRateLimitMonitor : BackgroundService, IApiRateLimitMonitor
{
    private readonly ILogger<ApiRateLimitMonitor> _logger;
    private readonly ConcurrentDictionary<string, ApiServiceMetrics> _serviceMetrics;
    private readonly ConcurrentDictionary<string, RateLimitInfo> _rateLimitInfo;
    private readonly ConcurrentQueue<ApiRequestRecord> _requestHistory;
    private readonly Timer _metricsTimer;
    private readonly Timer _analysisTimer;
    private bool _disposed;

    // Known API rate limits (simplified for monitoring)
    private static readonly Dictionary<string, LegacyApiRateLimitConfig> ApiConfigs = new()
    {
        ["Alpaca"] = new LegacyApiRateLimitConfig { RequestsPerMinute = 200, RequestsPerSecond = null },
        ["Polygon"] = new LegacyApiRateLimitConfig { RequestsPerMinute = null, RequestsPerSecond = 5 },
        ["Discord"] = new LegacyApiRateLimitConfig { RequestsPerMinute = 300, RequestsPerSecond = 5 },
        ["OpenAI"] = new LegacyApiRateLimitConfig { RequestsPerMinute = 60, RequestsPerSecond = null },
        ["Gemini"] = new LegacyApiRateLimitConfig { RequestsPerMinute = 10, RequestsPerSecond = null },
        ["Finnhub"] = new LegacyApiRateLimitConfig { RequestsPerMinute = 60, RequestsPerSecond = 1 }
    };

    public ApiRateLimitMonitor(ILogger<ApiRateLimitMonitor> logger)
    {
        _logger = logger;
        _serviceMetrics = new ConcurrentDictionary<string, ApiServiceMetrics>();
        _rateLimitInfo = new ConcurrentDictionary<string, RateLimitInfo>();
        _requestHistory = new ConcurrentQueue<ApiRequestRecord>();
        
        // Initialize service metrics for known APIs
        foreach (var apiConfig in ApiConfigs)
        {
            _serviceMetrics[apiConfig.Key] = new ApiServiceMetrics
            {
                ServiceName = apiConfig.Key,
                LegacyConfiguration = apiConfig.Value
            };
        }
        
        // Start metrics collection timer (every 10 seconds)
        _metricsTimer = new Timer(CollectMetrics, null, TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
        
        // Start analysis timer (every minute)
        _analysisTimer = new Timer(PerformAnalysis, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    public void RecordApiRequest(string service, string endpoint, string method, int statusCode, 
        TimeSpan duration, int? rateLimitRemaining = null, DateTime? rateLimitReset = null)
    {
        var record = new ApiRequestRecord
        {
            Timestamp = DateTime.UtcNow,
            Service = service,
            Endpoint = endpoint,
            Method = method,
            StatusCode = statusCode,
            Duration = duration,
            RateLimitRemaining = rateLimitRemaining,
            RateLimitReset = rateLimitReset
        };
        
        _requestHistory.Enqueue(record);
        
        // Keep only recent history (last 10,000 requests)
        while (_requestHistory.Count > 10000)
        {
            _requestHistory.TryDequeue(out _);
        }
        
        // Update service metrics
        _serviceMetrics.AddOrUpdate(service,
            new ApiServiceMetrics { ServiceName = service },
            (_, existing) =>
            {
                existing.TotalRequests++;
                existing.TotalLatencyMs += duration.TotalMilliseconds;
                existing.LastRequestTime = DateTime.UtcNow;
                
                if (statusCode >= 200 && statusCode < 300)
                {
                    existing.SuccessfulRequests++;
                }
                else
                {
                    existing.FailedRequests++;
                }
                
                if (duration.TotalMilliseconds > existing.MaxLatencyMs)
                {
                    existing.MaxLatencyMs = duration.TotalMilliseconds;
                }
                
                if (existing.MinLatencyMs == 0 || duration.TotalMilliseconds < existing.MinLatencyMs)
                {
                    existing.MinLatencyMs = duration.TotalMilliseconds;
                }
                
                return existing;
            });
        
        // Update rate limit info
        if (rateLimitRemaining.HasValue || rateLimitReset.HasValue)
        {
            _rateLimitInfo.AddOrUpdate(service,
                new RateLimitInfo
                {
                    Service = service,
                    Remaining = rateLimitRemaining,
                    ResetTime = rateLimitReset,
                    LastUpdated = DateTime.UtcNow
                },
                (_, existing) =>
                {
                    if (rateLimitRemaining.HasValue) existing.Remaining = rateLimitRemaining;
                    if (rateLimitReset.HasValue) existing.ResetTime = rateLimitReset;
                    existing.LastUpdated = DateTime.UtcNow;
                    return existing;
                });
        }
        
        // Update Prometheus metrics
        MetricsRegistry.ApiRequestDurationMs
            .WithLabels(service, endpoint, method)
            .Observe(duration.TotalMilliseconds);
        
        MetricsRegistry.ApiRequestsTotal
            .WithLabels(service, endpoint, method, statusCode.ToString())
            .Inc();
        
        if (rateLimitRemaining.HasValue)
        {
            MetricsRegistry.ApiRateLimitRemaining
                .WithLabels(service)
                .Set(rateLimitRemaining.Value);
        }
    }

    public void RecordRateLimitExceeded(string service, string endpoint, int? retryAfterSeconds = null)
    {
        _logger.LogWarning("Rate limit exceeded for {Service} endpoint {Endpoint} (retry after: {RetryAfter}s)",
            service, endpoint, retryAfterSeconds);
        
        _serviceMetrics.AddOrUpdate(service,
            new ApiServiceMetrics { ServiceName = service },
            (_, existing) =>
            {
                existing.RateLimitExceeded++;
                existing.LastRateLimitExceeded = DateTime.UtcNow;
                return existing;
            });
        
        // Update Prometheus metrics
        MetricsRegistry.ApiRateLimitExceeded
            .WithLabels(service)
            .Inc();
    }

    public async Task<ApiRateLimitStatus> GetRateLimitStatusAsync()
    {
        var status = new ApiRateLimitStatus
        {
            Timestamp = DateTime.UtcNow,
            Services = new Dictionary<string, ServiceRateLimitStatus>()
        };
        
        foreach (var service in _serviceMetrics.Keys)
        {
            var metrics = _serviceMetrics[service];
            var rateLimitInfo = _rateLimitInfo.GetValueOrDefault(service);
            var config = ApiConfigs.GetValueOrDefault(service);
            
            // Calculate current usage rates
            var now = DateTime.UtcNow;
            var recentRequests = _requestHistory
                .Where(r => r.Service == service && r.Timestamp > now.AddMinutes(-1))
                .Count();
            
            var requestsPerMinute = recentRequests;
            var requestsPerSecond = _requestHistory
                .Where(r => r.Service == service && r.Timestamp > now.AddSeconds(-1))
                .Count();
            
            status.Services[service] = new ServiceRateLimitStatus
            {
                ServiceName = service,
                CurrentRequestsPerMinute = requestsPerMinute,
                CurrentRequestsPerSecond = requestsPerSecond,
                MaxRequestsPerMinute = config?.RequestsPerMinute,
                MaxRequestsPerSecond = config?.RequestsPerSecond,
                RateLimitRemaining = rateLimitInfo?.Remaining,
                RateLimitResetTime = rateLimitInfo?.ResetTime,
                UtilizationPercentage = CalculateUtilization(requestsPerMinute, requestsPerSecond, config),
                Status = DetermineStatus(requestsPerMinute, requestsPerSecond, config, rateLimitInfo),
                LastRequestTime = metrics.LastRequestTime,
                TotalRateLimitExceeded = metrics.RateLimitExceeded
            };
        }
        
        return await Task.FromResult(status);
    }

    public async Task<ApiPerformanceMetrics> GetApiPerformanceMetricsAsync()
    {
        var metrics = new ApiPerformanceMetrics
        {
            Timestamp = DateTime.UtcNow,
            ServiceMetrics = _serviceMetrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Clone())
        };
        
        return await Task.FromResult(metrics);
    }

    public async Task<RateLimitAnalysis> AnalyzeRateLimitsAsync()
    {
        var analysis = new RateLimitAnalysis
        {
            Timestamp = DateTime.UtcNow,
            Recommendations = new List<string>(),
            Predictions = new Dictionary<string, RateLimitPrediction>()
        };
        
        foreach (var service in _serviceMetrics.Keys)
        {
            var metrics = _serviceMetrics[service];
            var config = ApiConfigs.GetValueOrDefault(service);
            
            if (config == null) continue;
            
            // Analyze recent usage patterns
            var recentRequests = _requestHistory
                .Where(r => r.Service == service && r.Timestamp > DateTime.UtcNow.AddHours(-1))
                .OrderBy(r => r.Timestamp)
                .ToList();
            
            if (recentRequests.Count > 10)
            {
                // Calculate trend
                var trend = CalculateRequestTrend(recentRequests);
                
                // Predict when rate limit might be hit
                var prediction = PredictRateLimitHit(service, recentRequests, config, trend);
                analysis.Predictions[service] = prediction;
                
                // Generate recommendations
                var recommendations = GenerateRecommendations(service, metrics, config, prediction);
                analysis.Recommendations.AddRange(recommendations);
            }
        }
        
        return await Task.FromResult(analysis);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("API rate limit monitor started");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }
        
        _logger.LogInformation("API rate limit monitor stopped");
    }

    public async Task StartMonitoringAsync()
    {
        _logger.LogInformation("Starting API rate limit monitoring");
        await Task.CompletedTask;
    }

    public async Task StopMonitoringAsync()
    {
        _logger.LogInformation("Stopping API rate limit monitoring");
        await StopAsync(CancellationToken.None);
    }

    private void CollectMetrics(object? state)
    {
        try
        {
            var totalRequests = _serviceMetrics.Values.Sum(m => m.TotalRequests);
            var totalErrors = _serviceMetrics.Values.Sum(m => m.FailedRequests);
            var totalRateLimitExceeded = _serviceMetrics.Values.Sum(m => m.RateLimitExceeded);
            
            _logger.LogDebug("API metrics: {TotalRequests} requests, {TotalErrors} errors, {RateLimitExceeded} rate limits exceeded",
                totalRequests, totalErrors, totalRateLimitExceeded);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect API metrics");
        }
    }

    private void PerformAnalysis(object? state)
    {
        try
        {
            // Perform rate limit analysis and log warnings
            var analysis = AnalyzeRateLimitsAsync().Result;
            
            foreach (var prediction in analysis.Predictions)
            {
                if (prediction.Value.TimeToRateLimit.HasValue && 
                    prediction.Value.TimeToRateLimit.Value < TimeSpan.FromMinutes(5))
                {
                    _logger.LogWarning("Service {Service} may hit rate limit in {TimeToLimit}",
                        prediction.Key, prediction.Value.TimeToRateLimit.Value);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to perform rate limit analysis");
        }
    }

    private double CalculateUtilization(int requestsPerMinute, int requestsPerSecond, LegacyApiRateLimitConfig? config)
    {
        if (config == null) return 0;
        
        var minuteUtilization = config.RequestsPerMinute.HasValue ? 
            (double)requestsPerMinute / config.RequestsPerMinute.Value : 0;
        
        var secondUtilization = config.RequestsPerSecond.HasValue ? 
            (double)requestsPerSecond / config.RequestsPerSecond.Value : 0;
        
        return Math.Max(minuteUtilization, secondUtilization) * 100;
    }

    private string DetermineStatus(int requestsPerMinute, int requestsPerSecond,
        LegacyApiRateLimitConfig? config, RateLimitInfo? rateLimitInfo)
    {
        if (config == null) return "Unknown";
        
        var utilization = CalculateUtilization(requestsPerMinute, requestsPerSecond, config);
        
        if (utilization > 90) return "Critical";
        if (utilization > 75) return "Warning";
        if (utilization > 50) return "Moderate";
        return "Normal";
    }

    private double CalculateRequestTrend(List<ApiRequestRecord> requests)
    {
        if (requests.Count < 2) return 0;
        
        // Simple linear trend calculation
        var timeSpan = (requests.Last().Timestamp - requests.First().Timestamp).TotalMinutes;
        return timeSpan > 0 ? requests.Count / timeSpan : 0;
    }

    private RateLimitPrediction PredictRateLimitHit(string service, List<ApiRequestRecord> recentRequests,
        LegacyApiRateLimitConfig config, double trend)
    {
        var prediction = new RateLimitPrediction
        {
            Service = service,
            CurrentTrend = trend
        };
        
        if (trend > 0 && config.RequestsPerMinute.HasValue)
        {
            var currentRate = recentRequests.Count(r => r.Timestamp > DateTime.UtcNow.AddMinutes(-1));
            var remainingCapacity = config.RequestsPerMinute.Value - currentRate;
            
            if (remainingCapacity > 0 && trend > 0)
            {
                var minutesToLimit = remainingCapacity / trend;
                prediction.TimeToRateLimit = TimeSpan.FromMinutes(minutesToLimit);
            }
        }
        
        return prediction;
    }

    private List<string> GenerateRecommendations(string service, ApiServiceMetrics metrics,
        LegacyApiRateLimitConfig config, RateLimitPrediction prediction)
    {
        var recommendations = new List<string>();
        
        if (metrics.RateLimitExceeded > 0)
        {
            recommendations.Add($"Service {service} has exceeded rate limits {metrics.RateLimitExceeded} times - consider implementing exponential backoff");
        }
        
        if (prediction.TimeToRateLimit.HasValue && prediction.TimeToRateLimit.Value < TimeSpan.FromMinutes(10))
        {
            recommendations.Add($"Service {service} may hit rate limit soon - consider reducing request frequency");
        }
        
        var errorRate = metrics.TotalRequests > 0 ? (double)metrics.FailedRequests / metrics.TotalRequests : 0;
        if (errorRate > 0.05) // 5% error rate
        {
            recommendations.Add($"Service {service} has high error rate ({errorRate:P1}) - investigate API issues");
        }
        
        return recommendations;
    }

    public override void Dispose()
    {
        if (!_disposed)
        {
            _metricsTimer?.Dispose();
            _analysisTimer?.Dispose();
            _disposed = true;
        }
        
        base.Dispose();
    }
}

/// <summary>
/// Legacy API rate limit configuration for monitoring (simplified version)
/// </summary>
public class LegacyApiRateLimitConfig
{
    public int? RequestsPerMinute { get; set; }
    public int? RequestsPerSecond { get; set; }
}

/// <summary>
/// API request record for tracking
/// </summary>
public class ApiRequestRecord
{
    public DateTime Timestamp { get; set; }
    public string Service { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
    public int StatusCode { get; set; }
    public TimeSpan Duration { get; set; }
    public int? RateLimitRemaining { get; set; }
    public DateTime? RateLimitReset { get; set; }
}

/// <summary>
/// Rate limit information for a service
/// </summary>
public class RateLimitInfo
{
    public string Service { get; set; } = string.Empty;
    public int? Remaining { get; set; }
    public DateTime? ResetTime { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// API service metrics
/// </summary>
public class ApiServiceMetrics
{
    public string ServiceName { get; set; } = string.Empty;
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public long RateLimitExceeded { get; set; }
    public double TotalLatencyMs { get; set; }
    public double MinLatencyMs { get; set; }
    public double MaxLatencyMs { get; set; }
    public DateTime LastRequestTime { get; set; }
    public DateTime? LastRateLimitExceeded { get; set; }
    public LegacyApiRateLimitConfig? LegacyConfiguration { get; set; }

    public double AverageLatencyMs => TotalRequests > 0 ? TotalLatencyMs / TotalRequests : 0;
    public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests : 0;
    public double ErrorRate => TotalRequests > 0 ? (double)FailedRequests / TotalRequests : 0;

    public ApiServiceMetrics Clone()
    {
        return new ApiServiceMetrics
        {
            ServiceName = ServiceName,
            TotalRequests = TotalRequests,
            SuccessfulRequests = SuccessfulRequests,
            FailedRequests = FailedRequests,
            RateLimitExceeded = RateLimitExceeded,
            TotalLatencyMs = TotalLatencyMs,
            MinLatencyMs = MinLatencyMs,
            MaxLatencyMs = MaxLatencyMs,
            LastRequestTime = LastRequestTime,
            LastRateLimitExceeded = LastRateLimitExceeded,
            LegacyConfiguration = LegacyConfiguration
        };
    }
}

/// <summary>
/// Overall API rate limit status
/// </summary>
public class ApiRateLimitStatus
{
    public DateTime Timestamp { get; set; }
    public Dictionary<string, ServiceRateLimitStatus> Services { get; set; } = new();
}

/// <summary>
/// Rate limit status for a specific service
/// </summary>
public class ServiceRateLimitStatus
{
    public string ServiceName { get; set; } = string.Empty;
    public int CurrentRequestsPerMinute { get; set; }
    public int CurrentRequestsPerSecond { get; set; }
    public int? MaxRequestsPerMinute { get; set; }
    public int? MaxRequestsPerSecond { get; set; }
    public int? RateLimitRemaining { get; set; }
    public DateTime? RateLimitResetTime { get; set; }
    public double UtilizationPercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime LastRequestTime { get; set; }
    public long TotalRateLimitExceeded { get; set; }
}

/// <summary>
/// API performance metrics
/// </summary>
public class ApiPerformanceMetrics
{
    public DateTime Timestamp { get; set; }
    public Dictionary<string, ApiServiceMetrics> ServiceMetrics { get; set; } = new();
}

/// <summary>
/// Rate limit analysis result
/// </summary>
public class RateLimitAnalysis
{
    public DateTime Timestamp { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, RateLimitPrediction> Predictions { get; set; } = new();
}

/// <summary>
/// Rate limit prediction for a service
/// </summary>
public class RateLimitPrediction
{
    public string Service { get; set; } = string.Empty;
    public double CurrentTrend { get; set; }
    public TimeSpan? TimeToRateLimit { get; set; }
    public double Confidence { get; set; }
}
