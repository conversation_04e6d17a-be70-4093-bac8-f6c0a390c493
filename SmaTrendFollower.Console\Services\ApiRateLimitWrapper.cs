using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Net;

namespace SmaTrendFollower.Services;

/// <summary>
/// HTTP client wrapper that automatically tracks API rate limits and performance
/// Integrates with existing HTTP clients to provide transparent monitoring
/// </summary>
public interface IApiRateLimitWrapper : IDisposable
{
    /// <summary>
    /// Wraps an HTTP client with rate limit monitoring
    /// </summary>
    HttpClient WrapHttpClient(HttpClient httpClient, string serviceName);
    
    /// <summary>
    /// Executes an HTTP request with automatic monitoring
    /// </summary>
    Task<HttpResponseMessage> ExecuteRequestAsync(HttpClient httpClient, string serviceName, 
        Func<Task<HttpResponseMessage>> requestFunc, string endpoint = "", string method = "");
    
    /// <summary>
    /// Executes a generic API call with monitoring
    /// </summary>
    Task<T> ExecuteApiCallAsync<T>(string serviceName, string operation, Func<Task<T>> apiCall);
}

/// <summary>
/// HTTP client wrapper implementation with automatic rate limit monitoring
/// </summary>
public sealed class ApiRateLimitWrapper : IApiRateLimitWrapper
{
    private readonly ILogger<ApiRateLimitWrapper> _logger;
    private readonly IApiRateLimitMonitor? _rateLimitMonitor;
    private readonly Dictionary<string, MonitoredHttpClient> _wrappedClients;
    private bool _disposed;

    public ApiRateLimitWrapper(
        ILogger<ApiRateLimitWrapper> logger,
        IApiRateLimitMonitor? rateLimitMonitor = null)
    {
        _logger = logger;
        _rateLimitMonitor = rateLimitMonitor;
        _wrappedClients = new Dictionary<string, MonitoredHttpClient>();
    }

    public HttpClient WrapHttpClient(HttpClient httpClient, string serviceName)
    {
        var wrappedClient = new MonitoredHttpClient(httpClient, serviceName, _rateLimitMonitor, _logger);
        _wrappedClients[serviceName] = wrappedClient;
        return wrappedClient;
    }

    public async Task<HttpResponseMessage> ExecuteRequestAsync(HttpClient httpClient, string serviceName, 
        Func<Task<HttpResponseMessage>> requestFunc, string endpoint = "", string method = "")
    {
        var stopwatch = Stopwatch.StartNew();
        HttpResponseMessage? response = null;
        
        try
        {
            response = await requestFunc();
            stopwatch.Stop();
            
            // Extract rate limit information from headers
            var rateLimitRemaining = ExtractRateLimitRemaining(response);
            var rateLimitReset = ExtractRateLimitReset(response);
            
            // Record the request
            _rateLimitMonitor?.RecordApiRequest(
                serviceName, 
                endpoint, 
                method, 
                (int)response.StatusCode, 
                stopwatch.Elapsed,
                rateLimitRemaining,
                rateLimitReset);
            
            // Check for rate limit exceeded
            if (response.StatusCode == HttpStatusCode.TooManyRequests)
            {
                var retryAfter = ExtractRetryAfter(response);
                _rateLimitMonitor?.RecordRateLimitExceeded(serviceName, endpoint, retryAfter);
            }
            
            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // Record failed request
            _rateLimitMonitor?.RecordApiRequest(
                serviceName, 
                endpoint, 
                method, 
                0, // Unknown status code
                stopwatch.Elapsed);
            
            _logger.LogError(ex, "API request failed for {Service} {Endpoint}", serviceName, endpoint);
            throw;
        }
    }

    public async Task<T> ExecuteApiCallAsync<T>(string serviceName, string operation, Func<Task<T>> apiCall)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var result = await apiCall();
            stopwatch.Stop();
            
            // Record successful API call
            _rateLimitMonitor?.RecordApiRequest(
                serviceName, 
                operation, 
                "API", 
                200, 
                stopwatch.Elapsed);
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // Check if it's a rate limit exception
            if (IsRateLimitException(ex))
            {
                _rateLimitMonitor?.RecordRateLimitExceeded(serviceName, operation);
            }
            
            // Record failed API call
            _rateLimitMonitor?.RecordApiRequest(
                serviceName, 
                operation, 
                "API", 
                500, // Assume server error for exceptions
                stopwatch.Elapsed);
            
            throw;
        }
    }

    private int? ExtractRateLimitRemaining(HttpResponseMessage response)
    {
        // Try common rate limit headers
        var headers = new[]
        {
            "X-RateLimit-Remaining",
            "X-Rate-Limit-Remaining", 
            "RateLimit-Remaining",
            "X-RateLimit-Limit-Remaining"
        };
        
        foreach (var header in headers)
        {
            if (response.Headers.TryGetValues(header, out var values))
            {
                var value = values.FirstOrDefault();
                if (int.TryParse(value, out var remaining))
                {
                    return remaining;
                }
            }
        }
        
        return null;
    }

    private DateTime? ExtractRateLimitReset(HttpResponseMessage response)
    {
        // Try common rate limit reset headers
        var headers = new[]
        {
            "X-RateLimit-Reset",
            "X-Rate-Limit-Reset",
            "RateLimit-Reset"
        };
        
        foreach (var header in headers)
        {
            if (response.Headers.TryGetValues(header, out var values))
            {
                var value = values.FirstOrDefault();
                
                // Try Unix timestamp
                if (long.TryParse(value, out var unixTimestamp))
                {
                    return DateTimeOffset.FromUnixTimeSeconds(unixTimestamp).DateTime;
                }
                
                // Try ISO 8601 format
                if (DateTime.TryParse(value, out var dateTime))
                {
                    return dateTime;
                }
            }
        }
        
        return null;
    }

    private int? ExtractRetryAfter(HttpResponseMessage response)
    {
        if (response.Headers.TryGetValues("Retry-After", out var values))
        {
            var value = values.FirstOrDefault();
            if (int.TryParse(value, out var retryAfter))
            {
                return retryAfter;
            }
        }
        
        return null;
    }

    private bool IsRateLimitException(Exception ex)
    {
        var message = ex.Message.ToLowerInvariant();
        return message.Contains("rate limit") ||
               message.Contains("too many requests") ||
               message.Contains("429") ||
               message.Contains("quota exceeded");
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            foreach (var client in _wrappedClients.Values)
            {
                client.Dispose();
            }
            _wrappedClients.Clear();
            _disposed = true;
        }
    }
}

/// <summary>
/// HTTP client that automatically monitors requests
/// </summary>
public class MonitoredHttpClient : HttpClient
{
    private readonly HttpClient _innerClient;
    private readonly string _serviceName;
    private readonly IApiRateLimitMonitor? _monitor;
    private readonly ILogger _logger;

    public MonitoredHttpClient(HttpClient innerClient, string serviceName, 
        IApiRateLimitMonitor? monitor, ILogger logger) : base()
    {
        _innerClient = innerClient;
        _serviceName = serviceName;
        _monitor = monitor;
        _logger = logger;
        
        // Copy properties from inner client
        BaseAddress = innerClient.BaseAddress;
        Timeout = innerClient.Timeout;
        
        // Copy headers
        foreach (var header in innerClient.DefaultRequestHeaders)
        {
            DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
        }
    }

    public override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        HttpResponseMessage? response = null;
        
        try
        {
            response = await _innerClient.SendAsync(request, cancellationToken);
            stopwatch.Stop();
            
            // Extract request information
            var endpoint = request.RequestUri?.PathAndQuery ?? "";
            var method = request.Method.Method;
            
            // Extract rate limit information
            var rateLimitRemaining = ExtractRateLimitRemaining(response);
            var rateLimitReset = ExtractRateLimitReset(response);
            
            // Record the request
            _monitor?.RecordApiRequest(
                _serviceName,
                endpoint,
                method,
                (int)response.StatusCode,
                stopwatch.Elapsed,
                rateLimitRemaining,
                rateLimitReset);
            
            // Check for rate limit exceeded
            if (response.StatusCode == HttpStatusCode.TooManyRequests)
            {
                var retryAfter = ExtractRetryAfter(response);
                _monitor?.RecordRateLimitExceeded(_serviceName, endpoint, retryAfter);
            }
            
            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            var endpoint = request.RequestUri?.PathAndQuery ?? "";
            var method = request.Method.Method;
            
            // Record failed request
            _monitor?.RecordApiRequest(
                _serviceName,
                endpoint,
                method,
                0, // Unknown status code
                stopwatch.Elapsed);
            
            _logger.LogError(ex, "HTTP request failed for {Service} {Method} {Endpoint}", 
                _serviceName, method, endpoint);
            
            throw;
        }
    }

    private int? ExtractRateLimitRemaining(HttpResponseMessage response)
    {
        var headers = new[] { "X-RateLimit-Remaining", "X-Rate-Limit-Remaining", "RateLimit-Remaining" };
        
        foreach (var header in headers)
        {
            if (response.Headers.TryGetValues(header, out var values))
            {
                var value = values.FirstOrDefault();
                if (int.TryParse(value, out var remaining))
                {
                    return remaining;
                }
            }
        }
        
        return null;
    }

    private DateTime? ExtractRateLimitReset(HttpResponseMessage response)
    {
        var headers = new[] { "X-RateLimit-Reset", "X-Rate-Limit-Reset", "RateLimit-Reset" };
        
        foreach (var header in headers)
        {
            if (response.Headers.TryGetValues(header, out var values))
            {
                var value = values.FirstOrDefault();
                
                if (long.TryParse(value, out var unixTimestamp))
                {
                    return DateTimeOffset.FromUnixTimeSeconds(unixTimestamp).DateTime;
                }
                
                if (DateTime.TryParse(value, out var dateTime))
                {
                    return dateTime;
                }
            }
        }
        
        return null;
    }

    private int? ExtractRetryAfter(HttpResponseMessage response)
    {
        if (response.Headers.TryGetValues("Retry-After", out var values))
        {
            var value = values.FirstOrDefault();
            if (int.TryParse(value, out var retryAfter))
            {
                return retryAfter;
            }
        }
        
        return null;
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _innerClient?.Dispose();
        }
        base.Dispose(disposing);
    }
}
