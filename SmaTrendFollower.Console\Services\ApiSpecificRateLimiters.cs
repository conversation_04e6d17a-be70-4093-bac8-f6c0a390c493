using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Alpaca-specific rate limiter with 200 req/min limit
/// </summary>
public interface IAlpacaRateLimiter
{
    Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.High, 
        int capacity = 1, CancellationToken cancellationToken = default);
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.High);
}

/// <summary>
/// Polygon-specific rate limiter with 100 req/sec limit (already implemented as SharedPolygonRateLimiter)
/// </summary>
public interface IPolygonRateLimiter
{
    Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.High, 
        int capacity = 1, CancellationToken cancellationToken = default);
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.High);
}

/// <summary>
/// OpenAI-specific rate limiter with configurable req/min limit
/// </summary>
public interface IOpenAIRateLimiter
{
    Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.Medium, 
        int capacity = 1, CancellationToken cancellationToken = default);
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.Medium);
}

/// <summary>
/// Gemini-specific rate limiter with estimated 60 req/min limit
/// </summary>
public interface IGeminiRateLimiter
{
    Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.Medium, 
        int capacity = 1, CancellationToken cancellationToken = default);
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.Medium);
}

/// <summary>
/// Discord-specific rate limiter with 50 req/sec limit
/// </summary>
public interface IDiscordRateLimiter
{
    Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.Low, 
        int capacity = 1, CancellationToken cancellationToken = default);
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.Low);
}

/// <summary>
/// Finnhub-specific rate limiter with 30 req/sec + 60 req/min limits
/// </summary>
public interface IFinnhubRateLimiter
{
    Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.Medium, 
        int capacity = 1, CancellationToken cancellationToken = default);
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.Medium);
}

/// <summary>
/// Base implementation for API-specific rate limiters
/// </summary>
public abstract class ApiSpecificRateLimiter
{
    protected readonly IUnifiedRateLimiter _unifiedLimiter;
    protected readonly ApiProvider _provider;
    protected readonly ILogger _logger;

    protected ApiSpecificRateLimiter(IUnifiedRateLimiter unifiedLimiter, ApiProvider provider, ILogger logger)
    {
        _unifiedLimiter = unifiedLimiter ?? throw new ArgumentNullException(nameof(unifiedLimiter));
        _provider = provider;
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected async Task<bool> TryAcquireInternalAsync(string serviceName, ApiServicePriority priority, 
        int capacity, CancellationToken cancellationToken)
    {
        var request = new ApiRateRequest(_provider, serviceName, priority, capacity, TimeSpan.FromSeconds(10));
        return await _unifiedLimiter.TryAcquireAsync(request, cancellationToken);
    }

    protected async Task<T> ExecuteInternalAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority)
    {
        if (!await TryAcquireInternalAsync(operationName, priority, 1, CancellationToken.None))
        {
            throw new InvalidOperationException($"Failed to acquire rate limit for {_provider} API call: {operationName}");
        }

        try
        {
            return await apiCall();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing {Provider} API call: {Operation}", _provider, operationName);
            throw;
        }
    }
}

/// <summary>
/// Alpaca rate limiter implementation
/// </summary>
public sealed class AlpacaRateLimiter : ApiSpecificRateLimiter, IAlpacaRateLimiter
{
    public AlpacaRateLimiter(IUnifiedRateLimiter unifiedLimiter, ILogger<AlpacaRateLimiter> logger)
        : base(unifiedLimiter, ApiProvider.Alpaca, logger)
    {
    }

    public async Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.High, 
        int capacity = 1, CancellationToken cancellationToken = default)
    {
        return await TryAcquireInternalAsync(serviceName, priority, capacity, cancellationToken);
    }

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.High)
    {
        return await ExecuteInternalAsync(apiCall, operationName, priority);
    }
}

/// <summary>
/// Polygon rate limiter implementation (delegates to existing SharedPolygonRateLimiter)
/// </summary>
public sealed class PolygonRateLimiter : ApiSpecificRateLimiter, IPolygonRateLimiter
{
    public PolygonRateLimiter(IUnifiedRateLimiter unifiedLimiter, ILogger<PolygonRateLimiter> logger)
        : base(unifiedLimiter, ApiProvider.Polygon, logger)
    {
    }

    public async Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.High, 
        int capacity = 1, CancellationToken cancellationToken = default)
    {
        return await TryAcquireInternalAsync(serviceName, priority, capacity, cancellationToken);
    }

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.High)
    {
        return await ExecuteInternalAsync(apiCall, operationName, priority);
    }
}

/// <summary>
/// OpenAI rate limiter implementation
/// </summary>
public sealed class OpenAIRateLimiter : ApiSpecificRateLimiter, IOpenAIRateLimiter
{
    public OpenAIRateLimiter(IUnifiedRateLimiter unifiedLimiter, ILogger<OpenAIRateLimiter> logger)
        : base(unifiedLimiter, ApiProvider.OpenAI, logger)
    {
    }

    public async Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.Medium, 
        int capacity = 1, CancellationToken cancellationToken = default)
    {
        return await TryAcquireInternalAsync(serviceName, priority, capacity, cancellationToken);
    }

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.Medium)
    {
        return await ExecuteInternalAsync(apiCall, operationName, priority);
    }
}

/// <summary>
/// Gemini rate limiter implementation
/// </summary>
public sealed class GeminiRateLimiter : ApiSpecificRateLimiter, IGeminiRateLimiter
{
    public GeminiRateLimiter(IUnifiedRateLimiter unifiedLimiter, ILogger<GeminiRateLimiter> logger)
        : base(unifiedLimiter, ApiProvider.Gemini, logger)
    {
    }

    public async Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.Medium, 
        int capacity = 1, CancellationToken cancellationToken = default)
    {
        return await TryAcquireInternalAsync(serviceName, priority, capacity, cancellationToken);
    }

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.Medium)
    {
        return await ExecuteInternalAsync(apiCall, operationName, priority);
    }
}

/// <summary>
/// Discord rate limiter implementation
/// </summary>
public sealed class DiscordRateLimiter : ApiSpecificRateLimiter, IDiscordRateLimiter
{
    public DiscordRateLimiter(IUnifiedRateLimiter unifiedLimiter, ILogger<DiscordRateLimiter> logger)
        : base(unifiedLimiter, ApiProvider.Discord, logger)
    {
    }

    public async Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.Low, 
        int capacity = 1, CancellationToken cancellationToken = default)
    {
        return await TryAcquireInternalAsync(serviceName, priority, capacity, cancellationToken);
    }

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.Low)
    {
        return await ExecuteInternalAsync(apiCall, operationName, priority);
    }
}

/// <summary>
/// Finnhub rate limiter implementation
/// </summary>
public sealed class FinnhubRateLimiter : ApiSpecificRateLimiter, IFinnhubRateLimiter
{
    public FinnhubRateLimiter(IUnifiedRateLimiter unifiedLimiter, ILogger<FinnhubRateLimiter> logger)
        : base(unifiedLimiter, ApiProvider.Finnhub, logger)
    {
    }

    public async Task<bool> TryAcquireAsync(string serviceName, ApiServicePriority priority = ApiServicePriority.Medium, 
        int capacity = 1, CancellationToken cancellationToken = default)
    {
        return await TryAcquireInternalAsync(serviceName, priority, capacity, cancellationToken);
    }

    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName, 
        ApiServicePriority priority = ApiServicePriority.Medium)
    {
        return await ExecuteInternalAsync(apiCall, operationName, priority);
    }
}
