using System.Diagnostics;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for tracking API usage, performance, and costs across all external APIs
/// </summary>
public interface IApiTrackingService
{
    /// <summary>
    /// Start tracking an API call
    /// </summary>
    IApiCallTracker StartTracking(ApiCallRequest request);
    
    /// <summary>
    /// Track a completed API call
    /// </summary>
    Task TrackApiCallAsync(ApiCallRequest request, ApiCallResult result, TimeSpan duration);
    
    /// <summary>
    /// Get usage statistics for a provider
    /// </summary>
    Task<ApiUsageStats> GetUsageStatsAsync(string provider, DateTime startDate, DateTime endDate);
    
    /// <summary>
    /// Get performance metrics for all providers
    /// </summary>
    Task<List<SmaTrendFollower.Models.ApiPerformanceMetrics>> GetPerformanceMetricsAsync();
    
    /// <summary>
    /// Get cost analysis for a time period
    /// </summary>
    Task<Dictionary<string, decimal>> GetCostAnalysisAsync(DateTime startDate, DateTime endDate);
    
    /// <summary>
    /// Clean up old API logs (retention policy)
    /// </summary>
    Task CleanupOldLogsAsync(TimeSpan retentionPeriod);
}

/// <summary>
/// Interface for tracking individual API calls
/// </summary>
public interface IApiCallTracker : IDisposable
{
    /// <summary>
    /// Complete the API call tracking with result
    /// </summary>
    Task CompleteAsync(ApiCallResult result);
}

/// <summary>
/// Implementation of API tracking service
/// </summary>
public class ApiTrackingService : IApiTrackingService
{
    private readonly IDbContextFactory<MLFeaturesDbContext> _contextFactory;
    private readonly ILogger<ApiTrackingService> _logger;

    public ApiTrackingService(
        IDbContextFactory<MLFeaturesDbContext> contextFactory,
        ILogger<ApiTrackingService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public IApiCallTracker StartTracking(ApiCallRequest request)
    {
        return new ApiCallTracker(this, request);
    }

    public async Task TrackApiCallAsync(ApiCallRequest request, ApiCallResult result, TimeSpan duration)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var now = DateTime.UtcNow;
            var log = new ApiCallLog
            {
                Timestamp = now,
                CreatedAt = now, // Explicitly set CreatedAt to fix INSERT error
                Provider = request.Provider,
                Operation = request.Operation,
                Symbol = request.Symbol,
                RequestData = TruncateString(request.RequestData, 2000),
                ResponseData = TruncateString(result.ResponseData, 2000),
                Success = result.Success,
                StatusCode = result.StatusCode,
                ErrorMessage = TruncateString(result.ErrorMessage, 500),
                DurationMs = (int)duration.TotalMilliseconds,
                TokensUsed = result.TokensUsed,
                Cost = result.Cost,
                Metadata = SerializeMetadata(request.Metadata, result.Metadata)
            };

            context.ApiCallLogs.Add(log);
            await context.SaveChangesAsync();

            // Update Prometheus metrics
            UpdatePrometheusMetrics(request, result, duration);

            _logger.LogDebug("Tracked API call: {Provider} {Operation} - Success: {Success}, Duration: {Duration}ms",
                request.Provider, request.Operation, result.Success, duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to track API call for {Provider} {Operation}", 
                request.Provider, request.Operation);
        }
    }

    public async Task<ApiUsageStats> GetUsageStatsAsync(string provider, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var logs = await context.ApiCallLogs
                .Where(l => l.Provider == provider && l.Timestamp >= startDate && l.Timestamp <= endDate)
                .ToListAsync();

            var totalCalls = logs.Count;
            var successfulCalls = logs.Count(l => l.Success);
            var failedCalls = totalCalls - successfulCalls;
            var successRate = totalCalls > 0 ? (double)successfulCalls / totalCalls : 0.0;
            var avgResponseTime = logs.Any() ? logs.Average(l => l.DurationMs) : 0.0;
            var totalTokens = logs.Where(l => l.TokensUsed.HasValue).Sum(l => l.TokensUsed!.Value);
            var totalCost = logs.Where(l => l.Cost.HasValue).Sum(l => l.Cost!.Value);

            var operationBreakdown = logs
                .GroupBy(l => l.Operation)
                .ToDictionary(g => g.Key, g => g.Count());

            var statusCodeBreakdown = logs
                .Where(l => l.StatusCode.HasValue)
                .GroupBy(l => l.StatusCode!.Value)
                .ToDictionary(g => g.Key, g => g.Count());

            return new ApiUsageStats(
                provider,
                startDate,
                endDate,
                totalCalls,
                successfulCalls,
                failedCalls,
                successRate,
                avgResponseTime,
                totalTokens > 0 ? totalTokens : null,
                totalCost > 0 ? totalCost : null,
                operationBreakdown,
                statusCodeBreakdown
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get usage stats for provider {Provider}", provider);
            return new ApiUsageStats(provider, startDate, endDate, 0, 0, 0, 0.0, 0.0, null, null, 
                new Dictionary<string, int>(), new Dictionary<int, int>());
        }
    }

    public async Task<List<SmaTrendFollower.Models.ApiPerformanceMetrics>> GetPerformanceMetricsAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var now = DateTime.UtcNow;
            var oneHourAgo = now.AddHours(-1);
            var oneDayAgo = now.AddDays(-1);

            var providers = await context.ApiCallLogs
                .Where(l => l.Timestamp >= oneDayAgo)
                .Select(l => l.Provider)
                .Distinct()
                .ToListAsync();

            var metrics = new List<SmaTrendFollower.Models.ApiPerformanceMetrics>();

            foreach (var provider in providers)
            {
                var hourlyLogs = await context.ApiCallLogs
                    .Where(l => l.Provider == provider && l.Timestamp >= oneHourAgo)
                    .ToListAsync();

                var dailyLogs = await context.ApiCallLogs
                    .Where(l => l.Provider == provider && l.Timestamp >= oneDayAgo)
                    .ToListAsync();

                var hourlySuccessRate = hourlyLogs.Any() ? 
                    (double)hourlyLogs.Count(l => l.Success) / hourlyLogs.Count : 1.0;
                
                var dailySuccessRate = dailyLogs.Any() ? 
                    (double)dailyLogs.Count(l => l.Success) / dailyLogs.Count : 1.0;

                var avgResponseTime = dailyLogs.Any() ? dailyLogs.Average(l => l.DurationMs) : 0.0;
                var dailyCost = dailyLogs.Where(l => l.Cost.HasValue).Sum(l => l.Cost!.Value);

                var recentErrors = dailyLogs
                    .Where(l => !l.Success && !string.IsNullOrEmpty(l.ErrorMessage))
                    .OrderByDescending(l => l.Timestamp)
                    .Take(5)
                    .Select(l => l.ErrorMessage!)
                    .ToList();

                metrics.Add(new SmaTrendFollower.Models.ApiPerformanceMetrics(
                    Provider: provider,
                    CalculatedAt: now,
                    SuccessRateLastHour: hourlySuccessRate,
                    SuccessRateLastDay: dailySuccessRate,
                    AverageResponseTimeMs: avgResponseTime,
                    CallsLastHour: hourlyLogs.Count,
                    CallsLastDay: dailyLogs.Count,
                    CostLastDay: dailyCost > 0 ? dailyCost : (decimal?)null,
                    RecentErrors: recentErrors
                ));
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get performance metrics");
            return new List<SmaTrendFollower.Models.ApiPerformanceMetrics>();
        }
    }

    public async Task<Dictionary<string, decimal>> GetCostAnalysisAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var costs = await context.ApiCallLogs
                .Where(l => l.Timestamp >= startDate && l.Timestamp <= endDate && l.Cost.HasValue)
                .GroupBy(l => l.Provider)
                .Select(g => new { Provider = g.Key, TotalCost = g.Sum(l => l.Cost!.Value) })
                .ToDictionaryAsync(x => x.Provider, x => x.TotalCost);

            return costs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get cost analysis");
            return new Dictionary<string, decimal>();
        }
    }

    public async Task CleanupOldLogsAsync(TimeSpan retentionPeriod)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var cutoffDate = DateTime.UtcNow - retentionPeriod;
            var oldLogs = context.ApiCallLogs.Where(l => l.Timestamp < cutoffDate);
            
            var count = await oldLogs.CountAsync();
            if (count > 0)
            {
                context.ApiCallLogs.RemoveRange(oldLogs);
                await context.SaveChangesAsync();
                
                _logger.LogInformation("Cleaned up {Count} old API call logs older than {CutoffDate}", 
                    count, cutoffDate);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup old API logs");
        }
    }

    private static string? TruncateString(string? input, int maxLength)
    {
        if (string.IsNullOrEmpty(input)) return input;
        return input.Length <= maxLength ? input : input[..maxLength];
    }

    private static string? SerializeMetadata(Dictionary<string, object>? requestMetadata, 
        Dictionary<string, object>? resultMetadata)
    {
        try
        {
            var combined = new Dictionary<string, object>();
            
            if (requestMetadata != null)
            {
                foreach (var kvp in requestMetadata)
                    combined[$"req_{kvp.Key}"] = kvp.Value;
            }
            
            if (resultMetadata != null)
            {
                foreach (var kvp in resultMetadata)
                    combined[$"res_{kvp.Key}"] = kvp.Value;
            }

            return combined.Any() ? JsonSerializer.Serialize(combined) : null;
        }
        catch
        {
            return null;
        }
    }

    private void UpdatePrometheusMetrics(ApiCallRequest request, ApiCallResult result, TimeSpan duration)
    {
        try
        {
            // Update API request metrics
            MetricsRegistry.ApiRequestDurationMs
                .WithLabels(request.Provider, request.Operation, "unknown")
                .Observe(duration.TotalMilliseconds);

            MetricsRegistry.ApiRequestsTotal
                .WithLabels(request.Provider, request.Operation, "unknown", 
                    result.StatusCode?.ToString() ?? "unknown")
                .Inc();

            // Update success/failure counters
            if (!result.Success)
            {
                MetricsRegistry.ApiRequestsTotal
                    .WithLabels(request.Provider, request.Operation, "unknown", "error")
                    .Inc();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update Prometheus metrics for API call");
        }
    }
}

/// <summary>
/// Individual API call tracker implementation
/// </summary>
internal class ApiCallTracker : IApiCallTracker
{
    private readonly ApiTrackingService _service;
    private readonly ApiCallRequest _request;
    private readonly Stopwatch _stopwatch;
    private bool _completed;

    public ApiCallTracker(ApiTrackingService service, ApiCallRequest request)
    {
        _service = service;
        _request = request;
        _stopwatch = Stopwatch.StartNew();
    }

    public async Task CompleteAsync(ApiCallResult result)
    {
        if (_completed) return;
        
        _stopwatch.Stop();
        _completed = true;
        
        await _service.TrackApiCallAsync(_request, result, _stopwatch.Elapsed);
    }

    public void Dispose()
    {
        if (!_completed)
        {
            // Auto-complete with failure if not explicitly completed
            _ = Task.Run(async () =>
            {
                await CompleteAsync(new ApiCallResult(
                    Success: false,
                    ErrorMessage: "API call tracker disposed without completion"
                ));
            });
        }
    }
}
