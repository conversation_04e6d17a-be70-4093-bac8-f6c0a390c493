using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Generic async polling service that wraps a polling function as a BackgroundService.
/// Used to convert async polling operations into hosted services for DI registration.
/// </summary>
internal sealed class AsyncPollingService : BackgroundService
{
    private readonly Func<CancellationToken, Task> _pollingFunction;
    private readonly ILogger<AsyncPollingService> _logger;

    /// <summary>
    /// Initializes a new instance of AsyncPollingService with the specified polling function.
    /// </summary>
    /// <param name="pollingFunction">The async function to execute continuously</param>
    /// <param name="logger">Logger for service lifecycle events</param>
    public AsyncPollingService(
        Func<CancellationToken, Task> pollingFunction,
        ILogger<AsyncPollingService> logger)
    {
        _pollingFunction = pollingFunction ?? throw new ArgumentNullException(nameof(pollingFunction));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executes the polling function continuously until cancellation is requested.
    /// Propagates OperationCanceledException and logs other exceptions.
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("AsyncPollingService started");
            await _pollingFunction(stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("AsyncPollingService cancelled");
            throw; // Propagate cancellation as expected by BackgroundService
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AsyncPollingService encountered an error");
            throw; // Re-throw to ensure service failure is visible
        }
        finally
        {
            _logger.LogInformation("AsyncPollingService stopped");
        }
    }
}
