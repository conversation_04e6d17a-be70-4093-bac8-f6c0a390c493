using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Json;
using System.Text.Json.Nodes;
using System.Threading.Channels;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that polls Brave Search News API for headlines and feeds them into the FinBERT sentiment pipeline.
/// Implements deduplication, rate limiting, and parallel symbol querying to complement Alpaca news feeds.
/// </summary>
internal sealed class BraveNewsPoller : BackgroundService
{
    private readonly BraveOptions _opt;
    private readonly IHttpClientFactory _http;
    private readonly ChannelWriter<HeadlineItem> _writer;
    private readonly ILogger<BraveNewsPoller> _log;
    private readonly IApiTrackingService _apiTracking;
    private readonly HashSet<string> _seen = new();     // Dedupe headlines by ID

    /// <summary>
    /// Initializes the Brave news poller with configuration and dependencies.
    /// </summary>
    /// <param name="opt">Brave Search API configuration options</param>
    /// <param name="http">HTTP client factory for API calls</param>
    /// <param name="chan">Channel for writing headlines to FinBERT worker</param>
    /// <param name="apiTracking">API tracking service for monitoring API usage</param>
    /// <param name="log">Logger for service operations</param>
    public BraveNewsPoller(IOptions<BraveOptions> opt,
                           IHttpClientFactory http,
                           Channel<HeadlineItem> chan,
                           IApiTrackingService apiTracking,
                           ILogger<BraveNewsPoller> log)
    {
        _opt = opt.Value ?? throw new ArgumentNullException(nameof(opt));
        _http = http ?? throw new ArgumentNullException(nameof(http));
        _writer = chan.Writer ?? throw new ArgumentNullException(nameof(chan));
        _apiTracking = apiTracking ?? throw new ArgumentNullException(nameof(apiTracking));
        _log = log ?? throw new ArgumentNullException(nameof(log));
    }

    /// <summary>
    /// Continuously polls Brave Search API for news headlines across configured symbols.
    /// Only runs during market hours (9:30 AM - 4:00 PM ET, Monday-Friday) to conserve API quotas.
    /// Respects rate limits and implements configurable polling intervals.
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken ct)
    {
        if (_opt.Symbols.Length == 0)
        {
            _log.LogWarning("Brave symbols array is empty; poller will remain idle.");
            return;
        }

        var delay = TimeSpan.FromMinutes(_opt.LookbackMins);
        _log.LogInformation("BraveNewsPoller started with {SymbolCount} symbols, {DelayMins}min intervals (market hours only)",
            _opt.Symbols.Length, _opt.LookbackMins);

        while (!ct.IsCancellationRequested)
        {
            try
            {
                // Check if we're in market hours before polling
                if (!IsMarketHours())
                {
                    _log.LogDebug("Outside market hours - skipping Brave news polling cycle");
                    await Task.Delay(TimeSpan.FromMinutes(5), ct); // Check again in 5 minutes
                    continue;
                }

                foreach (var sym in _opt.Symbols)
                {
                    await QuerySymbolAsync(sym, ct);

                    // Rate limiting: 1 req/sec for free tier
                    await Task.Delay(1000, ct);
                }
            }
            catch (OperationCanceledException) when (ct.IsCancellationRequested)
            {
                _log.LogInformation("BraveNewsPoller cancellation requested");
                throw;
            }
            catch (Exception ex)
            {
                _log.LogError(ex, "Brave news polling cycle failed");
            }

            await Task.Delay(delay, ct);
        }
    }

    /// <summary>
    /// Queries Brave Search API for news headlines related to a specific symbol.
    /// Parses JSON response and writes new headlines to the sentiment processing channel.
    /// </summary>
    /// <param name="sym">Stock symbol to query for news</param>
    /// <param name="ct">Cancellation token</param>
    private async Task QuerySymbolAsync(string sym, CancellationToken ct)
    {
        // Start API tracking
        var apiRequest = new ApiCallRequest(
            Provider: "BraveSearch",
            Operation: "news_search",
            Symbol: sym,
            RequestData: $"q={sym}&count={_opt.PageSize}"
        );

        using var tracker = _apiTracking.StartTracking(apiRequest);

        try
        {
            var url = $"{_opt.BaseUrl}?q={Uri.EscapeDataString(sym)}&count={_opt.PageSize}";
            var cli = _http.CreateClient("BraveNews");

            var response = await cli.GetAsync(url, ct);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadFromJsonAsync<JsonObject>(cancellationToken: ct);
            if (json?["results"] is not JsonArray results)
            {
                _log.LogDebug("No results found for symbol {Symbol}", sym);

                // Track successful API call with no results
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: true,
                    StatusCode: (int)response.StatusCode,
                    ResponseData: "No results found",
                    Metadata: new Dictionary<string, object>
                    {
                        ["symbol"] = sym,
                        ["results_count"] = 0
                    }
                ));
                return;
            }

            var newHeadlines = 0;
            var totalResults = results.Count;

            foreach (var r in results.OfType<JsonObject>())
            {
                var id = (string?)r["id"] ?? Guid.NewGuid().ToString("N");
                if (!_seen.Add(id)) continue; // Skip duplicates

                var headline = (string?)r["title"] ?? "";
                if (string.IsNullOrWhiteSpace(headline)) continue;

                var tsStr = (string?)r["publishedAt"] ?? "";
                if (!DateTime.TryParse(tsStr, out var tsUtc))
                    tsUtc = DateTime.UtcNow;

                var item = new HeadlineItem(id, sym.ToUpperInvariant(), headline, tsUtc);
                await _writer.WriteAsync(item, ct);
                newHeadlines++;
            }

            // Track successful API call
            await tracker.CompleteAsync(new ApiCallResult(
                Success: true,
                StatusCode: (int)response.StatusCode,
                ResponseData: $"Found {totalResults} results, {newHeadlines} new headlines",
                Metadata: new Dictionary<string, object>
                {
                    ["symbol"] = sym,
                    ["total_results"] = totalResults,
                    ["new_headlines"] = newHeadlines,
                    ["duplicates_filtered"] = totalResults - newHeadlines
                }
            ));

            if (newHeadlines > 0)
            {
                _log.LogDebug("Brave queued {Count} new headlines for {Symbol}", newHeadlines, sym);
            }
        }
        catch (HttpRequestException ex)
        {
            _log.LogWarning(ex, "Brave API HTTP error for symbol {Symbol}", sym);

            // Track failed API call
            await tracker.CompleteAsync(new ApiCallResult(
                Success: false,
                ErrorMessage: ex.Message,
                Metadata: new Dictionary<string, object> { ["symbol"] = sym }
            ));
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _log.LogWarning("Brave API timeout for symbol {Symbol}", sym);

            // Track timeout
            await tracker.CompleteAsync(new ApiCallResult(
                Success: false,
                ErrorMessage: "Request timeout",
                Metadata: new Dictionary<string, object> { ["symbol"] = sym }
            ));
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "Unexpected error querying Brave API for symbol {Symbol}", sym);

            // Track general error
            await tracker.CompleteAsync(new ApiCallResult(
                Success: false,
                ErrorMessage: ex.Message,
                Metadata: new Dictionary<string, object> { ["symbol"] = sym }
            ));
        }
    }

    /// <summary>
    /// Determines if the current time is during market hours (9:30 AM - 4:00 PM ET, Monday-Friday).
    /// </summary>
    private bool IsMarketHours()
    {
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow,
            TimeZoneInfo.FindSystemTimeZoneById("US Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // Check if it's during market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);

        return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
    }
}
