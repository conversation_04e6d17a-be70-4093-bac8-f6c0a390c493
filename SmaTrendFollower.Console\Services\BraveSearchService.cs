using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for interacting with Brave Search API
/// Provides both web search and AI-powered search capabilities
/// </summary>
public interface IBraveSearchService
{
    /// <summary>
    /// Performs a web search using Brave Search API
    /// </summary>
    Task<BraveSearchResponse?> SearchWebAsync(string query, int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs an AI-powered search using Brave AI API
    /// </summary>
    Task<BraveAiResponse?> SearchAiAsync(string query, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches for VIX data and extracts numeric value
    /// </summary>
    Task<decimal?> SearchVixValueAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches for SEC filings for a specific symbol
    /// </summary>
    Task<List<BraveWebResult>> SearchSecFilingsAsync(string symbol, CancellationToken cancellationToken = default);
}

public class BraveSearchService : IBraveSearchService
{
    private readonly ILogger<BraveSearchService> _logger;
    private readonly HttpClient _httpClient;
    private readonly BraveSearchConfig _config;
    private readonly SemaphoreSlim _rateLimiter;

    public BraveSearchService(
        ILogger<BraveSearchService> logger,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration)
    {
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient("brave-search");
        
        _config = new BraveSearchConfig
        {
            SearchApiKey = configuration["Brave:SearchApiKey"] ?? 
                          configuration.GetValue<string>("BRAVE_SEARCH_API_KEY") ?? "",
            AiApiKey = configuration["Brave:AiApiKey"] ?? 
                      configuration.GetValue<string>("BRAVE_AI_API_KEY") ?? "",
            RateLimitPerSecond = configuration.GetValue("Brave:RateLimitPerSecond", 1),
            MaxRequestsPerMonth = configuration.GetValue("Brave:MaxRequestsPerMonth", 2000),
            TimeoutSeconds = configuration.GetValue("Brave:TimeoutSeconds", 30)
        };

        _rateLimiter = new SemaphoreSlim(_config.RateLimitPerSecond, _config.RateLimitPerSecond);
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
    }

    public async Task<BraveSearchResponse?> SearchWebAsync(string query, int count = 10, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(_config.SearchApiKey))
        {
            _logger.LogWarning("Brave Search API key not configured");
            return null;
        }

        try
        {
            await _rateLimiter.WaitAsync(cancellationToken);
            
            try
            {
                var url = $"https://api.search.brave.com/res/v1/web/search?q={Uri.EscapeDataString(query)}&count={count}";
                
                var request = new HttpRequestMessage(HttpMethod.Get, url);
                request.Headers.Add("X-Subscription-Token", _config.SearchApiKey);
                request.Headers.Add("Accept", "application/json");

                _logger.LogDebug("Brave Search request: {Query} (count: {Count})", query, count);

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var searchResult = JsonSerializer.Deserialize<BraveSearchResponse>(content);

                _logger.LogInformation("Brave Search completed: {Query} -> {ResultCount} results", 
                    query, searchResult?.Web?.Results?.Length ?? 0);

                return searchResult;
            }
            finally
            {
                // Release rate limiter after 1 second to maintain 1 req/sec limit
                _ = Task.Delay(1000, cancellationToken).ContinueWith(_ => _rateLimiter.Release());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing Brave web search for query: {Query}", query);
            return null;
        }
    }

    public async Task<BraveAiResponse?> SearchAiAsync(string query, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(_config.AiApiKey))
        {
            _logger.LogWarning("Brave AI API key not configured");
            return null;
        }

        try
        {
            await _rateLimiter.WaitAsync(cancellationToken);
            
            try
            {
                var url = "https://api.search.brave.com/res/v1/ai/search";
                
                var requestBody = new { q = query };
                var json = JsonSerializer.Serialize(requestBody);
                
                var request = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(json, System.Text.Encoding.UTF8, "application/json")
                };
                request.Headers.Add("X-Subscription-Token", _config.AiApiKey);

                _logger.LogDebug("Brave AI Search request: {Query}", query);

                var response = await _httpClient.SendAsync(request, cancellationToken);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var aiResult = JsonSerializer.Deserialize<BraveAiResponse>(content);

                _logger.LogInformation("Brave AI Search completed: {Query}", query);

                return aiResult;
            }
            finally
            {
                // Release rate limiter after 1 second to maintain 1 req/sec limit
                _ = Task.Delay(1000, cancellationToken).ContinueWith(_ => _rateLimiter.Release());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing Brave AI search for query: {Query}", query);
            return null;
        }
    }

    public async Task<decimal?> SearchVixValueAsync(CancellationToken cancellationToken = default)
    {
        var queries = new[]
        {
            "VIX current value today",
            "CBOE VIX index current price",
            "volatility index VIX real time quote",
            "VIX quote live price market"
        };

        foreach (var query in queries)
        {
            try
            {
                var searchResult = await SearchWebAsync(query, 5, cancellationToken);
                
                if (searchResult?.Web?.Results != null)
                {
                    foreach (var result in searchResult.Web.Results)
                    {
                        var vixValue = ExtractVixValueFromText(result.Title + " " + result.Description);
                        if (vixValue.HasValue && vixValue.Value > 0 && vixValue.Value < 200)
                        {
                            _logger.LogInformation("VIX value extracted from Brave search: {Value:F2} (query: {Query})", 
                                vixValue.Value, query);
                            return vixValue.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Failed to extract VIX from query: {Query}", query);
            }
        }

        _logger.LogWarning("Could not extract VIX value from any Brave search results");
        return null;
    }

    public async Task<List<BraveWebResult>> SearchSecFilingsAsync(string symbol, CancellationToken cancellationToken = default)
    {
        var query = $"{symbol} SEC filing site:sec.gov";
        var searchResult = await SearchWebAsync(query, 10, cancellationToken);
        
        var filings = new List<BraveWebResult>();
        
        if (searchResult?.Web?.Results != null)
        {
            foreach (var result in searchResult.Web.Results)
            {
                if (result.Url?.Contains("sec.gov") == true && 
                    result.Title?.Contains(symbol, StringComparison.OrdinalIgnoreCase) == true)
                {
                    filings.Add(result);
                }
            }
        }

        _logger.LogInformation("Found {Count} SEC filing results for {Symbol}", filings.Count, symbol);
        return filings;
    }

    private static decimal? ExtractVixValueFromText(string text)
    {
        if (string.IsNullOrEmpty(text)) return null;

        // Look for patterns like "VIX 23.45" or "VIX: 23.45" or "23.45 VIX"
        var patterns = new[]
        {
            @"VIX[:\s]*(\d+\.?\d*)",
            @"(\d+\.?\d*)[:\s]*VIX",
            @"volatility[:\s]*(\d+\.?\d*)",
            @"(\d+\.?\d*)[:\s]*volatility"
        };

        foreach (var pattern in patterns)
        {
            var matches = System.Text.RegularExpressions.Regex.Matches(text, pattern, 
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            
            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                if (match.Groups.Count > 1 && decimal.TryParse(match.Groups[1].Value, out var value))
                {
                    if (value > 5 && value < 100) // Reasonable VIX range
                    {
                        return value;
                    }
                }
            }
        }

        return null;
    }

    public void Dispose()
    {
        _rateLimiter?.Dispose();
        _httpClient?.Dispose();
    }
}

/// <summary>
/// Configuration for Brave Search API
/// </summary>
public record BraveSearchConfig
{
    public string SearchApiKey { get; init; } = "";
    public string AiApiKey { get; init; } = "";
    public int RateLimitPerSecond { get; init; } = 1;
    public int MaxRequestsPerMonth { get; init; } = 2000;
    public int TimeoutSeconds { get; init; } = 30;
}

/// <summary>
/// Brave Search API response models
/// </summary>
public record BraveSearchResponse(
    [property: JsonPropertyName("web")] BraveWebResults? Web
);

public record BraveWebResults(
    [property: JsonPropertyName("results")] BraveWebResult[]? Results
);

public record BraveWebResult(
    [property: JsonPropertyName("title")] string? Title,
    [property: JsonPropertyName("url")] string? Url,
    [property: JsonPropertyName("description")] string? Description
);

public record BraveAiResponse(
    [property: JsonPropertyName("answer")] string? Answer,
    [property: JsonPropertyName("sources")] BraveAiSource[]? Sources
);

public record BraveAiSource(
    [property: JsonPropertyName("title")] string? Title,
    [property: JsonPropertyName("url")] string? Url
);
