using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EFCore.BulkExtensions;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for optimized bulk insert operations with performance monitoring.
/// Provides ~5x performance improvement over standard EF Core AddRange operations.
/// </summary>
public interface IBulkInsertOptimizationService
{
    /// <summary>
    /// Performs optimized bulk insert of stock bars with batching and performance monitoring
    /// </summary>
    Task<BulkInsertResult> BulkInsertStockBarsAsync(
        IDictionary<string, IDictionary<string, IEnumerable<IBar>>> symbolTimeFrameBars,
        int batchSize = 5000,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Performs optimized bulk insert of index bars with batching and performance monitoring
    /// </summary>
    Task<BulkInsertResult> BulkInsertIndexBarsAsync(
        IDictionary<string, IEnumerable<Services.IndexBar>> symbolBars,
        int batchSize = 5000,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance metrics for bulk insert operations
    /// </summary>
    BulkInsertMetrics GetPerformanceMetrics();
}

/// <summary>
/// Implementation of bulk insert optimization service using EFCore.BulkExtensions
/// </summary>
public sealed class BulkInsertOptimizationService : IBulkInsertOptimizationService
{
    private readonly IDbContextFactory<StockBarCacheDbContext> _stockDbContextFactory;
    private readonly IDbContextFactory<IndexCacheDbContext> _indexDbContextFactory;
    private readonly ILogger<BulkInsertOptimizationService> _logger;
    private readonly object _metricsLock = new();
    private BulkInsertMetrics _metrics = new();

    public BulkInsertOptimizationService(
        IDbContextFactory<StockBarCacheDbContext> stockDbContextFactory,
        IDbContextFactory<IndexCacheDbContext> indexDbContextFactory,
        ILogger<BulkInsertOptimizationService> logger)
    {
        _stockDbContextFactory = stockDbContextFactory;
        _indexDbContextFactory = indexDbContextFactory;
        _logger = logger;
    }

    public async Task<BulkInsertResult> BulkInsertStockBarsAsync(
        IDictionary<string, IDictionary<string, IEnumerable<IBar>>> symbolTimeFrameBars,
        int batchSize = 5000,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var totalBars = 0;
        var processedSymbols = 0;

        try
        {
            _logger.LogInformation("Starting optimized bulk insert for {SymbolCount} symbols with batch size {BatchSize}", 
                symbolTimeFrameBars.Count, batchSize);

            // Pre-process all bars to minimize memory allocations
            var allBars = new List<CachedStockBar>();
            foreach (var symbolEntry in symbolTimeFrameBars)
            {
                var symbol = symbolEntry.Key;
                foreach (var timeFrameEntry in symbolEntry.Value)
                {
                    var timeFrame = timeFrameEntry.Key;
                    var bars = timeFrameEntry.Value;

                    var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();
                    allBars.AddRange(cachedBars);
                    totalBars += cachedBars.Count;
                }
                processedSymbols++;
            }

            // Process in optimized batches using EFCore.BulkExtensions
            using var stockDbContext = await _stockDbContextFactory.CreateDbContextAsync(cancellationToken);
            var batches = allBars.Chunk(batchSize);
            var batchCount = 0;

            foreach (var batch in batches)
            {
                cancellationToken.ThrowIfCancellationRequested();

                using var transaction = await stockDbContext.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    // Use BulkInsert for ~5x performance improvement over AddRangeAsync
                    await stockDbContext.BulkInsertAsync(batch, cancellationToken: cancellationToken);
                    await transaction.CommitAsync(cancellationToken);

                    batchCount++;
                    if (batchCount % 10 == 0)
                    {
                        _logger.LogDebug("Processed {BatchCount} batches, {TotalBars} bars", batchCount, batchCount * batchSize);
                    }
                }
                catch
                {
                    await transaction.RollbackAsync(cancellationToken);
                    throw;
                }
            }

            stopwatch.Stop();
            UpdateMetrics(totalBars, stopwatch.Elapsed, true);

            _logger.LogInformation("Optimized bulk insert completed: {TotalBars} bars, {ProcessedSymbols} symbols in {ElapsedMs}ms " +
                                 "(~{BarsPerSecond:F0} bars/sec)", 
                totalBars, processedSymbols, stopwatch.ElapsedMilliseconds, 
                totalBars / Math.Max(0.001, stopwatch.Elapsed.TotalSeconds));

            return new BulkInsertResult(true, totalBars, processedSymbols, stopwatch.Elapsed, null);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateMetrics(totalBars, stopwatch.Elapsed, false);

            _logger.LogError(ex, "Optimized bulk insert failed after processing {ProcessedSymbols} symbols", processedSymbols);
            return new BulkInsertResult(false, totalBars, processedSymbols, stopwatch.Elapsed, ex.Message);
        }
    }

    public async Task<BulkInsertResult> BulkInsertIndexBarsAsync(
        IDictionary<string, IEnumerable<Services.IndexBar>> symbolBars,
        int batchSize = 5000,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var totalBars = 0;
        var processedSymbols = 0;

        try
        {
            _logger.LogInformation("Starting optimized bulk insert for {SymbolCount} index symbols with batch size {BatchSize}", 
                symbolBars.Count, batchSize);

            // Pre-process all bars to minimize memory allocations
            var allBars = new List<CachedIndexBar>();
            foreach (var symbolEntry in symbolBars)
            {
                var symbol = symbolEntry.Key;
                var bars = symbolEntry.Value;

                var cachedBars = bars.Select(bar => CachedIndexBar.FromIndexBar(symbol, bar)).ToList();
                allBars.AddRange(cachedBars);
                totalBars += cachedBars.Count;
                processedSymbols++;
            }

            // Process in optimized batches using EFCore.BulkExtensions
            using var indexDbContext = await _indexDbContextFactory.CreateDbContextAsync(cancellationToken);
            var batches = allBars.Chunk(batchSize);
            var batchCount = 0;

            foreach (var batch in batches)
            {
                cancellationToken.ThrowIfCancellationRequested();

                using var transaction = await indexDbContext.Database.BeginTransactionAsync(cancellationToken);
                try
                {
                    // Use BulkInsert for ~5x performance improvement over AddRangeAsync
                    await indexDbContext.BulkInsertAsync(batch, cancellationToken: cancellationToken);
                    await transaction.CommitAsync(cancellationToken);

                    batchCount++;
                    if (batchCount % 10 == 0)
                    {
                        _logger.LogDebug("Processed {BatchCount} index batches, {TotalBars} bars", batchCount, batchCount * batchSize);
                    }
                }
                catch
                {
                    await transaction.RollbackAsync(cancellationToken);
                    throw;
                }
            }

            stopwatch.Stop();
            UpdateMetrics(totalBars, stopwatch.Elapsed, true);

            _logger.LogInformation("Optimized index bulk insert completed: {TotalBars} bars, {ProcessedSymbols} symbols in {ElapsedMs}ms " +
                                 "(~{BarsPerSecond:F0} bars/sec)", 
                totalBars, processedSymbols, stopwatch.ElapsedMilliseconds, 
                totalBars / Math.Max(0.001, stopwatch.Elapsed.TotalSeconds));

            return new BulkInsertResult(true, totalBars, processedSymbols, stopwatch.Elapsed, null);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateMetrics(totalBars, stopwatch.Elapsed, false);

            _logger.LogError(ex, "Optimized index bulk insert failed after processing {ProcessedSymbols} symbols", processedSymbols);
            return new BulkInsertResult(false, totalBars, processedSymbols, stopwatch.Elapsed, ex.Message);
        }
    }

    public BulkInsertMetrics GetPerformanceMetrics()
    {
        lock (_metricsLock)
        {
            return _metrics;
        }
    }

    private void UpdateMetrics(int recordsProcessed, TimeSpan elapsed, bool success)
    {
        lock (_metricsLock)
        {
            _metrics = _metrics with
            {
                TotalOperations = _metrics.TotalOperations + 1,
                TotalRecordsProcessed = _metrics.TotalRecordsProcessed + recordsProcessed,
                TotalElapsedTime = _metrics.TotalElapsedTime + elapsed,
                SuccessfulOperations = _metrics.SuccessfulOperations + (success ? 1 : 0),
                AverageRecordsPerSecond = _metrics.TotalRecordsProcessed / Math.Max(0.001, _metrics.TotalElapsedTime.TotalSeconds)
            };
        }
    }
}

/// <summary>
/// Result of a bulk insert operation
/// </summary>
public record BulkInsertResult(
    bool Success,
    int TotalRecords,
    int ProcessedSymbols,
    TimeSpan ElapsedTime,
    string? ErrorMessage);

/// <summary>
/// Performance metrics for bulk insert operations
/// </summary>
public record BulkInsertMetrics(
    int TotalOperations = 0,
    int TotalRecordsProcessed = 0,
    TimeSpan TotalElapsedTime = default,
    int SuccessfulOperations = 0,
    double AverageRecordsPerSecond = 0.0);
