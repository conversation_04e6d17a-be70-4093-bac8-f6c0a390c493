using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using EFCore.BulkExtensions;

namespace SmaTrendFollower.Services;

/// <summary>
/// Performance test service to demonstrate the ~5x improvement from bulk insert optimization.
/// Compares traditional EF Core AddRange vs EFCore.BulkExtensions performance.
/// </summary>
public interface IBulkInsertPerformanceTest
{
    /// <summary>
    /// Runs performance comparison between traditional and optimized bulk insert methods
    /// </summary>
    Task<BulkInsertPerformanceResult> RunPerformanceTestAsync(
        int numberOfBars = 10000,
        int batchSize = 5000,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of bulk insert performance testing
/// </summary>
public sealed class BulkInsertPerformanceTest : IBulkInsertPerformanceTest
{
    private readonly StockBarCacheDbContext _dbContext;
    private readonly ILogger<BulkInsertPerformanceTest> _logger;

    public BulkInsertPerformanceTest(
        StockBarCacheDbContext dbContext,
        ILogger<BulkInsertPerformanceTest> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<BulkInsertPerformanceResult> RunPerformanceTestAsync(
        int numberOfBars = 10000,
        int batchSize = 5000,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting bulk insert performance test with {NumberOfBars} bars", numberOfBars);

        // Generate test data
        var testBars = GenerateTestBars(numberOfBars);
        
        // Test 1: Traditional EF Core AddRange + SaveChanges
        var traditionalResult = await TestTraditionalInsertAsync(testBars, batchSize, cancellationToken);
        
        // Clean up test data
        await CleanupTestDataAsync(cancellationToken);
        
        // Test 2: EFCore.BulkExtensions BulkInsert
        var optimizedResult = await TestOptimizedInsertAsync(testBars, batchSize, cancellationToken);
        
        // Clean up test data
        await CleanupTestDataAsync(cancellationToken);

        var performanceImprovement = traditionalResult.TotalSeconds / optimizedResult.TotalSeconds;

        var result = new BulkInsertPerformanceResult(
            numberOfBars,
            traditionalResult,
            optimizedResult,
            performanceImprovement);

        _logger.LogInformation("Performance test completed: Traditional={TraditionalMs}ms, Optimized={OptimizedMs}ms, " +
                             "Improvement={Improvement:F1}x faster",
            traditionalResult.TotalMilliseconds, optimizedResult.TotalMilliseconds, performanceImprovement);

        return result;
    }

    private async Task<TimeSpan> TestTraditionalInsertAsync(
        List<CachedStockBar> testBars, 
        int batchSize, 
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Testing traditional EF Core AddRange + SaveChanges approach");
        
        var stopwatch = Stopwatch.StartNew();
        
        var batches = testBars.Chunk(batchSize);
        foreach (var batch in batches)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                // Traditional approach: AddRange + SaveChanges
                await _dbContext.CachedStockBars.AddRangeAsync(batch, cancellationToken);
                await _dbContext.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    private async Task<TimeSpan> TestOptimizedInsertAsync(
        List<CachedStockBar> testBars, 
        int batchSize, 
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Testing optimized EFCore.BulkExtensions BulkInsert approach");
        
        var stopwatch = Stopwatch.StartNew();
        
        var batches = testBars.Chunk(batchSize);
        foreach (var batch in batches)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                // Optimized approach: BulkInsert
                await _dbContext.BulkInsertAsync(batch, cancellationToken: cancellationToken);
                await transaction.CommitAsync(cancellationToken);
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    private List<CachedStockBar> GenerateTestBars(int numberOfBars)
    {
        var testBars = new List<CachedStockBar>();
        var random = new Random(42); // Fixed seed for reproducible results
        var baseTime = DateTime.UtcNow.Date.AddDays(-30);

        for (int i = 0; i < numberOfBars; i++)
        {
            var bar = new CachedStockBar
            {
                Symbol = $"TEST{i % 100:D3}", // Create 100 different test symbols
                TimeFrame = "1Day",
                TimeUtc = baseTime.AddMinutes(i),
                Open = 100m + (decimal)(random.NextDouble() * 10),
                High = 105m + (decimal)(random.NextDouble() * 10),
                Low = 95m + (decimal)(random.NextDouble() * 10),
                Close = 100m + (decimal)(random.NextDouble() * 10),
                Volume = random.Next(1000, 100000),
                Vwap = 100m + (decimal)(random.NextDouble() * 10),
                TradeCount = (uint)random.Next(100, 1000),
                CachedAt = DateTime.UtcNow
            };
            
            testBars.Add(bar);
        }

        return testBars;
    }

    private async Task CleanupTestDataAsync(CancellationToken cancellationToken)
    {
        // Remove all test data (symbols starting with "TEST")
        await _dbContext.CachedStockBars
            .Where(b => b.Symbol.StartsWith("TEST"))
            .ExecuteDeleteAsync(cancellationToken);
    }
}

/// <summary>
/// Result of bulk insert performance test
/// </summary>
public record BulkInsertPerformanceResult(
    int NumberOfBars,
    TimeSpan TraditionalTime,
    TimeSpan OptimizedTime,
    double PerformanceImprovement)
{
    /// <summary>
    /// Gets a formatted summary of the performance test results
    /// </summary>
    public string GetSummary()
    {
        return $"Bulk Insert Performance Test Results:\n" +
               $"  Records: {NumberOfBars:N0}\n" +
               $"  Traditional (AddRange): {TraditionalTime.TotalMilliseconds:F0}ms\n" +
               $"  Optimized (BulkInsert): {OptimizedTime.TotalMilliseconds:F0}ms\n" +
               $"  Performance Improvement: {PerformanceImprovement:F1}x faster\n" +
               $"  Time Saved: {(TraditionalTime - OptimizedTime).TotalMilliseconds:F0}ms";
    }
}
