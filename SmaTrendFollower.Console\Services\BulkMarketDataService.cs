using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using System.Diagnostics;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Optimized bulk market data service for universe construction
/// Uses intelligent batching and parallel processing
/// </summary>
public class BulkMarketDataService
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<BulkMarketDataService> _logger;
    private readonly BulkDataConfig _config;

    public BulkMarketDataService(
        IMarketDataService marketDataService,
        ILogger<BulkMarketDataService> logger,
        BulkDataConfig config)
    {
        _marketDataService = marketDataService;
        _logger = logger;
        _config = config;
    }

    /// <summary>
    /// Fetch market data for multiple symbols with optimized batching
    /// </summary>
    public async Task<BulkDataResult> FetchBulkDataAsync(
        IEnumerable<string> symbols,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        var stopwatch = Stopwatch.StartNew();
        
        _logger.LogInformation("🚀 Starting bulk data fetch for {SymbolCount} symbols", symbolList.Count);

        var results = new ConcurrentDictionary<string, BulkSymbolData>();
        var successCount = 0;
        var errorCount = 0;

        // Strategy 1: Try bulk endpoint if available (Polygon grouped daily bars)
        if (_config.UseBulkEndpoints && symbolList.Count >= _config.BulkThreshold)
        {
            var bulkResults = await TryBulkFetchAsync(symbolList, startDate, endDate, cancellationToken);
            if (bulkResults.Any())
            {
                foreach (var kvp in bulkResults)
                {
                    results[kvp.Key] = kvp.Value;
                }
                
                var remainingSymbols = symbolList.Except(results.Keys).ToList();
                if (remainingSymbols.Any())
                {
                    _logger.LogInformation("📊 Bulk fetch got {BulkCount}/{Total} symbols, fetching {Remaining} individually",
                        results.Count, symbolList.Count, remainingSymbols.Count);
                    
                    var individualResults = await FetchIndividualSymbolsAsync(remainingSymbols, startDate, endDate, cancellationToken);
                    foreach (var kvp in individualResults)
                    {
                        results[kvp.Key] = kvp.Value;
                    }
                }
            }
            else
            {
                _logger.LogWarning("⚠️ Bulk fetch failed, falling back to individual requests");
                var individualResults = await FetchIndividualSymbolsAsync(symbolList, startDate, endDate, cancellationToken);
                foreach (var kvp in individualResults)
                {
                    results[kvp.Key] = kvp.Value;
                }
            }
        }
        else
        {
            // Strategy 2: Optimized individual requests with aggressive parallelization
            var individualResults = await FetchIndividualSymbolsAsync(symbolList, startDate, endDate, cancellationToken);
            foreach (var kvp in individualResults)
            {
                results[kvp.Key] = kvp.Value;
            }
        }

        stopwatch.Stop();
        
        successCount = results.Values.Count(r => r.Success);
        errorCount = results.Values.Count(r => !r.Success);

        var result = new BulkDataResult
        {
            SymbolData = results,
            TotalSymbols = symbolList.Count,
            SuccessfulSymbols = successCount,
            FailedSymbols = errorCount,
            FetchDuration = stopwatch.Elapsed,
            AverageLatencyMs = results.Values.Where(r => r.Success).Average(r => r.FetchDurationMs)
        };

        _logger.LogInformation("✅ Bulk data fetch completed: {Success}/{Total} successful in {Duration:F1}s " +
                             "(avg: {AvgLatency:F0}ms per symbol)",
            successCount, symbolList.Count, stopwatch.Elapsed.TotalSeconds, result.AverageLatencyMs);

        return result;
    }

    /// <summary>
    /// Try to use bulk endpoints for faster data retrieval
    /// </summary>
    private async Task<Dictionary<string, BulkSymbolData>> TryBulkFetchAsync(
        List<string> symbols,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var results = new Dictionary<string, BulkSymbolData>();
        
        try
        {
            // For Polygon: Use grouped daily bars endpoint
            // This is more efficient than individual symbol requests
            var bulkData = await _marketDataService.GetStockBarsAsync(symbols, startDate, endDate);

            foreach (var kvp in bulkData)
            {
                var symbol = kvp.Key;
                var bars = kvp.Value.Items.ToList();

                results[symbol] = new BulkSymbolData
                {
                    Symbol = symbol,
                    Bars = bars,
                    Success = bars.Any(),
                    FetchDurationMs = 0, // Bulk request, can't measure individual
                    FromCache = false,
                    Source = "Bulk"
                };
            }

            _logger.LogInformation("📦 Bulk endpoint returned data for {Count} symbols", results.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "❌ Bulk fetch failed: {Error}", ex.Message);
        }

        return results;
    }

    /// <summary>
    /// Fetch symbols individually with optimized parallelization
    /// </summary>
    private async Task<ConcurrentDictionary<string, BulkSymbolData>> FetchIndividualSymbolsAsync(
        List<string> symbols,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var results = new ConcurrentDictionary<string, BulkSymbolData>();
        
        // Use aggressive parallelization
        using var semaphore = new SemaphoreSlim(_config.MaxConcurrentRequests, _config.MaxConcurrentRequests);
        
        var fetchTasks = symbols.Select(async symbol =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var sw = Stopwatch.StartNew();
                
                // Add per-symbol timeout
                using var symbolCts = new CancellationTokenSource(_config.SymbolTimeoutMs);
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, symbolCts.Token);

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();
                
                sw.Stop();

                results[symbol] = new BulkSymbolData
                {
                    Symbol = symbol,
                    Bars = bars,
                    Success = bars.Any(),
                    FetchDurationMs = sw.ElapsedMilliseconds,
                    FromCache = false, // Could be enhanced to detect cache hits
                    Source = "Individual"
                };

                // Log slow requests for monitoring
                if (sw.ElapsedMilliseconds > _config.SlowRequestThresholdMs)
                {
                    _logger.LogDebug("🐌 Slow request for {Symbol}: {Duration}ms", symbol, sw.ElapsedMilliseconds);
                }
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                throw; // Propagate cancellation
            }
            catch (Exception ex)
            {
                _logger.LogDebug("❌ Failed to fetch {Symbol}: {Error}", symbol, ex.Message);
                results[symbol] = new BulkSymbolData
                {
                    Symbol = symbol,
                    Bars = new List<IBar>(),
                    Success = false,
                    FetchDurationMs = 0,
                    FromCache = false,
                    Source = "Individual"
                };
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(fetchTasks);
        return results;
    }
}

/// <summary>
/// Configuration for bulk data fetching
/// </summary>
public class BulkDataConfig
{
    /// <summary>
    /// Maximum concurrent requests for individual symbol fetching
    /// </summary>
    public int MaxConcurrentRequests { get; set; } = 50;
    
    /// <summary>
    /// Timeout per symbol in milliseconds
    /// </summary>
    public int SymbolTimeoutMs { get; set; } = 10000;
    
    /// <summary>
    /// Threshold for logging slow requests
    /// </summary>
    public int SlowRequestThresholdMs { get; set; } = 2000;
    
    /// <summary>
    /// Whether to attempt bulk endpoints first
    /// </summary>
    public bool UseBulkEndpoints { get; set; } = true;
    
    /// <summary>
    /// Minimum symbols required to attempt bulk fetch
    /// </summary>
    public int BulkThreshold { get; set; } = 100;
}

/// <summary>
/// Data for a single symbol from bulk fetch
/// </summary>
public class BulkSymbolData
{
    public string Symbol { get; set; } = string.Empty;
    public List<IBar> Bars { get; set; } = new();
    public bool Success { get; set; }
    public long FetchDurationMs { get; set; }
    public bool FromCache { get; set; }
    public string Source { get; set; } = string.Empty;
}

/// <summary>
/// Result of bulk data fetch operation
/// </summary>
public class BulkDataResult
{
    public ConcurrentDictionary<string, BulkSymbolData> SymbolData { get; set; } = new();
    public int TotalSymbols { get; set; }
    public int SuccessfulSymbols { get; set; }
    public int FailedSymbols { get; set; }
    public TimeSpan FetchDuration { get; set; }
    public double AverageLatencyMs { get; set; }
    
    public double SuccessRate => TotalSymbols > 0 ? (double)SuccessfulSymbols / TotalSymbols : 0;
    public double RequestsPerSecond => FetchDuration.TotalSeconds > 0 ? TotalSymbols / FetchDuration.TotalSeconds : 0;
}
