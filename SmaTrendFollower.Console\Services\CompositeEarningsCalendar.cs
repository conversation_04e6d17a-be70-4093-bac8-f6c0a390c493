using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Composite earnings calendar that tries multiple data sources in order
/// </summary>
public sealed class CompositeEarningsCalendar : IEarningsCalendar
{
    private readonly FinnhubEarningsCalendar _finnhubCalendar;
    private readonly PolygonEarningsCalendar _polygonCalendar;
    private readonly FallbackEarningsCalendar _fallbackCalendar;
    private readonly IMemoryCache _cache;
    private readonly ILogger<CompositeEarningsCalendar> _logger;

    private static readonly TimeSpan CacheDuration = TimeSpan.FromHours(6);

    public CompositeEarningsCalendar(
        FinnhubEarningsCalendar finnhubCalendar,
        PolygonEarningsCalendar polygonCalendar,
        FallbackEarningsCalendar fallbackCalendar,
        IMemoryCache cache,
        ILogger<CompositeEarningsCalendar> logger)
    {
        _finnhubCalendar = finnhubCalendar;
        _polygonCalendar = polygonCalendar;
        _fallbackCalendar = fallbackCalendar;
        _cache = cache;
        _logger = logger;
    }

    public async Task<DateTime?> GetNextEarningsAsync(string symbol, CancellationToken ct = default)
    {
        if (string.IsNullOrWhiteSpace(symbol))
        {
            _logger.LogWarning("Empty symbol provided to GetNextEarningsAsync");
            return null;
        }

        var cacheKey = $"composite_earnings:{symbol.ToUpperInvariant()}";
        
        // Check cache first
        if (_cache.TryGetValue(cacheKey, out DateTime? cachedDate))
        {
            _logger.LogDebug("Returning cached composite earnings date for {Symbol}: {Date}", symbol, cachedDate);
            return cachedDate;
        }

        // Try data sources in order of preference
        var dataSources = new (string Name, Func<Task<DateTime?>> Source)[]
        {
            ("Finnhub", () => _finnhubCalendar.GetNextEarningsAsync(symbol, ct)),
            ("Polygon", () => _polygonCalendar.GetNextEarningsAsync(symbol, ct)),
            ("Fallback", () => _fallbackCalendar.GetNextEarningsAsync(symbol, ct))
        };

        foreach (var (sourceName, dataSource) in dataSources)
        {
            try
            {
                _logger.LogDebug("Trying {Source} for earnings data for {Symbol}", sourceName, symbol);
                
                var result = await dataSource();
                if (result.HasValue)
                {
                    _logger.LogInformation("✅ Found earnings data for {Symbol} from {Source}: {Date}", 
                        symbol, sourceName, result.Value);
                    
                    // Cache the successful result
                    _cache.Set(cacheKey, result, CacheDuration);
                    return result;
                }
                else
                {
                    _logger.LogDebug("No earnings data found for {Symbol} from {Source}", symbol, sourceName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting earnings data for {Symbol} from {Source}", symbol, sourceName);
            }
        }

        _logger.LogWarning("❌ All earnings data sources failed for {Symbol}", symbol);
        
        // Cache null result for shorter duration to avoid repeated API calls
        _cache.Set(cacheKey, (DateTime?)null, TimeSpan.FromHours(1));
        return null;
    }
}
