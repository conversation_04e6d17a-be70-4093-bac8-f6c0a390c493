using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Comprehensive monitoring service that tracks system health, performance, and trading metrics
/// with real-time alerting capabilities
/// </summary>
public sealed class ComprehensiveMonitoringService : BackgroundService, IDisposable
{
    private readonly ILogger<ComprehensiveMonitoringService> _logger;
    private readonly IDiscordNotificationService _discordService;
    private readonly ILiveStateStore _liveStateStore;
    private readonly MonitoringConfiguration _config;
    private readonly Timer _healthCheckTimer;
    private readonly Timer _performanceTimer;
    private readonly Timer _alertTimer;
    
    private readonly ConcurrentDictionary<string, MonitoringSystemMetric> _systemMetrics = new();
    private readonly ConcurrentDictionary<string, DateTime> _lastAlertTimes = new();
    private readonly ConcurrentQueue<AlertEvent> _pendingAlerts = new();
    
    private volatile bool _isDisposed;

    public ComprehensiveMonitoringService(
        ILogger<ComprehensiveMonitoringService> logger,
        IDiscordNotificationService discordService,
        ILiveStateStore liveStateStore,
        IOptions<MonitoringConfiguration> config)
    {
        _logger = logger;
        _discordService = discordService;
        _liveStateStore = liveStateStore;
        _config = config.Value;
        
        // Initialize timers
        _healthCheckTimer = new Timer(PerformHealthChecks, null, TimeSpan.Zero, TimeSpan.FromMinutes(2));
        _performanceTimer = new Timer(CollectPerformanceMetrics, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
        _alertTimer = new Timer(ProcessPendingAlerts, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("🔍 ComprehensiveMonitoringService started");
        
        // Send startup notification
        await _discordService.SendMessageAsync("🚀 **SmaTrendFollower Monitoring Started**\n" +
            "✅ System monitoring active\n" +
            "✅ Real-time alerts enabled\n" +
            "✅ Performance tracking online");

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                
                // Periodic comprehensive health check
                await PerformComprehensiveHealthCheckAsync();
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error in monitoring service");
            await SendCriticalAlert("Monitoring Service Failure", ex.Message);
        }
        finally
        {
            await _discordService.SendMessageAsync("🛑 **SmaTrendFollower Monitoring Stopped**");
            _logger.LogInformation("🔍 ComprehensiveMonitoringService stopped");
        }
    }

    private async void PerformHealthChecks(object? state)
    {
        if (_isDisposed) return;
        
        try
        {
            // Check Redis connectivity
            var redisHealthy = await _liveStateStore.IsHealthyAsync();
            RecordMetric("Redis.Health", redisHealthy ? 1 : 0);
            
            if (!redisHealthy)
            {
                await QueueAlert(AlertLevel.Critical, "Redis Connection", "Redis connection lost - state management compromised");
            }
            
            // Check system resources
            var process = Process.GetCurrentProcess();
            var memoryMB = process.WorkingSet64 / 1024 / 1024;
            var cpuTime = process.TotalProcessorTime.TotalMilliseconds;
            
            RecordMetric("System.MemoryMB", memoryMB);
            RecordMetric("System.CpuTimeMs", cpuTime);
            
            // Memory alert
            if (memoryMB > _config.MemoryThresholdMB)
            {
                await QueueAlert(AlertLevel.Warning, "High Memory Usage", 
                    $"Memory usage: {memoryMB:N0} MB (threshold: {_config.MemoryThresholdMB:N0} MB)");
            }
            
            _logger.LogDebug("Health check completed - Redis: {RedisHealth}, Memory: {MemoryMB} MB", 
                redisHealthy, memoryMB);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during health check");
        }
    }

    private async void CollectPerformanceMetrics(object? state)
    {
        if (_isDisposed) return;
        
        try
        {
            // Collect GC metrics
            var gen0 = GC.CollectionCount(0);
            var gen1 = GC.CollectionCount(1);
            var gen2 = GC.CollectionCount(2);
            var totalMemory = GC.GetTotalMemory(false) / 1024 / 1024;
            
            RecordMetric("GC.Gen0Collections", gen0);
            RecordMetric("GC.Gen1Collections", gen1);
            RecordMetric("GC.Gen2Collections", gen2);
            RecordMetric("GC.TotalMemoryMB", totalMemory);
            
            // Check for excessive GC pressure
            var previousGen2 = GetPreviousMetricValue("GC.Gen2Collections");
            if (previousGen2.HasValue && (gen2 - previousGen2.Value) > 5)
            {
                await QueueAlert(AlertLevel.Warning, "High GC Pressure", 
                    $"Excessive Gen2 collections: {gen2 - previousGen2.Value} in last minute");
            }
            
            _logger.LogDebug("Performance metrics collected - Memory: {TotalMemoryMB} MB, Gen2 GC: {Gen2Count}", 
                totalMemory, gen2);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting performance metrics");
        }
    }

    private async void ProcessPendingAlerts(object? state)
    {
        if (_isDisposed) return;
        
        var alertsProcessed = 0;
        while (_pendingAlerts.TryDequeue(out var alert) && alertsProcessed < 10) // Process max 10 alerts per cycle
        {
            try
            {
                // Check if we should suppress this alert (rate limiting)
                var alertKey = $"{alert.Category}:{alert.Level}";
                var now = DateTime.UtcNow;
                
                if (_lastAlertTimes.TryGetValue(alertKey, out var lastAlert))
                {
                    var timeSinceLastAlert = now - lastAlert;
                    var suppressionTime = alert.Level == AlertLevel.Critical ? 
                        TimeSpan.FromMinutes(5) : TimeSpan.FromMinutes(15);
                    
                    if (timeSinceLastAlert < suppressionTime)
                    {
                        continue; // Skip this alert due to rate limiting
                    }
                }
                
                // Send the alert
                await SendAlert(alert);
                _lastAlertTimes[alertKey] = now;
                alertsProcessed++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing alert: {AlertCategory}", alert.Category);
            }
        }
    }

    private async Task PerformComprehensiveHealthCheckAsync()
    {
        try
        {
            var healthReport = new List<string>();
            var issues = new List<string>();
            
            // Check Redis
            var redisHealthy = await _liveStateStore.IsHealthyAsync();
            healthReport.Add($"Redis: {(redisHealthy ? "✅ Healthy" : "❌ Unhealthy")}");
            if (!redisHealthy) issues.Add("Redis connection issues");
            
            // Check memory usage
            var process = Process.GetCurrentProcess();
            var memoryMB = process.WorkingSet64 / 1024 / 1024;
            var memoryStatus = memoryMB < _config.MemoryThresholdMB ? "✅ Normal" : "⚠️ High";
            healthReport.Add($"Memory: {memoryStatus} ({memoryMB:N0} MB)");
            
            // Check GC pressure
            var gen2Count = GC.CollectionCount(2);
            var previousGen2 = GetPreviousMetricValue("GC.Gen2Collections") ?? 0;
            var gen2Delta = gen2Count - previousGen2;
            var gcStatus = gen2Delta < 3 ? "✅ Normal" : "⚠️ High";
            healthReport.Add($"GC Pressure: {gcStatus} ({gen2Delta} Gen2 collections)");
            
            // Send periodic health report if there are issues
            if (issues.Any())
            {
                var message = "🔍 **System Health Report**\n" +
                             string.Join("\n", healthReport) + "\n\n" +
                             "⚠️ **Issues Detected:**\n" +
                             string.Join("\n", issues.Select(i => $"• {i}"));
                
                await _discordService.SendMessageAsync(message);
            }
            
            _logger.LogInformation("Comprehensive health check completed - Issues: {IssueCount}", issues.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during comprehensive health check");
        }
    }

    private async Task QueueAlert(AlertLevel level, string category, string message)
    {
        _pendingAlerts.Enqueue(new AlertEvent
        {
            Level = level,
            Category = category,
            Message = message,
            Timestamp = DateTime.UtcNow
        });
        
        // For critical alerts, send immediately
        if (level == AlertLevel.Critical)
        {
            await SendCriticalAlert(category, message);
        }
    }

    private async Task SendAlert(AlertEvent alert)
    {
        var emoji = alert.Level switch
        {
            AlertLevel.Critical => "🚨",
            AlertLevel.Warning => "⚠️",
            AlertLevel.Info => "ℹ️",
            _ => "📊"
        };
        
        var message = $"{emoji} **{alert.Level.ToString().ToUpper()} ALERT**\n" +
                     $"📂 Category: {alert.Category}\n" +
                     $"📝 {alert.Message}\n" +
                     $"🕐 {alert.Timestamp:HH:mm:ss UTC}";
        
        await _discordService.SendMessageAsync(message);
        _logger.LogWarning("Alert sent: {Level} - {Category} - {Message}", alert.Level, alert.Category, alert.Message);
    }

    private async Task SendCriticalAlert(string category, string message)
    {
        var criticalMessage = $"🚨 **CRITICAL SYSTEM ALERT** 🚨\n" +
                             $"📂 {category}\n" +
                             $"📝 {message}\n" +
                             $"🕐 {DateTime.UtcNow:HH:mm:ss UTC}\n" +
                             $"⚡ **Immediate attention required!**";
        
        await _discordService.SendMessageAsync(criticalMessage);
        _logger.LogCritical("CRITICAL ALERT: {Category} - {Message}", category, message);
    }

    private void RecordMetric(string name, double value)
    {
        _systemMetrics.AddOrUpdate(name,
            new MonitoringSystemMetric { Name = name, Value = value, Timestamp = DateTime.UtcNow },
            (key, existing) => new MonitoringSystemMetric { Name = name, Value = value, Timestamp = DateTime.UtcNow });
    }

    private double? GetPreviousMetricValue(string name)
    {
        return _systemMetrics.TryGetValue(name, out var metric) ? metric.Value : null;
    }

    public override void Dispose()
    {
        if (_isDisposed) return;
        
        _isDisposed = true;
        _healthCheckTimer?.Dispose();
        _performanceTimer?.Dispose();
        _alertTimer?.Dispose();
        
        base.Dispose();
    }
}

public class MonitoringConfiguration
{
    public long MemoryThresholdMB { get; set; } = 1000; // 1GB
    public bool EnableDiscordAlerts { get; set; } = true;
    public bool EnablePerformanceMonitoring { get; set; } = true;
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(2);
}

public class MonitoringSystemMetric
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public DateTime Timestamp { get; set; }
}

public class AlertEvent
{
    public AlertLevel Level { get; set; }
    public string Category { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

public enum AlertLevel
{
    Info,
    Warning,
    Critical
}
