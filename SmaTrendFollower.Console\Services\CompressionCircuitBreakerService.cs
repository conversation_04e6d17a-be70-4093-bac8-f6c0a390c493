using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Circuit breaker for compression operations to prevent repeated failures
/// Disables compression temporarily when multiple failures occur
/// </summary>
public interface ICompressionCircuitBreakerService
{
    /// <summary>
    /// Check if compression should be attempted
    /// </summary>
    bool ShouldAttemptCompression();

    /// <summary>
    /// Record a successful compression
    /// </summary>
    void RecordSuccess();

    /// <summary>
    /// Record a failed compression
    /// </summary>
    void RecordFailure(string symbol, string timeFrame, Exception exception);

    /// <summary>
    /// Get compression circuit breaker statistics
    /// </summary>
    CompressionCircuitBreakerStats GetStats();
}

/// <summary>
/// Implementation of compression circuit breaker
/// </summary>
public sealed class CompressionCircuitBreakerService : ICompressionCircuitBreakerService
{
    private readonly ILogger<CompressionCircuitBreakerService> _logger;
    
    // Circuit breaker state
    private volatile bool _circuitOpen = false;
    private DateTime _lastFailureTime = DateTime.MinValue;
    private readonly TimeSpan _circuitOpenDuration = TimeSpan.FromMinutes(5); // Stay open for 5 minutes
    
    // Statistics
    private long _totalAttempts = 0;
    private long _successCount = 0;
    private long _failureCount = 0;
    private readonly object _statsLock = new object();
    
    // Failure tracking
    private readonly Queue<DateTime> _recentFailures = new Queue<DateTime>();
    private readonly TimeSpan _failureWindow = TimeSpan.FromMinutes(2); // Track failures in 2-minute window
    private const int MaxFailuresInWindow = 3; // Open circuit after 3 failures in window

    public CompressionCircuitBreakerService(ILogger<CompressionCircuitBreakerService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public bool ShouldAttemptCompression()
    {
        lock (_statsLock)
        {
            Interlocked.Increment(ref _totalAttempts);
            
            // Clean up old failures
            CleanupOldFailures();
            
            // Check if circuit should be reset
            if (_circuitOpen && DateTime.UtcNow - _lastFailureTime > _circuitOpenDuration)
            {
                _circuitOpen = false;
                _logger.LogInformation("Compression circuit breaker RESET - attempting compression again");
            }
            
            if (_circuitOpen)
            {
                _logger.LogDebug("Compression circuit breaker OPEN - skipping compression");
                return false;
            }
            
            return true;
        }
    }

    public void RecordSuccess()
    {
        lock (_statsLock)
        {
            Interlocked.Increment(ref _successCount);
            
            // Reset circuit if it was open
            if (_circuitOpen)
            {
                _circuitOpen = false;
                _logger.LogInformation("Compression circuit breaker RESET after successful compression");
            }
        }
    }

    public void RecordFailure(string symbol, string timeFrame, Exception exception)
    {
        lock (_statsLock)
        {
            Interlocked.Increment(ref _failureCount);
            
            var now = DateTime.UtcNow;
            _recentFailures.Enqueue(now);
            _lastFailureTime = now;
            
            // Clean up old failures
            CleanupOldFailures();
            
            // Check if we should open the circuit
            if (_recentFailures.Count >= MaxFailuresInWindow && !_circuitOpen)
            {
                _circuitOpen = true;
                _logger.LogError("Compression circuit breaker OPENED after {FailureCount} failures in {Window} minutes. " +
                               "Latest failure: {Symbol} {TimeFrame} - {Error}",
                    _recentFailures.Count, _failureWindow.TotalMinutes, symbol, timeFrame, exception.Message);
            }
            else
            {
                _logger.LogWarning("Compression failure recorded: {Symbol} {TimeFrame} - {Error} " +
                                 "({FailureCount}/{MaxFailures} in window)",
                    symbol, timeFrame, exception.Message, _recentFailures.Count, MaxFailuresInWindow);
            }
        }
    }

    public CompressionCircuitBreakerStats GetStats()
    {
        lock (_statsLock)
        {
            CleanupOldFailures();

            return new CompressionCircuitBreakerStats
            {
                TotalAttempts = Interlocked.Read(ref _totalAttempts),
                SuccessCount = Interlocked.Read(ref _successCount),
                FailureCount = Interlocked.Read(ref _failureCount),
                RecentFailures = _recentFailures.Count,
                CircuitOpen = _circuitOpen,
                LastFailureTime = _lastFailureTime,
                SuccessRate = _totalAttempts > 0 ? (double)_successCount / _totalAttempts : 0.0
            };
        }
    }

    /// <summary>
    /// Remove failures older than the tracking window
    /// </summary>
    private void CleanupOldFailures()
    {
        var cutoff = DateTime.UtcNow - _failureWindow;
        while (_recentFailures.Count > 0 && _recentFailures.Peek() < cutoff)
        {
            _recentFailures.Dequeue();
        }
    }
}

/// <summary>
/// Statistics for compression circuit breaker
/// </summary>
public class CompressionCircuitBreakerStats
{
    public long TotalAttempts { get; set; }
    public long SuccessCount { get; set; }
    public long FailureCount { get; set; }
    public int RecentFailures { get; set; }
    public bool CircuitOpen { get; set; }
    public DateTime LastFailureTime { get; set; }
    public double SuccessRate { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
