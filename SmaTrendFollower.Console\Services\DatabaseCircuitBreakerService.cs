using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.CircuitBreaker;
using Polly.Extensions.Http;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Circuit breaker service for database operations with Redis fallback
/// Handles SQLite lock scenarios gracefully by falling back to Redis-only caching
/// </summary>
public interface IDatabaseCircuitBreakerService
{
    /// <summary>
    /// Execute database operation with circuit breaker protection
    /// Falls back to Redis-only caching when SQLite is experiencing issues
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operation">Database operation to execute</param>
    /// <param name="fallbackOperation">Fallback operation using Redis</param>
    /// <param name="operationName">Name for logging</param>
    /// <returns>Result from primary or fallback operation</returns>
    Task<T> ExecuteWithFallbackAsync<T>(
        Func<Task<T>> operation,
        Func<Task<T>> fallbackOperation,
        string operationName);

    /// <summary>
    /// Execute database cache operation with circuit breaker protection
    /// Specifically designed for bar caching operations
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="timeFrame">Time frame</param>
    /// <param name="bars">Bars to cache</param>
    /// <param name="sqliteOperation">SQLite caching operation</param>
    /// <param name="redisOperation">Redis fallback operation</param>
    Task CacheBarsWithCircuitBreakerAsync(
        string symbol,
        string timeFrame,
        IEnumerable<IBar> bars,
        Func<Task> sqliteOperation,
        Func<Task> redisOperation);

    /// <summary>
    /// Get circuit breaker statistics for monitoring
    /// </summary>
    DatabaseCircuitBreakerStats GetStats();
}

/// <summary>
/// Implementation of database circuit breaker service
/// </summary>
public sealed class DatabaseCircuitBreakerService : IDatabaseCircuitBreakerService
{
    private readonly IHighFrequencyCache _redisCache;
    private readonly ILogger<DatabaseCircuitBreakerService> _logger;
    private readonly IAsyncPolicy _circuitBreakerPolicy;
    
    // Statistics tracking
    private long _totalOperations = 0;
    private long _fallbackOperations = 0;
    private long _circuitBreakerTrips = 0;
    private DateTime _lastCircuitBreakerTrip = DateTime.MinValue;

    public DatabaseCircuitBreakerService(
        IHighFrequencyCache redisCache,
        ILogger<DatabaseCircuitBreakerService> logger)
    {
        _redisCache = redisCache ?? throw new ArgumentNullException(nameof(redisCache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Configure circuit breaker for database operations
        _circuitBreakerPolicy = Policy
            .Handle<Exception>(ex => IsDatabaseLockException(ex))
            .AdvancedCircuitBreakerAsync(
                failureThreshold: 0.5, // Open when 50% of requests fail
                samplingDuration: TimeSpan.FromSeconds(30), // Sample over 30 seconds
                minimumThroughput: 3, // Minimum 3 operations before circuit can open
                durationOfBreak: TimeSpan.FromMinutes(2), // Stay open for 2 minutes
                onBreak: (exception, duration) =>
                {
                    Interlocked.Increment(ref _circuitBreakerTrips);
                    _lastCircuitBreakerTrip = DateTime.UtcNow;
                    _logger.LogError("Database circuit breaker OPENED for {Duration}s due to: {Exception}. " +
                                   "Falling back to Redis-only operations.",
                        duration.TotalSeconds, exception.Message);
                },
                onReset: () =>
                {
                    _logger.LogInformation("Database circuit breaker RESET - SQLite operations resumed");
                },
                onHalfOpen: () =>
                {
                    _logger.LogInformation("Database circuit breaker HALF-OPEN - testing SQLite connection");
                });
    }

    public async Task<T> ExecuteWithFallbackAsync<T>(
        Func<Task<T>> operation,
        Func<Task<T>> fallbackOperation,
        string operationName)
    {
        Interlocked.Increment(ref _totalOperations);

        try
        {
            return await _circuitBreakerPolicy.ExecuteAsync(async () =>
            {
                _logger.LogDebug("Executing database operation: {OperationName}", operationName);
                return await operation();
            });
        }
        catch (BrokenCircuitException)
        {
            Interlocked.Increment(ref _fallbackOperations);
            _logger.LogWarning("Circuit breaker open for {OperationName}, using fallback operation", operationName);
            return await fallbackOperation();
        }
        catch (Exception ex) when (IsDatabaseLockException(ex))
        {
            Interlocked.Increment(ref _fallbackOperations);
            _logger.LogWarning(ex, "Database lock detected for {OperationName}, using fallback operation", operationName);
            return await fallbackOperation();
        }
    }

    public async Task CacheBarsWithCircuitBreakerAsync(
        string symbol,
        string timeFrame,
        IEnumerable<IBar> bars,
        Func<Task> sqliteOperation,
        Func<Task> redisOperation)
    {
        Interlocked.Increment(ref _totalOperations);

        try
        {
            await _circuitBreakerPolicy.ExecuteAsync(async () =>
            {
                _logger.LogDebug("Caching bars in SQLite for {Symbol} {TimeFrame}", symbol, timeFrame);
                await sqliteOperation();
            });

            // If SQLite succeeds, also cache recent bars in Redis for fast access
            if (timeFrame == "Day")
            {
                var barsList = bars.ToList();
                var mostRecentBar = barsList.OrderByDescending(b => b.TimeUtc).FirstOrDefault();
                if (mostRecentBar != null)
                {
                    await _redisCache.CacheBarSnapshotAsync(symbol, mostRecentBar);
                }
            }
        }
        catch (BrokenCircuitException)
        {
            Interlocked.Increment(ref _fallbackOperations);
            _logger.LogWarning("Circuit breaker open for {Symbol} {TimeFrame}, using Redis-only caching", symbol, timeFrame);
            await redisOperation();
        }
        catch (Exception ex) when (IsDatabaseLockException(ex))
        {
            Interlocked.Increment(ref _fallbackOperations);
            _logger.LogWarning(ex, "Database lock detected for {Symbol} {TimeFrame}, using Redis-only caching", symbol, timeFrame);
            await redisOperation();
        }
    }

    public DatabaseCircuitBreakerStats GetStats()
    {
        return new DatabaseCircuitBreakerStats
        {
            TotalOperations = Interlocked.Read(ref _totalOperations),
            FallbackOperations = Interlocked.Read(ref _fallbackOperations),
            CircuitBreakerTrips = Interlocked.Read(ref _circuitBreakerTrips),
            LastCircuitBreakerTrip = _lastCircuitBreakerTrip,
            FallbackRate = _totalOperations > 0 ? (double)_fallbackOperations / _totalOperations : 0.0
        };
    }

    /// <summary>
    /// Determines if an exception is a database lock that should trigger circuit breaker
    /// </summary>
    private static bool IsDatabaseLockException(Exception ex)
    {
        return ex.Message.Contains("database is locked", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("SQLite Error 5", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("database disk image is malformed", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("database schema has changed", StringComparison.OrdinalIgnoreCase);
    }
}

/// <summary>
/// Statistics for database circuit breaker monitoring
/// </summary>
public class DatabaseCircuitBreakerStats
{
    public long TotalOperations { get; set; }
    public long FallbackOperations { get; set; }
    public long CircuitBreakerTrips { get; set; }
    public DateTime LastCircuitBreakerTrip { get; set; }
    public double FallbackRate { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
