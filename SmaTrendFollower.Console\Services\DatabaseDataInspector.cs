using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service to inspect actual database contents and timestamps
/// </summary>
public interface IDatabaseDataInspector
{
    Task<DatabaseContentReport> GetDatabaseContentReportAsync();
}

public class DatabaseDataInspector : IDatabaseDataInspector
{
    private readonly IDbContextFactory<StockBarCacheDbContext> _contextFactory;
    private readonly ILogger<DatabaseDataInspector> _logger;

    public DatabaseDataInspector(
        IDbContextFactory<StockBarCacheDbContext> contextFactory,
        ILogger<DatabaseDataInspector> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public async Task<DatabaseContentReport> GetDatabaseContentReportAsync()
    {
        using var context = await _contextFactory.CreateDbContextAsync();

        var report = new DatabaseContentReport();

        try
        {
            // Get total count of bars
            report.TotalBars = await context.CachedStockBars.CountAsync();

            if (report.TotalBars == 0)
            {
                report.IsEmpty = true;
                return report;
            }

            // Get most recent bar by TimeUtc (market data date)
            var mostRecentBar = await context.CachedStockBars
                .OrderByDescending(b => b.TimeUtc)
                .FirstOrDefaultAsync();

            if (mostRecentBar != null)
            {
                report.MostRecentBarDate = mostRecentBar.TimeUtc;
                report.MostRecentBarSymbol = mostRecentBar.Symbol;
            }

            // Get oldest bar by TimeUtc
            var oldestBar = await context.CachedStockBars
                .OrderBy(b => b.TimeUtc)
                .FirstOrDefaultAsync();

            if (oldestBar != null)
            {
                report.OldestBarDate = oldestBar.TimeUtc;
                report.OldestBarSymbol = oldestBar.Symbol;
            }

            // Get most recently cached data (when it was inserted)
            var mostRecentlyCached = await context.CachedStockBars
                .OrderByDescending(b => b.CachedAt)
                .FirstOrDefaultAsync();

            if (mostRecentlyCached != null)
            {
                report.MostRecentCacheTime = mostRecentlyCached.CachedAt;
                report.MostRecentCacheSymbol = mostRecentlyCached.Symbol;
            }

            // Get oldest cached data
            var oldestCached = await context.CachedStockBars
                .OrderBy(b => b.CachedAt)
                .FirstOrDefaultAsync();

            if (oldestCached != null)
            {
                report.OldestCacheTime = oldestCached.CachedAt;
                report.OldestCacheSymbol = oldestCached.Symbol;
            }

            // Get unique symbol count
            report.UniqueSymbols = await context.CachedStockBars
                .Select(b => b.Symbol)
                .Distinct()
                .CountAsync();

            // Get sample of recent data (last 10 bars)
            report.RecentSampleBars = await context.CachedStockBars
                .OrderByDescending(b => b.TimeUtc)
                .Take(10)
                .Select(b => new SampleBar
                {
                    Symbol = b.Symbol,
                    TimeUtc = b.TimeUtc,
                    CachedAt = b.CachedAt,
                    Close = b.Close
                })
                .ToListAsync();

            // Check data freshness
            var now = DateTime.UtcNow;
            var marketCloseToday = now.Date.AddHours(21); // 4 PM ET = 9 PM UTC
            var lastTradingDay = now.Date;
            
            // If it's weekend or before market open, last trading day was Friday
            if (now.DayOfWeek == DayOfWeek.Saturday)
                lastTradingDay = now.Date.AddDays(-1); // Friday
            else if (now.DayOfWeek == DayOfWeek.Sunday)
                lastTradingDay = now.Date.AddDays(-2); // Friday
            else if (now.TimeOfDay < TimeSpan.FromHours(14.5)) // Before 9:30 AM ET
                lastTradingDay = now.Date.AddDays(-1); // Previous day

            report.ExpectedMostRecentDate = lastTradingDay;
            report.DataIsFresh = report.MostRecentBarDate?.Date >= lastTradingDay.AddDays(-1);

            report.IsEmpty = false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating database content report");
            report.ErrorMessage = ex.Message;
        }

        return report;
    }
}

public class DatabaseContentReport
{
    public bool IsEmpty { get; set; }
    public int TotalBars { get; set; }
    public int UniqueSymbols { get; set; }
    
    public DateTime? MostRecentBarDate { get; set; }
    public string? MostRecentBarSymbol { get; set; }
    
    public DateTime? OldestBarDate { get; set; }
    public string? OldestBarSymbol { get; set; }
    
    public DateTime MostRecentCacheTime { get; set; }
    public string? MostRecentCacheSymbol { get; set; }
    
    public DateTime OldestCacheTime { get; set; }
    public string? OldestCacheSymbol { get; set; }
    
    public DateTime ExpectedMostRecentDate { get; set; }
    public bool DataIsFresh { get; set; }
    
    public List<SampleBar> RecentSampleBars { get; set; } = new();
    
    public string? ErrorMessage { get; set; }
}

public class SampleBar
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime TimeUtc { get; set; }
    public DateTime CachedAt { get; set; }
    public decimal Close { get; set; }
}
