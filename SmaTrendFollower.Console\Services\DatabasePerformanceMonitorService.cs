using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Implementation of database performance monitoring service
/// Tracks connection pool usage, query performance, and error patterns
/// </summary>
public class DatabasePerformanceMonitorService : IDatabasePerformanceMonitorService
{
    private readonly ILogger<DatabasePerformanceMonitorService> _logger;
    private readonly object _metricsLock = new object();
    
    private DatabasePerformanceMetrics _metrics;
    private readonly ConcurrentQueue<OperationTiming> _recentOperations;
    private readonly ConcurrentDictionary<string, long> _operationCounts;
    private readonly ConcurrentDictionary<string, TimeSpan> _operationTotalTimes;
    
    private long _totalOperationTime; // In ticks for atomic operations
    private long _maxOperationTime;   // In ticks for atomic operations
    private long _minOperationTime = long.MaxValue; // In ticks for atomic operations

    public DatabasePerformanceMonitorService(ILogger<DatabasePerformanceMonitorService> logger)
    {
        _logger = logger;
        _metrics = new DatabasePerformanceMetrics
        {
            LastResetTime = DateTime.UtcNow,
            MinOperationTime = TimeSpan.MaxValue
        };
        _recentOperations = new ConcurrentQueue<OperationTiming>();
        _operationCounts = new ConcurrentDictionary<string, long>();
        _operationTotalTimes = new ConcurrentDictionary<string, TimeSpan>();
    }

    public void RecordOperationTime(string operationName, TimeSpan duration, bool success)
    {
        var timing = new OperationTiming
        {
            OperationName = operationName,
            Duration = duration,
            Success = success,
            Timestamp = DateTime.UtcNow
        };

        _recentOperations.Enqueue(timing);
        
        // Keep only last 1000 operations to prevent memory growth
        while (_recentOperations.Count > 1000)
        {
            _recentOperations.TryDequeue(out _);
        }

        // Update operation counts
        _operationCounts.AddOrUpdate(operationName, 1, (key, value) => value + 1);
        _operationTotalTimes.AddOrUpdate(operationName, duration, (key, value) => value.Add(duration));

        // Update metrics atomically
        lock (_metricsLock)
        {
            _metrics.TotalOperations++;
            _metrics.LastOperationTime = DateTime.UtcNow;
            
            if (success)
            {
                _metrics.SuccessfulOperations++;
            }
            else
            {
                _metrics.FailedOperations++;
            }

            // Update timing statistics using atomic operations for thread safety
            var durationTicks = duration.Ticks;
            Interlocked.Add(ref _totalOperationTime, durationTicks);
            
            // Update max time
            long currentMax;
            do
            {
                currentMax = Interlocked.Read(ref _maxOperationTime);
                if (durationTicks <= currentMax) break;
            } while (Interlocked.CompareExchange(ref _maxOperationTime, durationTicks, currentMax) != currentMax);
            
            // Update min time
            long currentMin;
            do
            {
                currentMin = Interlocked.Read(ref _minOperationTime);
                if (durationTicks >= currentMin) break;
            } while (Interlocked.CompareExchange(ref _minOperationTime, durationTicks, currentMin) != currentMin);

            // Calculate average
            if (_metrics.TotalOperations > 0)
            {
                _metrics.AverageOperationTime = new TimeSpan(Interlocked.Read(ref _totalOperationTime) / _metrics.TotalOperations);
            }
            
            _metrics.MaxOperationTime = new TimeSpan(Interlocked.Read(ref _maxOperationTime));
            _metrics.MinOperationTime = new TimeSpan(Interlocked.Read(ref _minOperationTime));
        }

        // Log slow operations
        if (duration.TotalSeconds > 5)
        {
            _logger.LogWarning("Slow database operation detected: {OperationName} took {Duration}ms (Success: {Success})", 
                operationName, duration.TotalMilliseconds, success);
        }
    }

    public void RecordConnectionPoolEvent(string eventType, int activeConnections, int poolSize)
    {
        lock (_metricsLock)
        {
            _metrics.CurrentActiveConnections = activeConnections;
            _metrics.ConnectionPoolSize = poolSize;
            
            if (activeConnections > _metrics.MaxActiveConnections)
            {
                _metrics.MaxActiveConnections = activeConnections;
            }
        }

        // Log high connection pool usage
        var usagePercentage = poolSize > 0 ? (double)activeConnections / poolSize * 100 : 0;
        if (usagePercentage > 80)
        {
            _logger.LogWarning("High connection pool usage: {ActiveConnections}/{PoolSize} ({UsagePercentage:F1}%) - Event: {EventType}", 
                activeConnections, poolSize, usagePercentage, eventType);
        }
    }

    public void RecordDatabaseError(string operationName, Exception exception)
    {
        lock (_metricsLock)
        {
            switch (exception)
            {
                case TimeoutException:
                case OperationCanceledException when exception.Message.Contains("timeout"):
                    _metrics.TimeoutErrors++;
                    break;
                    
                case InvalidOperationException ioe when ioe.Message.Contains("connection") || 
                                                       ioe.Message.Contains("Cannot access a closed file") ||
                                                       ioe.Message.Contains("Exception while reading from stream"):
                    _metrics.ConnectionErrors++;
                    break;
                    
                case InvalidOperationException:
                    _metrics.InvalidOperationErrors++;
                    break;
                    
                default:
                    _metrics.OtherErrors++;
                    break;
            }
        }

        _logger.LogError(exception, "Database error in operation {OperationName}: {ErrorType} - {ErrorMessage}", 
            operationName, exception.GetType().Name, exception.Message);
    }

    public DatabasePerformanceMetrics GetCurrentMetrics()
    {
        lock (_metricsLock)
        {
            // Return a copy to prevent external modification
            return new DatabasePerformanceMetrics
            {
                TotalOperations = _metrics.TotalOperations,
                SuccessfulOperations = _metrics.SuccessfulOperations,
                FailedOperations = _metrics.FailedOperations,
                AverageOperationTime = _metrics.AverageOperationTime,
                MaxOperationTime = _metrics.MaxOperationTime,
                MinOperationTime = _metrics.MinOperationTime == TimeSpan.MaxValue ? TimeSpan.Zero : _metrics.MinOperationTime,
                CurrentActiveConnections = _metrics.CurrentActiveConnections,
                MaxActiveConnections = _metrics.MaxActiveConnections,
                ConnectionPoolSize = _metrics.ConnectionPoolSize,
                TimeoutErrors = _metrics.TimeoutErrors,
                ConnectionErrors = _metrics.ConnectionErrors,
                InvalidOperationErrors = _metrics.InvalidOperationErrors,
                OtherErrors = _metrics.OtherErrors,
                LastResetTime = _metrics.LastResetTime,
                LastOperationTime = _metrics.LastOperationTime
            };
        }
    }

    public void ResetMetrics()
    {
        lock (_metricsLock)
        {
            _metrics = new DatabasePerformanceMetrics
            {
                LastResetTime = DateTime.UtcNow,
                MinOperationTime = TimeSpan.MaxValue
            };
            
            Interlocked.Exchange(ref _totalOperationTime, 0);
            Interlocked.Exchange(ref _maxOperationTime, 0);
            Interlocked.Exchange(ref _minOperationTime, long.MaxValue);
        }
        
        _operationCounts.Clear();
        _operationTotalTimes.Clear();
        
        // Clear recent operations
        while (_recentOperations.TryDequeue(out _)) { }
        
        _logger.LogInformation("Database performance metrics reset");
    }

    public string GetPerformanceSummary()
    {
        var metrics = GetCurrentMetrics();
        var uptime = DateTime.UtcNow - metrics.LastResetTime;
        
        return $"Database Performance Summary (Uptime: {uptime:hh\\:mm\\:ss}): " +
               $"Operations: {metrics.TotalOperations} (Success: {metrics.SuccessRate:F1}%), " +
               $"Avg Time: {metrics.AverageOperationTime.TotalMilliseconds:F1}ms, " +
               $"Max Time: {metrics.MaxOperationTime.TotalMilliseconds:F1}ms, " +
               $"Connections: {metrics.CurrentActiveConnections}/{metrics.ConnectionPoolSize} " +
               $"(Max: {metrics.MaxActiveConnections}), " +
               $"Errors: Timeout={metrics.TimeoutErrors}, Connection={metrics.ConnectionErrors}, " +
               $"InvalidOp={metrics.InvalidOperationErrors}, Other={metrics.OtherErrors}";
    }
}
