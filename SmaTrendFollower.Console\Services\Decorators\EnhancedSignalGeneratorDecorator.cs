using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Services;

/// <summary>
/// Decorator that adds enhanced signal generation capabilities to existing ISignalGenerator
/// Provides backward compatibility while adding robust error recovery and adaptive strategies
/// </summary>
public sealed class EnhancedSignalGeneratorDecorator : IEnhancedSignalGenerator, IDisposable
{
    private readonly ISignalGenerator _innerService;
    private readonly IRobustSignalGenerationService? _robustService;
    private readonly ILogger<EnhancedSignalGeneratorDecorator> _logger;
    private readonly EnhancedServicesOptions _options;

    public EnhancedSignalGeneratorDecorator(
        ISignalGenerator innerService,
        ILogger<EnhancedSignalGeneratorDecorator> logger,
        EnhancedServicesOptions options,
        IRobustSignalGenerationService? robustService = null)
    {
        _innerService = innerService;
        _robustService = robustService;
        _logger = logger;
        _options = options;
    }

    // === Enhanced Methods ===

    public async Task<RobustSignalResult> GenerateSignalsRobustlyAsync(
        int topN = 10, 
        CancellationToken cancellationToken = default)
    {
        if (!_options.EnableAdaptiveSignalGeneration || _robustService == null)
        {
            // Fallback to original service
            try
            {
                _logger.LogDebug("Using fallback signal generation (enhanced services disabled)");
                var signals = await _innerService.RunAsync(topN, cancellationToken);
                
                return new RobustSignalResult
                {
                    RequestedCount = topN,
                    Signals = signals.ToList(),
                    IsSuccess = true,
                    StartTime = DateTime.UtcNow,
                    EndTime = DateTime.UtcNow,
                    TotalDuration = TimeSpan.Zero,
                    AttemptedSymbols = signals.Count(),
                    SuccessfulSymbols = signals.Count(),
                    SuccessRate = 1.0,
                    GenerationMethods = new List<string> { "Original" }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Original signal generation failed");
                
                return new RobustSignalResult
                {
                    RequestedCount = topN,
                    Signals = new List<TradingSignal>(),
                    IsSuccess = false,
                    StartTime = DateTime.UtcNow,
                    EndTime = DateTime.UtcNow,
                    TotalDuration = TimeSpan.Zero,
                    ErrorMessage = ex.Message,
                    AttemptedSymbols = 0,
                    SuccessfulSymbols = 0,
                    SuccessRate = 0.0,
                    GenerationMethods = new List<string> { "Original" },
                    Warnings = new List<string> { $"Original generation failed: {ex.Message}" }
                };
            }
        }

        return await _robustService.GenerateSignalsRobustlyAsync(topN, cancellationToken);
    }

    public SymbolErrorStatistics GetErrorStatistics()
    {
        if (!_options.EnableAdaptiveSignalGeneration || _robustService == null)
        {
            // Return empty statistics when enhanced services are disabled
            return new SymbolErrorStatistics
            {
                TotalSymbolsTracked = 0,
                SymbolsWithErrors = 0,
                TotalErrors = 0,
                AverageErrorsPerSymbol = 0.0,
                TopErrorSymbols = new Dictionary<string, int>()
            };
        }

        return _robustService.GetErrorStatistics();
    }

    public void ClearErrorHistory(string symbol)
    {
        if (_options.EnableAdaptiveSignalGeneration && _robustService != null)
        {
            _robustService.ClearErrorHistory(symbol);
        }
        else
        {
            _logger.LogDebug("Error history clearing skipped (enhanced services disabled)");
        }
    }

    // === Original Interface Implementation ===

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default)
    {
        // Always delegate to the original service for backward compatibility
        // Enhanced functionality is available through the enhanced methods
        try
        {
            return await _innerService.RunAsync(topN, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Original signal generation failed, attempting enhanced fallback");
            
            // If enhanced services are enabled, try robust generation as fallback
            if (_options.EnableAdaptiveSignalGeneration && _robustService != null)
            {
                try
                {
                    var robustResult = await _robustService.GenerateSignalsRobustlyAsync(topN, cancellationToken);
                    _logger.LogInformation("Enhanced fallback succeeded with {Count} signals", robustResult.Signals.Count);
                    return robustResult.Signals;
                }
                catch (Exception fallbackEx)
                {
                    _logger.LogError(fallbackEx, "Enhanced fallback also failed");
                }
            }
            
            // If all else fails, return empty collection
            _logger.LogWarning("All signal generation methods failed, returning empty collection");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    public void Dispose()
    {
        if (_innerService is IDisposable disposableInner)
        {
            disposableInner.Dispose();
        }
        
        _robustService?.Dispose();
    }
}
