using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Net.Http.Json;
using System.Threading.Channels;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that processes news articles from channel and calls FinBERT for sentiment analysis.
/// Implements parallel processing with semaphore rate limiting and stores results in Redis.
/// </summary>
internal sealed class FinbertWorker : BackgroundService
{
    private readonly ChannelReader<HeadlineItem> _reader;
    private readonly IHttpClientFactory _http;
    private readonly IDatabase _redis;
    private readonly ILogger<FinbertWorker> _log;
    private readonly FinbertOptions _opt;
    private readonly LlmSentimentOptions _llmOpt;
    private readonly LlmSentimentService _llmSvc;
    private readonly IApiTrackingService _apiTracking;
    private const string LatestField = "latest";

    /// <summary>
    /// Initializes the FinBERT worker with channel reader and dependencies.
    /// </summary>
    /// <param name="channel">Channel for receiving news articles from polling service</param>
    /// <param name="http">HTTP client factory for FinBERT API calls</param>
    /// <param name="redisSvc">Redis connection service for storing sentiment scores</param>
    /// <param name="opt">FinBERT configuration options</param>
    /// <param name="llmSvc">LLM sentiment service for overlay analysis</param>
    /// <param name="llmOpt">LLM sentiment configuration options</param>
    /// <param name="apiTracking">API tracking service for monitoring API usage</param>
    /// <param name="log">Logger for worker operations</param>
    public FinbertWorker(
        Channel<HeadlineItem> channel,
        IHttpClientFactory http,
        IOptimizedRedisConnectionService redisSvc,
        IOptions<FinbertOptions> opt,
        LlmSentimentService llmSvc,
        IOptions<LlmSentimentOptions> llmOpt,
        IApiTrackingService apiTracking,
        ILogger<FinbertWorker> log)
    {
        _reader = channel.Reader ?? throw new ArgumentNullException(nameof(channel));
        _http = http ?? throw new ArgumentNullException(nameof(http));
        _redis = redisSvc.GetDatabaseAsync().GetAwaiter().GetResult();
        _opt = opt.Value ?? throw new ArgumentNullException(nameof(opt));
        _llmSvc = llmSvc ?? throw new ArgumentNullException(nameof(llmSvc));
        _llmOpt = llmOpt.Value ?? throw new ArgumentNullException(nameof(llmOpt));
        _apiTracking = apiTracking ?? throw new ArgumentNullException(nameof(apiTracking));
        _log = log ?? throw new ArgumentNullException(nameof(log));
    }

    /// <summary>
    /// Continuously processes news articles from channel with parallel FinBERT calls.
    /// Uses semaphore to limit concurrent HTTP requests to FinBERT endpoint.
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken ct)
    {
        var mode = _opt.EnableFinbertEndpoint ? "FinBERT+LLM" : "LLM-only";
        _log.LogInformation("FinbertWorker started with parallelism: {Parallelism}, mode: {Mode}", _opt.Parallelism, mode);
        
        var sem = new SemaphoreSlim(_opt.Parallelism);
        
        try
        {
            await foreach (var article in _reader.ReadAllAsync(ct))
            {
                await sem.WaitAsync(ct);

                // Use Task.Factory.StartNew with TaskScheduler.Default to avoid thread pool starvation
                // and ensure proper thread management instead of fire-and-forget Task.Run
                _ = Task.Factory.StartNew(async () =>
                {
                    try
                    {
                        await ProcessAsync(article, ct);
                    }
                    catch (Exception ex)
                    {
                        _log.LogDebug(ex, "FinBERT processing error for article {Id}", article.Id);
                    }
                    finally
                    {
                        sem.Release();
                    }
                }, ct, TaskCreationOptions.DenyChildAttach, TaskScheduler.Default).Unwrap();
            }
        }
        catch (OperationCanceledException)
        {
            _log.LogInformation("FinbertWorker cancelled");
            throw; // Propagate cancellation as expected by BackgroundService
        }
        finally
        {
            sem.Dispose();
            _log.LogInformation("FinbertWorker stopped");
        }
    }

    /// <summary>
    /// Processes a single news article: calls FinBERT API and stores sentiment in Redis.
    /// Falls back to LLM sentiment analysis if FinBERT is unavailable.
    /// Handles symbol extraction, sentiment scoring, and Redis storage with TTL.
    /// </summary>
    /// <param name="n">News article to process</param>
    /// <param name="ct">Cancellation token</param>
    private async Task ProcessAsync(HeadlineItem n, CancellationToken ct)
    {
        double score = 0.0;
        bool finbertSuccess = false;

        // Try FinBERT HTTP endpoint first (if enabled) with retry logic
        if (_opt.EnableFinbertEndpoint)
        {
            var finbertResult = await TryFinbertWithRetryAsync(n.Headline, n.Id, ct);
            if (finbertResult.HasValue)
            {
                score = finbertResult.Value;
                finbertSuccess = true;

                // ---------- LLM overlay if low-confidence ----------
                if (Math.Abs(score) < _llmOpt.ConfidenceCutoff)
                {
                    var llm = await _llmSvc.GetSentimentWithFallbackAsync(n.Headline, ct);
                    if (llm is not null)
                    {
                        var originalScore = score;
                        score = (1.0 - _llmOpt.BlendWeight) * score
                              + _llmOpt.BlendWeight * llm.Value;

                        _log.LogDebug("LLM overlay for {Symbol}: FinBERT={FinBert:+0.000;-0.000}, LLM={Llm:+0.000;-0.000}, Blended={Final:+0.000;-0.000}",
                            n.Symbol, originalScore, llm.Value, score);
                    }
                }
            }
        }
        else
        {
            _log.LogDebug("FinBERT endpoint disabled, using LLM sentiment for article {Id}", n.Id);
        }

        // ---------- Fallback to LLM sentiment if FinBERT failed ----------
        if (!finbertSuccess)
        {
            try
            {
                var llm = await _llmSvc.GetSentimentWithFallbackAsync(n.Headline, ct);
                if (llm is not null)
                {
                    score = llm.Value;
                    _log.LogDebug("LLM fallback sentiment for {Symbol}: {Score:+0.000;-0.000} (ID: {Id})",
                        n.Symbol, score, n.Id);
                }
                else
                {
                    _log.LogDebug("FinBERT, OpenAI, and Gemini all failed for article {Id}, skipping", n.Id);
                    return;
                }
            }
            catch (Exception ex)
            {
                _log.LogWarning(ex, "All sentiment providers failed for article {Id}: {Headline}", n.Id, n.Headline);
                return;
            }
        }

        // Store sentiment for the symbol associated with this headline
        if (string.IsNullOrWhiteSpace(n.Symbol))
        {
            _log.LogDebug("No symbol found for headline {Id}: {Headline}", n.Id, n.Headline);
            return;
        }

        try
        {
            var dateKey = n.CreatedAtUtc.Date.ToString("yyyyMMdd");
            var symbol = n.Symbol;

            var key = $"Sentiment:{symbol}:{dateKey}";
            var field = n.Id; // Use headline ID as field name

            // Store both the specific headline sentiment and update latest
            await _redis.HashSetAsync(key, new[]
            {
                new HashEntry(field, score),
                new HashEntry(LatestField, score)
            });

            // Set TTL for the key
            await _redis.KeyExpireAsync(key, TimeSpan.FromDays(_opt.TtlDays));

            var source = finbertSuccess ? "FinBERT" : "LLM";
            _log.LogDebug("{Source} {Symbol} {Score:+0.000;-0.000} (ID: {Id})",
                source, symbol, score, n.Id);
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "Failed to store sentiment in Redis for article {Id}: {Headline}", n.Id, n.Headline);
        }
    }

    /// <summary>
    /// Attempts to call FinBERT with retry logic. Since FinBERT is self-hosted, we can retry once on failure.
    /// </summary>
    /// <param name="headline">Text to analyze</param>
    /// <param name="articleId">Article ID for logging</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score (-1.0 to ****) or null if failed after retry</returns>
    private async Task<double?> TryFinbertWithRetryAsync(string headline, string articleId, CancellationToken ct)
    {
        const int maxAttempts = 2; // Original attempt + 1 retry

        for (int attempt = 1; attempt <= maxAttempts; attempt++)
        {
            // Start API tracking
            var apiRequest = new ApiCallRequest(
                Provider: "FinBERT",
                Operation: "sentiment_analysis",
                RequestData: headline.Length > 100 ? headline[..100] + "..." : headline
            );

            using var tracker = _apiTracking.StartTracking(apiRequest);

            try
            {
                var client = _http.CreateClient("Finbert");
                var response = await client.PostAsJsonAsync(_opt.BaseUrl, new { text = headline }, ct);
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadFromJsonAsync<FinbertResp>(cancellationToken: ct);
                if (json == null)
                {
                    _log.LogWarning("FinBERT returned null response for article {Id} (attempt {Attempt})", articleId, attempt);

                    // Track failed API call
                    await tracker.CompleteAsync(new ApiCallResult(
                        Success: false,
                        StatusCode: (int)response.StatusCode,
                        ErrorMessage: "Null response from FinBERT API"
                    ));
                    continue; // Try again if we have attempts left
                }

                // Convert FinBERT response to sentiment score (-1 to +1)
                var probs = json.probabilities;
                double score;

                if (probs.positive > probs.negative && probs.positive > probs.neutral)
                {
                    score = probs.positive; // Positive sentiment
                }
                else if (probs.negative > probs.positive && probs.negative > probs.neutral)
                {
                    score = -probs.negative; // Negative sentiment (negative score)
                }
                else
                {
                    score = 0.0; // Neutral sentiment
                }

                // Track successful API call
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: true,
                    StatusCode: (int)response.StatusCode,
                    ResponseData: $"pos:{probs.positive:F3} neg:{probs.negative:F3} neu:{probs.neutral:F3}",
                    Metadata: new Dictionary<string, object>
                    {
                        ["confidence"] = Math.Max(Math.Max(probs.positive, probs.negative), probs.neutral),
                        ["sentiment_score"] = score,
                        ["attempt"] = attempt
                    }
                ));

                _log.LogDebug("FinBERT success for article {Id} (attempt {Attempt}): {Score:+0.000;-0.000}",
                    articleId, attempt, score);
                return score;
            }
            catch (HttpRequestException ex)
            {
                _log.LogDebug(ex, "FinBERT HTTP error for article {Id} (attempt {Attempt})", articleId, attempt);

                // Track failed API call
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: false,
                    ErrorMessage: ex.Message,
                    Metadata: new Dictionary<string, object> { ["attempt"] = attempt }
                ));
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _log.LogDebug("FinBERT timeout for article {Id} (attempt {Attempt})", articleId, attempt);

                // Track timeout
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: false,
                    ErrorMessage: "Request timeout",
                    Metadata: new Dictionary<string, object> { ["attempt"] = attempt }
                ));
            }
            catch (Exception ex)
            {
                _log.LogDebug(ex, "FinBERT error for article {Id} (attempt {Attempt})", articleId, attempt);

                // Track general error
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: false,
                    ErrorMessage: ex.Message,
                    Metadata: new Dictionary<string, object> { ["attempt"] = attempt }
                ));
            }

            // If this was not the last attempt, wait a bit before retrying
            if (attempt < maxAttempts)
            {
                await Task.Delay(100, ct); // Brief delay before retry
                _log.LogDebug("Retrying FinBERT for article {Id} (attempt {NextAttempt})", articleId, attempt + 1);
            }
        }

        _log.LogDebug("FinBERT failed after {MaxAttempts} attempts for article {Id}, falling back to LLM",
            maxAttempts, articleId);
        return null;
    }

    /// <summary>
    /// FinBERT API response model matching the actual JSON structure.
    /// </summary>
    /// <param name="sentiment">Sentiment label: "positive", "negative", or "neutral"</param>
    /// <param name="probabilities">Probability scores for each sentiment class</param>
    /// <param name="text">Original text that was analyzed</param>
    private sealed record FinbertResp(string sentiment, FinbertProbabilities probabilities, string text);

    /// <summary>
    /// FinBERT probability scores for each sentiment class.
    /// </summary>
    /// <param name="positive">Probability of positive sentiment (0.0 to 1.0)</param>
    /// <param name="negative">Probability of negative sentiment (0.0 to 1.0)</param>
    /// <param name="neutral">Probability of neutral sentiment (0.0 to 1.0)</param>
    private sealed record FinbertProbabilities(double positive, double negative, double neutral);
}
