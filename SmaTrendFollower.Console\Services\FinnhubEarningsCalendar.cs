using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Finnhub.io earnings calendar implementation (free tier available)
/// Includes rate limiting for 30 req/sec AND 60 req/min limits
/// </summary>
public sealed class FinnhubEarningsCalendar : IEarningsCalendar, IDisposable
{
    private readonly HttpClient _http;
    private readonly string? _apiKey;
    private readonly IMemoryCache _cache;
    private readonly FinnhubRateLimitHelper _rateLimitHelper;
    private readonly ILogger<FinnhubEarningsCalendar> _logger;

    private static readonly TimeSpan CacheDuration = TimeSpan.FromHours(6);
    private bool _disposed = false;

    public FinnhubEarningsCalendar(
        HttpClient http,
        IConfiguration configuration,
        IMemoryCache cache,
        FinnhubRateLimitHelper rateLimitHelper,
        ILogger<FinnhubEarningsCalendar> logger)
    {
        _http = http;
        _cache = cache;
        _rateLimitHelper = rateLimitHelper;
        _logger = logger;

        // Finnhub API key is optional for some endpoints but recommended
        _apiKey = Environment.GetEnvironmentVariable("FINNHUB_API_KEY")
                  ?? configuration["Finnhub:ApiKey"];

        _http.BaseAddress = new Uri("https://finnhub.io/api/v1/");
        _http.Timeout = TimeSpan.FromSeconds(30);
    }

    public async Task<DateTime?> GetNextEarningsAsync(string symbol, CancellationToken ct = default)
    {
        if (string.IsNullOrWhiteSpace(symbol))
        {
            _logger.LogWarning("Empty symbol provided to GetNextEarningsAsync");
            return null;
        }

        var cacheKey = $"finnhub_earnings:{symbol.ToUpperInvariant()}";
        
        // Check cache first
        if (_cache.TryGetValue(cacheKey, out DateTime? cachedDate))
        {
            _logger.LogDebug("Returning cached Finnhub earnings date for {Symbol}: {Date}", symbol, cachedDate);
            return cachedDate;
        }

        try
        {
            // Use rate-limited Finnhub API call
            return await _rateLimitHelper.ExecuteAsync(async () =>
            {
                // Use Finnhub earnings calendar endpoint
                // Get earnings for the next 30 days
                var fromDate = DateTime.UtcNow.ToString("yyyy-MM-dd");
                var toDate = DateTime.UtcNow.AddDays(30).ToString("yyyy-MM-dd");

                var url = $"calendar/earnings?from={fromDate}&to={toDate}";
                if (!string.IsNullOrEmpty(_apiKey))
                {
                    url += $"&token={_apiKey}";
                }

                _logger.LogDebug("Fetching Finnhub earnings calendar from {FromDate} to {ToDate} for {Symbol}", fromDate, toDate, symbol);

                var response = await _http.GetAsync(url, ct).ConfigureAwait(false);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Finnhub earnings API returned {StatusCode} for {Symbol}", response.StatusCode, symbol);
                    return null;
                }

                var content = await response.Content.ReadAsStringAsync(ct).ConfigureAwait(false);
                var nextEarnings = ParseFinnhubEarningsResponse(content, symbol);

                if (nextEarnings.HasValue)
                {
                    _logger.LogInformation("✅ Found Finnhub earnings data for {Symbol}: {Date}", symbol, nextEarnings.Value);
                    _cache.Set(cacheKey, nextEarnings, CacheDuration);
                    return nextEarnings;
                }
                else
                {
                    _logger.LogDebug("No upcoming earnings found for {Symbol} in Finnhub data", symbol);
                    // Cache null result for shorter duration
                    _cache.Set(cacheKey, (DateTime?)null, TimeSpan.FromHours(1));
                    return null;
                }
            }, $"GetEarnings-{symbol}");
        }
        catch (TaskCanceledException)
        {
            _logger.LogWarning("Timeout fetching Finnhub earnings data for {Symbol}", symbol);
            return null;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error fetching Finnhub earnings data for {Symbol}", symbol);
            return null;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON parsing error for Finnhub earnings data for {Symbol}", symbol);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error fetching Finnhub earnings data for {Symbol}", symbol);
            return null;
        }
    }

    private DateTime? ParseFinnhubEarningsResponse(string content, string symbol)
    {
        try
        {
            var root = JsonSerializer.Deserialize<JsonElement>(content);
            
            if (!root.TryGetProperty("earningsCalendar", out var earningsArray) || 
                earningsArray.ValueKind != JsonValueKind.Array)
            {
                _logger.LogDebug("No earningsCalendar array found in Finnhub response");
                return null;
            }

            var now = DateTime.UtcNow;
            DateTime? nextEarnings = null;
            
            foreach (var earning in earningsArray.EnumerateArray())
            {
                // Check if this earning is for our symbol
                if (earning.TryGetProperty("symbol", out var symbolElement))
                {
                    var earningSymbol = symbolElement.GetString();
                    if (string.Equals(earningSymbol, symbol, StringComparison.OrdinalIgnoreCase))
                    {
                        // Get the date
                        if (earning.TryGetProperty("date", out var dateElement))
                        {
                            var dateStr = dateElement.GetString();
                            if (!string.IsNullOrEmpty(dateStr) && DateTime.TryParse(dateStr, out var earningDate))
                            {
                                // Convert to UTC
                                earningDate = DateTime.SpecifyKind(earningDate, DateTimeKind.Utc);
                                
                                // Only consider future earnings
                                if (earningDate > now && (nextEarnings == null || earningDate < nextEarnings))
                                {
                                    nextEarnings = earningDate;
                                    _logger.LogDebug("Found Finnhub earnings for {Symbol} on {Date}", symbol, earningDate);
                                    
                                    // Log additional details if available
                                    if (earning.TryGetProperty("epsEstimate", out var epsElement))
                                    {
                                        _logger.LogDebug("EPS Estimate: {EPS}", epsElement.GetDecimal());
                                    }
                                    if (earning.TryGetProperty("revenueEstimate", out var revenueElement))
                                    {
                                        _logger.LogDebug("Revenue Estimate: {Revenue}", revenueElement.GetDecimal());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            return nextEarnings;
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Error parsing Finnhub earnings response for {Symbol}", symbol);
            return null;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _rateLimitHelper?.Dispose();
            _disposed = true;
        }
    }
}
