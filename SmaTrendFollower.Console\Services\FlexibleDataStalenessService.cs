using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced data staleness service with flexible policies and graduated degradation
/// Provides emergency overrides and context-aware staleness handling
/// </summary>
public sealed class FlexibleDataStalenessService : IFlexibleDataStalenessService
{
    private readonly ILogger<FlexibleDataStalenessService> _logger;
    private readonly FlexibleStalenessConfiguration _config;
    private readonly IMarketHoursService _marketHoursService;
    private volatile bool _emergencyModeActive = false;
    private volatile StalenessPolicy _currentPolicy = StalenessPolicy.Standard;
    private DateTime _lastPolicyChange = DateTime.MinValue;

    public FlexibleDataStalenessService(
        ILogger<FlexibleDataStalenessService> logger,
        IOptions<FlexibleStalenessConfiguration> config,
        IMarketHoursService marketHoursService)
    {
        _logger = logger;
        _config = config.Value;
        _marketHoursService = marketHoursService;
    }

    /// <summary>
    /// Validates data freshness with flexible policies and graduated responses
    /// </summary>
    public Task<FlexibleStalenessResult> ValidateDataFreshnessAsync(
        DateTime dataTimestamp,
        DataType dataType,
        string dataSource,
        StalenessContext? context = null)
    {
        var isMarketHours = _marketHoursService.IsMarketHours();
        var currentPolicy = DetermineCurrentPolicy(context);
        var thresholds = GetThresholds(dataType, isMarketHours, currentPolicy);
        var dataAge = DateTime.UtcNow - dataTimestamp;

        var result = new FlexibleStalenessResult
        {
            DataAge = dataAge,
            DataType = dataType,
            DataSource = dataSource,
            IsMarketHours = isMarketHours,
            Policy = currentPolicy,
            ValidationTimestamp = DateTime.UtcNow,
            Context = context
        };

        // Determine staleness level
        result.StalenessLevel = DetermineStalenessLevel(dataAge, thresholds);
        result.IsAcceptable = IsDataAcceptable(result.StalenessLevel, currentPolicy);
        result.QualityScore = CalculateQualityScore(result.StalenessLevel, dataAge, thresholds);
        result.RecommendedAction = DetermineRecommendedAction(result);

        // Log based on staleness level
        LogStalenessResult(result);

        // Record metrics
        RecordStalenessMetrics(result);

        return Task.FromResult(result);
    }

    /// <summary>
    /// Activates emergency mode with relaxed staleness policies
    /// </summary>
    public Task ActivateEmergencyModeAsync(string reason, TimeSpan? duration = null)
    {
        _emergencyModeActive = true;
        _currentPolicy = StalenessPolicy.Emergency;
        _lastPolicyChange = DateTime.UtcNow;

        _logger.LogWarning("Emergency mode activated: {Reason}. Policy changed to Emergency.", reason);
        MetricsRegistry.EmergencyModeActivations.Inc();

        // Schedule automatic deactivation
        var emergencyDuration = duration ?? _config.EmergencyModeDuration;
        _ = Task.Run(async () =>
        {
            await Task.Delay(emergencyDuration);
            await DeactivateEmergencyModeAsync("Automatic timeout");
        });

        return Task.CompletedTask;
    }

    /// <summary>
    /// Deactivates emergency mode and returns to standard policies
    /// </summary>
    public Task DeactivateEmergencyModeAsync(string reason)
    {
        if (_emergencyModeActive)
        {
            _emergencyModeActive = false;
            _currentPolicy = StalenessPolicy.Standard;
            _lastPolicyChange = DateTime.UtcNow;

            _logger.LogInformation("Emergency mode deactivated: {Reason}. Policy returned to Standard.", reason);
            MetricsRegistry.EmergencyModeDeactivations.Inc();
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Sets a specific staleness policy
    /// </summary>
    public Task SetPolicyAsync(StalenessPolicy policy, string reason)
    {
        var oldPolicy = _currentPolicy;
        _currentPolicy = policy;
        _lastPolicyChange = DateTime.UtcNow;

        if (policy == StalenessPolicy.Emergency)
        {
            _emergencyModeActive = true;
        }
        else
        {
            _emergencyModeActive = false;
        }

        _logger.LogInformation("Staleness policy changed from {OldPolicy} to {NewPolicy}: {Reason}",
            oldPolicy, policy, reason);

        return Task.CompletedTask;
    }

    /// <summary>
    /// Gets current staleness policy and status
    /// </summary>
    public StalenessStatus GetCurrentStatus()
    {
        return new StalenessStatus
        {
            CurrentPolicy = _currentPolicy,
            EmergencyModeActive = _emergencyModeActive,
            LastPolicyChange = _lastPolicyChange,
            IsMarketHours = _marketHoursService.IsMarketHours()
        };
    }

    private StalenessPolicy DetermineCurrentPolicy(StalenessContext? context)
    {
        // Emergency mode overrides everything
        if (_emergencyModeActive)
            return StalenessPolicy.Emergency;

        // Context-specific policy overrides
        if (context != null)
        {
            if (context.IsCriticalOperation && _config.AllowCriticalOverrides)
                return StalenessPolicy.Relaxed;

            if (context.HasFallbackData && _config.AllowFallbackDataUsage)
                return StalenessPolicy.Relaxed;

            if (context.IsBackfillOperation)
                return StalenessPolicy.Lenient;
        }

        return _currentPolicy;
    }

    private StalenessThresholds GetThresholds(DataType dataType, bool isMarketHours, StalenessPolicy policy)
    {
        var baseThresholds = _config.GetThresholds(dataType, isMarketHours);
        
        return policy switch
        {
            StalenessPolicy.Strict => new StalenessThresholds
            {
                Fresh = baseThresholds.Fresh * 0.5,
                Acceptable = baseThresholds.Acceptable * 0.7,
                Stale = baseThresholds.Stale * 0.8,
                VeryStale = baseThresholds.VeryStale
            },
            StalenessPolicy.Relaxed => new StalenessThresholds
            {
                Fresh = baseThresholds.Fresh * 2.0,
                Acceptable = baseThresholds.Acceptable * 3.0,
                Stale = baseThresholds.Stale * 4.0,
                VeryStale = baseThresholds.VeryStale * 2.0
            },
            StalenessPolicy.Lenient => new StalenessThresholds
            {
                Fresh = baseThresholds.Fresh * 4.0,
                Acceptable = baseThresholds.Acceptable * 6.0,
                Stale = baseThresholds.Stale * 8.0,
                VeryStale = baseThresholds.VeryStale * 4.0
            },
            StalenessPolicy.Emergency => new StalenessThresholds
            {
                Fresh = TimeSpan.FromDays(1),
                Acceptable = TimeSpan.FromDays(2),
                Stale = TimeSpan.FromDays(7),
                VeryStale = TimeSpan.FromDays(30)
            },
            _ => baseThresholds
        };
    }

    private static StalenessLevel DetermineStalenessLevel(TimeSpan dataAge, StalenessThresholds thresholds)
    {
        if (dataAge <= thresholds.Fresh)
            return StalenessLevel.Fresh;
        if (dataAge <= thresholds.Acceptable)
            return StalenessLevel.Acceptable;
        if (dataAge <= thresholds.Stale)
            return StalenessLevel.Stale;
        if (dataAge <= thresholds.VeryStale)
            return StalenessLevel.VeryStale;
        
        return StalenessLevel.Ancient;
    }

    private static bool IsDataAcceptable(StalenessLevel level, StalenessPolicy policy)
    {
        return policy switch
        {
            StalenessPolicy.Strict => level <= StalenessLevel.Fresh,
            StalenessPolicy.Standard => level <= StalenessLevel.Acceptable,
            StalenessPolicy.Relaxed => level <= StalenessLevel.Stale,
            StalenessPolicy.Lenient => level <= StalenessLevel.VeryStale,
            StalenessPolicy.Emergency => level <= StalenessLevel.Ancient,
            _ => level <= StalenessLevel.Acceptable
        };
    }

    private static double CalculateQualityScore(StalenessLevel level, TimeSpan dataAge, StalenessThresholds thresholds)
    {
        return level switch
        {
            StalenessLevel.Fresh => 1.0,
            StalenessLevel.Acceptable => 0.8,
            StalenessLevel.Stale => 0.6,
            StalenessLevel.VeryStale => 0.4,
            StalenessLevel.Ancient => 0.2,
            _ => 0.0
        };
    }

    private static RecommendedAction DetermineRecommendedAction(FlexibleStalenessResult result)
    {
        if (result.IsAcceptable)
        {
            return result.StalenessLevel switch
            {
                StalenessLevel.Fresh => RecommendedAction.UseData,
                StalenessLevel.Acceptable => RecommendedAction.UseDataWithWarning,
                StalenessLevel.Stale => RecommendedAction.UseDataWithCaution,
                StalenessLevel.VeryStale => RecommendedAction.UseDataAsLastResort,
                _ => RecommendedAction.UseDataAsLastResort
            };
        }

        return result.StalenessLevel switch
        {
            StalenessLevel.Stale => RecommendedAction.RefreshData,
            StalenessLevel.VeryStale => RecommendedAction.RefreshDataUrgently,
            StalenessLevel.Ancient => RecommendedAction.RejectData,
            _ => RecommendedAction.RefreshData
        };
    }

    private void LogStalenessResult(FlexibleStalenessResult result)
    {
        var logLevel = result.StalenessLevel switch
        {
            StalenessLevel.Fresh => LogLevel.Debug,
            StalenessLevel.Acceptable => LogLevel.Debug,
            StalenessLevel.Stale => LogLevel.Information,
            StalenessLevel.VeryStale => LogLevel.Warning,
            StalenessLevel.Ancient => LogLevel.Error,
            _ => LogLevel.Warning
        };

        _logger.Log(logLevel,
            "Data staleness check: {DataType} from {DataSource} is {Level} ({Age} old, policy: {Policy}, acceptable: {Acceptable})",
            result.DataType, result.DataSource, result.StalenessLevel, result.DataAge, result.Policy, result.IsAcceptable);
    }

    private static void RecordStalenessMetrics(FlexibleStalenessResult result)
    {
        MetricsRegistry.DataStalenessChecks
            .WithLabels(result.DataSource, result.StalenessLevel.ToString(), result.IsAcceptable.ToString())
            .Inc();

        MetricsRegistry.DataQualityScore
            .WithLabels(result.DataSource, result.DataType.ToString())
            .Set(result.QualityScore);
    }
}

/// <summary>
/// Interface for flexible data staleness service
/// </summary>
public interface IFlexibleDataStalenessService
{
    Task<FlexibleStalenessResult> ValidateDataFreshnessAsync(DateTime dataTimestamp, DataType dataType, string dataSource, StalenessContext? context = null);
    Task ActivateEmergencyModeAsync(string reason, TimeSpan? duration = null);
    Task DeactivateEmergencyModeAsync(string reason);
    Task SetPolicyAsync(StalenessPolicy policy, string reason);
    StalenessStatus GetCurrentStatus();
}
