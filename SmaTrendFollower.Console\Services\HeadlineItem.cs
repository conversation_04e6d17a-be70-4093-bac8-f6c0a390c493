namespace SmaTrendFollower.Services;

/// <summary>
/// Common DTO consumed by FinBERTWorker for sentiment analysis.
/// Represents a unified headline structure from multiple news sources (Alpaca, Brave Search).
/// </summary>
public sealed record HeadlineItem
(
    /// <summary>
    /// Unique identifier for the headline (article ID or generated GUID)
    /// </summary>
    string Id,
    
    /// <summary>
    /// Stock symbol associated with the headline (uppercase)
    /// </summary>
    string Symbol,
    
    /// <summary>
    /// News headline/title text for sentiment analysis
    /// </summary>
    string Headline,
    
    /// <summary>
    /// UTC timestamp when the headline was created/published
    /// </summary>
    DateTime CreatedAtUtc
);
