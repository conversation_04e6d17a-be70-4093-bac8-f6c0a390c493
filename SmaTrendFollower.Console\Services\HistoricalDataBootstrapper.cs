using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Service to bootstrap historical data for the trading system
/// </summary>
public class HistoricalDataBootstrapper
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<HistoricalDataBootstrapper> _logger;

    public HistoricalDataBootstrapper(
        IMarketDataService marketDataService,
        ILogger<HistoricalDataBootstrapper> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
    }

    /// <summary>
    /// Populates historical data for a list of symbols
    /// </summary>
    public async Task<BootstrapResult> PopulateHistoricalDataAsync(
        IEnumerable<string> symbols, 
        int daysBack = 300,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("🚀 Starting historical data bootstrap for {Count} symbols", symbols.Count());
        
        var result = new BootstrapResult();
        var endDate = DateTime.UtcNow.Date.AddDays(-1); // Yesterday to avoid weekend issues
        var startDate = endDate.AddDays(-daysBack);
        
        _logger.LogInformation("📅 Date range: {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd} ({Days} days)",
            startDate, endDate, daysBack);

        var semaphore = new SemaphoreSlim(5, 5); // Limit concurrent requests
        var tasks = symbols.Select(async symbol =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                return await PopulateSymbolDataAsync(symbol, startDate, endDate, cancellationToken);
            }
            finally
            {
                semaphore.Release();
            }
        });

        var symbolResults = await Task.WhenAll(tasks);
        
        // Aggregate results
        result.TotalSymbols = symbolResults.Length;
        result.SuccessfulSymbols = symbolResults.Count(r => r.Success);
        result.FailedSymbols = symbolResults.Count(r => !r.Success);
        result.TotalBarsPopulated = symbolResults.Sum(r => r.BarsPopulated);
        result.SymbolResults = symbolResults.ToList();

        _logger.LogInformation("✅ Bootstrap complete: {Success}/{Total} symbols successful, {Bars} total bars populated",
            result.SuccessfulSymbols, result.TotalSymbols, result.TotalBarsPopulated);

        if (result.FailedSymbols > 0)
        {
            _logger.LogWarning("⚠️ {Failed} symbols failed to populate", result.FailedSymbols);
            foreach (var failed in symbolResults.Where(r => !r.Success))
            {
                _logger.LogWarning("❌ {Symbol}: {Error}", failed.Symbol, failed.ErrorMessage);
            }
        }

        return result;
    }

    /// <summary>
    /// Populates historical data for a single symbol
    /// </summary>
    private async Task<SymbolBootstrapResult> PopulateSymbolDataAsync(
        string symbol, 
        DateTime startDate, 
        DateTime endDate,
        CancellationToken cancellationToken)
    {
        var result = new SymbolBootstrapResult { Symbol = symbol };
        
        try
        {
            _logger.LogDebug("📊 Fetching data for {Symbol}...", symbol);
            
            var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var bars = barsPage.Items.ToList();
            
            result.BarsPopulated = bars.Count;
            result.Success = true;
            
            _logger.LogInformation("✅ {Symbol}: {Count} bars populated", symbol, bars.Count);
            
            // Validate data quality
            if (bars.Count < 200)
            {
                _logger.LogWarning("⚠️ {Symbol}: Only {Count} bars available (need 200+ for SMA200)", symbol, bars.Count);
            }
            
            // Check for data gaps
            var gaps = DetectDataGaps(bars);
            if (gaps.Any())
            {
                _logger.LogWarning("⚠️ {Symbol}: {GapCount} data gaps detected", symbol, gaps.Count);
                result.DataGaps = gaps;
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            _logger.LogError(ex, "❌ Failed to populate data for {Symbol}", symbol);
        }

        return result;
    }

    /// <summary>
    /// Detects gaps in historical data
    /// </summary>
    private List<DataGap> DetectDataGaps(List<Alpaca.Markets.IBar> bars)
    {
        var gaps = new List<DataGap>();
        
        if (bars.Count < 2) return gaps;
        
        for (int i = 1; i < bars.Count; i++)
        {
            var prevDate = bars[i - 1].TimeUtc.Date;
            var currentDate = bars[i].TimeUtc.Date;
            var daysDiff = (currentDate - prevDate).Days;
            
            // Skip weekends (gaps of 3 days are normal Friday -> Monday)
            if (daysDiff > 3)
            {
                gaps.Add(new DataGap
                {
                    StartDate = prevDate,
                    EndDate = currentDate,
                    DaysMissing = daysDiff - 1
                });
            }
        }
        
        return gaps;
    }

    /// <summary>
    /// Gets the default universe of symbols to bootstrap
    /// </summary>
    public static List<string> GetDefaultUniverse()
    {
        return new List<string>
        {
            // Major ETFs
            "SPY", "QQQ", "IWM", "VTI", "VEA", "VWO", "AGG", "TLT", "GLD", "VIX",
            
            // Large Cap Tech
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "NFLX", "CRM", "ADBE",
            
            // Large Cap Non-Tech
            "BRK.B", "JNJ", "V", "WMT", "JPM", "PG", "UNH", "HD", "MA", "DIS",
            
            // Mid Cap Growth
            "ROKU", "SQ", "SHOP", "TWLO", "ZM", "DOCU", "OKTA", "SNOW", "PLTR", "RBLX",
            
            // Defensive
            "KO", "PEP", "MCD", "WMT", "PG", "JNJ", "PFE", "VZ", "T", "XOM",
            
            // Financial
            "JPM", "BAC", "WFC", "GS", "MS", "C", "USB", "PNC", "TFC", "COF"
        };
    }
}

/// <summary>
/// Result of historical data bootstrap operation
/// </summary>
public class BootstrapResult
{
    public int TotalSymbols { get; set; }
    public int SuccessfulSymbols { get; set; }
    public int FailedSymbols { get; set; }
    public int TotalBarsPopulated { get; set; }
    public List<SymbolBootstrapResult> SymbolResults { get; set; } = new();
    
    public double SuccessRate => TotalSymbols > 0 ? (double)SuccessfulSymbols / TotalSymbols : 0;
}

/// <summary>
/// Result of bootstrap operation for a single symbol
/// </summary>
public class SymbolBootstrapResult
{
    public string Symbol { get; set; } = string.Empty;
    public bool Success { get; set; }
    public int BarsPopulated { get; set; }
    public string? ErrorMessage { get; set; }
    public List<DataGap> DataGaps { get; set; } = new();
}

/// <summary>
/// Represents a gap in historical data
/// </summary>
public class DataGap
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int DaysMissing { get; set; }
}
