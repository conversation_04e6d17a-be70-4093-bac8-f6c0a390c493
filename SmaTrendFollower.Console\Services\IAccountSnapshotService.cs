using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Provides access to the most recent account information from streaming updates
/// </summary>
public interface IAccountSnapshotService
{
    /// <summary>
    /// Gets the most recent account update from the streaming connection.
    /// Returns null until the first stream message is received.
    /// </summary>
    IAccount? Latest { get; }
}
