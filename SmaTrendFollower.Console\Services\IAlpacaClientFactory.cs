using Alpaca.Markets;

namespace SmaTrendFollower.Services;

public interface IAlpacaClientFactory : IDisposable
{
    IAlpacaTradingClient CreateTradingClient();
    IAlpacaDataClient CreateDataClient();
    IAlpacaStreamingClient CreateStreamingClient();
    IAlpacaDataStreamingClient CreateDataStreamingClient();
    IAlpacaRateLimitHelper GetRateLimitHelper();

    /// <summary>
    /// Creates a streaming client for the specified environment
    /// </summary>
    /// <param name="live">True for live trading environment, false for paper trading</param>
    /// <returns>Alpaca streaming client configured for the specified environment</returns>
    IAlpacaStreamingClient CreateStreamingClient(bool live);
}
