using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for recording bars to disk for back-test replay
/// </summary>
public interface IBarRecorder
{
    /// <summary>
    /// Records bars to disk in CSV format with Zstd compression
    /// </summary>
    /// <param name="bars">Collection of bars to record</param>
    /// <param name="symbol">Symbol name</param>
    /// <param name="tfUtcKey">Time frame key (e.g., "Day", "Minute")</param>
    void Record(IEnumerable<IBar> bars, string symbol, string tfUtcKey);
}
