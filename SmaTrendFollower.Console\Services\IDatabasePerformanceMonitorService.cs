using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for monitoring database performance metrics including connection pool usage,
/// query execution times, and error rates to help diagnose performance issues
/// </summary>
public interface IDatabasePerformanceMonitorService
{
    /// <summary>
    /// Record a database operation execution time
    /// </summary>
    void RecordOperationTime(string operationName, TimeSpan duration, bool success);

    /// <summary>
    /// Record a database connection pool event
    /// </summary>
    void RecordConnectionPoolEvent(string eventType, int activeConnections, int poolSize);

    /// <summary>
    /// Record a database error
    /// </summary>
    void RecordDatabaseError(string operationName, Exception exception);

    /// <summary>
    /// Get current performance metrics
    /// </summary>
    DatabasePerformanceMetrics GetCurrentMetrics();

    /// <summary>
    /// Reset all metrics (useful for testing or periodic resets)
    /// </summary>
    void ResetMetrics();

    /// <summary>
    /// Get performance summary for logging
    /// </summary>
    string GetPerformanceSummary();
}

/// <summary>
/// Database performance metrics
/// </summary>
public class DatabasePerformanceMetrics
{
    public long TotalOperations { get; set; }
    public long SuccessfulOperations { get; set; }
    public long FailedOperations { get; set; }
    public double SuccessRate => TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations * 100 : 0;
    
    public TimeSpan AverageOperationTime { get; set; }
    public TimeSpan MaxOperationTime { get; set; }
    public TimeSpan MinOperationTime { get; set; }
    
    public int CurrentActiveConnections { get; set; }
    public int MaxActiveConnections { get; set; }
    public int ConnectionPoolSize { get; set; }
    
    public long TimeoutErrors { get; set; }
    public long ConnectionErrors { get; set; }
    public long InvalidOperationErrors { get; set; }
    public long OtherErrors { get; set; }
    
    public DateTime LastResetTime { get; set; }
    public DateTime LastOperationTime { get; set; }
}

/// <summary>
/// Operation timing information
/// </summary>
public class OperationTiming
{
    public string OperationName { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public bool Success { get; set; }
    public DateTime Timestamp { get; set; }
}
