namespace SmaTrendFollower.Services;

/// <summary>
/// Service for retrieving earnings announcement dates for symbols
/// </summary>
public interface IEarningsCalendar
{
    /// <summary>
    /// Gets the next earnings announcement date for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol to check</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Next earnings date if available, null otherwise</returns>
    Task<DateTime?> GetNextEarningsAsync(string symbol, CancellationToken ct = default);
}
