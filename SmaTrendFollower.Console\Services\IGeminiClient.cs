namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Gemini AI client for sentiment analysis
/// </summary>
public interface IGeminiClient
{
    /// <summary>
    /// Gets sentiment score from Gemini AI for the given text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score between -1 (very negative) and +1 (very positive), or null if failed</returns>
    Task<double?> GetSentimentAsync(string text, CancellationToken ct);
}
