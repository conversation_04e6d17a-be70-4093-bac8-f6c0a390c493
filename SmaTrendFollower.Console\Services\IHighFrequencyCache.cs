using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// High-frequency cache interface for real-time trading data
/// Designed to reduce SQLite lock contention during high-volume signal generation
/// Uses Redis for sub-millisecond access to frequently accessed data
/// </summary>
public interface IHighFrequencyCache
{
    /// <summary>
    /// Cache current bar data for immediate access during signal generation
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <param name="bar">Current bar data</param>
    /// <param name="expiry">Cache expiry time (default: 5 minutes)</param>
    Task CacheBarSnapshotAsync(string symbol, IBar bar, TimeSpan? expiry = null);

    /// <summary>
    /// Get cached current bar data for a symbol
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <returns>Cached bar data or null if not found</returns>
    Task<IBar?> GetBarSnapshotAsync(string symbol);

    /// <summary>
    /// Cache multiple bar snapshots in a batch operation
    /// </summary>
    /// <param name="barSnapshots">Dictionary of symbol to bar data</param>
    /// <param name="expiry">Cache expiry time (default: 5 minutes)</param>
    Task CacheBatchBarSnapshotsAsync(Dictionary<string, IBar> barSnapshots, TimeSpan? expiry = null);

    /// <summary>
    /// Get multiple cached bar snapshots in a batch operation
    /// </summary>
    /// <param name="symbols">List of symbols to retrieve</param>
    /// <returns>Dictionary of symbol to bar data (null values for missing data)</returns>
    Task<Dictionary<string, IBar?>> GetBatchBarSnapshotsAsync(IEnumerable<string> symbols);

    /// <summary>
    /// Cache signal generation status to prevent duplicate processing
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <param name="status">Processing status</param>
    /// <param name="expiry">Cache expiry time (default: 2 minutes)</param>
    Task CacheSignalStatusAsync(string symbol, string status, TimeSpan? expiry = null);

    /// <summary>
    /// Get cached signal generation status
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <returns>Processing status or null if not found</returns>
    Task<string?> GetSignalStatusAsync(string symbol);

    /// <summary>
    /// Cache position sizing calculation to avoid recalculation
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <param name="positionSize">Calculated position size</param>
    /// <param name="expiry">Cache expiry time (default: 10 minutes)</param>
    Task CachePositionSizeAsync(string symbol, decimal positionSize, TimeSpan? expiry = null);

    /// <summary>
    /// Get cached position sizing calculation
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <returns>Cached position size or null if not found</returns>
    Task<decimal?> GetPositionSizeAsync(string symbol);

    /// <summary>
    /// Clear all cached data for a specific symbol
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    Task ClearSymbolCacheAsync(string symbol);

    /// <summary>
    /// Clear all high-frequency cache data
    /// </summary>
    Task ClearAllCacheAsync();

    /// <summary>
    /// Get cache statistics for monitoring
    /// </summary>
    /// <returns>Cache statistics including hit rates and memory usage</returns>
    Task<HighFrequencyCacheStats> GetCacheStatsAsync();
}

/// <summary>
/// Statistics for high-frequency cache monitoring
/// </summary>
public class HighFrequencyCacheStats
{
    public int TotalKeys { get; set; }
    public int BarSnapshotKeys { get; set; }
    public int SignalStatusKeys { get; set; }
    public int PositionSizeKeys { get; set; }
    public long MemoryUsageBytes { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
