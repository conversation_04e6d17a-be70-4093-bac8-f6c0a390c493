namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for guarding live trading operations to ensure they only occur during market hours.
/// This service restricts TRADING operations only - data operations, API calls, and background
/// processes (like universe refresh) can run outside market hours with appropriate staleness handling.
/// </summary>
public interface IMarketSessionGuard
{
    /// <summary>
    /// Determines if live trading is allowed right now.
    /// Returns true only during market hours (9:30 AM - 4:00 PM ET, Monday-Friday).
    /// </summary>
    /// <returns>True if live trading is allowed, false otherwise</returns>
    Task<bool> CanTradeNowAsync();

    /// <summary>
    /// Gets the reason why trading is blocked (if CanTradeNowAsync returns false).
    /// Empty string if trading is allowed.
    /// </summary>
    string Reason { get; }
}
