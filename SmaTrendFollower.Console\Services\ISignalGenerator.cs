using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

public interface ISignalGenerator
{
    Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default);
}

/// <summary>
/// Enhanced signal generator interface with additional capabilities
/// Backward compatible with ISignalGenerator
/// </summary>
public interface IEnhancedSignalGenerator : ISignalGenerator
{
    /// <summary>
    /// Generates signals with enhanced error recovery and partial success handling
    /// </summary>
    Task<RobustSignalResult> GenerateSignalsRobustlyAsync(int topN = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets error statistics for monitoring and troubleshooting
    /// </summary>
    SymbolErrorStatistics GetErrorStatistics();

    /// <summary>
    /// Clears error history for a specific symbol
    /// </summary>
    void ClearErrorHistory(string symbol);
}
