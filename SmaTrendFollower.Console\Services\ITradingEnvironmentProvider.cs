namespace SmaTrendFollower.Services;

/// <summary>
/// Provides trading environment detection (live vs paper trading)
/// </summary>
public interface ITradingEnvironmentProvider
{
    /// <summary>
    /// Determines if the current environment is configured for live trading
    /// </summary>
    bool IsLive { get; }
    
    /// <summary>
    /// Gets the current trading environment name for logging
    /// </summary>
    string EnvironmentName { get; }
}
