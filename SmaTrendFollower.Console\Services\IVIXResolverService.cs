using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced VIX resolver service with comprehensive 7-level fallback system
/// Enforces 15-minute data freshness requirement for trading decisions
/// </summary>
public interface IVIXResolverService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when VIX data is successfully retrieved
    /// </summary>
    event EventHandler<VixDataRetrievedEventArgs>? VixDataRetrieved;
    
    /// <summary>
    /// Fired when VIX fallback level changes
    /// </summary>
    event EventHandler<VixFallbackLevelChangedEventArgs>? FallbackLevelChanged;
    
    /// <summary>
    /// Fired when VIX data becomes stale
    /// </summary>
    event EventHandler<VixDataStaleEventArgs>? VixDataStale;
    
    /// <summary>
    /// Fired when trading should be halted due to VIX data unavailability
    /// </summary>
    event EventHandler<TradingHaltEventArgs>? TradingHaltRequested;
    
    // === Core Methods ===
    
    /// <summary>
    /// Get current VIX value using 7-level fallback system
    /// Enforces 15-minute freshness requirement
    /// </summary>
    Task<VixResolverResult> GetCurrentVixAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get VIX value with specific freshness requirement
    /// </summary>
    Task<VixResolverResult> GetVixWithFreshnessAsync(TimeSpan maxAge, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Force refresh VIX data from all sources
    /// </summary>
    Task<VixResolverResult> RefreshVixDataAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Check if current VIX data is fresh enough for trading
    /// </summary>
    Task<bool> IsVixDataFreshAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get VIX data age in minutes
    /// </summary>
    Task<double?> GetVixDataAgeMinutesAsync(CancellationToken cancellationToken = default);
    
    // === Fallback Level Methods ===
    
    /// <summary>
    /// Level 1: Polygon /v3/snapshot?ticker=I:VIX (Real-time indices data)
    /// </summary>
    Task<VixDataPoint?> GetVixFromPolygonLastTradeAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Level 2: Polygon WebSocket AM.I:VIX
    /// </summary>
    Task<VixDataPoint?> GetVixFromPolygonWebSocketAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Level 3: Synthetic VIX via VXX+UVXY from Alpaca
    /// </summary>
    Task<VixDataPoint?> GetSyntheticVixFromAlpacaAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Level 4: Synthetic VIX via VXX+UVXY from Polygon
    /// </summary>
    Task<VixDataPoint?> GetSyntheticVixFromPolygonAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Level 5: Google Search API + GPT extraction
    /// </summary>
    Task<VixDataPoint?> GetVixFromGoogleSearchAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Level 6: Brave Search API + GPT extraction
    /// </summary>
    Task<VixDataPoint?> GetVixFromBraveSearchAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Level 7: Redis cached value (stale, <1hr old)
    /// </summary>
    Task<VixDataPoint?> GetVixFromCacheAsync(CancellationToken cancellationToken = default);
    
    // === Status and Configuration ===
    
    /// <summary>
    /// Get current fallback level being used
    /// </summary>
    VixFallbackLevel GetCurrentFallbackLevel();
    
    /// <summary>
    /// Get fallback level statistics
    /// </summary>
    Task<VixFallbackStatistics> GetFallbackStatisticsAsync();
    
    /// <summary>
    /// Update configuration
    /// </summary>
    Task UpdateConfigurationAsync(VixResolverConfig config);
    
    /// <summary>
    /// Get VIX retrieval history
    /// </summary>
    Task<IEnumerable<VixRetrievalHistoryEntry>> GetRetrievalHistoryAsync(int hours = 24);
}

/// <summary>
/// VIX fallback levels in order of preference
/// User preference: Polygon primary -> web scraping -> synthetic VIX -> cache -> halt
/// </summary>
public enum VixFallbackLevel
{
    PolygonLastTrade = 1,
    PolygonWebSocket = 2,
    GoogleSearch = 3,          // Moved up - web scraping has real-time data
    BraveSearch = 4,           // Moved up - web scraping has real-time data
    SyntheticAlpaca = 5,       // Moved down - synthetic calculations can be inaccurate
    SyntheticPolygon = 6,      // Moved down - synthetic calculations can be inaccurate
    RedisCache = 7,
    TradingHalt = 8
}

/// <summary>
/// VIX resolver result with metadata
/// </summary>
public class VixResolverResult
{
    public decimal? VixValue { get; set; }
    public VixFallbackLevel FallbackLevel { get; set; }
    public DateTime RetrievedAt { get; set; }
    public TimeSpan DataAge { get; set; }
    public bool IsFresh { get; set; }
    public bool ShouldHaltTrading { get; set; }
    public string Source { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public VixDataQuality Quality { get; set; }
}

/// <summary>
/// VIX data point with timestamp and metadata
/// </summary>
public class VixDataPoint
{
    public decimal Value { get; set; }
    public DateTime Timestamp { get; set; }
    public string Source { get; set; } = string.Empty;
    public VixDataQuality Quality { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// VIX data quality classification
/// </summary>
public enum VixDataQuality
{
    RealTime,
    NearRealTime,
    Delayed,
    Synthetic,
    Estimated,
    Stale,
    Unknown
}

/// <summary>
/// VIX resolver configuration
/// </summary>
public record VixResolverConfig(
    TimeSpan FreshnessThreshold = default,
    TimeSpan CacheStaleThreshold = default,
    int MaxRetryAttempts = 3,
    TimeSpan RetryDelay = default,
    bool EnableSyntheticCalculation = true,
    bool EnableWebScraping = true,
    bool AllowStaleCache = true,
    bool HaltTradingOnFailure = true
)
{
    public VixResolverConfig() : this(
        FreshnessThreshold: TimeSpan.FromMinutes(30),
        CacheStaleThreshold: TimeSpan.FromHours(1),
        RetryDelay: TimeSpan.FromSeconds(5)
    ) { }
}

/// <summary>
/// VIX fallback statistics
/// </summary>
public class VixFallbackStatistics
{
    public Dictionary<VixFallbackLevel, int> UsageCount { get; set; } = new();
    public Dictionary<VixFallbackLevel, TimeSpan> AverageResponseTime { get; set; } = new();
    public Dictionary<VixFallbackLevel, double> SuccessRate { get; set; } = new();
    public DateTime LastUpdated { get; set; }
    public VixFallbackLevel MostReliableLevel { get; set; }
    public VixFallbackLevel CurrentPreferredLevel { get; set; }
}

/// <summary>
/// VIX retrieval history entry
/// </summary>
public class VixRetrievalHistoryEntry
{
    public DateTime Timestamp { get; set; }
    public decimal? VixValue { get; set; }
    public VixFallbackLevel FallbackLevel { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public bool Success { get; set; }
    public string Source { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
}

/// <summary>
/// Event args for VIX data retrieved
/// </summary>
public class VixDataRetrievedEventArgs : EventArgs
{
    public decimal VixValue { get; set; }
    public VixFallbackLevel FallbackLevel { get; set; }
    public DateTime RetrievedAt { get; set; }
    public TimeSpan DataAge { get; set; }
    public string Source { get; set; } = string.Empty;
}

/// <summary>
/// Event args for fallback level changed
/// </summary>
public class VixFallbackLevelChangedEventArgs : EventArgs
{
    public VixFallbackLevel PreviousLevel { get; set; }
    public VixFallbackLevel NewLevel { get; set; }
    public string Reason { get; set; } = string.Empty;
    public DateTime ChangedAt { get; set; }
}

/// <summary>
/// Event args for VIX data stale
/// </summary>
public class VixDataStaleEventArgs : EventArgs
{
    public TimeSpan DataAge { get; set; }
    public TimeSpan FreshnessThreshold { get; set; }
    public DateTime LastUpdated { get; set; }
    public VixFallbackLevel CurrentLevel { get; set; }
}

/// <summary>
/// Event args for trading halt
/// </summary>
public class TradingHaltEventArgs : EventArgs
{
    public string Reason { get; set; } = string.Empty;
    public DateTime HaltRequestedAt { get; set; }
    public TimeSpan DataAge { get; set; }
    public VixFallbackLevel LastAttemptedLevel { get; set; }
}
