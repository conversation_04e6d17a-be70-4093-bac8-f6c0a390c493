using Microsoft.Extensions.Logging;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Monitoring;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Intelligent batch processor that adapts batch sizes based on performance and rate limits
/// </summary>
public sealed class IntelligentBatchProcessor<T> : IBatchProcessor<T>, IDisposable where T : notnull
{
    private readonly IAdaptiveRateLimitingService _rateLimitService;
    private readonly string _provider;
    private readonly Func<IEnumerable<T>, CancellationToken, Task<IDictionary<T, bool>>> _batchOperation;
    private readonly BatchProcessorOptions _options;
    private readonly ILogger _logger;
    private readonly SemaphoreSlim _processingLock = new(1, 1);
    
    private volatile int _currentBatchSize;
    private long _recentSuccessRateBits = BitConverter.DoubleToInt64Bits(1.0);
    private readonly ConcurrentQueue<BatchResult> _recentResults = new();

    private double RecentSuccessRate
    {
        get => BitConverter.Int64BitsToDouble(Interlocked.Read(ref _recentSuccessRateBits));
        set => Interlocked.Exchange(ref _recentSuccessRateBits, BitConverter.DoubleToInt64Bits(value));
    }

    public IntelligentBatchProcessor(
        IAdaptiveRateLimitingService rateLimitService,
        string provider,
        Func<IEnumerable<T>, CancellationToken, Task<IDictionary<T, bool>>> batchOperation,
        BatchProcessorOptions options,
        ILogger logger)
    {
        _rateLimitService = rateLimitService;
        _provider = provider;
        _batchOperation = batchOperation;
        _options = options;
        _logger = logger;
        _currentBatchSize = options.InitialBatchSize;
    }

    /// <summary>
    /// Processes items in intelligent batches with adaptive sizing
    /// </summary>
    public async Task<IDictionary<T, BatchProcessResult<TResult>>> ProcessBatchAsync<TResult>(
        IEnumerable<T> items,
        CancellationToken cancellationToken = default)
    {
        // For now, only support bool results - cast at the end
        var boolResults = await ProcessBatchAsync(items, cancellationToken);
        var results = new Dictionary<T, BatchProcessResult<TResult>>();

        foreach (var (key, value) in boolResults)
        {
            if (value.IsSuccess && value.Data is TResult result)
            {
                results[key] = BatchProcessResult<TResult>.Success(result, value.ProcessingTime);
            }
            else if (value.IsSuccess && typeof(TResult) == typeof(bool))
            {
                results[key] = BatchProcessResult<TResult>.Success((TResult)(object)value.Data!, value.ProcessingTime);
            }
            else
            {
                results[key] = BatchProcessResult<TResult>.Failure(value.ErrorMessage ?? "Type conversion failed", value.ProcessingTime);
            }
        }

        return results;
    }

    /// <summary>
    /// Processes items in intelligent batches with adaptive sizing (bool-specific implementation)
    /// </summary>
    public async Task<IDictionary<T, BatchProcessResult<bool>>> ProcessBatchAsync(
        IEnumerable<T> items,
        CancellationToken cancellationToken = default)
    {
        var itemList = items.ToList();
        var results = new ConcurrentDictionary<T, BatchProcessResult<bool>>();
        
        if (!itemList.Any())
            return results;

        _logger.LogInformation("Starting intelligent batch processing for {Count} items with provider {Provider}", 
            itemList.Count, _provider);

        var stopwatch = Stopwatch.StartNew();
        var batches = CreateAdaptiveBatches(itemList);
        var processedCount = 0;
        var successCount = 0;

        try
        {
            // Process batches with controlled concurrency
            var semaphore = new SemaphoreSlim(_options.MaxConcurrentBatches, _options.MaxConcurrentBatches);
            var batchTasks = batches.Select(async (batch, index) =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    var batchResults = await ProcessSingleBatchAsync(batch, index, cancellationToken);
                    
                    foreach (var (item, result) in batchResults)
                    {
                        results[item] = result;
                        Interlocked.Increment(ref processedCount);
                        if (result.IsSuccess)
                            Interlocked.Increment(ref successCount);
                    }
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(batchTasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during batch processing for provider {Provider}", _provider);
            
            // Mark unprocessed items as failed
            foreach (var item in itemList.Where(i => !results.ContainsKey(i)))
            {
                results[item] = BatchProcessResult<bool>.Failure($"Batch processing error: {ex.Message}");
            }
        }

        stopwatch.Stop();
        
        var finalSuccessRate = processedCount > 0 ? (double)successCount / processedCount : 0.0;
        
        _logger.LogInformation("Batch processing completed for {Provider}: {Processed}/{Total} items processed, " +
                             "{Success:P2} success rate, {Duration:F1}s total time",
            _provider, processedCount, itemList.Count, finalSuccessRate, stopwatch.Elapsed.TotalSeconds);

        // Record metrics
        MetricsRegistry.BatchProcessingDuration
            .WithLabels(_provider)
            .Observe(stopwatch.Elapsed.TotalSeconds);
        
        MetricsRegistry.BatchProcessingResults
            .WithLabels(_provider, "success")
            .Set(successCount);
        
        MetricsRegistry.BatchProcessingResults
            .WithLabels(_provider, "failure")
            .Set(processedCount - successCount);

        // Update batch size based on results
        await UpdateBatchSizeAsync(finalSuccessRate, stopwatch.Elapsed, batches.Count);

        return results;
    }

    private List<List<T>> CreateAdaptiveBatches(List<T> items)
    {
        var batches = new List<List<T>>();
        var currentBatchSize = _currentBatchSize;
        
        for (int i = 0; i < items.Count; i += currentBatchSize)
        {
            var batchItems = items.Skip(i).Take(currentBatchSize).ToList();
            batches.Add(batchItems);
        }

        _logger.LogDebug("Created {BatchCount} batches with size {BatchSize} for {TotalItems} items",
            batches.Count, currentBatchSize, items.Count);

        return batches;
    }

    private async Task<IDictionary<T, BatchProcessResult<bool>>> ProcessSingleBatchAsync(
        List<T> batch,
        int batchIndex,
        CancellationToken cancellationToken)
    {
        var results = new Dictionary<T, BatchProcessResult<bool>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Acquire rate limit permit
            var rateLimitResult = await _rateLimitService.TryAcquireAsync(
                _provider, 
                "batch_operation", 
                priority: 1, 
                cancellationToken);

            if (!rateLimitResult.IsSuccess)
            {
                _logger.LogWarning("Rate limit acquisition failed for batch {Index}: {Error}", 
                    batchIndex, rateLimitResult.ErrorMessage);

                foreach (var item in batch)
                {
                    results[item] = BatchProcessResult<bool>.Failure($"Rate limit: {rateLimitResult.ErrorMessage}");
                }
                return results;
            }

            try
            {
                // Execute the batch operation
                var operationResults = await _batchOperation(batch, cancellationToken);
                
                // Process results
                foreach (var item in batch)
                {
                    if (operationResults.TryGetValue(item, out var success))
                    {
                        results[item] = success 
                            ? BatchProcessResult<bool>.Success(true)
                            : BatchProcessResult<bool>.Failure("Operation returned false");
                    }
                    else
                    {
                        results[item] = BatchProcessResult<bool>.Failure("No result returned for item");
                    }
                }

                var batchSuccessCount = results.Values.Count(r => r.IsSuccess);
                var batchSuccessRate = (double)batchSuccessCount / batch.Count;

                _logger.LogDebug("Batch {Index} completed: {Success}/{Total} items successful ({Rate:P2})",
                    batchIndex, batchSuccessCount, batch.Count, batchSuccessRate);

                // Record batch result for adaptive sizing
                RecordBatchResult(batch.Count, batchSuccessRate, stopwatch.Elapsed);

                // Release rate limit with success info
                _rateLimitService.Release(_provider, "batch_operation", batchSuccessRate > 0.5, stopwatch.Elapsed);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Batch operation failed for batch {Index}", batchIndex);

                foreach (var item in batch)
                {
                    results[item] = BatchProcessResult<bool>.Failure($"Batch operation error: {ex.Message}");
                }

                // Release rate limit with failure info
                _rateLimitService.Release(_provider, "batch_operation", false, stopwatch.Elapsed, ex.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error processing batch {Index}", batchIndex);

            foreach (var item in batch)
            {
                results[item] = BatchProcessResult<bool>.Failure($"Critical error: {ex.Message}");
            }
        }

        return results;
    }

    private void RecordBatchResult(int batchSize, double successRate, TimeSpan duration)
    {
        _recentResults.Enqueue(new BatchResult(DateTime.UtcNow, batchSize, successRate, duration));
        
        // Keep only recent results (last 10 batches)
        while (_recentResults.Count > 10)
        {
            _recentResults.TryDequeue(out _);
        }
        
        // Update recent success rate
        var recentResults = _recentResults.ToArray();
        if (recentResults.Any())
        {
            RecentSuccessRate = recentResults.Average(r => r.SuccessRate);
        }
    }

    private async Task UpdateBatchSizeAsync(double overallSuccessRate, TimeSpan totalDuration, int batchCount)
    {
        if (!await _processingLock.WaitAsync(100))
            return; // Skip if another update is in progress

        try
        {
            var oldBatchSize = _currentBatchSize;
            var newBatchSize = oldBatchSize;

            // Adjust batch size based on success rate and performance
            if (overallSuccessRate < _options.MinSuccessRateThreshold)
            {
                // Decrease batch size if success rate is low
                newBatchSize = Math.Max(_options.MinBatchSize, (int)(oldBatchSize * 0.8));
                _logger.LogInformation("Decreasing batch size due to low success rate ({Rate:P2}): {Old} -> {New}",
                    overallSuccessRate, oldBatchSize, newBatchSize);
            }
            else if (overallSuccessRate > _options.TargetSuccessRateThreshold)
            {
                // Consider increasing batch size if success rate is high
                var avgDurationPerBatch = totalDuration.TotalSeconds / batchCount;
                
                if (avgDurationPerBatch < _options.TargetBatchDurationSeconds)
                {
                    newBatchSize = Math.Min(_options.MaxBatchSize, (int)(oldBatchSize * 1.2));
                    _logger.LogDebug("Increasing batch size due to high success rate and fast processing: {Old} -> {New}",
                        oldBatchSize, newBatchSize);
                }
            }

            _currentBatchSize = newBatchSize;

            if (newBatchSize != oldBatchSize)
            {
                MetricsRegistry.BatchSizeAdjustments
                    .WithLabels(_provider, newBatchSize > oldBatchSize ? "increase" : "decrease")
                    .Inc();
            }
        }
        finally
        {
            _processingLock.Release();
        }
    }

    public void Dispose()
    {
        _processingLock?.Dispose();
    }
}

/// <summary>
/// Interface for batch processors
/// </summary>
public interface IBatchProcessor<T> where T : notnull
{
    Task<IDictionary<T, BatchProcessResult<TResult>>> ProcessBatchAsync<TResult>(
        IEnumerable<T> items,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of processing a single item in a batch
/// </summary>
public sealed class BatchProcessResult<T>
{
    public bool IsSuccess { get; private set; }
    public T? Data { get; private set; }
    public string? ErrorMessage { get; private set; }
    public TimeSpan ProcessingTime { get; private set; }

    private BatchProcessResult() { }

    public static BatchProcessResult<T> Success(T data, TimeSpan? processingTime = null) => 
        new() { IsSuccess = true, Data = data, ProcessingTime = processingTime ?? TimeSpan.Zero };

    public static BatchProcessResult<T> Failure(string errorMessage, TimeSpan? processingTime = null) => 
        new() { IsSuccess = false, ErrorMessage = errorMessage, ProcessingTime = processingTime ?? TimeSpan.Zero };
}

/// <summary>
/// Record of a batch processing result for adaptive sizing
/// </summary>
internal sealed record BatchResult(DateTime Timestamp, int BatchSize, double SuccessRate, TimeSpan Duration);
