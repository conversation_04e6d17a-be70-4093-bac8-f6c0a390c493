using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Json;
using System.Text.Json.Nodes;
using System.Threading;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for performing sentiment analysis using LLM providers (OpenAI or Gemini).
/// Used as an overlay on top of FinBERT for low-confidence cases.
/// Includes circuit breaker logic to prevent excessive API calls when providers are failing.
/// </summary>
public class LlmSentimentService
{
    private readonly IHttpClientFactory _http;
    private readonly LlmSentimentOptions _opt;
    private readonly ILogger<LlmSentimentService> _log;
    private readonly IApiTrackingService _apiTracking;

    // Circuit breaker state tracking
    private int _openAiFailureCount = 0;
    private int _geminiFailureCount = 0;
    private DateTime _openAiLastFailure = DateTime.MinValue;
    private DateTime _geminiLastFailure = DateTime.MinValue;
    private const int MaxFailuresBeforeCircuitBreak = 5;
    private static readonly TimeSpan CircuitBreakerDuration = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Initializes the LLM sentiment service with HTTP client factory and configuration.
    /// </summary>
    /// <param name="http">HTTP client factory for API calls</param>
    /// <param name="opt">LLM sentiment configuration options</param>
    /// <param name="apiTracking">API tracking service for monitoring API usage</param>
    /// <param name="log">Logger for service operations</param>
    public LlmSentimentService(IHttpClientFactory http,
                               IOptions<LlmSentimentOptions> opt,
                               IApiTrackingService apiTracking,
                               ILogger<LlmSentimentService> log)
    {
        _http = http ?? throw new ArgumentNullException(nameof(http));
        _opt = opt?.Value ?? throw new ArgumentNullException(nameof(opt));
        _apiTracking = apiTracking ?? throw new ArgumentNullException(nameof(apiTracking));
        _log = log ?? throw new ArgumentNullException(nameof(log));
    }

    /// <summary>
    /// Gets sentiment score from the configured LLM provider.
    /// Returns null if the call fails or times out.
    /// </summary>
    /// <param name="text">Text to analyze for sentiment</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score (-1.0 to ****) or null if failed</returns>
    public virtual async Task<double?> GetSentimentAsync(string text, CancellationToken ct = default)
    {
        try
        {
            return _opt.Provider switch
            {
                "OpenAI" => await QueryOpenAiAsync(text, ct),
                "Gemini" => await QueryGeminiAsync(text, ct),
                _ => null
            };
        }
        catch (Exception ex)
        {
            _log.LogDebug(ex, "LLM sentiment call failed for provider {Provider}", _opt.Provider);
            return null;
        }
    }

    /// <summary>
    /// Gets sentiment score with automatic fallback between OpenAI and Gemini.
    /// Tries OpenAI first, then falls back to Gemini if OpenAI fails.
    /// Includes circuit breaker logic to prevent excessive API calls when providers are failing.
    /// </summary>
    /// <param name="text">Text to analyze for sentiment</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score (-1.0 to ****) or null if both providers fail</returns>
    public virtual async Task<double?> GetSentimentWithFallbackAsync(string text, CancellationToken ct = default)
    {
        // Check OpenAI circuit breaker
        if (!IsOpenAiCircuitBreakerOpen())
        {
            try
            {
                var openAiResult = await QueryOpenAiAsync(text, ct);
                if (openAiResult.HasValue)
                {
                    // Reset failure count on success
                    Interlocked.Exchange(ref _openAiFailureCount, 0);
                    _log.LogDebug("OpenAI sentiment successful for text: {Text}", text.Length > 50 ? text[..50] + "..." : text);
                    return openAiResult;
                }
            }
            catch (Exception ex)
            {
                RecordOpenAiFailure();
                _log.LogDebug(ex, "OpenAI sentiment failed (failures: {Count}), trying Gemini fallback", _openAiFailureCount);
            }
        }
        else
        {
            _log.LogDebug("OpenAI circuit breaker is open, skipping to Gemini");
        }

        // Check Gemini circuit breaker
        if (!IsGeminiCircuitBreakerOpen())
        {
            try
            {
                var geminiResult = await QueryGeminiAsync(text, ct);
                if (geminiResult.HasValue)
                {
                    // Reset failure count on success
                    Interlocked.Exchange(ref _geminiFailureCount, 0);
                    _log.LogDebug("Gemini fallback sentiment successful for text: {Text}", text.Length > 50 ? text[..50] + "..." : text);
                    return geminiResult;
                }
            }
            catch (Exception ex)
            {
                RecordGeminiFailure();
                _log.LogDebug(ex, "Gemini fallback also failed (failures: {Count})", _geminiFailureCount);
            }
        }
        else
        {
            _log.LogDebug("Gemini circuit breaker is open, no LLM sentiment available");
        }

        _log.LogDebug("Both OpenAI and Gemini failed or circuit breakers are open for sentiment analysis");
        return null;
    }

    /// <summary>
    /// Queries OpenAI API for sentiment analysis.
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score or null if failed</returns>
    private async Task<double?> QueryOpenAiAsync(string text, CancellationToken ct)
    {
        // Start API tracking
        var apiRequest = new ApiCallRequest(
            Provider: "OpenAI",
            Operation: "sentiment_analysis",
            RequestData: text.Length > 100 ? text[..100] + "..." : text
        );

        using var tracker = _apiTracking.StartTracking(apiRequest);

        try
        {
            var body = new
            {
                model = _opt.Model,
                temperature = 0,
                messages = new[]
                {
                    new { role = "system", content = "Return JSON {\"label\":\"positive|negative|neutral\",\"score\":0-1} only." },
                    new { role = "user", content = text.Length > 400 ? text[..400] : text }
                }
            };

            var cli = _http.CreateClient("OpenAI");
            var res = await cli.PostAsJsonAsync("/v1/chat/completions", body, ct);
            res.EnsureSuccessStatusCode();

            var json = await res.Content.ReadFromJsonAsync<JsonObject>(cancellationToken: ct);
            var msg = json?["choices"]?[0]?["message"]?["content"]?.GetValue<string>() ?? "";

            if (string.IsNullOrWhiteSpace(msg))
            {
                _log.LogWarning("OpenAI returned empty response");

                // Track failed API call
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: false,
                    StatusCode: (int)res.StatusCode,
                    ErrorMessage: "Empty response from OpenAI API"
                ));
                return null;
            }

            var doc = JsonNode.Parse(msg);
            if (doc == null)
            {
                _log.LogWarning("Failed to parse OpenAI response as JSON: {Response}", msg);

                // Track parsing failure
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: false,
                    StatusCode: (int)res.StatusCode,
                    ErrorMessage: "Failed to parse JSON response",
                    ResponseData: msg.Length > 200 ? msg[..200] + "..." : msg
                ));
                return null;
            }

            var label = doc["label"]?.GetValue<string>();
            var score = doc["score"]?.GetValue<double>() ?? 0.0;

            var sentimentScore = label switch
            {
                "positive" => score,
                "negative" => -score,
                _ => 0.0
            };

            // Extract token usage if available
            var usage = json?["usage"];
            var tokensUsed = usage?["total_tokens"]?.GetValue<int>();
            var estimatedCost = tokensUsed.HasValue ? tokensUsed.Value * 0.000002m : (decimal?)null; // Rough estimate for GPT-3.5

            // Track successful API call
            await tracker.CompleteAsync(new ApiCallResult(
                Success: true,
                StatusCode: (int)res.StatusCode,
                ResponseData: $"label:{label} score:{score:F3}",
                TokensUsed: tokensUsed,
                Cost: estimatedCost,
                Metadata: new Dictionary<string, object>
                {
                    ["model"] = _opt.Model,
                    ["sentiment_score"] = sentimentScore,
                    ["label"] = label ?? "unknown",
                    ["confidence"] = score
                }
            ));

            return sentimentScore;
        }
        catch (Exception ex)
        {
            // Track failed API call
            await tracker.CompleteAsync(new ApiCallResult(
                Success: false,
                ErrorMessage: ex.Message
            ));
            throw;
        }
    }

    /// <summary>
    /// Queries Gemini API for sentiment analysis.
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Sentiment score or null if failed</returns>
    private async Task<double?> QueryGeminiAsync(string text, CancellationToken ct)
    {
        // Start API tracking
        var apiRequest = new ApiCallRequest(
            Provider: "Gemini",
            Operation: "sentiment_analysis",
            RequestData: text.Length > 100 ? text[..100] + "..." : text
        );

        using var tracker = _apiTracking.StartTracking(apiRequest);

        try
        {
            var body = new
            {
                contents = new[] { new { parts = new[] { new { text } } } },
                generationConfig = new { temperature = 0 }
            };

            var cli = _http.CreateClient("Gemini");
            var res = await cli.PostAsJsonAsync($"/v1beta/models/{_opt.Model}:generateContent", body, ct);
            res.EnsureSuccessStatusCode();

            var json = await res.Content.ReadFromJsonAsync<JsonObject>(cancellationToken: ct);
            var msg = json?["candidates"]?[0]?["content"]?["parts"]?[0]?["text"]?.GetValue<string>() ?? "";

            if (string.IsNullOrWhiteSpace(msg))
            {
                _log.LogWarning("Gemini returned empty response");

                // Track failed API call
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: false,
                    StatusCode: (int)res.StatusCode,
                    ErrorMessage: "Empty response from Gemini API"
                ));
                return null;
            }

            var doc = JsonNode.Parse(msg);
            if (doc == null)
            {
                _log.LogWarning("Failed to parse Gemini response as JSON: {Response}", msg);

                // Track parsing failure
                await tracker.CompleteAsync(new ApiCallResult(
                    Success: false,
                    StatusCode: (int)res.StatusCode,
                    ErrorMessage: "Failed to parse JSON response",
                    ResponseData: msg.Length > 200 ? msg[..200] + "..." : msg
                ));
                return null;
            }

            var label = doc["label"]?.GetValue<string>();
            var score = doc["score"]?.GetValue<double>() ?? 0.0;

            var sentimentScore = label switch
            {
                "positive" => score,
                "negative" => -score,
                _ => 0.0
            };

            // Extract token usage if available (Gemini doesn't always provide this)
            var usage = json?["usageMetadata"];
            var tokensUsed = usage?["totalTokenCount"]?.GetValue<int>();
            var estimatedCost = tokensUsed.HasValue ? tokensUsed.Value * 0.000001m : (decimal?)null; // Rough estimate for Gemini

            // Track successful API call
            await tracker.CompleteAsync(new ApiCallResult(
                Success: true,
                StatusCode: (int)res.StatusCode,
                ResponseData: $"label:{label} score:{score:F3}",
                TokensUsed: tokensUsed,
                Cost: estimatedCost,
                Metadata: new Dictionary<string, object>
                {
                    ["model"] = _opt.Model,
                    ["sentiment_score"] = sentimentScore,
                    ["label"] = label ?? "unknown",
                    ["confidence"] = score
                }
            ));

            return sentimentScore;
        }
        catch (Exception ex)
        {
            // Track failed API call
            await tracker.CompleteAsync(new ApiCallResult(
                Success: false,
                ErrorMessage: ex.Message
            ));
            throw;
        }
    }

    /// <summary>
    /// Checks if OpenAI circuit breaker is open (too many recent failures).
    /// </summary>
    private bool IsOpenAiCircuitBreakerOpen()
    {
        if (_openAiFailureCount < MaxFailuresBeforeCircuitBreak)
            return false;

        var timeSinceLastFailure = DateTime.UtcNow - _openAiLastFailure;
        if (timeSinceLastFailure > CircuitBreakerDuration)
        {
            // Reset circuit breaker after timeout
            Interlocked.Exchange(ref _openAiFailureCount, 0);
            _log.LogInformation("OpenAI circuit breaker reset after {Duration} minutes", CircuitBreakerDuration.TotalMinutes);
            return false;
        }

        return true;
    }

    /// <summary>
    /// Checks if Gemini circuit breaker is open (too many recent failures).
    /// </summary>
    private bool IsGeminiCircuitBreakerOpen()
    {
        if (_geminiFailureCount < MaxFailuresBeforeCircuitBreak)
            return false;

        var timeSinceLastFailure = DateTime.UtcNow - _geminiLastFailure;
        if (timeSinceLastFailure > CircuitBreakerDuration)
        {
            // Reset circuit breaker after timeout
            Interlocked.Exchange(ref _geminiFailureCount, 0);
            _log.LogInformation("Gemini circuit breaker reset after {Duration} minutes", CircuitBreakerDuration.TotalMinutes);
            return false;
        }

        return true;
    }

    /// <summary>
    /// Records an OpenAI failure and potentially opens the circuit breaker.
    /// </summary>
    private void RecordOpenAiFailure()
    {
        var newCount = Interlocked.Increment(ref _openAiFailureCount);
        _openAiLastFailure = DateTime.UtcNow;

        if (newCount == MaxFailuresBeforeCircuitBreak)
        {
            _log.LogWarning("OpenAI circuit breaker OPENED after {Count} consecutive failures. Will retry in {Duration} minutes.",
                MaxFailuresBeforeCircuitBreak, CircuitBreakerDuration.TotalMinutes);
        }
    }

    /// <summary>
    /// Records a Gemini failure and potentially opens the circuit breaker.
    /// </summary>
    private void RecordGeminiFailure()
    {
        var newCount = Interlocked.Increment(ref _geminiFailureCount);
        _geminiLastFailure = DateTime.UtcNow;

        if (newCount == MaxFailuresBeforeCircuitBreak)
        {
            _log.LogWarning("Gemini circuit breaker OPENED after {Count} consecutive failures. Will retry in {Duration} minutes.",
                MaxFailuresBeforeCircuitBreak, CircuitBreakerDuration.TotalMinutes);
        }
    }
}
