using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for handling market calendar logic, including trading days and holidays
/// </summary>
public interface IMarketCalendarService
{
    /// <summary>
    /// Gets the last trading day, avoiding weekends and market holidays
    /// </summary>
    DateTime GetLastTradingDay();
    
    /// <summary>
    /// Gets the last trading day before a specific date
    /// </summary>
    DateTime GetLastTradingDayBefore(DateTime date);
    
    /// <summary>
    /// Checks if a date is a market holiday
    /// </summary>
    bool IsMarketHoliday(DateTime date);
    
    /// <summary>
    /// Checks if a date is a trading day (not weekend or holiday)
    /// </summary>
    bool IsTradingDay(DateTime date);
}

public class MarketCalendarService : IMarketCalendarService
{
    private readonly ILogger<MarketCalendarService> _logger;

    public MarketCalendarService(ILogger<MarketCalendarService> logger)
    {
        _logger = logger;
    }

    public DateTime GetLastTradingDay()
    {
        return GetLastTradingDayBefore(DateTime.UtcNow.Date);
    }

    public DateTime GetLastTradingDayBefore(DateTime date)
    {
        // Go back until we find a trading day
        for (int i = 1; i <= 10; i++) // Look back up to 10 days
        {
            var candidateDate = date.AddDays(-i);
            
            if (IsTradingDay(candidateDate))
            {
                _logger.LogDebug("Last trading day before {Date} is {TradingDay}", 
                    date.ToString("yyyy-MM-dd"), candidateDate.ToString("yyyy-MM-dd"));
                return candidateDate;
            }
        }
        
        // Fallback: just use 5 days ago if we can't find a good trading day
        var fallback = date.AddDays(-5);
        _logger.LogWarning("Could not find trading day within 10 days of {Date}, using fallback {Fallback}", 
            date.ToString("yyyy-MM-dd"), fallback.ToString("yyyy-MM-dd"));
        return fallback;
    }

    public bool IsTradingDay(DateTime date)
    {
        // Skip weekends
        if (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday)
            return false;
            
        // Skip market holidays
        return !IsMarketHoliday(date);
    }

    public bool IsMarketHoliday(DateTime date)
    {
        // New Year's Day
        if (date.Month == 1 && date.Day == 1)
            return true;
            
        // Independence Day
        if (date.Month == 7 && date.Day == 4)
            return true;
            
        // Christmas Day
        if (date.Month == 12 && date.Day == 25)
            return true;
            
        // Martin Luther King Jr. Day (3rd Monday in January)
        if (date.Month == 1 && date.DayOfWeek == DayOfWeek.Monday)
        {
            var firstMonday = GetFirstMondayOfMonth(date.Year, 1);
            if (date == firstMonday.AddDays(14)) // 3rd Monday
                return true;
        }
        
        // Presidents' Day (3rd Monday in February)
        if (date.Month == 2 && date.DayOfWeek == DayOfWeek.Monday)
        {
            var firstMonday = GetFirstMondayOfMonth(date.Year, 2);
            if (date == firstMonday.AddDays(14)) // 3rd Monday
                return true;
        }
        
        // Good Friday (Friday before Easter) - simplified approximation
        var easter = GetEasterSunday(date.Year);
        if (date == easter.AddDays(-2)) // Good Friday
            return true;
        
        // Memorial Day (last Monday in May)
        if (date.Month == 5 && date.DayOfWeek == DayOfWeek.Monday)
        {
            var lastMonday = GetLastMondayOfMonth(date.Year, 5);
            if (date == lastMonday)
                return true;
        }
        
        // Juneteenth (June 19th, observed if weekend)
        if (date.Month == 6)
        {
            if (date.Day == 19 && date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday)
                return true;
            if (date.Day == 18 && date.DayOfWeek == DayOfWeek.Friday) // Observed Friday if 19th is Saturday
                return true;
            if (date.Day == 20 && date.DayOfWeek == DayOfWeek.Monday) // Observed Monday if 19th is Sunday
                return true;
        }
        
        // Labor Day (1st Monday in September)
        if (date.Month == 9 && date.DayOfWeek == DayOfWeek.Monday)
        {
            var firstMonday = GetFirstMondayOfMonth(date.Year, 9);
            if (date == firstMonday)
                return true;
        }
        
        // Thanksgiving (4th Thursday in November)
        if (date.Month == 11 && date.DayOfWeek == DayOfWeek.Thursday)
        {
            var firstThursday = GetFirstThursdayOfMonth(date.Year, 11);
            if (date == firstThursday.AddDays(21)) // 4th Thursday
                return true;
        }
        
        return false;
    }
    
    private DateTime GetFirstMondayOfMonth(int year, int month)
    {
        var firstDay = new DateTime(year, month, 1);
        var daysUntilMonday = ((int)DayOfWeek.Monday - (int)firstDay.DayOfWeek + 7) % 7;
        return firstDay.AddDays(daysUntilMonday);
    }
    
    private DateTime GetLastMondayOfMonth(int year, int month)
    {
        var lastDay = new DateTime(year, month, DateTime.DaysInMonth(year, month));
        var daysSinceMonday = ((int)lastDay.DayOfWeek - (int)DayOfWeek.Monday + 7) % 7;
        return lastDay.AddDays(-daysSinceMonday);
    }
    
    private DateTime GetFirstThursdayOfMonth(int year, int month)
    {
        var firstDay = new DateTime(year, month, 1);
        var daysUntilThursday = ((int)DayOfWeek.Thursday - (int)firstDay.DayOfWeek + 7) % 7;
        return firstDay.AddDays(daysUntilThursday);
    }
    
    /// <summary>
    /// Calculate Easter Sunday using the algorithm
    /// </summary>
    private DateTime GetEasterSunday(int year)
    {
        // Simplified Easter calculation (Gregorian calendar)
        int a = year % 19;
        int b = year / 100;
        int c = year % 100;
        int d = b / 4;
        int e = b % 4;
        int f = (b + 8) / 25;
        int g = (b - f + 1) / 3;
        int h = (19 * a + b - d - g + 15) % 30;
        int i = c / 4;
        int k = c % 4;
        int l = (32 + 2 * e + 2 * i - h - k) % 7;
        int m = (a + 11 * h + 22 * l) / 451;
        int month = (h + l - 7 * m + 114) / 31;
        int day = ((h + l - 7 * m + 114) % 31) + 1;
        
        return new DateTime(year, month, day);
    }
}
