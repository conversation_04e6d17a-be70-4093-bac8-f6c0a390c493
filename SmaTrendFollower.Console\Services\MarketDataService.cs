using SmaTrendFollower.Models;
using SmaTrendFollower.Exceptions;
using SmaTrendFollower.Console.Configuration;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Net;

namespace SmaTrendFollower.Services;

public sealed class MarketDataService : IMarketDataService
{
    private readonly IAlpacaClientFactory _alpacaFactory;
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly IIndexCacheService _indexCacheService;
    private readonly IStockBarCacheService _stockBarCacheService;
    private readonly ICacheMetricsService? _metricsService;
    private readonly ILogger<MarketDataService> _logger;
    private readonly IVixFallbackService _vixFallbackService;
    private readonly IBarRecorder? _barRecorder;
    private readonly TimeoutConfiguration _timeouts;

    public MarketDataService(
        IAlpacaClientFactory alpacaFactory,
        IPolygonClientFactory polygonFactory,
        IIndexCacheService indexCacheService,
        IStockBarCacheService stockBarCacheService,
        ILogger<MarketDataService> logger,
        IVixFallbackService vixFallbackService,
        IEnumerable<IBarRecorder> barRecorders,
        ICacheMetricsService? metricsService = null,
        TimeoutConfiguration? timeouts = null)
    {
        _alpacaFactory = alpacaFactory;
        _polygonFactory = polygonFactory;
        _indexCacheService = indexCacheService;
        _stockBarCacheService = stockBarCacheService;
        _metricsService = metricsService;
        _logger = logger;
        _vixFallbackService = vixFallbackService;
        _barRecorder = barRecorders.FirstOrDefault(); // null when test env
        _timeouts = timeouts ?? new TimeoutConfiguration();
    }

    // === Historical Data Methods ===

    public async Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        return await GetStockBarsWithCacheAsync(symbol, "Day", startDate, endDate);
    }

    public async Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        return await GetStockBarsWithCacheAsync(symbol, "Minute", startDate, endDate);
    }

    /// <summary>
    /// Gets stock bars with intelligent caching to minimize API calls.
    /// Checks cache first and only fetches missing data from APIs.
    /// </summary>
    private async Task<IPage<IBar>> GetStockBarsWithCacheAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Requesting stock bars for {Symbol} {TimeFrame} from {StartDate} to {EndDate}",
                symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

            // Step 1: Get cached data for the requested range
            var cacheStopwatch = System.Diagnostics.Stopwatch.StartNew();
            var cachedBars = await _stockBarCacheService.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate);
            var cachedBarsList = cachedBars.ToList();
            cacheStopwatch.Stop();

            _metricsService?.RecordCacheOperation("cache_read", cacheStopwatch.Elapsed.TotalMilliseconds, cachedBarsList.Count);

            // Step 2: Determine if we need to fetch additional data from APIs
            var missingRange = await _stockBarCacheService.GetMissingDateRangeAsync(symbol, timeFrame, startDate, endDate);

            if (missingRange.HasValue)
            {
                _logger.LogDebug("Cache miss for {Symbol} {TimeFrame}: fetching data from {MissingStart} to {MissingEnd}",
                    symbol, timeFrame, missingRange.Value.startDate.ToString("yyyy-MM-dd"), missingRange.Value.endDate.ToString("yyyy-MM-dd"));

                // Step 3: Fetch missing data from APIs
                var apiStopwatch = System.Diagnostics.Stopwatch.StartNew();
                var newBars = await GetStockBarsFromApiAsync(symbol, timeFrame, missingRange.Value.startDate, missingRange.Value.endDate);
                var newBarsList = newBars.Items.ToList();
                apiStopwatch.Stop();

                // Step 4: Cache the new data
                if (newBarsList.Any())
                {
                    var cacheWriteStopwatch = System.Diagnostics.Stopwatch.StartNew();
                    await _stockBarCacheService.CacheBarsAsync(symbol, timeFrame, newBarsList);
                    cacheWriteStopwatch.Stop();

                    _metricsService?.RecordCacheOperation("cache_write", cacheWriteStopwatch.Elapsed.TotalMilliseconds, newBarsList.Count);
                    _logger.LogDebug("Cached {Count} new bars for {Symbol} {TimeFrame}", newBarsList.Count, symbol, timeFrame);
                }

                // Step 5: Combine cached and new data, ensuring no duplicates
                var allBars = cachedBarsList.Concat(newBarsList)
                    .GroupBy(b => b.TimeUtc)
                    .Select(g => g.OrderBy(x => x.TimeUtc).First()) // Add OrderBy for deterministic results
                    .Where(b => b.TimeUtc >= startDate && b.TimeUtc <= endDate)
                    .OrderBy(b => b.TimeUtc)
                    .ToList();

                stopwatch.Stop();
                _metricsService?.RecordCacheMiss(symbol, timeFrame, stopwatch.Elapsed.TotalMilliseconds, apiStopwatch.Elapsed.TotalMilliseconds);

                _logger.LogInformation("Retrieved {TotalCount} stock bars for {Symbol} {TimeFrame} ({CachedCount} cached, {NewCount} from API)",
                    allBars.Count, symbol, timeFrame, cachedBarsList.Count, newBarsList.Count);

                return new CachedBarPage(allBars, symbol);
            }
            else
            {
                // All data is available in cache
                stopwatch.Stop();
                _metricsService?.RecordCacheHit(symbol, timeFrame, stopwatch.Elapsed.TotalMilliseconds);

                _logger.LogDebug("Cache hit for {Symbol} {TimeFrame}: returning {Count} cached bars", symbol, timeFrame, cachedBarsList.Count);
                var filteredBars = cachedBarsList.Where(b => b.TimeUtc >= startDate && b.TimeUtc <= endDate).OrderBy(b => b.TimeUtc).ToList();
                return new CachedBarPage(filteredBars, symbol);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving stock bars for {Symbol} {TimeFrame}, falling back to direct API call", symbol, timeFrame);
            // Fallback to direct API call if caching fails
            return await GetStockBarsFromApiAsync(symbol, timeFrame, startDate, endDate);
        }
    }

    /// <summary>
    /// Fetches stock bars directly from APIs without caching logic.
    /// Uses Polygon as primary source for daily bars (better historical data coverage),
    /// Alpaca as primary for minute bars (real-time trading integration).
    /// </summary>
    private async Task<IPage<IBar>> GetStockBarsFromApiAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        var alpacaTimeFrame = timeFrame == "Day" ? BarTimeFrame.Day : BarTimeFrame.Minute;

        // For same-day requests, ensure we have a valid time range
        if (startDate.Date == endDate.Date)
        {
            var (adjustedStart, adjustedEnd) = AdjustSameDayRequest(startDate, endDate, symbol);
            startDate = adjustedStart;
            endDate = adjustedEnd;
        }

        // FIXED: Enhanced date range validation with better error reporting
        if (startDate > endDate)
        {
            _logger.LogWarning("Invalid date range detected for {Symbol}: startDate \"{StartDate:yyyy-MM-dd}\" > endDate \"{EndDate:yyyy-MM-dd}\". " +
                "This indicates a cache calculation error. Using safe date range.",
                symbol, startDate, endDate);

            // Use safe date range: yesterday back 300 days
            endDate = DateTime.SpecifyKind(DateTime.UtcNow.Date.AddDays(-1), DateTimeKind.Utc);
            startDate = DateTime.SpecifyKind(endDate.AddDays(-300), DateTimeKind.Utc);

            _logger.LogInformation("Adjusted to safe date range for {Symbol}: \"{StartDate:yyyy-MM-dd}\" to \"{EndDate:yyyy-MM-dd}\"",
                symbol, startDate, endDate);
        }

        // Prevent future dates (additional safety check for non-same-day requests)
        var maxAllowedDate = DateTime.SpecifyKind(DateTime.UtcNow.Date.AddDays(-1), DateTimeKind.Utc); // Yesterday is the latest allowed
        if (endDate > maxAllowedDate)
        {
            _logger.LogWarning("EndDate {EndDate:yyyy-MM-dd} is in the future, adjusting to {MaxDate:yyyy-MM-dd} for {Symbol}",
                endDate, maxAllowedDate, symbol);
            endDate = maxAllowedDate;

            // Ensure startDate is still valid
            if (startDate >= endDate)
            {
                startDate = DateTime.SpecifyKind(endDate.AddDays(-300), DateTimeKind.Utc);
                _logger.LogDebug("Adjusted startDate to {StartDate:yyyy-MM-dd} to maintain valid range for {Symbol}",
                    startDate, symbol);
            }
        }

        _logger.LogDebug("Requesting {TimeFrame} bars for {Symbol} from {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}",
            timeFrame, symbol, startDate, endDate);

        // For daily bars, use Polygon as primary (better historical coverage)
        if (timeFrame == "Day")
        {
            try
            {
                var polygonBars = await GetStockBarsFromPolygonAsync(symbol, startDate, endDate);
                _logger.LogDebug("Retrieved {Count} daily bars for {Symbol} from Polygon (primary)",
                    polygonBars.Items.Count(), symbol);

                // Record bars for back-test replay
                _barRecorder?.Record(polygonBars.Items, symbol, "Day");

                return polygonBars;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Polygon failed for daily bars {Symbol}, falling back to Alpaca", symbol);
                _metricsService?.RecordApiThrottle("Polygon", symbol);

                // Fallback to Alpaca for daily bars
                var rateLimitHelper = _alpacaFactory.GetRateLimitHelper();
                return await rateLimitHelper.ExecuteAsync(async () =>
                {
                    try
                    {
                        using var dataClient = _alpacaFactory.CreateDataClient();
                        var request = new HistoricalBarsRequest(symbol, BarTimeFrame.Day,
                            new Interval<DateTime>(startDate, endDate));
                        var response = await dataClient.ListHistoricalBarsAsync(request);

                        _logger.LogDebug("Retrieved {Count} daily bars for {Symbol} from Alpaca (fallback)",
                            response.Items.Count(), symbol);

                        _barRecorder?.Record(response.Items, symbol, "Day");
                        return response;
                    }
                    catch (Exception alpacaEx)
                    {
                        _logger.LogError(alpacaEx, "Both Polygon and Alpaca failed for daily bars {Symbol}", symbol);
                        throw new InvalidOperationException($"Both Polygon and Alpaca APIs failed for daily bars {symbol}", ex);
                    }
                }, $"GetStockBars-{symbol}-{timeFrame}-AlpacaFallback");
            }
        }

        // For minute bars, use Alpaca as primary (real-time trading integration)
        var alpacaRateLimitHelper = _alpacaFactory.GetRateLimitHelper();
        return await alpacaRateLimitHelper.ExecuteAsync(async () =>
        {
            try
            {
                using var dataClient = _alpacaFactory.CreateDataClient();
                var request = new HistoricalBarsRequest(symbol, BarTimeFrame.Minute,
                    new Interval<DateTime>(startDate, endDate));
                var response = await dataClient.ListHistoricalBarsAsync(request);

                _logger.LogDebug("Retrieved {Count} minute bars for {Symbol} from Alpaca (primary)",
                    response.Items.Count(), symbol);

                _barRecorder?.Record(response.Items, symbol, "Minute");
                return response;
            }
            catch (Exception ex) when (ex.Message.Contains("TooManyRequests") || ex.Message.Contains("429"))
            {
                _metricsService?.RecordApiThrottle("Alpaca", symbol);
                _logger.LogWarning("Alpaca throttle detected for minute bars {Symbol}, attempting Polygon fallback", symbol);
                return await GetStockBarsFromPolygonFallbackAsync(symbol, startDate, endDate);
            }
            catch (HttpRequestException ex)
            {
                _logger.LogWarning(ex, "Alpaca network error for minute bars {Symbol}, attempting Polygon fallback", symbol);
                _metricsService?.RecordApiThrottle("Alpaca", symbol);

                try
                {
                    return await GetStockBarsFromPolygonFallbackAsync(symbol, startDate, endDate);
                }
                catch (Exception fallbackEx)
                {
                    _logger.LogError(fallbackEx, "Both Alpaca and Polygon failed for minute bars {Symbol}", symbol);
                    throw new InvalidOperationException($"Both Alpaca and Polygon APIs failed for minute bars {symbol}", ex);
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _logger.LogWarning(ex, "Alpaca timeout for minute bars {Symbol}, attempting Polygon fallback", symbol);
                _metricsService?.RecordApiThrottle("Alpaca", symbol);

                try
                {
                    return await GetStockBarsFromPolygonFallbackAsync(symbol, startDate, endDate);
                }
                catch (Exception fallbackEx)
                {
                    _logger.LogError(fallbackEx, "Both Alpaca and Polygon failed for minute bars {Symbol}", symbol);
                    throw new InvalidOperationException($"Both Alpaca and Polygon APIs failed for minute bars {symbol}", ex);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving minute bars for {Symbol} from Alpaca", symbol);
                _metricsService?.RecordApiThrottle("Alpaca", symbol);
                throw;
            }
        }, $"GetStockBars-{symbol}-{timeFrame}");
    }

    /// <summary>
    /// Gets daily stock bars from Polygon API (primary source for historical data)
    /// </summary>
    private async Task<IPage<IBar>> GetStockBarsFromPolygonAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync<IPage<IBar>>(async () =>
        {
            try
            {
                var httpClient = _polygonFactory.CreateClient();

                var startDateStr = startDate.ToString("yyyy-MM-dd");
                var endDateStr = endDate.ToString("yyyy-MM-dd");

                // Use Polygon's aggregates endpoint for daily bars
                var url = $"/v2/aggs/ticker/{symbol}/range/1/day/{startDateStr}/{endDateStr}?adjusted=true&sort=asc&limit=50000";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                var response = await httpClient.GetAsync(urlWithApiKey);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Polygon daily bars failed with {StatusCode} for {Symbol}. Response: {Response}",
                        response.StatusCode, symbol, errorContent);

                    // Handle specific error cases
                    if (response.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        _metricsService?.RecordApiThrottle("Polygon", symbol);
                        throw new InvalidOperationException($"Polygon rate limit exceeded for {symbol}");
                    }
                    else if (response.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        throw new InvalidOperationException($"Polygon API authentication failed for {symbol}");
                    }
                    else if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        _logger.LogInformation("Symbol {Symbol} not found in Polygon, returning empty result", symbol);
                        return new PolygonBarPage(new List<PolygonBar>(), symbol);
                    }

                    throw new InvalidOperationException($"Polygon daily bars failed for {symbol}: {response.StatusCode}");
                }

                var content = await response.Content.ReadAsStringAsync();
                var jsonDoc = JsonDocument.Parse(content);

                var bars = new List<PolygonBar>();

                if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
                {
                    foreach (var bar in results.EnumerateArray())
                    {
                        if (TryParsePolygonBar(bar, symbol, out var polygonBar) && polygonBar != null)
                        {
                            bars.Add(polygonBar);
                        }
                    }
                }

                _logger.LogInformation("Polygon primary: Retrieved {Count} daily bars for {Symbol}", bars.Count, symbol);

                // Convert to Alpaca-compatible format
                return new PolygonBarPage(bars, symbol);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Polygon daily bars for {Symbol}", symbol);
                throw;
            }
        }, $"GetStockBarsPolygon-{symbol}");
    }

    private async Task<IPage<IBar>> GetStockBarsFromPolygonFallbackAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync<IPage<IBar>>(async () =>
        {
            try
            {
                var httpClient = _polygonFactory.CreateClient();

                var startDateStr = startDate.ToString("yyyy-MM-dd");
                var endDateStr = endDate.ToString("yyyy-MM-dd");

                // Use Polygon's aggregates endpoint for minute bars
                var url = $"/v2/aggs/ticker/{symbol}/range/1/minute/{startDateStr}/{endDateStr}?adjusted=true&sort=asc&limit=50000";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                var response = await httpClient.GetAsync(urlWithApiKey);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Polygon fallback failed with {StatusCode} for {Symbol}. Response: {Response}",
                        response.StatusCode, symbol, errorContent);

                    // Handle specific error cases
                    if (response.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        _metricsService?.RecordApiThrottle("Polygon", symbol);
                        throw new InvalidOperationException($"Polygon rate limit exceeded for {symbol}");
                    }
                    else if (response.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        throw new InvalidOperationException($"Polygon API authentication failed for {symbol}");
                    }
                    else if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        _logger.LogInformation("Symbol {Symbol} not found in Polygon, returning empty result", symbol);
                        return new PolygonBarPage(new List<PolygonBar>(), symbol);
                    }

                    throw new InvalidOperationException($"Polygon fallback failed for {symbol}: {response.StatusCode}");
                }

                var content = await response.Content.ReadAsStringAsync();
                var jsonDoc = JsonDocument.Parse(content);

                var bars = new List<PolygonBar>();

                if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
                {
                    foreach (var bar in results.EnumerateArray())
                    {
                        if (TryParsePolygonBar(bar, symbol, out var polygonBar) && polygonBar != null)
                        {
                            bars.Add(polygonBar);
                        }
                    }
                }

                _logger.LogInformation("Polygon fallback successful: Retrieved {Count} minute bars for {Symbol}", bars.Count, symbol);

                // Convert to Alpaca-compatible format
                return new PolygonBarPage(bars, symbol);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Polygon fallback for {Symbol}", symbol);
                throw;
            }
        }, $"GetStockBarsFallback-{symbol}");
    }

    public async Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        return await GetMultipleStockBarsWithCacheAsync(symbols, "Day", startDate, endDate);
    }

    public async Task<IDictionary<string, IPage<IBar>>> GetStockMinuteBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        return await GetMultipleStockBarsWithCacheAsync(symbols, "Minute", startDate, endDate);
    }

    private async Task<IDictionary<string, IPage<IBar>>> GetMultipleStockBarsWithCacheAsync(IEnumerable<string> symbols, string timeFrame, DateTime startDate, DateTime endDate)
    {
        var results = new Dictionary<string, IPage<IBar>>();
        var symbolList = symbols.ToList();

        _logger.LogInformation("Retrieving {TimeFrame} bars for {Count} symbols with caching", timeFrame, symbolList.Count);

        // Process in parallel batches for better performance
        const int batchSize = 15; // Increased batch size
        const int maxConcurrentBatches = 4; // Allow more concurrent operations

        var semaphore = new SemaphoreSlim(maxConcurrentBatches, maxConcurrentBatches);
        var allTasks = new List<Task<(string Symbol, IPage<IBar>? Bars, bool Success)>>();

        for (int i = 0; i < symbolList.Count; i += batchSize)
        {
            var batch = symbolList.Skip(i).Take(batchSize);
            foreach (var symbol in batch)
            {
                var task = ProcessSymbolWithSemaphore(symbol, timeFrame, startDate, endDate, semaphore);
                allTasks.Add(task);
            }
        }

        // Wait for all tasks to complete
        var allResults = await Task.WhenAll(allTasks);

        // Aggregate results
        foreach (var result in allResults.Where(r => r.Success))
        {
            results[result.Symbol] = result.Bars!;
        }

        _logger.LogInformation("Successfully retrieved {TimeFrame} bars for {SuccessCount}/{TotalCount} symbols with caching",
            timeFrame, results.Count, symbolList.Count);

        return results;
    }

    private async Task<(string Symbol, IPage<IBar>? Bars, bool Success)> ProcessSymbolWithSemaphore(
        string symbol, string timeFrame, DateTime startDate, DateTime endDate, SemaphoreSlim semaphore)
    {
        await semaphore.WaitAsync();
        try
        {
            var bars = await GetStockBarsWithCacheAsync(symbol, timeFrame, startDate, endDate);
            return (symbol, bars, true);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve {TimeFrame} bars for {Symbol}, skipping", timeFrame, symbol);
            return (symbol, null, false);
        }
        finally
        {
            semaphore.Release();
        }
    }

    // === Account & Positions Methods ===

    public async Task<IAccount> GetAccountAsync()
    {
        var rateLimitHelper = _alpacaFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync(async () =>
        {
            try
            {
                using var tradingClient = _alpacaFactory.CreateTradingClient();
                var account = await tradingClient.GetAccountAsync();

                _logger.LogDebug("Retrieved account information: Equity={Equity:C}, BuyingPower={BuyingPower:C}",
                    account.Equity, account.BuyingPower);

                return account;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving account information from Alpaca");
                throw;
            }
        }, "GetAccount");
    }

    public async Task<IReadOnlyList<IPosition>> GetPositionsAsync()
    {
        var rateLimitHelper = _alpacaFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync(async () =>
        {
            try
            {
                using var tradingClient = _alpacaFactory.CreateTradingClient();
                var positions = await tradingClient.ListPositionsAsync();
                var positionList = positions.ToList();

                _logger.LogDebug("Retrieved {Count} positions from Alpaca", positionList.Count);

                return positionList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving positions from Alpaca");
                throw;
            }
        }, "GetPositions");
    }

    public async Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100)
    {
        var rateLimitHelper = _alpacaFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync(async () =>
        {
            try
            {
                using var tradingClient = _alpacaFactory.CreateTradingClient();

                var request = new ListOrdersRequest
                {
                    OrderStatusFilter = OrderStatusFilter.All, // Use All instead of Filled
                    LimitOrderNumber = limitCount,
                    OrderListSorting = SortDirection.Descending // Most recent first
                };

                var orders = await tradingClient.ListOrdersAsync(request);
                var orderList = orders.ToList();

                _logger.LogDebug("Retrieved {Count} recent fills from Alpaca", orderList.Count);

                return orderList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recent fills from Alpaca");
                throw;
            }
        }, "GetRecentFills");
    }

    // === Index Data Methods ===

    public async Task<decimal?> GetIndexValueAsync(string indexSymbol)
    {
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync<decimal?>(async () =>
        {
            try
            {
                var httpClient = _polygonFactory.CreateClient();

                // Special handling for VIX with Stocks Developer subscription
                if (indexSymbol.Equals("I:VIX", StringComparison.OrdinalIgnoreCase) ||
                    indexSymbol.Equals("VIX", StringComparison.OrdinalIgnoreCase))
                {
                    return await GetVixValueFromPolygonAsync(httpClient);
                }

                // Use Polygon's real-time quote endpoint for other indices
                var url = $"/v2/last/trade/{indexSymbol}";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                var response = await httpClient.GetAsync(urlWithApiKey);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Polygon API returned {StatusCode} for index {Symbol}",
                        response.StatusCode, indexSymbol);
                    return null;
                }

                var content = await response.Content.ReadAsStringAsync();
                var jsonDoc = JsonDocument.Parse(content);

                if (jsonDoc.RootElement.TryGetProperty("results", out var results) &&
                    results.TryGetProperty("p", out var priceElement))
                {
                    var price = priceElement.GetDecimal();
                    _logger.LogDebug("Retrieved index value for {Symbol}: {Price}", indexSymbol, price);
                    return price;
                }

                _logger.LogWarning("Could not parse index value from Polygon response for {Symbol}", indexSymbol);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving index value for {Symbol} from Polygon", indexSymbol);
                return null;
            }
        }, $"GetIndexValue-{indexSymbol}");
    }

    /// <summary>
    /// Gets VIX value using real-time data sources compatible with Polygon.io and Alpaca subscriptions.
    ///
    /// VIX Data Sources (in order of preference during trading hours):
    /// 1. Real-time VIX data from Polygon minute aggregates (real-time, not delayed)
    /// 2. Real-time VIX ETF proxy estimation from Alpaca (VIXY, VXX, UVXY) - AlgoTrader Plus subscription
    /// 3. Previous day VIX data from Polygon aggregates - Fallback method
    /// 4. Fallback to VixFallbackService (web scraping, synthetic calculation)
    ///
    /// Data Freshness: All real-time data is validated against 18-minute staleness threshold.
    /// Delayed data (15+ minutes) is rejected to ensure trading decisions use current market conditions.
    /// </summary>
    private async Task<decimal?> GetVixValueFromPolygonAsync(HttpClient httpClient)
    {
        var isMarketHours = IsMarketHours();

        // Strategy 1: Try to get current/intraday VIX data first (during trading hours)
        if (isMarketHours)
        {
            var currentVix = await TryGetCurrentVixFromPolygonAsync(httpClient);
            if (currentVix.HasValue)
            {
                _logger.LogDebug("VIX retrieved from current/intraday data: {Value:F2}", currentVix.Value);
                return currentVix.Value;
            }
        }

        // Strategy 2: Use comprehensive fallback strategy (web scraping -> synthetic VIX)
        _logger.LogDebug("Polygon VIX failed, using comprehensive fallback strategy");

        try
        {
            var fallbackVix = await GetVixValueWithFallbackAsync();
            if (fallbackVix > 0)
            {
                _logger.LogDebug("VIX retrieved from fallback strategy: {Value:F2}", fallbackVix);
                return fallbackVix;
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to get VIX from fallback strategy");
        }

        // Strategy 3: Use previous day VIX data as fallback (works with Stocks Developer subscription)
        try
        {
            var url = "/v2/aggs/ticker/I:VIX/prev";
            var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
            var response = await httpClient.GetAsync(urlWithApiKey);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var jsonDoc = JsonDocument.Parse(content);

                if (jsonDoc.RootElement.TryGetProperty("results", out var results) &&
                    results.ValueKind == JsonValueKind.Array && results.GetArrayLength() > 0)
                {
                    var firstResult = results[0];
                    if (firstResult.TryGetProperty("c", out var closeElement))
                    {
                        var vixClose = closeElement.GetDecimal();
                        _logger.LogDebug("VIX retrieved from I:VIX previous day data: {Value:F2}", vixClose);
                        return vixClose;
                    }
                }
                else
                {
                    _logger.LogDebug("I:VIX previous day response format unexpected: {Content}", content);
                }
            }
            else
            {
                _logger.LogDebug("I:VIX previous day request failed: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to get VIX from I:VIX previous day data");
        }

        _logger.LogWarning("All Polygon VIX retrieval methods failed");
        return null;
    }

    /// <summary>
    /// Attempts to get current VIX data from various Polygon endpoints during trading hours.
    /// </summary>
    private async Task<decimal?> TryGetCurrentVixFromPolygonAsync(HttpClient httpClient)
    {
        // Try different current VIX endpoints using real-time data (not delayed)
        var endTime = DateTime.UtcNow;
        var startTime = endTime.AddMinutes(-5);

        // CRITICAL FIX: Ensure startTime is not after endTime (can happen near day boundaries)
        if (startTime > endTime)
        {
            startTime = endTime.AddMinutes(-1); // Use 1 minute lookback as minimum
        }

        // For minute-level data, ensure we're requesting within the same day or use proper date range
        var startDateStr = startTime.ToString("yyyy-MM-dd");
        var endDateStr = endTime.ToString("yyyy-MM-dd");

        // CRITICAL FIX: If dates are different (crossed day boundary), adjust both dates and times properly
        if (startDateStr != endDateStr)
        {
            // Use the end date for both, but ensure proper time ordering
            startDateStr = endDateStr;
            // Reset times to ensure start < end within the same day
            startTime = endTime.Date; // Start of the end date
            endTime = endTime.Date.AddDays(1).AddTicks(-1); // End of the end date
            _logger.LogDebug("Adjusted VIX date range to avoid day boundary crossing: using {Date} with proper time range {StartTime} to {EndTime}",
                endDateStr, startTime.ToString("HH:mm:ss"), endTime.ToString("HH:mm:ss"));
        }

        // During market hours, try real-time data first. After hours, go straight to previous day data.
        var currentVixEndpoints = IsMarketHours() ? new[]
        {
            ($"v2/aggs/ticker/I:VIX/range/1/minute/{startDateStr}/{endDateStr}?adjusted=true&sort=desc&limit=1", "Real-time I:VIX minute aggregates"),
            ($"v2/aggs/ticker/VIX/range/1/minute/{startDateStr}/{endDateStr}?adjusted=true&sort=desc&limit=1", "Real-time VIX minute aggregates (fallback)"),
            ("v2/aggs/ticker/I:VIX/prev", "Previous day I:VIX close"),
            ("v2/aggs/ticker/VIX/prev", "Previous day VIX close (fallback)")
        } : new[]
        {
            ("v2/aggs/ticker/I:VIX/prev", "Previous day I:VIX close"),
            ("v2/aggs/ticker/VIX/prev", "Previous day VIX close (fallback)")
        };

        foreach (var (endpoint, description) in currentVixEndpoints)
        {
            try
            {
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(endpoint);

                // Use a shorter timeout for individual VIX endpoint attempts (15 seconds each)
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(15));
                var response = await httpClient.GetAsync(urlWithApiKey, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var jsonDoc = JsonDocument.Parse(content);

                    // Try different response formats
                    decimal? price = null;
                    DateTime? dataTimestamp = null;

                    // Format 1: Minute aggregates (real-time data)
                    if (jsonDoc.RootElement.TryGetProperty("results", out var results) &&
                        results.ValueKind == JsonValueKind.Array && results.GetArrayLength() > 0)
                    {
                        var latestBar = results[0]; // First element is most recent due to sort=desc
                        if (latestBar.TryGetProperty("c", out var closeElement))
                        {
                            price = closeElement.GetDecimal();

                            // Check data freshness for minute aggregates
                            if (latestBar.TryGetProperty("t", out var timestampElement))
                            {
                                dataTimestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).UtcDateTime;
                                var dataAge = DateTime.UtcNow - dataTimestamp.Value;

                                if (dataAge > TimeSpan.FromMinutes(18))
                                {
                                    _logger.LogWarning("VIX data from {Endpoint} is stale (age: {Age}), rejecting", description, dataAge);
                                    price = null; // Reject stale data
                                }
                            }
                        }
                    }
                    // Format 2: Previous day aggregates
                    else if (jsonDoc.RootElement.TryGetProperty("results", out var prevResults) &&
                             prevResults.TryGetProperty("c", out var prevCloseElement))
                    {
                        price = prevCloseElement.GetDecimal();
                        // Previous day data is acceptable as fallback
                    }
                    // Format 3: Legacy endpoint format (v1 endpoints)
                    else if (jsonDoc.RootElement.TryGetProperty("last", out var lastElement) &&
                             lastElement.TryGetProperty("price", out var legacyPriceElement))
                    {
                        price = legacyPriceElement.GetDecimal();
                    }
                    // Format 4: Check for error status in legacy endpoints
                    else if (jsonDoc.RootElement.TryGetProperty("status", out var statusElement) &&
                             statusElement.GetString() == "notfound")
                    {
                        _logger.LogDebug("Legacy endpoint returned 'notfound' for {Description}", description);
                        // Continue to next endpoint
                    }

                    if (price.HasValue)
                    {
                        _logger.LogDebug("Current VIX retrieved from {Description}: {Value:F2}", description, price.Value);
                        return price.Value;
                    }
                }
                else
                {
                    _logger.LogDebug("Current VIX endpoint failed: {Description} - {StatusCode}", description, response.StatusCode);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogDebug("VIX endpoint timed out after 15 seconds: {Description} - {Endpoint}", description, endpoint);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Exception trying current VIX endpoint: {Description} - {Endpoint}", description, endpoint);
            }
        }

        return null;
    }

    /// <summary>
    /// Determines if the current time is during market hours (9:30 AM - 4:00 PM ET, Monday-Friday).
    /// </summary>
    private bool IsMarketHours()
    {
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow,
            TimeZoneInfo.FindSystemTimeZoneById("US Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // Check if it's during market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);

        return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
    }

    public async Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            _logger.LogDebug("Requesting index bars for {Symbol} from {StartDate} to {EndDate}",
                indexSymbol, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

            // Step 1: Get cached data for the requested range
            var cachedBars = await _indexCacheService.GetCachedBarsAsync(indexSymbol, startDate, endDate);
            var cachedBarsList = cachedBars.ToList();

            // Step 2: Determine if we need to fetch additional data from Polygon
            var missingRange = await _indexCacheService.GetMissingDateRangeAsync(indexSymbol, startDate, endDate);

            if (missingRange.HasValue)
            {
                _logger.LogDebug("Cache miss for {Symbol}: fetching data from {MissingStart} to {MissingEnd}",
                    indexSymbol, missingRange.Value.startDate.ToString("yyyy-MM-dd"), missingRange.Value.endDate.ToString("yyyy-MM-dd"));

                // Step 3: Fetch missing data from Polygon
                var newBars = await GetIndexBarsFromPolygonAsync(indexSymbol, missingRange.Value.startDate, missingRange.Value.endDate);
                var newBarsList = newBars.ToList();

                // Step 4: Cache the new data
                if (newBarsList.Any())
                {
                    await _indexCacheService.CacheBarsAsync(indexSymbol, newBarsList);
                    _logger.LogDebug("Cached {Count} new bars for {Symbol}", newBarsList.Count, indexSymbol);
                }

                // Step 5: Combine cached and new data, ensuring no duplicates
                var allBars = cachedBarsList.Concat(newBarsList)
                    .GroupBy(b => b.TimeUtc)
                    .Select(g => g.OrderBy(x => x.TimeUtc).First()) // Add OrderBy for deterministic results
                    .Where(b => b.TimeUtc >= startDate && b.TimeUtc <= endDate)
                    .OrderBy(b => b.TimeUtc)
                    .ToList();

                _logger.LogInformation("Retrieved {TotalCount} index bars for {Symbol} ({CachedCount} cached, {NewCount} from API)",
                    allBars.Count, indexSymbol, cachedBarsList.Count, newBarsList.Count);

                return allBars;
            }
            else
            {
                // All data is available in cache
                _logger.LogDebug("Cache hit for {Symbol}: returning {Count} cached bars", indexSymbol, cachedBarsList.Count);
                return cachedBarsList.Where(b => b.TimeUtc >= startDate && b.TimeUtc <= endDate).OrderBy(b => b.TimeUtc);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving index bars for {Symbol}, falling back to direct API call", indexSymbol);
            // Fallback to direct API call if caching fails
            return await GetIndexBarsFromPolygonAsync(indexSymbol, startDate, endDate);
        }
    }

    /// <summary>
    /// Fetches index bars directly from Polygon API without caching logic.
    /// Used internally by the cached GetIndexBarsAsync method.
    /// </summary>
    private async Task<IEnumerable<IndexBar>> GetIndexBarsFromPolygonAsync(string indexSymbol, DateTime startDate, DateTime endDate)
    {
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync<IEnumerable<IndexBar>>(async () =>
        {
            try
            {
                var httpClient = _polygonFactory.CreateClient();

                // Validate date range to prevent "to cannot be before from" error
                if (startDate > endDate)
                {
                    _logger.LogDebug("Adjusting date range for {Symbol}: startDate {StartDate} is after endDate {EndDate}. Using endDate for both.",
                        indexSymbol, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
                    startDate = endDate; // Use the same date for both to avoid API errors
                }

                // Ensure we don't request future dates
                var maxEndDate = DateTime.SpecifyKind(DateTime.UtcNow.Date, DateTimeKind.Utc);
                if (endDate > maxEndDate)
                {
                    _logger.LogDebug("Adjusting endDate from {OriginalEnd} to {MaxEnd} for {Symbol}",
                        endDate.ToString("yyyy-MM-dd"), maxEndDate.ToString("yyyy-MM-dd"), indexSymbol);
                    endDate = maxEndDate;
                }

                // Ensure startDate is not after the adjusted endDate
                if (startDate > endDate)
                {
                    _logger.LogDebug("Adjusting startDate from {OriginalStart} to {AdjustedStart} for {Symbol}",
                        startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"), indexSymbol);
                    startDate = endDate;
                }

                // Handle same-day requests properly for Polygon API
                if (startDate.Date == endDate.Date)
                {
                    // For same-day requests, ensure we use the date only (no time component)
                    // This prevents Polygon API "to cannot be before from" errors
                    startDate = startDate.Date;
                    endDate = endDate.Date;
                    _logger.LogDebug("Normalized same-day request for {Symbol}: {Date}",
                        indexSymbol, startDate.ToString("yyyy-MM-dd"));
                }

                // Format dates for Polygon API (YYYY-MM-DD)
                var startDateStr = startDate.ToString("yyyy-MM-dd");
                var endDateStr = endDate.ToString("yyyy-MM-dd");

                // Use Polygon's aggregates endpoint for historical index data
                var url = $"/v2/aggs/ticker/{indexSymbol}/range/1/day/{startDateStr}/{endDateStr}?adjusted=true&sort=asc";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                var response = await httpClient.GetAsync(urlWithApiKey);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("Polygon API returned {StatusCode} for index bars {Symbol}. Response: {Response}",
                        response.StatusCode, indexSymbol, errorContent);

                    // Handle specific error cases for index data
                    if (response.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        _metricsService?.RecordApiThrottle("Polygon", indexSymbol);
                        _logger.LogWarning("Polygon rate limit hit for index {Symbol}, will retry", indexSymbol);
                        throw new InvalidOperationException($"Polygon rate limit exceeded for index {indexSymbol}");
                    }
                    else if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        _logger.LogInformation("Index {Symbol} not found in Polygon", indexSymbol);
                    }
                    else if (response.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        _logger.LogError("Polygon API authentication failed for index {Symbol}", indexSymbol);
                        throw new InvalidOperationException($"Polygon API authentication failed for index {indexSymbol}");
                    }

                    return Enumerable.Empty<IndexBar>();
                }

                var content = await response.Content.ReadAsStringAsync();
                var jsonDoc = JsonDocument.Parse(content);

                var bars = new List<IndexBar>();

                if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
                {
                    foreach (var bar in results.EnumerateArray())
                    {
                        if (TryParseIndexBar(bar, out var indexBar))
                        {
                            bars.Add(indexBar);
                        }
                    }
                }

                _logger.LogDebug("Retrieved {Count} index bars for {Symbol} from Polygon API", bars.Count, indexSymbol);
                return bars;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving index bars for {Symbol} from Polygon API", indexSymbol);
                return Enumerable.Empty<IndexBar>();
            }
        }, $"GetIndexBarsFromPolygon-{indexSymbol}");
    }

    // === Options Data Methods ===

    public async Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null)
    {
        return await GetOptionsDataAsync(underlyingSymbol, expirationDate, null, null);
    }

    /// <summary>
    /// Gets options chain data with optional strike price filtering for performance optimization
    /// </summary>
    /// <param name="underlyingSymbol">The underlying symbol</param>
    /// <param name="expirationDate">Optional expiration date filter</param>
    /// <param name="currentPrice">Current underlying price for strike band calculation</param>
    /// <param name="strikeBandPercent">Strike band percentage (e.g., 15 for ±15%)</param>
    /// <returns>Filtered options data</returns>
    public async Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null, decimal? currentPrice = null, double? strikeBandPercent = null)
    {
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync<IEnumerable<OptionData>>(async () =>
        {
            try
            {
                var httpClient = _polygonFactory.CreateClient();

                // Use snapshot endpoint for current market data with strike filtering when price is available
                string url;
                if (currentPrice.HasValue && strikeBandPercent.HasValue)
                {
                    // Calculate strike band range
                    var bandMultiplier = (decimal)(strikeBandPercent.Value / 100.0);
                    var lowerStrike = currentPrice.Value * (1 - bandMultiplier);
                    var upperStrike = currentPrice.Value * (1 + bandMultiplier);

                    // Use snapshot endpoint with strike filtering for better performance
                    url = $"/v3/snapshot/options/{underlyingSymbol}?limit=1000&strike_price.gte={lowerStrike:F2}&strike_price.lte={upperStrike:F2}";

                    if (expirationDate.HasValue)
                    {
                        url += $"&expiration_date.gte={expirationDate.Value:yyyy-MM-dd}";
                    }

                    _logger.LogDebug("Using strike band filtering for {Symbol}: {Lower:F2} - {Upper:F2} (±{Band:F1}%)",
                        underlyingSymbol, lowerStrike, upperStrike, strikeBandPercent.Value);
                }
                else
                {
                    // Fallback to contracts endpoint without filtering
                    url = $"/v3/reference/options/contracts?underlying_ticker={underlyingSymbol}&limit=1000";

                    if (expirationDate.HasValue)
                    {
                        url += $"&expiration_date={expirationDate.Value:yyyy-MM-dd}";
                    }

                    _logger.LogDebug("Using unfiltered options contracts for {Symbol} (no current price provided)", underlyingSymbol);
                }

                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                var response = await httpClient.GetAsync(urlWithApiKey);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Polygon options API returned {StatusCode} for {Symbol}", response.StatusCode, underlyingSymbol);
                    return Enumerable.Empty<OptionData>();
                }

                var content = await response.Content.ReadAsStringAsync();
                var jsonDoc = JsonDocument.Parse(content);

                var options = new List<OptionData>();

                if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
                {
                    foreach (var option in results.EnumerateArray())
                    {
                        if (TryParseOptionData(option, out var optionData))
                        {
                            options.Add(optionData);
                        }
                    }
                }

                _logger.LogDebug("Retrieved {Count} options contracts for {Symbol} from Polygon", options.Count, underlyingSymbol);
                return options;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving options data for {Symbol} from Polygon", underlyingSymbol);
                return Enumerable.Empty<OptionData>();
            }
        }, $"GetOptionsData-{underlyingSymbol}");
    }

    public async Task<IEnumerable<VixTermData>> GetVixTermStructureAsync()
    {
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync<IEnumerable<VixTermData>>(async () =>
        {
            try
            {
                var httpClient = _polygonFactory.CreateClient();

                // Get VIX futures data for term structure
                var url = "/v3/reference/options/contracts?underlying_ticker=VIX&contract_type=future&limit=100";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                var response = await httpClient.GetAsync(urlWithApiKey);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Polygon VIX term structure API returned {StatusCode}", response.StatusCode);
                    return Enumerable.Empty<VixTermData>();
                }

                var content = await response.Content.ReadAsStringAsync();
                var jsonDoc = JsonDocument.Parse(content);

                var termData = new List<VixTermData>();

                if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
                {
                    foreach (var contract in results.EnumerateArray())
                    {
                        if (TryParseVixTermData(contract, out var vixData))
                        {
                            termData.Add(vixData);
                        }
                    }
                }

                _logger.LogDebug("Retrieved {Count} VIX term structure points from Polygon", termData.Count);
                return termData.OrderBy(v => v.ExpirationDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving VIX term structure from Polygon");
                return Enumerable.Empty<VixTermData>();
            }
        }, "GetVixTermStructure");
    }

    // === Enhanced Options Data Methods ===

    public async Task<IEnumerable<OptionQuote>> GetOptionsQuotesAsync(IEnumerable<string> optionSymbols)
    {
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync<IEnumerable<OptionQuote>>(async () =>
        {
            try
            {
                var httpClient = _polygonFactory.CreateClient();
                var quotes = new List<OptionQuote>();

                foreach (var symbol in optionSymbols)
                {
                    var url = $"/v3/quotes/{symbol}";
                    var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                    var response = await httpClient.GetAsync(urlWithApiKey);

                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        var jsonDoc = JsonDocument.Parse(content);

                        if (TryParseOptionQuote(jsonDoc.RootElement, symbol, out var quote))
                        {
                            quotes.Add(quote);
                        }
                    }

                    // Rate limiting delay
                    await Task.Delay(200); // 5 requests per second limit
                }

                _logger.LogDebug("Retrieved {Count} options quotes", quotes.Count);
                return quotes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving options quotes");
                return Enumerable.Empty<OptionQuote>();
            }
        }, "GetOptionsQuotes");
    }

    public async Task<IEnumerable<OptionData>> GetProtectivePutOptionsAsync(string underlyingSymbol, int daysToExpiration = 30)
    {
        var targetExpiration = DateTime.UtcNow.AddDays(daysToExpiration);
        var options = await GetOptionsDataAsync(underlyingSymbol, null, null, null);

        return options
            .Where(o => o.OptionType.ToLower() == "put")
            .Where(o => Math.Abs((o.ExpirationDate - targetExpiration).TotalDays) <= 7) // Within a week of target
            .OrderBy(o => Math.Abs((o.ExpirationDate - targetExpiration).TotalDays))
            .ThenBy(o => o.Strike);
    }

    public async Task<IEnumerable<OptionData>> GetCoveredCallOptionsAsync(string underlyingSymbol, decimal currentPrice, int daysToExpiration = 7)
    {
        var targetExpiration = DateTime.UtcNow.AddDays(daysToExpiration);
        var options = await GetOptionsDataAsync(underlyingSymbol, null, currentPrice, null);

        return options
            .Where(o => o.OptionType.ToLower() == "call")
            .Where(o => o.Strike > currentPrice) // OTM calls only
            .Where(o => Math.Abs((o.ExpirationDate - targetExpiration).TotalDays) <= 3) // Within 3 days of target
            .OrderBy(o => Math.Abs((o.ExpirationDate - targetExpiration).TotalDays))
            .ThenBy(o => o.Strike);
    }

    public async Task<IEnumerable<string>> GetUniverseWithAdvFilterAsync(decimal minAdv = 20_000_000m)
    {
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();

        return await rateLimitHelper.ExecuteAsync<IEnumerable<string>>(async () =>
        {
            try
            {
                var httpClient = _polygonFactory.CreateClient();

                // Get tickers with ADV filter from Polygon
                var url = $"/v3/reference/tickers?market=stocks&active=true&limit=1000";
                var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                var response = await httpClient.GetAsync(urlWithApiKey);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Failed to get universe from Polygon, using default symbols");
                    return GetDefaultUniverse();
                }

                var content = await response.Content.ReadAsStringAsync();
                var jsonDoc = JsonDocument.Parse(content);

                var symbols = new List<string> { "SPY" }; // Always include SPY

                if (jsonDoc.RootElement.TryGetProperty("results", out var results))
                {
                    foreach (var ticker in results.EnumerateArray())
                    {
                        if (ticker.TryGetProperty("ticker", out var symbolElement))
                        {
                            var symbol = symbolElement.GetString();
                            if (!string.IsNullOrEmpty(symbol) && symbol.Length <= 5 && !symbol.Contains("."))
                            {
                                symbols.Add(symbol);
                            }
                        }

                        if (symbols.Count >= 500) break; // Limit to top 500
                    }
                }

                _logger.LogInformation("Retrieved {Count} symbols for universe screening", symbols.Count);
                return symbols.Distinct();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting universe with ADV filter, using defaults");
                return GetDefaultUniverse();
            }
        }, "GetUniverseWithAdvFilter");
    }

    public async Task<VixAnalysis> GetVixAnalysisAsync()
    {
        try
        {
            // Get current VIX value with comprehensive fallback strategy
            var currentVix = await GetVixValueWithFallbackAsync();

            // FIXED: Get 30 days of VIX data for SMA calculation with proper date handling
            var endDate = DateTime.SpecifyKind(DateTime.UtcNow.Date.AddDays(-1), DateTimeKind.Utc); // Use yesterday to avoid future date issues
            var startDate = DateTime.SpecifyKind(endDate.AddDays(-45), DateTimeKind.Utc); // 45 days back from yesterday

            // Validate date range
            if (startDate > endDate)
            {
                _logger.LogWarning("Invalid VIX analysis date range: start {Start:yyyy-MM-dd} > end {End:yyyy-MM-dd}, adjusting",
                    startDate, endDate);
                startDate = endDate.AddDays(-45);
            }

            var vixBars = await GetIndexBarsAsync("I:VIX", startDate, endDate);
            var vixBarsList = vixBars.ToList();

            if (vixBarsList.Count < 30)
            {
                _logger.LogError("Insufficient VIX historical data for analysis - cannot proceed without real data");
                throw new VixDataUnavailableException("Insufficient VIX historical data for analysis");
            }

            // Calculate 30-day SMA
            var last30Bars = vixBarsList.TakeLast(30).ToList();
            var vixSma30 = last30Bars.Average(b => b.Close);

            // Calculate change metrics
            var previousVix = vixBarsList.Count > 1 ? vixBarsList[^2].Close : currentVix;
            var vixChange = currentVix - previousVix;
            var vixChangePercent = previousVix != 0 ? vixChange / previousVix : 0;

            // Determine if above SMA and if spike
            var isAboveSma = currentVix > vixSma30;
            var isSpike = vixChangePercent > 0.20m || currentVix > 30m; // 20% spike or above 30

            return new VixAnalysis(currentVix, vixSma30, vixChange, vixChangePercent,
                isAboveSma, isSpike, DateTime.UtcNow);
        }
        catch (VixDataUnavailableException)
        {
            // Re-throw VIX data exceptions to halt trading
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing VIX analysis - cannot proceed without real VIX data");
            throw new VixDataUnavailableException("VIX analysis failed - real data required");
        }
    }

    public async Task<bool> IsVixSpikeAsync(decimal threshold = 25.0m)
    {
        try
        {
            var vixAnalysis = await GetVixAnalysisAsync();
            var isSpike = vixAnalysis.CurrentVix > threshold || vixAnalysis.IsSpike;

            _logger.LogDebug("VIX spike check: Current={Current:F2}, Threshold={Threshold:F2}, IsSpike={IsSpike}",
                vixAnalysis.CurrentVix, threshold, isSpike);

            return isSpike;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking VIX spike, defaulting to false");
            return false; // Conservative default - assume no spike if we can't determine
        }
    }

    /// <summary>
    /// Gets VIX value with comprehensive fallback strategy:
    /// 1. Primary: Polygon API (I:VIX) with Redis caching
    /// 2. Fallback 1: Web scraping with timeout
    /// 3. Fallback 2: Synthetic VIX using VIX ETFs from Alpaca.Markets
    /// 4. Fallback 3: VIX ETFs from Polygon if Alpaca fails
    /// 5. Final: Halt trading for 15 minutes and retry entire cycle
    /// </summary>
    private async Task<decimal> GetVixValueWithFallbackAsync()
    {
        var dataSource = "Unknown";

        // Skip Polygon VIX during after-hours to avoid timeouts - go straight to web scraping
        if (IsMarketHours())
        {
            try
            {
                // Primary: Try Polygon API first with configured VIX timeout (only during market hours)
                dataSource = "Polygon";
                var vixTimeout = _timeouts?.MarketData.VixData ?? TimeSpan.FromSeconds(45);
                using var polygonCts = new CancellationTokenSource(vixTimeout);
                var polygonVix = await GetIndexValueAsync("I:VIX").WaitAsync(polygonCts.Token);
                if (polygonVix.HasValue && polygonVix.Value > 0)
                {
                    _logger.LogDebug("VIX retrieved from {DataSource}: {Value:F2}", dataSource, polygonVix.Value);
                    await CachePolygonVixAsync(polygonVix.Value);
                    return polygonVix.Value;
                }

                _logger.LogWarning("Polygon VIX data unavailable (null or zero value), moving to web scraping fallback");
            }
            catch (OperationCanceledException)
            {
                var vixTimeout = _timeouts?.MarketData.VixData ?? TimeSpan.FromSeconds(45);
                _logger.LogWarning("Polygon VIX request timed out after {Timeout}, moving to web scraping fallback", vixTimeout);
            }
            catch (HttpRequestException httpEx)
            {
                _logger.LogWarning(httpEx, "Polygon VIX HTTP request failed, moving to web scraping fallback");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Polygon VIX access failed, moving to web scraping fallback");
            }
        }
        else
        {
            _logger.LogDebug("Market closed - skipping Polygon VIX API and using web scraping directly");
        }

        try
        {
            // Fallback 1: Try web scraping with timeout (use configured timeout or 20 seconds default)
            dataSource = "WebScraping";
            var webScrapingTimeout = _timeouts?.MarketData.VixData ?? TimeSpan.FromSeconds(20);
            using var webCts = new CancellationTokenSource(webScrapingTimeout);
            var webVix = await _vixFallbackService.GetVixFromWebAsync().WaitAsync(webCts.Token);
            if (webVix.HasValue && webVix.Value > 0)
            {
                _logger.LogInformation("VIX retrieved from web scraping: {Value:F2}", webVix.Value);
                return webVix.Value;
            }

            _logger.LogWarning("Web scraping VIX data unavailable, moving to synthetic VIX calculation from Alpaca");
        }
        catch (OperationCanceledException)
        {
            var webScrapingTimeout = _timeouts?.MarketData.VixData ?? TimeSpan.FromSeconds(20);
            _logger.LogWarning("Web scraping VIX request timed out after {Timeout}, moving to synthetic VIX calculation", webScrapingTimeout);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Web scraping VIX access failed, moving to synthetic VIX calculation from Alpaca");
        }

        try
        {
            // Fallback 2: Calculate synthetic VIX using VIX ETFs from Alpaca.Markets
            dataSource = "SyntheticAlpaca";
            using var alpacaCts = new CancellationTokenSource(TimeSpan.FromSeconds(15));
            var syntheticVixAlpaca = await _vixFallbackService.CalculateSyntheticVixAsync().WaitAsync(alpacaCts.Token);
            if (syntheticVixAlpaca.HasValue && syntheticVixAlpaca.Value > 0)
            {
                _logger.LogInformation("VIX calculated synthetically from Alpaca: {Value:F2}", syntheticVixAlpaca.Value);
                return syntheticVixAlpaca.Value;
            }

            _logger.LogWarning("Synthetic VIX calculation from Alpaca failed, moving to Polygon ETF fallback");
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Synthetic VIX calculation from Alpaca timed out after 15 seconds, moving to Polygon ETF fallback");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Alpaca synthetic VIX calculation failed, moving to Polygon ETF fallback");
        }

        try
        {
            // Fallback 3: Calculate synthetic VIX using VIX ETFs from Polygon
            dataSource = "SyntheticPolygon";
            using var polygonEtfCts = new CancellationTokenSource(TimeSpan.FromSeconds(15));
            var syntheticVixPolygon = await _vixFallbackService.CalculateSyntheticVixFromPolygonAsync().WaitAsync(polygonEtfCts.Token);
            if (syntheticVixPolygon.HasValue && syntheticVixPolygon.Value > 0)
            {
                _logger.LogInformation("VIX calculated synthetically from Polygon: {Value:F2}", syntheticVixPolygon.Value);
                return syntheticVixPolygon.Value;
            }

            _logger.LogWarning("Synthetic VIX calculation from Polygon failed, halting trading for 15 minutes");
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Synthetic VIX calculation from Polygon timed out after 15 seconds, halting trading");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Polygon synthetic VIX calculation failed, halting trading for 15 minutes");
        }

        // Final fallback: Halt trading for 15 minutes and throw exception to trigger retry
        dataSource = "TradingHalt";
        _logger.LogCritical("🚨 CRITICAL: ALL VIX DATA SOURCES FAILED - HALTING TRADING FOR 15 MINUTES. " +
                           "Will retry entire VIX data acquisition cycle after 15-minute wait period. " +
                           "INVESTIGATE DATA SOURCE FAILURES IMMEDIATELY!");

        // Throw a specific exception that the trading service can catch to implement the 15-minute halt
        throw new VixDataUnavailableException("All VIX data sources failed - trading halted for 15 minutes");
    }

    /// <summary>
    /// Caches Polygon VIX data for fallback use
    /// </summary>
    private Task CachePolygonVixAsync(decimal vixValue)
    {
        try
        {
            // This would ideally cache the Polygon VIX data in Redis
            // For now, we'll let the VixFallbackService handle caching
            _logger.LogDebug("Polygon VIX value available for caching: {Value:F2}", vixValue);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error caching Polygon VIX data");
        }

        return Task.CompletedTask;
    }

    // === Helper Methods ===

    /// <summary>
    /// Parses a Polygon index bar from JSON, ensuring proper timezone conversion.
    /// Polygon timestamps are in milliseconds since Unix epoch (UTC) and are converted to DateTime UTC
    /// to maintain consistency with Alpaca bar timestamps.
    /// </summary>
    private static bool TryParseIndexBar(JsonElement barElement, out IndexBar indexBar)
    {
        indexBar = default;

        try
        {
            if (!barElement.TryGetProperty("t", out var timestampElement) ||
                !barElement.TryGetProperty("o", out var openElement) ||
                !barElement.TryGetProperty("h", out var highElement) ||
                !barElement.TryGetProperty("l", out var lowElement) ||
                !barElement.TryGetProperty("c", out var closeElement))
            {
                return false;
            }

            // CRITICAL: Convert Polygon timestamp (milliseconds since epoch, UTC) to DateTimeOffset first,
            // then to UTC DateTime to ensure proper timezone alignment with Alpaca data
            var timestampMs = timestampElement.GetInt64();
            var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampMs).UtcDateTime;

            var open = openElement.GetDecimal();
            var high = highElement.GetDecimal();
            var low = lowElement.GetDecimal();
            var close = closeElement.GetDecimal();

            // Volume is optional for indices (VIX doesn't have volume)
            var volume = 0L;
            if (barElement.TryGetProperty("v", out var volumeElement))
            {
                volume = volumeElement.GetInt64();
            }

            indexBar = new IndexBar(timestamp, open, high, low, close, volume);
            return true;
        }
        catch (Exception ex) when (ex is JsonException || ex is FormatException || ex is OverflowException)
        {
            // Log specific parsing errors for debugging
            return false;
        }
    }

    /// <summary>
    /// Parses a Polygon stock bar from JSON, ensuring proper timezone conversion.
    /// Polygon timestamps are in milliseconds since Unix epoch (UTC) and are converted to DateTime UTC
    /// to maintain consistency with Alpaca bar timestamps for seamless data mixing.
    /// </summary>
    private static bool TryParsePolygonBar(JsonElement barElement, string symbol, out PolygonBar? polygonBar)
    {
        polygonBar = null;

        try
        {
            if (!barElement.TryGetProperty("t", out var timestampElement) ||
                !barElement.TryGetProperty("o", out var openElement) ||
                !barElement.TryGetProperty("h", out var highElement) ||
                !barElement.TryGetProperty("l", out var lowElement) ||
                !barElement.TryGetProperty("c", out var closeElement) ||
                !barElement.TryGetProperty("v", out var volumeElement))
            {
                return false;
            }

            // CRITICAL: Convert Polygon timestamp (milliseconds since epoch, UTC) to DateTimeOffset first,
            // then to UTC DateTime to ensure proper timezone alignment with Alpaca data
            var timestampMs = timestampElement.GetInt64();

            var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampMs).UtcDateTime;

            // Handle scientific notation by parsing as double first, then converting to decimal
            var open = decimal.Parse(openElement.GetDouble().ToString("F6"));
            var high = decimal.Parse(highElement.GetDouble().ToString("F6"));
            var low = decimal.Parse(lowElement.GetDouble().ToString("F6"));
            var close = decimal.Parse(closeElement.GetDouble().ToString("F6"));
            var volume = (long)volumeElement.GetDouble(); // Handle scientific notation in volume

            polygonBar = new PolygonBar
            {
                Open = open,
                High = high,
                Low = low,
                Close = close,
                Volume = volume,
                Timestamp = timestampMs  // Use the original milliseconds value
            };
            return true;
        }
        catch (Exception ex) when (ex is JsonException || ex is FormatException || ex is OverflowException || ex is ArgumentOutOfRangeException)
        {
            // Log specific parsing errors for debugging
            return false;
        }
    }

    private static bool TryParseOptionData(JsonElement optionElement, out OptionData optionData)
    {
        optionData = default;

        try
        {
            if (!optionElement.TryGetProperty("ticker", out var tickerElement) ||
                !optionElement.TryGetProperty("underlying_ticker", out var underlyingElement))
            {
                return false;
            }

            var symbol = tickerElement.GetString() ?? "";
            var underlying = underlyingElement.GetString() ?? "";

            // Parse expiration date
            var expirationDate = DateTime.MinValue;
            if (optionElement.TryGetProperty("expiration_date", out var expElement))
            {
                DateTime.TryParse(expElement.GetString(), out expirationDate);
            }

            // Parse strike price
            var strike = 0m;
            if (optionElement.TryGetProperty("strike_price", out var strikeElement))
            {
                strike = strikeElement.GetDecimal();
            }

            // Parse option type
            var optionType = "";
            if (optionElement.TryGetProperty("contract_type", out var typeElement))
            {
                optionType = typeElement.GetString() ?? "";
            }

            optionData = new OptionData(
                symbol, underlying, expirationDate, strike, optionType,
                null, null, null, null, null, null, null, null, null, null);

            return true;
        }
        catch
        {
            return false;
        }
    }

    private static bool TryParseVixTermData(JsonElement contractElement, out VixTermData vixData)
    {
        vixData = default;

        try
        {
            if (!contractElement.TryGetProperty("expiration_date", out var expElement))
            {
                return false;
            }

            if (!DateTime.TryParse(expElement.GetString(), out var expirationDate))
            {
                return false;
            }

            // For now, we'll need to get the price separately
            // This is a simplified implementation
            var price = 0m;
            var daysToExpiration = (int)(expirationDate - DateTime.UtcNow).TotalDays;

            vixData = new VixTermData(expirationDate, price, daysToExpiration);
            return true;
        }
        catch
        {
            return false;
        }
    }

    // === Additional Helper Methods ===

    private static IEnumerable<string> GetDefaultUniverse()
    {
        return new[]
        {
            "SPY", "QQQ", "IWM", "DIA", "VTI", "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA",
            "TSLA", "META", "BRK.B", "UNH", "JNJ", "V", "PG", "JPM", "HD", "MA"
        };
    }

    private static bool TryParseOptionQuote(JsonElement quoteElement, string symbol, out OptionQuote optionQuote)
    {
        optionQuote = default;

        try
        {
            if (!quoteElement.TryGetProperty("results", out var results) ||
                !results.TryGetProperty("bid", out var bidElement) ||
                !results.TryGetProperty("ask", out var askElement))
            {
                return false;
            }

            var bid = bidElement.GetDecimal();
            var ask = askElement.GetDecimal();
            var lastPrice = results.TryGetProperty("last", out var lastElement) ? lastElement.GetDecimal() : (bid + ask) / 2;

            // Parse option symbol to extract details (simplified)
            var parts = symbol.Split('C', 'P');
            var underlying = parts.Length > 0 ? parts[0] : "";
            var optionType = symbol.Contains('C') ? "call" : "put";

            optionQuote = new OptionQuote(
                symbol, underlying, DateTime.MinValue, 0, optionType,
                bid, ask, lastPrice, 0, 0, 0, 0, 0, 0, 0, DateTime.UtcNow);

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Adjusts same-day date requests to prevent future dates
    /// FIXED: Ensures we never create future dates during same-day adjustments
    /// </summary>
    /// <param name="startDate">Original start date</param>
    /// <param name="endDate">Original end date</param>
    /// <param name="symbol">Symbol for logging</param>
    /// <returns>Adjusted start and end dates that are guaranteed to be historical</returns>
    private (DateTime start, DateTime end) AdjustSameDayRequest(DateTime startDate, DateTime endDate, string symbol)
    {
        // FIXED: Prevent future dates by checking against max allowed date first
        var targetDate = endDate.Date;
        var maxAllowedDate = DateTime.SpecifyKind(DateTime.UtcNow.Date.AddDays(-1), DateTimeKind.Utc); // Yesterday is the latest allowed

        // Ensure we never create future dates
        if (targetDate > maxAllowedDate)
        {
            targetDate = maxAllowedDate;
            _logger.LogDebug("Adjusted target date from {OriginalDate:yyyy-MM-dd} to {AdjustedDate:yyyy-MM-dd} to prevent future date for {Symbol}",
                endDate.Date, targetDate, symbol);
        }

        var adjustedStart = targetDate;
        var adjustedEnd = targetDate.AddDays(1).AddTicks(-1); // End of day - now safe since targetDate is validated

        _logger.LogDebug("Adjusted same-day request for {Symbol}: {StartDate} to {EndDate}",
            symbol, adjustedStart.ToString("yyyy-MM-dd HH:mm:ss"), adjustedEnd.ToString("yyyy-MM-dd HH:mm:ss"));

        return (adjustedStart, adjustedEnd);
    }
}
