using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for market schedule coordination
/// </summary>
public interface IMarketScheduleCoordinatorService
{
    /// <summary>
    /// Get current market schedule status
    /// </summary>
    Task<MarketScheduleStatus> GetStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if market is currently open
    /// </summary>
    bool IsMarketOpen();

    /// <summary>
    /// Get next market event time
    /// </summary>
    DateTime GetNextMarketEventTime();

    /// <summary>
    /// Manually trigger universe refresh
    /// </summary>
    Task TriggerUniverseRefreshAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Manually trigger subscription setup
    /// </summary>
    Task TriggerSubscriptionSetupAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Manually trigger trading start
    /// </summary>
    Task TriggerTradingStartAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Background service that coordinates market schedule events:
/// - 8:30 AM ET: Universe refresh
/// - 9:25 AM ET: WebSocket subscription setup
/// - 9:30 AM ET: Trading engine start
/// </summary>
public sealed class MarketScheduleCoordinatorService : BackgroundService, IMarketScheduleCoordinatorService, IDisposable
{
    private readonly IDailyUniverseRefreshService _universeRefreshService;
    private readonly IWebSocketSymbolSubscriptionManager _subscriptionManager;
    private readonly ILogger<MarketScheduleCoordinatorService> _logger;
    private readonly MarketScheduleConfig _config;

    private readonly Timer _scheduleTimer;
    private readonly object _statusLock = new();

    private MarketScheduleStatus _currentStatus = new();
    private DateTime _lastUniverseRefresh = DateTime.MinValue;
    private DateTime _lastSubscriptionSetup = DateTime.MinValue;
    private DateTime _lastTradingStart = DateTime.MinValue;

    public MarketScheduleCoordinatorService(
        IDailyUniverseRefreshService universeRefreshService,
        IWebSocketSymbolSubscriptionManager subscriptionManager,
        IConfiguration configuration,
        ILogger<MarketScheduleCoordinatorService> logger)
    {
        _universeRefreshService = universeRefreshService ?? throw new ArgumentNullException(nameof(universeRefreshService));
        _subscriptionManager = subscriptionManager ?? throw new ArgumentNullException(nameof(subscriptionManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Load configuration
        _config = MarketScheduleConfig.Default;
        configuration.GetSection("MarketSchedule").Bind(_config);

        // Create timer for schedule checking (every minute)
        _scheduleTimer = new Timer(OnScheduleTimerElapsed, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        _logger.LogInformation("MarketScheduleCoordinatorService initialized with schedule: Universe={UniverseTime}, Subscription={SubscriptionTime}, Trading={TradingTime}",
            _config.UniverseRefreshTimeEt, _config.SubscriptionSetupTimeEt, _config.TradingStartTimeEt);

        // Initialize status
        UpdateStatus();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MarketScheduleCoordinatorService started");

        // Check if we need to perform any startup actions
        await CheckStartupActionsAsync(stoppingToken);

        // Keep the service running
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Check every 5 minutes
                UpdateStatus();
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }

        _logger.LogInformation("MarketScheduleCoordinatorService stopped");
    }

    public async Task<MarketScheduleStatus> GetStatusAsync(CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        lock (_statusLock)
        {
            return _currentStatus with { }; // Return a copy
        }
    }

    public bool IsMarketOpen()
    {
        var now = GetCurrentEasternTime();
        var today = now.Date;

        // Check if it's a weekday
        if (now.DayOfWeek == DayOfWeek.Saturday || now.DayOfWeek == DayOfWeek.Sunday)
        {
            return false;
        }

        // Check if it's within market hours
        var marketOpen = today.Add(_config.TradingStartTimeEt);
        var marketClose = today.Add(_config.TradingEndTimeEt);

        return now >= marketOpen && now <= marketClose;
    }

    public DateTime GetNextMarketEventTime()
    {
        var now = GetCurrentEasternTime();
        var today = now.Date;

        var events = new[]
        {
            today.Add(_config.UniverseRefreshTimeEt),
            today.Add(_config.SubscriptionSetupTimeEt),
            today.Add(_config.TradingStartTimeEt),
            today.Add(_config.TradingEndTimeEt)
        };

        // Find next event today
        var nextEventToday = events.Where(e => e > now).OrderBy(e => e).FirstOrDefault();
        if (nextEventToday != default)
        {
            return nextEventToday;
        }

        // No more events today, return tomorrow's first event
        var tomorrow = today.AddDays(1);
        return tomorrow.Add(_config.UniverseRefreshTimeEt);
    }

    public async Task TriggerUniverseRefreshAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Manually triggering universe refresh");
        try
        {
            await _universeRefreshService.RefreshUniverseAsync(cancellationToken);
            _lastUniverseRefresh = DateTime.UtcNow;
            UpdateStatus();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual universe refresh");
            throw;
        }
    }

    public async Task TriggerSubscriptionSetupAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Manually triggering subscription setup");
        try
        {
            await _subscriptionManager.RefreshSubscriptionsAsync(cancellationToken);
            _lastSubscriptionSetup = DateTime.UtcNow;
            UpdateStatus();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual subscription setup");
            throw;
        }
    }

    public async Task TriggerTradingStartAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Manually triggering trading start");
        try
        {
            // This would trigger the trading engine start
            // For now, we'll just log and update status
            _lastTradingStart = DateTime.UtcNow;
            UpdateStatus();
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual trading start");
            throw;
        }
    }

    private async Task CheckStartupActionsAsync(CancellationToken cancellationToken)
    {
        var now = GetCurrentEasternTime();
        var today = now.Date;

        // Check if we should perform universe refresh
        var universeRefreshTime = today.Add(_config.UniverseRefreshTimeEt);
        if (now >= universeRefreshTime && !HasCompletedTodayAction(_lastUniverseRefresh, universeRefreshTime))
        {
            _logger.LogInformation("Performing startup universe refresh");
            try
            {
                await TriggerUniverseRefreshAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during startup universe refresh");
            }
        }

        // Check if we should perform subscription setup
        var subscriptionSetupTime = today.Add(_config.SubscriptionSetupTimeEt);
        if (now >= subscriptionSetupTime && !HasCompletedTodayAction(_lastSubscriptionSetup, subscriptionSetupTime))
        {
            _logger.LogInformation("Performing startup subscription setup");
            try
            {
                await TriggerSubscriptionSetupAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during startup subscription setup");
            }
        }

        // Check if we should start trading
        var tradingStartTime = today.Add(_config.TradingStartTimeEt);
        if (now >= tradingStartTime && !HasCompletedTodayAction(_lastTradingStart, tradingStartTime))
        {
            _logger.LogInformation("Performing startup trading start");
            try
            {
                await TriggerTradingStartAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during startup trading start");
            }
        }
    }

    private async void OnScheduleTimerElapsed(object? state)
    {
        try
        {
            var now = GetCurrentEasternTime();
            var today = now.Date;

            // Check for universe refresh
            var universeRefreshTime = today.Add(_config.UniverseRefreshTimeEt);
            if (ShouldTriggerAction(now, universeRefreshTime, _lastUniverseRefresh))
            {
                _logger.LogInformation("Scheduled universe refresh triggered");
                await TriggerUniverseRefreshAsync(CancellationToken.None);
            }

            // Check for subscription setup
            var subscriptionSetupTime = today.Add(_config.SubscriptionSetupTimeEt);
            if (ShouldTriggerAction(now, subscriptionSetupTime, _lastSubscriptionSetup))
            {
                _logger.LogInformation("Scheduled subscription setup triggered");
                await TriggerSubscriptionSetupAsync(CancellationToken.None);
            }

            // Check for trading start
            var tradingStartTime = today.Add(_config.TradingStartTimeEt);
            if (ShouldTriggerAction(now, tradingStartTime, _lastTradingStart))
            {
                _logger.LogInformation("Scheduled trading start triggered");
                await TriggerTradingStartAsync(CancellationToken.None);
            }

            UpdateStatus();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during scheduled action check");
        }
    }

    private bool ShouldTriggerAction(DateTime now, DateTime scheduledTime, DateTime lastActionTime)
    {
        // Trigger if:
        // 1. Current time is past the scheduled time
        // 2. We haven't completed this action today
        // 3. We're within the trigger window (to avoid triggering too late)
        var triggerWindow = TimeSpan.FromMinutes(_config.TriggerWindowMinutes);
        return now >= scheduledTime && 
               now <= scheduledTime.Add(triggerWindow) &&
               !HasCompletedTodayAction(lastActionTime, scheduledTime);
    }

    private bool HasCompletedTodayAction(DateTime lastActionTime, DateTime scheduledTime)
    {
        return lastActionTime.Date == scheduledTime.Date && lastActionTime >= scheduledTime;
    }

    private void UpdateStatus()
    {
        var now = GetCurrentEasternTime();
        var today = now.Date;

        lock (_statusLock)
        {
            _currentStatus = new MarketScheduleStatus
            {
                CurrentTimeEt = now,
                IsMarketOpen = IsMarketOpen(),
                NextEventTime = GetNextMarketEventTime(),
                UniverseRefreshTime = today.Add(_config.UniverseRefreshTimeEt),
                SubscriptionSetupTime = today.Add(_config.SubscriptionSetupTimeEt),
                TradingStartTime = today.Add(_config.TradingStartTimeEt),
                TradingEndTime = today.Add(_config.TradingEndTimeEt),
                LastUniverseRefresh = _lastUniverseRefresh,
                LastSubscriptionSetup = _lastSubscriptionSetup,
                LastTradingStart = _lastTradingStart,
                UniverseRefreshCompleted = HasCompletedTodayAction(_lastUniverseRefresh, today.Add(_config.UniverseRefreshTimeEt)),
                SubscriptionSetupCompleted = HasCompletedTodayAction(_lastSubscriptionSetup, today.Add(_config.SubscriptionSetupTimeEt)),
                TradingStartCompleted = HasCompletedTodayAction(_lastTradingStart, today.Add(_config.TradingStartTimeEt))
            };
        }
    }

    private DateTime GetCurrentEasternTime()
    {
        // Convert UTC to Eastern Time
        var easternZone = TimeZoneInfo.FindSystemTimeZoneById("US Eastern Standard Time");
        return TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, easternZone);
    }

    public new void Dispose()
    {
        _scheduleTimer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Configuration for market schedule coordination
/// </summary>
public class MarketScheduleConfig
{
    /// <summary>
    /// Time to refresh universe (Eastern Time)
    /// </summary>
    public TimeSpan UniverseRefreshTimeEt { get; set; } = new TimeSpan(8, 30, 0); // 8:30 AM ET

    /// <summary>
    /// Time to setup WebSocket subscriptions (Eastern Time)
    /// </summary>
    public TimeSpan SubscriptionSetupTimeEt { get; set; } = new TimeSpan(9, 25, 0); // 9:25 AM ET

    /// <summary>
    /// Time to start trading (Eastern Time)
    /// </summary>
    public TimeSpan TradingStartTimeEt { get; set; } = new TimeSpan(9, 30, 0); // 9:30 AM ET

    /// <summary>
    /// Time to end trading (Eastern Time)
    /// </summary>
    public TimeSpan TradingEndTimeEt { get; set; } = new TimeSpan(16, 0, 0); // 4:00 PM ET

    /// <summary>
    /// Window in minutes after scheduled time to still trigger action
    /// </summary>
    public int TriggerWindowMinutes { get; set; } = 30;

    /// <summary>
    /// Default configuration
    /// </summary>
    public static MarketScheduleConfig Default => new()
    {
        UniverseRefreshTimeEt = new TimeSpan(8, 30, 0),
        SubscriptionSetupTimeEt = new TimeSpan(9, 25, 0),
        TradingStartTimeEt = new TimeSpan(9, 30, 0),
        TradingEndTimeEt = new TimeSpan(16, 0, 0),
        TriggerWindowMinutes = 30
    };
}

/// <summary>
/// Status information for market schedule coordination
/// </summary>
public record MarketScheduleStatus
{
    public DateTime CurrentTimeEt { get; init; }
    public bool IsMarketOpen { get; init; }
    public DateTime NextEventTime { get; init; }
    public DateTime UniverseRefreshTime { get; init; }
    public DateTime SubscriptionSetupTime { get; init; }
    public DateTime TradingStartTime { get; init; }
    public DateTime TradingEndTime { get; init; }
    public DateTime LastUniverseRefresh { get; init; }
    public DateTime LastSubscriptionSetup { get; init; }
    public DateTime LastTradingStart { get; init; }
    public bool UniverseRefreshCompleted { get; init; }
    public bool SubscriptionSetupCompleted { get; init; }
    public bool TradingStartCompleted { get; init; }
}
