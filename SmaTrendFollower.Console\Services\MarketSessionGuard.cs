using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Guards live trading operations to ensure they only occur during market hours (9:30 AM - 4:00 PM ET, Monday-Friday).
/// Note: This does NOT restrict data operations, API calls, or background processes like universe refresh,
/// which can run outside market hours with appropriate staleness thresholds (8 hours for after-market data).
/// </summary>
public sealed class MarketSessionGuard : IMarketSessionGuard
{
    private readonly ITimeProvider _timeProvider;
    private readonly IMarketCalendarService _marketCalendarService;
    private readonly ILogger<MarketSessionGuard> _logger;

    public string Reason { get; private set; } = string.Empty;

    public MarketSessionGuard(ITimeProvider timeProvider, IMarketCalendarService marketCalendarService, ILogger<MarketSessionGuard> logger)
    {
        _timeProvider = timeProvider;
        _marketCalendarService = marketCalendarService;
        _logger = logger;
    }

    public Task<bool> CanTradeNowAsync()
    {
        var now = _timeProvider.UtcNow;
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(now,
            TimeZoneInfo.FindSystemTimeZoneById("US Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
        {
            Reason = "Weekend - markets closed";
            _logger.LogInformation("Live trading blocked: {Reason}", Reason);
            return Task.FromResult(false);
        }

        // Check if it's a market holiday
        if (_marketCalendarService.IsMarketHoliday(easternTime.Date))
        {
            Reason = "Market holiday - markets closed";
            _logger.LogInformation("Live trading blocked: {Reason}", Reason);
            return Task.FromResult(false);
        }

        // Check if it's during market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);  // 9:30 AM ET
        var marketClose = new TimeSpan(16, 0, 0); // 4:00 PM ET
        var currentTime = easternTime.TimeOfDay;

        if (currentTime < marketOpen || currentTime > marketClose)
        {
            Reason = $"Outside market hours (9:30 AM - 4:00 PM ET) - current time: {easternTime:HH:mm:ss} ET";
            _logger.LogInformation("Live trading blocked: {Reason}", Reason);
            return Task.FromResult(false);
        }

        // Trading is allowed during market hours on weekdays
        Reason = string.Empty;
        _logger.LogInformation("Live trading allowed - market is open ({CurrentTime} ET)", easternTime.ToString("HH:mm:ss"));
        return Task.FromResult(true);
    }
}
