using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Prometheus;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that ensures model assets are available at startup
/// </summary>
internal sealed class ModelAssetBootstrapService : BackgroundService
{
    private static readonly Gauge ModelReady =
        Metrics.CreateGauge("model_assets_ready", "1 when model zip present", "type");

    private readonly ILogger<ModelAssetBootstrapService> _log;
    private readonly IServiceProvider _serviceProvider;

    private readonly string _posZip = Path.Combine("Model", "position_model.zip");
    private readonly string _slipZip = Path.Combine("Model", "slippage_model.zip");

    public ModelAssetBootstrapService(IServiceProvider serviceProvider, ILogger<ModelAssetBootstrapService> log)
    {
        _serviceProvider = serviceProvider;
        _log = log;
    }

    protected override async Task ExecuteAsync(CancellationToken ct)
    {
        Directory.CreateDirectory("Model");

        using var scope = _serviceProvider.CreateScope();
        var momTrainer = scope.ServiceProvider.GetRequiredService<IMomentumModelTrainer>();
        var slipTrainer = scope.ServiceProvider.GetRequiredService<ISlippageModelTrainer>();

        // Position model
        if (!File.Exists(_posZip) || new FileInfo(_posZip).Length == 0)
        {
            _log.LogWarning("position_model.zip missing → training default model");
            var ok = await momTrainer.TrainModelAsync(ct);
            ModelReady.WithLabels("position").Set(ok ? 1 : 0);
        }
        else
            ModelReady.WithLabels("position").Set(1);

        // Slippage model
        if (!File.Exists(_slipZip) || new FileInfo(_slipZip).Length == 0)
        {
            _log.LogWarning("slippage_model.zip missing → training default model");
            var ok = await slipTrainer.TrainAsync(ct);
            ModelReady.WithLabels("slippage").Set(ok ? 1 : 0);
        }
        else
            ModelReady.WithLabels("slippage").Set(1);

        _log.LogInformation("Model-asset bootstrap complete");
    }
}
