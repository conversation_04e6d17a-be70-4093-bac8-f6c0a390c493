using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Console.Extensions;
using StackExchange.Redis;
using System.Threading;
using MathNet.Numerics.LinearAlgebra;
using MathNet.Numerics.LinearAlgebra.Double;

namespace SmaTrendFollower.Services;

/// <summary>
/// Thread-safe storage for momentum model thresholds
/// </summary>
internal static class MomentumThresholds
{
    private static double[] _current = new double[] { 0, 0 };

    public static double[] Current
        => Volatile.Read(ref _current);

    public static void Update(double[] w)
        => Interlocked.Exchange(ref _current, w);
}

/// <summary>
/// Interface for momentum model training with sentiment integration
/// </summary>
public interface IMomentumModelTrainer
{
    /// <summary>
    /// Creates feature vector for ML model including sentiment data
    /// </summary>
    Task<float[]> CreateFeatureVectorAsync(string symbol, DateTime date, CancellationToken cancellationToken = default);

    /// <summary>
    /// Trains momentum model with sentiment features
    /// </summary>
    Task<bool> TrainModelAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sentiment score for a symbol on a specific date
    /// </summary>
    Task<double> GetSentimentScoreAsync(string symbol, DateTime date, CancellationToken cancellationToken = default);
}

/// <summary>
/// Momentum model trainer that incorporates news sentiment analysis into ML features
/// </summary>
public sealed class MomentumModelTrainer : IMomentumModelTrainer
{
    private readonly IDatabase _redisDatabase;
    private readonly ILogger<MomentumModelTrainer> _logger;
    private readonly IMomentumCache _momentumCache;
    private readonly IMarketDataService _marketDataService;

    public MomentumModelTrainer(
        IOptimizedRedisConnectionService redisConnectionService,
        ILogger<MomentumModelTrainer> logger,
        IMomentumCache momentumCache,
        IMarketDataService marketDataService)
    {
        _redisDatabase = redisConnectionService.GetDatabaseAsync().GetAwaiter().GetResult();
        _logger = logger;
        _momentumCache = momentumCache;
        _marketDataService = marketDataService;
    }

    /// <summary>
    /// Creates feature vector for ML model including sentiment data
    /// </summary>
    public async Task<float[]> CreateFeatureVectorAsync(string symbol, DateTime date, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get basic momentum features
            var endDate = date.Date;
            var startDate = endDate.AddDays(-200); // Get 200 days of data
            var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var bars = barsPage?.Items?.ToList();
            if (bars == null || bars.Count < 50)
            {
                // Use Debug level instead of Warning to reduce Discord noise
                // This is normal during ML training and doesn't require immediate attention
                _logger.LogDebug("Insufficient bar data for {Symbol} ({Count} bars, need 50+)", symbol, bars?.Count ?? 0);
                return new float[] { 0, 0, 0 }; // Default feature vector
            }

            // Calculate RSI
            var rsi = bars.GetRsi14();

            // Calculate MACD
            var macd = bars.GetMacd();
            var macdHistogram = macd.Histogram;

            // Get sentiment score
            var sentiment = await GetSentimentScoreAsync(symbol, date, cancellationToken);

            // Create feature vector: [RSI, MACD Histogram, Sentiment]
            var features = new float[]
            {
                (float)rsi,
                (float)macdHistogram,
                (float)sentiment
            };

            _logger.LogDebug("Created feature vector for {Symbol}: RSI={RSI:F2}, MACD={MACD:F4}, Sentiment={Sentiment:F3}",
                symbol, rsi, macdHistogram, sentiment);

            return features;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature vector for {Symbol}", symbol);
            return new float[] { 0, 0, 0 }; // Default feature vector on error
        }
    }

    /// <summary>
    /// Gets sentiment score for a symbol on a specific date
    /// </summary>
    public async Task<double> GetSentimentScoreAsync(string symbol, DateTime date, CancellationToken cancellationToken = default)
    {
        try
        {
            var dateKey = date.ToString("yyyyMMdd");
            var sentimentKey = $"Sentiment:{symbol}:{dateKey}";

            // Try to get the latest sentiment score for the symbol on the given date
            var latestSentiment = await _redisDatabase.HashGetAsync(sentimentKey, "latest");
            
            if (latestSentiment.HasValue && double.TryParse(latestSentiment, out var sentiment))
            {
                _logger.LogDebug("Retrieved sentiment for {Symbol} on {Date}: {Sentiment:F3}", 
                    symbol, dateKey, sentiment);
                return sentiment;
            }

            // If no latest sentiment, try to get any sentiment from that day
            var allSentiments = await _redisDatabase.HashGetAllAsync(sentimentKey);
            if (allSentiments.Any())
            {
                var sentimentValues = allSentiments
                    .Where(kv => kv.Name != "latest" && !string.IsNullOrEmpty(kv.Value) && double.TryParse(kv.Value, out _))
                    .Select(kv => double.Parse(kv.Value!))
                    .ToList();

                if (sentimentValues.Any())
                {
                    // Return average sentiment for the day
                    var avgSentiment = sentimentValues.Average();
                    _logger.LogDebug("Retrieved average sentiment for {Symbol} on {Date}: {Sentiment:F3} (from {Count} news items)",
                        symbol, dateKey, avgSentiment, sentimentValues.Count);
                    return avgSentiment;
                }
            }

            // No sentiment data found, return neutral
            _logger.LogDebug("No sentiment data found for {Symbol} on {Date}, returning neutral (0.0)", symbol, dateKey);
            return 0.0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sentiment for {Symbol} on {Date}", symbol, date);
            return 0.0; // Return neutral sentiment on error
        }
    }

    /// <summary>
    /// Trains momentum model with sentiment features
    /// </summary>
    public async Task<bool> TrainModelAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting momentum model training with sentiment features...");

            // Collect training data
            var trainingData = await CollectTrainingDataAsync(cancellationToken);
            if (trainingData.Count < 100)
            {
                _logger.LogWarning("Insufficient training data: {Count} samples", trainingData.Count);
                return false;
            }

            // Prepare feature matrix and target vector
            var features = trainingData.Select(d => d.Features).ToArray();
            var targets = trainingData.Select(d => d.Target).ToArray();

            // Train robust OLS model using MathNet.Numerics
            var ols = new RobustOLSRegression();
            ols.Fit(features, targets);

            // Update thresholds with thread-safe operation
            MomentumThresholds.Update(ols.Weights);

            _logger.LogInformation("Momentum model training completed successfully. Weights: [{Weights}]",
                string.Join(", ", ols.Weights.Select(w => w.ToString("F4"))));

            return true;
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Momentum model training was cancelled");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during momentum model training");
            return false;
        }
    }

    /// <summary>
    /// Collects training data for momentum model
    /// </summary>
    private async Task<List<TrainingDataPoint>> CollectTrainingDataAsync(CancellationToken cancellationToken)
    {
        var trainingData = new List<TrainingDataPoint>();
        var endDate = DateTime.UtcNow.Date;
        var startDate = endDate.AddDays(-365); // One year of training data

        try
        {
            // Get universe of symbols (simplified - would use actual universe provider)
            var symbols = new[] { "SPY", "QQQ", "IWM", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META" };

            foreach (var symbol in symbols)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var currentDate = startDate;
                while (currentDate <= endDate)
                {
                    try
                    {
                        var features = await CreateFeatureVectorAsync(symbol, currentDate, cancellationToken);

                        // Calculate target (next day return)
                        var nextDate = currentDate.AddDays(1);
                        var target = await CalculateNextDayReturnAsync(symbol, currentDate, nextDate, cancellationToken);

                        if (target.HasValue && features.Length == 3)
                        {
                            trainingData.Add(new TrainingDataPoint
                            {
                                Features = features,
                                Target = (double)target.Value
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Error collecting training data for {Symbol} on {Date}", symbol, currentDate);
                    }

                    currentDate = currentDate.AddDays(1);
                }
            }

            _logger.LogInformation("Collected {Count} training data points", trainingData.Count);
            return trainingData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting training data");
            return trainingData;
        }
    }

    /// <summary>
    /// Calculates next day return for target variable
    /// </summary>
    private async Task<decimal?> CalculateNextDayReturnAsync(string symbol, DateTime currentDate, DateTime nextDate, CancellationToken cancellationToken)
    {
        try
        {
            var currentBars = await _marketDataService.GetStockBarsAsync(symbol, currentDate, currentDate.AddDays(1));
            var nextBars = await _marketDataService.GetStockBarsAsync(symbol, nextDate, nextDate.AddDays(1));

            var currentBar = currentBars?.Items?.FirstOrDefault();
            var nextBar = nextBars?.Items?.FirstOrDefault();

            if (currentBar != null && nextBar != null && currentBar.Close > 0)
            {
                return (nextBar.Close - currentBar.Close) / currentBar.Close;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error calculating return for {Symbol}", symbol);
            return null;
        }
    }
}

/// <summary>
/// Training data point for momentum model
/// </summary>
internal class TrainingDataPoint
{
    public float[] Features { get; set; } = Array.Empty<float>();
    public double Target { get; set; }
}

/// <summary>
/// Robust OLS regression implementation using MathNet.Numerics
/// Uses SVD-based pseudo-inverse to handle singular matrices and correlated features
/// </summary>
internal class RobustOLSRegression
{
    public double[] Weights { get; private set; } = Array.Empty<double>();

    public void Fit(float[][] features, double[] targets)
    {
        if (features.Length != targets.Length)
            throw new ArgumentException("Features and targets must have same length");

        var n = features.Length;
        var p = features[0].Length + 1; // +1 for intercept

        try
        {
            // Create design matrix with intercept using MathNet.Numerics
            var X = DenseMatrix.Create(n, p, 0.0);
            var y = DenseVector.Create(n, 0.0);

            for (int i = 0; i < n; i++)
            {
                X[i, 0] = 1.0; // Intercept
                for (int j = 0; j < features[i].Length; j++)
                {
                    X[i, j + 1] = features[i][j];
                }
                y[i] = targets[i];
            }

            // Use SVD-based pseudo-inverse for numerical stability
            // This handles singular matrices and correlated features gracefully
            var svd = X.Svd(true);
            var weights = svd.Solve(y);

            Weights = weights.ToArray();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to fit regression model: {ex.Message}", ex);
        }
    }

}
