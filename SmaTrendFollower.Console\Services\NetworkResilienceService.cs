using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service to monitor and improve network resilience for database and API connections
/// </summary>
public interface INetworkResilienceService
{
    /// <summary>
    /// Check if a network endpoint is reachable
    /// </summary>
    Task<bool> IsEndpointReachableAsync(string host, int port, TimeSpan timeout);
    
    /// <summary>
    /// Get network health status
    /// </summary>
    Task<NetworkHealthStatus> GetNetworkHealthAsync();
    
    /// <summary>
    /// Handle socket exception with appropriate retry logic
    /// </summary>
    Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3, TimeSpan? delay = null);
    
    /// <summary>
    /// Handle socket exception for void operations
    /// </summary>
    Task ExecuteWithRetryAsync(Func<Task> operation, int maxRetries = 3, TimeSpan? delay = null);
}

public class NetworkHealthStatus
{
    public bool IsHealthy { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public DateTime CheckTime { get; set; }
    public Dictionary<string, bool> EndpointStatus { get; set; } = new();
}

public class NetworkResilienceService : INetworkResilienceService
{
    private readonly ILogger<NetworkResilienceService> _logger;
    private readonly NetworkResilienceOptions _options;

    public NetworkResilienceService(ILogger<NetworkResilienceService> logger, IOptions<NetworkResilienceOptions> options)
    {
        _logger = logger;
        _options = options.Value;
    }

    public async Task<bool> IsEndpointReachableAsync(string host, int port, TimeSpan timeout)
    {
        try
        {
            using var client = new TcpClient();
            var connectTask = client.ConnectAsync(host, port);
            var timeoutTask = Task.Delay(timeout);
            
            var completedTask = await Task.WhenAny(connectTask, timeoutTask);
            
            if (completedTask == timeoutTask)
            {
                _logger.LogWarning("Connection to {Host}:{Port} timed out after {Timeout}", host, port, timeout);
                return false;
            }
            
            if (connectTask.IsFaulted)
            {
                _logger.LogWarning(connectTask.Exception, "Failed to connect to {Host}:{Port}", host, port);
                return false;
            }
            
            return client.Connected;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking endpoint {Host}:{Port}", host, port);
            return false;
        }
    }

    public async Task<NetworkHealthStatus> GetNetworkHealthAsync()
    {
        var status = new NetworkHealthStatus
        {
            CheckTime = DateTime.UtcNow
        };

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            // Check critical endpoints
            var endpoints = new[]
            {
                ("PostgreSQL", "*************", 5432),
                ("Redis", "localhost", 6379),
                ("Polygon", "api.polygon.io", 443),
                ("Alpaca", "paper-api.alpaca.markets", 443)
            };

            var tasks = endpoints.Select(async endpoint =>
            {
                var (name, host, port) = endpoint;
                var isReachable = await IsEndpointReachableAsync(host, port, TimeSpan.FromSeconds(5));
                status.EndpointStatus[name] = isReachable;
                return isReachable;
            });

            var results = await Task.WhenAll(tasks);
            status.IsHealthy = results.All(r => r);
            
            if (!status.IsHealthy)
            {
                var failedEndpoints = status.EndpointStatus.Where(kvp => !kvp.Value).Select(kvp => kvp.Key);
                status.ErrorMessage = $"Failed endpoints: {string.Join(", ", failedEndpoints)}";
            }
        }
        catch (Exception ex)
        {
            status.IsHealthy = false;
            status.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Error during network health check");
        }
        finally
        {
            stopwatch.Stop();
            status.ResponseTime = stopwatch.Elapsed;
        }

        return status;
    }

    public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3, TimeSpan? delay = null)
    {
        var retryDelay = delay ?? TimeSpan.FromSeconds(2);
        Exception? lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex) when (IsRetriableException(ex))
            {
                lastException = ex;
                
                if (attempt == maxRetries)
                {
                    _logger.LogError(ex, "Operation failed after {MaxRetries} attempts", maxRetries);
                    break;
                }

                var currentDelay = TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * Math.Pow(2, attempt - 1));
                _logger.LogWarning(ex, "Operation failed on attempt {Attempt}/{MaxRetries}, retrying in {Delay}ms", 
                    attempt, maxRetries, currentDelay.TotalMilliseconds);
                
                await Task.Delay(currentDelay);
            }
        }

        throw lastException ?? new InvalidOperationException("Operation failed without exception");
    }

    public async Task ExecuteWithRetryAsync(Func<Task> operation, int maxRetries = 3, TimeSpan? delay = null)
    {
        await ExecuteWithRetryAsync(async () =>
        {
            await operation();
            return true;
        }, maxRetries, delay);
    }

    private static bool IsRetriableException(Exception ex)
    {
        return ex switch
        {
            SocketException socketEx => IsRetriableSocketError(socketEx.SocketErrorCode),
            IOException ioEx when ioEx.InnerException is SocketException socketEx => IsRetriableSocketError(socketEx.SocketErrorCode),
            TimeoutException => true,
            TaskCanceledException => true,
            HttpRequestException httpEx => IsRetriableHttpException(httpEx),
            _ => false
        };
    }

    private static bool IsRetriableSocketError(SocketError error)
    {
        return error switch
        {
            SocketError.ConnectionReset => true,
            SocketError.ConnectionAborted => true,
            SocketError.ConnectionRefused => true,
            SocketError.TimedOut => true,
            SocketError.NetworkDown => true,
            SocketError.NetworkUnreachable => true,
            SocketError.HostDown => true,
            SocketError.HostUnreachable => true,
            SocketError.TryAgain => true,
            _ => false
        };
    }

    private static bool IsRetriableHttpException(HttpRequestException ex)
    {
        var message = ex.Message.ToLowerInvariant();
        return message.Contains("timeout") ||
               message.Contains("connection") ||
               message.Contains("network") ||
               message.Contains("socket");
    }
}

public class NetworkResilienceOptions
{
    public int DefaultMaxRetries { get; set; } = 3;
    public TimeSpan DefaultRetryDelay { get; set; } = TimeSpan.FromSeconds(2);
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(5);
    public TimeSpan EndpointTimeout { get; set; } = TimeSpan.FromSeconds(10);
}
