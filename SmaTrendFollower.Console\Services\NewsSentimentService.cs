using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Threading.Channels;
using System.Net.Http.Json;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service that polls Alpaca news API via HTTP and queues fresh articles for FinBERT sentiment analysis.
/// Implements polling pattern with configurable lookback window to avoid duplicate processing.
/// </summary>
internal sealed class NewsSentimentService
{
    private readonly IHttpClientFactory _httpFactory;
    private readonly ChannelWriter<HeadlineItem> _writer;
    private readonly ILogger<NewsSentimentService> _log;
    private readonly TimeSpan _poll;
    private DateTime _last = DateTime.UtcNow.AddMinutes(-20); // Start with 20min lookback

    /// <summary>
    /// Initializes the news sentiment service with HTTP client and channel writer.
    /// </summary>
    /// <param name="httpFactory">HTTP client factory for Alpaca API calls</param>
    /// <param name="channel">Channel for queuing news articles to FinBERT worker</param>
    /// <param name="opt">FinBERT configuration options</param>
    /// <param name="log">Logger for service operations</param>
    public NewsSentimentService(
        IHttpClientFactory httpFactory,
        Channel<HeadlineItem> channel,
        IOptions<FinbertOptions> opt,
        ILogger<NewsSentimentService> log)
    {
        _httpFactory = httpFactory ?? throw new ArgumentNullException(nameof(httpFactory));
        _writer = channel.Writer ?? throw new ArgumentNullException(nameof(channel));
        _log = log ?? throw new ArgumentNullException(nameof(log));
        _poll = TimeSpan.FromMinutes(opt.Value.LookbackMins);
    }

    /// <summary>
    /// Continuously polls Alpaca news API via HTTP for fresh articles and queues them for processing.
    /// Maintains last-seen timestamp to avoid duplicate processing.
    /// </summary>
    /// <param name="ct">Cancellation token for graceful shutdown</param>
    public async Task PollAsync(CancellationToken ct)
    {
        _log.LogInformation("Starting news polling with {Interval} interval", _poll);

        while (!ct.IsCancellationRequested)
        {
            try
            {
                // Use Alpaca HTTP client to fetch news
                var httpClient = _httpFactory.CreateClient("Alpaca");
                var url = "v1beta1/news?limit=50"; // Fetch latest 50 news articles

                var response = await httpClient.GetAsync(url, ct);
                response.EnsureSuccessStatusCode();

                var newsResponse = await response.Content.ReadFromJsonAsync<AlpacaNewsResponse>(cancellationToken: ct);
                if (newsResponse?.news == null)
                {
                    _log.LogWarning("No news data received from Alpaca API");
                    continue;
                }

                var fresh = newsResponse.news.Where(n => n.CreatedAtUtc > _last).ToList();

                // Queue fresh articles for FinBERT processing
                foreach (var article in fresh)
                {
                    // Convert NewsArticle to HeadlineItem for unified processing
                    var headlineItem = new HeadlineItem(
                        article.Id.ToString(),
                        article.Symbols.FirstOrDefault() ?? "UNKNOWN",
                        article.Headline,
                        article.CreatedAtUtc);
                    await _writer.WriteAsync(headlineItem, ct);
                }

                // Update last-seen timestamp
                if (newsResponse.news.Any())
                {
                    _last = newsResponse.news.Max(n => n.CreatedAtUtc);
                }

                _log.LogDebug("Queued {Count} fresh news items (total fetched: {Total})",
                    fresh.Count, newsResponse.news.Length);
            }
            catch (Exception ex)
            {
                _log.LogError(ex, "News poll failed");
            }

            // Wait for next polling cycle
            await Task.Delay(_poll, ct);
        }

        _log.LogInformation("News polling stopped");
    }

}

/// <summary>
/// Alpaca news API response model matching the expected JSON structure.
/// </summary>
/// <param name="news">Array of news articles</param>
/// <param name="next_page_token">Token for pagination (if any)</param>
internal sealed record AlpacaNewsResponse(NewsArticle[] news, string? next_page_token);

/// <summary>
/// News article model compatible with FinBERT worker processing.
/// Represents a single news article from Alpaca with required fields for sentiment analysis.
/// </summary>
public sealed record NewsArticle
{
    /// <summary>
    /// Unique identifier for the news article
    /// </summary>
    public long Id { get; init; }

    /// <summary>
    /// News article headline/title
    /// </summary>
    public string Headline { get; init; } = string.Empty;

    /// <summary>
    /// List of stock symbols mentioned in the article
    /// </summary>
    public List<string> Symbols { get; init; } = new();

    /// <summary>
    /// UTC timestamp when the article was created
    /// </summary>
    public DateTime CreatedAtUtc { get; init; }

    /// <summary>
    /// Article summary or content (optional)
    /// </summary>
    public string? Summary { get; init; }

    /// <summary>
    /// Article author (optional)
    /// </summary>
    public string? Author { get; init; }

    /// <summary>
    /// Article URL (optional)
    /// </summary>
    public string? Url { get; init; }
}
