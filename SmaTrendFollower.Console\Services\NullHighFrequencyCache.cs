using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Null implementation of IHighFrequencyCache for when Redis is not available
/// Provides graceful degradation by returning null/empty results without caching
/// </summary>
public sealed class NullHighFrequencyCache : IHighFrequencyCache
{
    private readonly ILogger<NullHighFrequencyCache> _logger;

    public NullHighFrequencyCache(ILogger<NullHighFrequencyCache> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _logger.LogInformation("High-frequency cache disabled - Redis not available");
    }

    public Task CacheBarSnapshotAsync(string symbol, IBar bar, TimeSpan? expiry = null)
    {
        // No-op: Redis not available
        return Task.CompletedTask;
    }

    public Task<IBar?> GetBarSnapshotAsync(string symbol)
    {
        // Always return null: no cache available
        return Task.FromResult<IBar?>(null);
    }

    public Task CacheBatchBarSnapshotsAsync(Dictionary<string, IBar> barSnapshots, TimeSpan? expiry = null)
    {
        // No-op: Redis not available
        return Task.CompletedTask;
    }

    public Task<Dictionary<string, IBar?>> GetBatchBarSnapshotsAsync(IEnumerable<string> symbols)
    {
        // Return empty results for all symbols
        var result = symbols.ToDictionary(symbol => symbol, _ => (IBar?)null);
        return Task.FromResult(result);
    }

    public Task CacheSignalStatusAsync(string symbol, string status, TimeSpan? expiry = null)
    {
        // No-op: Redis not available
        return Task.CompletedTask;
    }

    public Task<string?> GetSignalStatusAsync(string symbol)
    {
        // Always return null: no cache available
        return Task.FromResult<string?>(null);
    }

    public Task CachePositionSizeAsync(string symbol, decimal positionSize, TimeSpan? expiry = null)
    {
        // No-op: Redis not available
        return Task.CompletedTask;
    }

    public Task<decimal?> GetPositionSizeAsync(string symbol)
    {
        // Always return null: no cache available
        return Task.FromResult<decimal?>(null);
    }

    public Task ClearSymbolCacheAsync(string symbol)
    {
        // No-op: Redis not available
        return Task.CompletedTask;
    }

    public Task ClearAllCacheAsync()
    {
        // No-op: Redis not available
        return Task.CompletedTask;
    }

    public Task<HighFrequencyCacheStats> GetCacheStatsAsync()
    {
        // Return empty stats
        return Task.FromResult(new HighFrequencyCacheStats
        {
            TotalKeys = 0,
            BarSnapshotKeys = 0,
            SignalStatusKeys = 0,
            PositionSizeKeys = 0,
            MemoryUsageBytes = 0
        });
    }
}
