using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Optimized signal generation service that reduces database queries and API calls
/// through intelligent caching and batch processing
/// </summary>
public class OptimizedSignalGenerationService : ISignalGenerator
{
    private readonly ISignalGenerator _baseSignalGenerator;
    private readonly ILogger<OptimizedSignalGenerationService> _logger;
    private readonly OptimizedSignalConfiguration _config;

    // Performance caches
    private readonly ConcurrentDictionary<string, CachedSignalData> _signalCache = new();
    private readonly ConcurrentDictionary<string, DateTime> _lastProcessedTime = new();
    private readonly SemaphoreSlim _processingLock = new(1, 1);

    // Performance metrics
    private readonly ConcurrentDictionary<string, PerformanceMetrics> _performanceMetrics = new();

    public OptimizedSignalGenerationService(
        ISignalGenerator baseSignalGenerator,
        ILogger<OptimizedSignalGenerationService> logger,
        IOptions<OptimizedSignalConfiguration> config)
    {
        _baseSignalGenerator = baseSignalGenerator;
        _logger = logger;
        _config = config.Value;
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default)
    {
        // Use the base signal generator for the main interface
        return await _baseSignalGenerator.RunAsync(topN, cancellationToken);
    }

    public async Task<List<TradingSignal>> GenerateSignalsAsync(List<string> symbols, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var results = new ConcurrentBag<TradingSignal>();
        var processedCount = 0;
        var cacheHits = 0;
        var errors = 0;

        try
        {
            // Filter symbols that need processing
            var symbolsToProcess = await FilterSymbolsForProcessingAsync(symbols);
            
            _logger.LogInformation("🚀 Optimized signal generation: {Total} symbols, {ToProcess} need processing, {Cached} cached",
                symbols.Count, symbolsToProcess.Count, symbols.Count - symbolsToProcess.Count);

            // Process in optimized batches
            var batches = CreateOptimizedBatches(symbolsToProcess);
            
            var batchResult = await ProcessBatchesAsync(batches, results, cancellationToken);
            processedCount = batchResult.processedCount;
            errors = batchResult.errors;

            // Add cached signals
            cacheHits = await AddCachedSignalsAsync(symbols.Except(symbolsToProcess), results);

            stopwatch.Stop();
            
            var finalResults = results.ToList();
            LogPerformanceMetrics(stopwatch.Elapsed, symbols.Count, processedCount, cacheHits, errors, finalResults.Count);
            
            return finalResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Optimized signal generation failed after {Elapsed}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    private Task<List<string>> FilterSymbolsForProcessingAsync(List<string> symbols)
    {
        var symbolsToProcess = new List<string>();
        var now = DateTime.UtcNow;

        foreach (var symbol in symbols)
        {
            // Check if we have recent cached data
            if (_signalCache.TryGetValue(symbol, out var cachedData) && 
                _lastProcessedTime.TryGetValue(symbol, out var lastProcessed))
            {
                var age = now - lastProcessed;
                if (age < _config.CacheValidityPeriod && cachedData.IsValid)
                {
                    continue; // Skip - use cached data
                }
            }

            symbolsToProcess.Add(symbol);
        }

        return Task.FromResult(symbolsToProcess);
    }

    private List<List<string>> CreateOptimizedBatches(List<string> symbols)
    {
        var batches = new List<List<string>>();
        var batchSize = Math.Min(_config.OptimalBatchSize, symbols.Count);
        
        for (int i = 0; i < symbols.Count; i += batchSize)
        {
            var batch = symbols.Skip(i).Take(batchSize).ToList();
            batches.Add(batch);
        }

        return batches;
    }

    private async Task<(int processedCount, int errors)> ProcessBatchesAsync(
        List<List<string>> batches,
        ConcurrentBag<TradingSignal> results,
        CancellationToken cancellationToken)
    {
        var processedCount = 0;
        var errors = 0;
        var semaphore = new SemaphoreSlim(_config.MaxConcurrentBatches, _config.MaxConcurrentBatches);
        var tasks = batches.Select(async batch =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var batchResult = await ProcessBatchAsync(batch, results, cancellationToken);
                Interlocked.Add(ref processedCount, batchResult.processedCount);
                Interlocked.Add(ref errors, batchResult.errors);
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
        return (processedCount, errors);
    }

    private async Task<(int processedCount, int errors)> ProcessBatchAsync(
        List<string> batch,
        ConcurrentBag<TradingSignal> results,
        CancellationToken cancellationToken)
    {
        var batchStopwatch = Stopwatch.StartNew();
        var processedCount = 0;
        var errors = 0;

        try
        {
            // Use base signal generator for actual processing - convert to IEnumerable<TradingSignal>
            var batchSignals = await _baseSignalGenerator.RunAsync(batch.Count, cancellationToken);

            foreach (var signal in batchSignals.Take(batch.Count))
            {
                results.Add(signal);

                // Cache the signal data
                CacheSignalData(signal);

                processedCount++;
            }

            batchStopwatch.Stop();

            if (batchStopwatch.ElapsedMilliseconds > _config.SlowBatchThresholdMs)
            {
                _logger.LogWarning("⚠️ Slow batch processing: {Count} symbols in {Elapsed}ms",
                    batch.Count, batchStopwatch.ElapsedMilliseconds);
            }
        }
        catch (Exception ex)
        {
            errors++;
            _logger.LogError(ex, "❌ Batch processing failed for {Count} symbols", batch.Count);
        }

        return (processedCount, errors);
    }

    private Task<int> AddCachedSignalsAsync(IEnumerable<string> cachedSymbols, ConcurrentBag<TradingSignal> results)
    {
        var cacheHits = 0;
        foreach (var symbol in cachedSymbols)
        {
            if (_signalCache.TryGetValue(symbol, out var cachedData) && cachedData.IsValid)
            {
                results.Add(cachedData.Signal);
                cacheHits++;
            }
        }
        return Task.FromResult(cacheHits);
    }

    private void CacheSignalData(TradingSignal signal)
    {
        var cachedData = new CachedSignalData
        {
            Signal = signal,
            CachedAt = DateTime.UtcNow,
            IsValid = true
        };

        _signalCache.AddOrUpdate(signal.Symbol, cachedData, (_, _) => cachedData);
        _lastProcessedTime.AddOrUpdate(signal.Symbol, DateTime.UtcNow, (_, _) => DateTime.UtcNow);
    }

    private void LogPerformanceMetrics(TimeSpan elapsed, int totalSymbols, int processed, int cacheHits, int errors, int signalsGenerated)
    {
        var avgTimePerSymbol = totalSymbols > 0 ? elapsed.TotalMilliseconds / totalSymbols : 0;
        var cacheHitRate = totalSymbols > 0 ? (double)cacheHits / totalSymbols * 100 : 0;
        var errorRate = totalSymbols > 0 ? (double)errors / totalSymbols * 100 : 0;

        _logger.LogInformation(
            "📊 Signal generation completed: {Elapsed}ms total, {AvgPerSymbol:F1}ms/symbol, " +
            "{Processed} processed, {CacheHits} cached ({CacheHitRate:F1}%), {Errors} errors ({ErrorRate:F1}%), {Generated} signals",
            elapsed.TotalMilliseconds, avgTimePerSymbol, processed, cacheHits, cacheHitRate, errors, errorRate, signalsGenerated);

        // Log warning if performance is degraded
        if (avgTimePerSymbol > _config.SlowSymbolThresholdMs)
        {
            _logger.LogWarning("⚠️ Signal generation performance degraded: {AvgPerSymbol:F1}ms/symbol exceeds threshold {Threshold}ms",
                avgTimePerSymbol, _config.SlowSymbolThresholdMs);
        }
    }

    public void Dispose()
    {
        _processingLock?.Dispose();
        if (_baseSignalGenerator is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}

/// <summary>
/// Cached signal data with validity tracking
/// </summary>
public class CachedSignalData
{
    public TradingSignal Signal { get; set; }
    public DateTime CachedAt { get; set; }
    public bool IsValid { get; set; }
}

/// <summary>
/// Performance metrics for signal generation
/// </summary>
public class PerformanceMetrics
{
    public TimeSpan AverageProcessingTime { get; set; }
    public int TotalProcessed { get; set; }
    public int ErrorCount { get; set; }
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Configuration for optimized signal generation
/// </summary>
public class OptimizedSignalConfiguration
{
    public TimeSpan CacheValidityPeriod { get; set; } = TimeSpan.FromMinutes(5);
    public int OptimalBatchSize { get; set; } = 10;
    public int MaxConcurrentBatches { get; set; } = 3;
    public int SlowBatchThresholdMs { get; set; } = 30000; // 30 seconds
    public int SlowSymbolThresholdMs { get; set; } = 5000; // 5 seconds per symbol
}
