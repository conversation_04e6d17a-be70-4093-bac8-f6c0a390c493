using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Console.Monitoring;
using SmaTrendFollower.Console.Services;
using System.Collections.Concurrent;
using System.Diagnostics;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// High-performance universe builder with optimized pagination and parallelization
/// </summary>
public class OptimizedUniverseBuilder
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<OptimizedUniverseBuilder> _logger;
    private readonly OptimizedUniverseConfig _config;
    private readonly CacheWarmingService? _cacheWarmingService;
    // Optional services removed for compilation

    public OptimizedUniverseBuilder(
        IMarketDataService marketDataService,
        ILogger<OptimizedUniverseBuilder> logger,
        OptimizedUniverseConfig config,
        CacheWarmingService? cacheWarmingService = null)
    {
        _marketDataService = marketDataService;
        _logger = logger;
        _config = config;
        _cacheWarmingService = cacheWarmingService;
    }

    /// <summary>
    /// Build universe with optimized parallel processing and bulk data fetching
    /// </summary>
    public async Task<UniverseBuildResult> BuildUniverseAsync(
        IEnumerable<string> candidateSymbols,
        UniverseFilterCriteria criteria,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var symbols = candidateSymbols.ToList();

        _logger.LogInformation("🚀 Starting optimized universe build for {SymbolCount} candidates", symbols.Count);

        // Phase 0: Cache pre-warming now handled by UnifiedCacheWarmingCoordinator
        // Skipping individual cache warming to avoid conflicts
        _logger.LogDebug("Cache warming coordinated by UnifiedCacheWarmingCoordinator - skipping individual warming");

        // Phase 1: OPTIMIZED parallel batch processing
        const int batchSize = 2500; // Increased batch size for better throughput
        const int maxConcurrentBatches = 4; // Process multiple batches in parallel
        var allResults = new ConcurrentDictionary<string, MarketDataResult>();

        var semaphore = new SemaphoreSlim(maxConcurrentBatches, maxConcurrentBatches);
        var batchTasks = new List<Task>();

        for (int i = 0; i < symbols.Count; i += batchSize)
        {
            var batch = symbols.Skip(i).Take(batchSize).ToList();
            var batchNumber = (i / batchSize) + 1;
            var totalBatches = (symbols.Count + batchSize - 1) / batchSize;

            var batchTask = ProcessBatchAsync(batch, criteria, allResults, batchNumber, totalBatches, semaphore, cancellationToken);
            batchTasks.Add(batchTask);
        }

        _logger.LogInformation("🔄 Waiting for {BatchCount} batch tasks to complete...", batchTasks.Count);

        try
        {
            await Task.WhenAll(batchTasks);
            _logger.LogInformation("✅ All batch tasks completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error in Task.WhenAll for batch tasks");
            throw;
        }

        var marketDataResults = allResults;
        
        // Phase 2: Apply filters in parallel
        var filteredResults = await ApplyFiltersInParallelAsync(marketDataResults, criteria, cancellationToken);
        
        // Phase 3: Rank and select top symbols
        var finalUniverse = RankAndSelectSymbols(filteredResults, criteria);

        stopwatch.Stop();
        
        var result = new UniverseBuildResult
        {
            Symbols = finalUniverse,
            TotalCandidates = symbols.Count,
            QualifiedSymbols = finalUniverse.Count,
            BuildDuration = stopwatch.Elapsed,
            ApiCallCount = marketDataResults.Values.Count(r => r.Success),
            CacheHitCount = marketDataResults.Values.Count(r => r.FromCache)
        };

        _logger.LogInformation("✅ Universe build completed: {Qualified}/{Total} symbols in {Duration:F1}s " +
                             "({ApiCalls} API calls, {CacheHits} cache hits)",
            result.QualifiedSymbols, result.TotalCandidates, result.BuildDuration.TotalSeconds,
            result.ApiCallCount, result.CacheHitCount);

        // Universe build completed successfully

        // Update performance metrics
        UniverseMetricsHelper.UpdateUniverseMetrics(result, _logger);

        // Record cache hit rate if cache warming service is available
        if (_cacheWarmingService != null && result.TotalCandidates > 0)
        {
            var hitRate = (result.CacheHitCount * 100.0) / result.TotalCandidates;
            _logger.LogInformation("📊 Cache hit rate: {HitRate:F2}%", hitRate);
        }

        return result;
    }

    /// <summary>
    /// Process a single batch of symbols in parallel with semaphore control
    /// </summary>
    private async Task ProcessBatchAsync(
        List<string> batch,
        UniverseFilterCriteria criteria,
        ConcurrentDictionary<string, MarketDataResult> allResults,
        int batchNumber,
        int totalBatches,
        SemaphoreSlim semaphore,
        CancellationToken cancellationToken)
    {
        await semaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("📦 Processing batch {BatchNum}/{TotalBatches}: {BatchSize} symbols",
                batchNumber, totalBatches, batch.Count);

            var batchResults = await FetchMarketDataInParallelAsync(batch, criteria, cancellationToken);

            // Merge results
            foreach (var kvp in batchResults)
            {
                allResults[kvp.Key] = kvp.Value;
            }

            _logger.LogInformation("✅ Batch {BatchNum}/{TotalBatches} completed: {SuccessCount}/{TotalCount} successful",
                batchNumber, totalBatches, batchResults.Values.Count(r => r.Success), batch.Count);
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// Phase 1: Fetch market data with optimized parallelization
    /// </summary>
    private async Task<ConcurrentDictionary<string, MarketDataResult>> FetchMarketDataInParallelAsync(
        List<string> symbols,
        UniverseFilterCriteria criteria,
        CancellationToken cancellationToken)
    {
        var results = new ConcurrentDictionary<string, MarketDataResult>();
        var endDate = DateTime.UtcNow.Date.AddDays(-1);
        var startDate = endDate.AddDays(-criteria.AnalysisPeriodDays);

        _logger.LogInformation("📊 Fetching market data for {SymbolCount} symbols with {MaxConcurrency} concurrent requests",
            symbols.Count, _config.MaxConcurrentRequests);

        // Use aggressive parallelization with semaphore control
        using var semaphore = new SemaphoreSlim(_config.MaxConcurrentRequests, _config.MaxConcurrentRequests);

        // Progress tracking and circuit breaker
        var processedCount = 0;
        var failedCount = 0;
        var progressLock = new object();
        const int maxFailureRate = 50; // Stop if more than 50% fail

        var fetchTasks = symbols.Select(async symbol =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var sw = Stopwatch.StartNew();

                // Add timeout per symbol to prevent hangs
                using var symbolCts = new CancellationTokenSource(_config.SymbolTimeoutMs);
                using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, symbolCts.Token);

                _logger.LogDebug("🔄 Starting fetch for symbol: {Symbol}", symbol);
                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                _logger.LogDebug("✅ Completed fetch for symbol: {Symbol} in {Duration}ms", symbol, sw.ElapsedMilliseconds);
                var bars = response.Items.ToList();

                results[symbol] = new MarketDataResult
                {
                    Symbol = symbol,
                    Bars = bars,
                    Success = bars.Count >= _config.MinBarsRequired,
                    FetchDuration = sw.Elapsed,
                    FromCache = false // Could be enhanced to detect cache hits
                };

                if (sw.ElapsedMilliseconds > _config.SlowRequestThresholdMs)
                {
                    _logger.LogDebug("⚠️ Slow request for {Symbol}: {Duration}ms", symbol, sw.ElapsedMilliseconds);
                }
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                throw; // Propagate cancellation
            }
            catch (Exception ex)
            {
                _logger.LogDebug("❌ Failed to fetch data for {Symbol}: {Error}", symbol, ex.Message);
                results[symbol] = new MarketDataResult
                {
                    Symbol = symbol,
                    Bars = new List<IBar>(),
                    Success = false,
                    FetchDuration = TimeSpan.Zero,
                    FromCache = false
                };

                // Circuit breaker: track failures
                lock (progressLock)
                {
                    failedCount++;
                }
            }
            finally
            {
                semaphore.Release();

                // Progress logging and circuit breaker check
                lock (progressLock)
                {
                    processedCount++;
                    if (processedCount % 25 == 0 || processedCount == symbols.Count)
                    {
                        var failureRate = (failedCount * 100.0) / processedCount;
                        _logger.LogInformation("📊 Progress: {Processed}/{Total} symbols processed ({Percentage:F1}%) - Failures: {FailureRate:F1}%",
                            processedCount, symbols.Count, (processedCount * 100.0) / symbols.Count, failureRate);

                        // Circuit breaker: stop if too many failures
                        if (processedCount > 200 && failureRate > maxFailureRate)
                        {
                            _logger.LogError("🚨 Circuit breaker triggered: {FailureRate:F1}% failure rate exceeds {MaxRate}%",
                                failureRate, maxFailureRate);
                            throw new InvalidOperationException($"Circuit breaker: {failureRate:F1}% failure rate exceeds {maxFailureRate}%");
                        }
                    }
                }
            }
        });

        _logger.LogInformation("🔄 Waiting for {TaskCount} fetch tasks to complete...", fetchTasks.Count());

        try
        {
            await Task.WhenAll(fetchTasks);
            _logger.LogInformation("✅ All fetch tasks completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error in Task.WhenAll for fetch tasks");
            throw;
        }

        var successCount = results.Values.Count(r => r.Success);
        var avgDuration = results.Values.Where(r => r.Success).Average(r => r.FetchDuration.TotalMilliseconds);

        _logger.LogInformation("📈 Market data fetch completed: {Success}/{Total} successful " +
                             "(avg: {AvgDuration:F0}ms per symbol)",
            successCount, symbols.Count, avgDuration);

        return results;
    }

    /// <summary>
    /// Phase 2: Apply filters in parallel
    /// </summary>
    private async Task<List<FilteredSymbolResult>> ApplyFiltersInParallelAsync(
        ConcurrentDictionary<string, MarketDataResult> marketDataResults,
        UniverseFilterCriteria criteria,
        CancellationToken cancellationToken)
    {
        var filteredResults = new ConcurrentBag<FilteredSymbolResult>();
        
        _logger.LogInformation("🔍 Applying filters to {SymbolCount} symbols", marketDataResults.Count);

        var successfulResults = marketDataResults.Values.Where(r => r.Success).ToList();
        
        // Process filtering in parallel
        var filterTasks = successfulResults.Select(async result =>
        {
            await Task.Yield(); // Ensure async execution
            
            try
            {
                var filterResult = ApplyFiltersToSymbol(result, criteria);
                if (filterResult.PassesAllFilters)
                {
                    filteredResults.Add(filterResult);
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug("❌ Filter error for {Symbol}: {Error}", result.Symbol, ex.Message);
            }
        });

        await Task.WhenAll(filterTasks);
        
        var results = filteredResults.ToList();
        _logger.LogInformation("✅ Filtering completed: {Qualified}/{Total} symbols passed all filters",
            results.Count, successfulResults.Count);

        return results;
    }

    /// <summary>
    /// Apply all filters to a single symbol
    /// </summary>
    private FilteredSymbolResult ApplyFiltersToSymbol(MarketDataResult marketData, UniverseFilterCriteria criteria)
    {
        var bars = marketData.Bars;
        var currentPrice = bars.Last().Close;
        var volumes = bars.Select(b => (long)b.Volume).ToList();
        var closes = bars.Select(b => b.Close).ToList();

        // Calculate metrics
        var avgVolume = volumes.Average();
        var volatility = CalculateVolatility(closes);
        var rankingScore = CalculateRankingScore(currentPrice, avgVolume, volatility);

        // Apply filters
        var passesPrice = currentPrice >= criteria.MinPrice;
        var passesVolume = avgVolume >= criteria.MinAverageVolume;
        var passesVolatility = volatility >= criteria.MinVolatilityPercent / 100m;

        return new FilteredSymbolResult
        {
            Symbol = marketData.Symbol,
            CurrentPrice = currentPrice,
            AverageVolume = avgVolume,
            Volatility = volatility,
            RankingScore = rankingScore,
            PassesPriceFilter = passesPrice,
            PassesVolumeFilter = passesVolume,
            PassesVolatilityFilter = passesVolatility,
            PassesAllFilters = passesPrice && passesVolume && passesVolatility
        };
    }

    /// <summary>
    /// Phase 3: Rank and select top symbols
    /// </summary>
    private List<string> RankAndSelectSymbols(List<FilteredSymbolResult> filteredResults, UniverseFilterCriteria criteria)
    {
        IEnumerable<FilteredSymbolResult> query = filteredResults.OrderByDescending(r => r.RankingScore);

        // Apply MaxSymbols limit if specified
        if (criteria.MaxSymbols.HasValue && criteria.MaxSymbols.Value > 0)
        {
            query = query.Take(criteria.MaxSymbols.Value);
        }

        return query.Select(r => r.Symbol).ToList();
    }

    private decimal CalculateVolatility(List<decimal> closes)
    {
        if (closes.Count < 2) return 0;
        
        var returns = new List<decimal>();
        for (int i = 1; i < closes.Count; i++)
        {
            returns.Add((closes[i] - closes[i - 1]) / closes[i - 1]);
        }
        
        var avgReturn = returns.Average();
        var variance = returns.Sum(r => (r - avgReturn) * (r - avgReturn)) / returns.Count;
        return (decimal)Math.Sqrt((double)variance) * (decimal)Math.Sqrt(252); // Annualized
    }

    private decimal CalculateRankingScore(decimal price, double avgVolume, decimal volatility)
    {
        // Simple ranking: higher volume and moderate volatility preferred
        var volumeScore = (decimal)Math.Log10(avgVolume);
        var volatilityScore = volatility > 0.5m ? 1m / volatility : 0m; // Prefer moderate volatility
        return volumeScore + volatilityScore;
    }
}

/// <summary>
/// Configuration for optimized universe building
/// </summary>
public class OptimizedUniverseConfig
{
    public int MaxConcurrentRequests { get; set; } = 100; // OPTIMIZED: Increased parallelization
    public int SymbolTimeoutMs { get; set; } = 15000; // OPTIMIZED: Increased timeout for stability
    public int SlowRequestThresholdMs { get; set; } = 3000; // OPTIMIZED: Adjusted threshold
    public int MinBarsRequired { get; set; } = 5; // Minimum bars for analysis
}

/// <summary>
/// Result of market data fetch for a symbol
/// </summary>
public class MarketDataResult
{
    public string Symbol { get; set; } = string.Empty;
    public List<IBar> Bars { get; set; } = new();
    public bool Success { get; set; }
    public TimeSpan FetchDuration { get; set; }
    public bool FromCache { get; set; }
}

/// <summary>
/// Result of filtering for a symbol
/// </summary>
public class FilteredSymbolResult
{
    public string Symbol { get; set; } = string.Empty;
    public decimal CurrentPrice { get; set; }
    public double AverageVolume { get; set; }
    public decimal Volatility { get; set; }
    public decimal RankingScore { get; set; }
    public bool PassesPriceFilter { get; set; }
    public bool PassesVolumeFilter { get; set; }
    public bool PassesVolatilityFilter { get; set; }
    public bool PassesAllFilters { get; set; }
}

/// <summary>
/// Final result of universe building
/// </summary>
public class UniverseBuildResult
{
    public List<string> Symbols { get; set; } = new();
    public int TotalCandidates { get; set; }
    public int QualifiedSymbols { get; set; }
    public TimeSpan BuildDuration { get; set; }
    public int ApiCallCount { get; set; }
    public int CacheHitCount { get; set; }
}

/// <summary>
/// Update performance metrics for monitoring
/// </summary>
public static class UniverseMetricsHelper
{
    public static void UpdateUniverseMetrics(UniverseBuildResult result, ILogger logger)
    {
        try
        {
            // Update Prometheus metrics
            CacheMetrics.BackgroundPersistenceBatchTime.Observe(result.BuildDuration.TotalMilliseconds);

            if (result.TotalCandidates > 0)
            {
                var successRate = (result.QualifiedSymbols * 100.0) / result.TotalCandidates;
                var cacheHitRate = (result.CacheHitCount * 100.0) / result.TotalCandidates;

                logger.LogDebug("Universe building metrics - Success rate: {SuccessRate:F2}%, Cache hit rate: {CacheHitRate:F2}%",
                    successRate, cacheHitRate);
            }
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Failed to update universe metrics");
        }
    }
}
