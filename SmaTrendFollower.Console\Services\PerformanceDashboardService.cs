using Microsoft.Extensions.Logging;
using SmaTrendFollower.Monitoring;
using System.Text;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Performance dashboard service for real-time trading metrics visualization
/// </summary>
public sealed class PerformanceDashboardService
{
    private readonly ILogger<PerformanceDashboardService> _logger;
    private readonly Dictionary<string, DashboardMetric> _metrics = new();
    private readonly object _metricsLock = new();
    private DateTime _lastDashboardUpdate = DateTime.MinValue;

    public PerformanceDashboardService(ILogger<PerformanceDashboardService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Records a performance metric
    /// </summary>
    public void RecordMetric(string category, string name, double value, string? unit = null)
    {
        lock (_metricsLock)
        {
            var key = $"{category}.{name}";
            _metrics[key] = new DashboardMetric
            {
                Category = category,
                Name = name,
                Value = value,
                Unit = unit ?? "",
                Timestamp = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// Records signal generation metrics
    /// </summary>
    public void RecordSignalMetrics(int signalsGenerated, double avgMomentum, double avgReturn, TimeSpan duration)
    {
        RecordMetric("Signals", "Generated", signalsGenerated, "count");
        RecordMetric("Signals", "AvgMomentum", avgMomentum, "score");
        RecordMetric("Signals", "AvgReturn", avgReturn, "pct");
        RecordMetric("Signals", "GenerationTime", duration.TotalMilliseconds, "ms");
    }

    /// <summary>
    /// Records trade execution metrics
    /// </summary>
    public void RecordTradeMetrics(int tradesExecuted, double avgSlippage, double totalVolume)
    {
        RecordMetric("Trades", "Executed", tradesExecuted, "count");
        RecordMetric("Trades", "AvgSlippage", avgSlippage, "bps");
        RecordMetric("Trades", "TotalVolume", totalVolume, "USD");
    }

    /// <summary>
    /// Records market data metrics
    /// </summary>
    public void RecordMarketDataMetrics(int dataPoints, double latency, string source)
    {
        RecordMetric("MarketData", "DataPoints", dataPoints, "count");
        RecordMetric("MarketData", "Latency", latency, "ms");
        RecordMetric("MarketData", "Source", source == "Alpaca" ? 1 : 0, "source");
    }

    /// <summary>
    /// Records VIX data metrics
    /// </summary>
    public void RecordVixMetrics(double vixValue, string source, double retrievalTime)
    {
        RecordMetric("VIX", "Value", vixValue, "index");
        RecordMetric("VIX", "RetrievalTime", retrievalTime, "ms");
        RecordMetric("VIX", "Source", GetVixSourceCode(source), "source");
    }

    /// <summary>
    /// Records system performance metrics
    /// </summary>
    public void RecordSystemMetrics(long memoryUsage, double cpuUsage, int activeConnections)
    {
        RecordMetric("System", "MemoryMB", memoryUsage / (1024 * 1024), "MB");
        RecordMetric("System", "CpuUsage", cpuUsage, "pct");
        RecordMetric("System", "Connections", activeConnections, "count");
    }

    /// <summary>
    /// Displays the performance dashboard
    /// </summary>
    public void DisplayDashboard(bool forceUpdate = false)
    {
        var now = DateTime.UtcNow;
        if (!forceUpdate && (now - _lastDashboardUpdate).TotalSeconds < 30)
            return; // Throttle dashboard updates

        _lastDashboardUpdate = now;

        var dashboard = GenerateDashboard();
        _logger.LogInformation("\n{Dashboard}", dashboard);
    }

    /// <summary>
    /// Generates the dashboard display
    /// </summary>
    private string GenerateDashboard()
    {
        var sb = new StringBuilder();
        
        sb.AppendLine("╔══════════════════════════════════════════════════════════════════════════════╗");
        sb.AppendLine("║                           📊 TRADING PERFORMANCE DASHBOARD                   ║");
        sb.AppendLine("╠══════════════════════════════════════════════════════════════════════════════╣");
        sb.AppendLine($"║ Updated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss UTC}                                        ║");
        sb.AppendLine("╠══════════════════════════════════════════════════════════════════════════════╣");

        lock (_metricsLock)
        {
            // Signal Generation Section
            sb.AppendLine("║ 🎯 SIGNAL GENERATION                                                         ║");
            sb.AppendLine("╠──────────────────────────────────────────────────────────────────────────╢");
            AppendMetricLine(sb, "Signals Generated", "Signals.Generated", "count");
            AppendMetricLine(sb, "Avg Momentum", "Signals.AvgMomentum", "score", "F2");
            AppendMetricLine(sb, "Avg 6M Return", "Signals.AvgReturn", "pct", "P2");
            AppendMetricLine(sb, "Generation Time", "Signals.GenerationTime", "ms", "F0");

            // Trade Execution Section
            sb.AppendLine("╠══════════════════════════════════════════════════════════════════════════════╣");
            sb.AppendLine("║ 💼 TRADE EXECUTION                                                           ║");
            sb.AppendLine("╠──────────────────────────────────────────────────────────────────────────╢");
            AppendMetricLine(sb, "Trades Executed", "Trades.Executed", "count");
            AppendMetricLine(sb, "Avg Slippage", "Trades.AvgSlippage", "bps", "F1");
            AppendMetricLine(sb, "Total Volume", "Trades.TotalVolume", "USD", "C0");

            // Market Data Section
            sb.AppendLine("╠══════════════════════════════════════════════════════════════════════════════╣");
            sb.AppendLine("║ 📊 MARKET DATA                                                               ║");
            sb.AppendLine("╠──────────────────────────────────────────────────────────────────────────╢");
            AppendMetricLine(sb, "Data Points", "MarketData.DataPoints", "count");
            AppendMetricLine(sb, "Latency", "MarketData.Latency", "ms", "F0");
            AppendMetricLine(sb, "Source", "MarketData.Source", "", "", value => value == 1 ? "Alpaca" : "Polygon");

            // VIX Data Section
            sb.AppendLine("╠══════════════════════════════════════════════════════════════════════════════╣");
            sb.AppendLine("║ 📈 VIX DATA                                                                  ║");
            sb.AppendLine("╠──────────────────────────────────────────────────────────────────────────╢");
            AppendMetricLine(sb, "VIX Value", "VIX.Value", "index", "F2");
            AppendMetricLine(sb, "Retrieval Time", "VIX.RetrievalTime", "ms", "F0");
            AppendMetricLine(sb, "Source", "VIX.Source", "", "", value => GetVixSourceName((int)value));

            // System Performance Section
            sb.AppendLine("╠══════════════════════════════════════════════════════════════════════════════╣");
            sb.AppendLine("║ ⚡ SYSTEM PERFORMANCE                                                        ║");
            sb.AppendLine("╠──────────────────────────────────────────────────────────────────────────╢");
            AppendMetricLine(sb, "Memory Usage", "System.MemoryMB", "MB", "F0");
            AppendMetricLine(sb, "CPU Usage", "System.CpuUsage", "pct", "P1");
            AppendMetricLine(sb, "Connections", "System.Connections", "count");
        }

        sb.AppendLine("╚══════════════════════════════════════════════════════════════════════════════╝");
        
        return sb.ToString();
    }

    private void AppendMetricLine(StringBuilder sb, string label, string metricKey, string unit, 
        string format = "F0", Func<double, string>? valueFormatter = null)
    {
        var value = "N/A";
        var age = "";

        if (_metrics.TryGetValue(metricKey, out var metric))
        {
            if (valueFormatter != null)
            {
                value = valueFormatter(metric.Value);
            }
            else
            {
                value = format switch
                {
                    "P1" => metric.Value.ToString("P1"),
                    "P2" => metric.Value.ToString("P2"),
                    "C0" => metric.Value.ToString("C0"),
                    _ => metric.Value.ToString(format)
                };
            }

            var ageSpan = DateTime.UtcNow - metric.Timestamp;
            if (ageSpan.TotalMinutes < 1)
                age = "now";
            else if (ageSpan.TotalMinutes < 60)
                age = $"{ageSpan.TotalMinutes:F0}m ago";
            else
                age = $"{ageSpan.TotalHours:F0}h ago";
        }

        var displayUnit = string.IsNullOrEmpty(unit) ? "" : $" {unit}";
        var line = $"║ {label,-20} {value,-15}{displayUnit,-8} {age,-15} ║";
        
        // Ensure line is exactly 80 characters
        if (line.Length > 80)
            line = line.Substring(0, 77) + "...║";
        else if (line.Length < 80)
            line = line.PadRight(80 - 1) + "║";
            
        sb.AppendLine(line);
    }

    /// <summary>
    /// Gets VIX source code for metrics
    /// </summary>
    private static int GetVixSourceCode(string source)
    {
        return source.ToLowerInvariant() switch
        {
            "polygon" => 1,
            "webscraping" => 2,
            "syntheticalapaca" => 3,
            "syntheticpolygon" => 4,
            "bravesearch" => 5,
            _ => 0
        };
    }

    /// <summary>
    /// Gets VIX source name from code
    /// </summary>
    private static string GetVixSourceName(int code)
    {
        return code switch
        {
            1 => "Polygon",
            2 => "Web Scraping",
            3 => "Synthetic (Alpaca)",
            4 => "Synthetic (Polygon)",
            5 => "Brave Search",
            _ => "Unknown"
        };
    }

    /// <summary>
    /// Gets current metrics snapshot
    /// </summary>
    public Dictionary<string, DashboardMetric> GetMetricsSnapshot()
    {
        lock (_metricsLock)
        {
            return new Dictionary<string, DashboardMetric>(_metrics);
        }
    }

    /// <summary>
    /// Exports metrics to JSON
    /// </summary>
    public string ExportMetricsToJson()
    {
        var snapshot = GetMetricsSnapshot();
        return JsonSerializer.Serialize(snapshot, new JsonSerializerOptions 
        { 
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
    }

    /// <summary>
    /// Clears old metrics (older than specified age)
    /// </summary>
    public void ClearOldMetrics(TimeSpan maxAge)
    {
        var cutoff = DateTime.UtcNow - maxAge;
        
        lock (_metricsLock)
        {
            var keysToRemove = _metrics
                .Where(kvp => kvp.Value.Timestamp < cutoff)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in keysToRemove)
            {
                _metrics.Remove(key);
            }
        }
    }
}

/// <summary>
/// Dashboard metric data structure
/// </summary>
public sealed class DashboardMetric
{
    public string Category { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}
