using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

public sealed class PolygonClientFactory : IPolygonClientFactory
{
    private readonly HttpClient _client;
    private readonly PolygonRateLimitHelper _rateLimitHelper;
    private readonly IConfiguration _configuration;
    private readonly ILogger<PolygonWebSocketClient> _webSocketLogger;
    private readonly string? _apiKey;

    public PolygonClientFactory(
        IHttpClientFactory httpFactory,
        IConfiguration config,
        ILogger<PolygonRateLimitHelper> rateLimitLogger,
        ILogger<PolygonWebSocketClient> webSocketLogger)
    {
        // Use the Polly-configured named client
        _client = httpFactory.CreateClient("Polygon");

        // Ensure BaseAddress is set (fallback if not configured properly)
        if (_client.BaseAddress == null)
        {
            _client.BaseAddress = new Uri("https://api.polygon.io/");
            _client.Timeout = TimeSpan.FromSeconds(75);
            _client.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
            _client.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        // Configuration is now handled by AddPollyHttpClients in ServiceConfiguration
        // Base address, timeout, and headers are pre-configured with Polly policies

        _configuration = config;
        _webSocketLogger = webSocketLogger;

        // Store API key for query parameter injection
        _apiKey = config["Polygon:ApiKey"]
                  ?? config["POLY_API_KEY"]
                  ?? Environment.GetEnvironmentVariable("POLY_API_KEY")
                  ?? Environment.GetEnvironmentVariable("POLYGON_API_KEY")
                  ?? throw new InvalidOperationException("Polygon API key not configured. Set Polygon:ApiKey in appsettings.json or POLY_API_KEY/POLYGON_API_KEY environment variable");

        _rateLimitHelper = new PolygonRateLimitHelper(rateLimitLogger);
    }

    public HttpClient CreateClient() => _client;

    public IPolygonWebSocketClient CreateWebSocketClient()
    {
        return new PolygonWebSocketClient(_configuration, _webSocketLogger);
    }

    public IPolygonRateLimitHelper GetRateLimitHelper() => _rateLimitHelper;

    /// <summary>
    /// Adds the API key as a query parameter to the given URL
    /// </summary>
    /// <param name="url">The base URL without API key</param>
    /// <returns>URL with apiKey query parameter appended</returns>
    public string AddApiKeyToUrl(string url)
    {
        if (string.IsNullOrEmpty(_apiKey))
        {
            return url;
        }

        var separator = url.Contains('?') ? "&" : "?";
        return $"{url}{separator}apiKey={_apiKey}";
    }

    public void Dispose()
    {
        _rateLimitHelper?.Dispose();
        _client?.Dispose();
    }
}
