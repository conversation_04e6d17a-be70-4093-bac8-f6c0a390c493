using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Polygon.io implementation of earnings calendar service
/// </summary>
public sealed class PolygonEarningsCalendar : IEarningsCalendar
{
    private readonly HttpClient _http;
    private readonly string _apiKey;
    private readonly IMemoryCache _cache;
    private readonly ILogger<PolygonEarningsCalendar> _logger;
    
    private static readonly TimeSpan CacheDuration = TimeSpan.FromHours(6);
    
    public PolygonEarningsCalendar(
        HttpClient http, 
        IConfiguration configuration,
        IMemoryCache cache,
        ILogger<PolygonEarningsCalendar> logger)
    {
        _http = http;
        _cache = cache;
        _logger = logger;
        
        _apiKey = configuration["Polygon:ApiKey"]
                  ?? configuration["POLY_API_KEY"]
                  ?? Environment.GetEnvironmentVariable("POLY_API_KEY")
                  ?? Environment.GetEnvironmentVariable("POLYGON_API_KEY")
                  ?? throw new InvalidOperationException("Polygon API key not configured. Set Polygon:ApiKey in appsettings.json or POLY_API_KEY/POLYGON_API_KEY environment variable");
        
        _http.BaseAddress = new Uri("https://api.polygon.io/");
        _http.Timeout = TimeSpan.FromSeconds(30);
    }

    public Task<DateTime?> GetNextEarningsAsync(string symbol, CancellationToken ct = default)
    {
        if (string.IsNullOrWhiteSpace(symbol))
        {
            _logger.LogWarning("Empty symbol provided to GetNextEarningsAsync");
            return Task.FromResult<DateTime?>(null);
        }

        var cacheKey = $"earnings:{symbol.ToUpperInvariant()}";

        // Check cache first
        if (_cache.TryGetValue(cacheKey, out DateTime? cachedDate))
        {
            _logger.LogDebug("Returning cached earnings date for {Symbol}: {Date}", symbol, cachedDate);
            return Task.FromResult(cachedDate);
        }

        // Note: Benzinga earnings endpoint requires separate $99/month subscription
        // Since user doesn't have Benzinga subscription, we'll skip Polygon API entirely
        // and rely on our robust fallback mechanisms

        _logger.LogDebug("Skipping Polygon earnings endpoints (no Benzinga subscription) for {Symbol}", symbol);

        // Return null to trigger fallback mechanisms
        _cache.Set(cacheKey, (DateTime?)null, TimeSpan.FromHours(1));
        return Task.FromResult<DateTime?>(null);


    }

    private Task<DateTime?> ParseEarningsResponseAsync(string content, string symbol)
    {
        try
        {
            var root = JsonSerializer.Deserialize<JsonElement>(content);

            // Try different response structures
            JsonElement resultsArray;

            // Structure 1: { "results": [...] }
            if (root.TryGetProperty("results", out resultsArray) && resultsArray.ValueKind == JsonValueKind.Array)
            {
                return Task.FromResult(ParseEarningsFromResults(resultsArray, symbol));
            }

            // Structure 2: { "earnings": [...] }
            if (root.TryGetProperty("earnings", out resultsArray) && resultsArray.ValueKind == JsonValueKind.Array)
            {
                return Task.FromResult(ParseEarningsFromResults(resultsArray, symbol));
            }

            // Structure 3: Direct array
            if (root.ValueKind == JsonValueKind.Array)
            {
                return Task.FromResult(ParseEarningsFromResults(root, symbol));
            }

            _logger.LogDebug("Unrecognized earnings response structure for {Symbol}", symbol);
            return Task.FromResult<DateTime?>(null);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON parsing error for earnings data for {Symbol}", symbol);
            return Task.FromResult<DateTime?>(null);
        }
    }

    private DateTime? ParseEarningsFromResults(JsonElement resultsArray, string symbol)
    {
        if (resultsArray.GetArrayLength() == 0)
        {
            _logger.LogDebug("No earnings data found in results for {Symbol}", symbol);
            return null;
        }

        var now = DateTime.UtcNow;
        DateTime? nextEarnings = null;

        foreach (var result in resultsArray.EnumerateArray())
        {
            // Try different field names for earnings date
            var dateFields = new[] { "date", "reportTime", "report_time", "earnings_date", "announcement_date" };
            var timeFields = new[] { "time" };

            foreach (var dateField in dateFields)
            {
                if (result.TryGetProperty(dateField, out var dateElement))
                {
                    var dateStr = dateElement.GetString();
                    if (!string.IsNullOrEmpty(dateStr))
                    {
                        DateTime reportTime;

                        // For Benzinga format, combine date and time if available
                        if (dateField == "date" && result.TryGetProperty("time", out var timeElement))
                        {
                            var timeStr = timeElement.GetString();
                            if (!string.IsNullOrEmpty(timeStr))
                            {
                                var combinedDateTime = $"{dateStr}T{timeStr}";
                                if (DateTime.TryParse(combinedDateTime, out reportTime))
                                {
                                    // Benzinga times are in UTC
                                    reportTime = DateTime.SpecifyKind(reportTime, DateTimeKind.Utc);
                                }
                                else if (DateTime.TryParse(dateStr, out reportTime))
                                {
                                    reportTime = DateTime.SpecifyKind(reportTime, DateTimeKind.Utc);
                                }
                                else
                                {
                                    continue;
                                }
                            }
                            else if (DateTime.TryParse(dateStr, out reportTime))
                            {
                                reportTime = DateTime.SpecifyKind(reportTime, DateTimeKind.Utc);
                            }
                            else
                            {
                                continue;
                            }
                        }
                        else if (DateTime.TryParse(dateStr, out reportTime))
                        {
                            // Convert to UTC if needed
                            if (reportTime.Kind == DateTimeKind.Unspecified)
                            {
                                reportTime = DateTime.SpecifyKind(reportTime, DateTimeKind.Utc);
                            }
                            else if (reportTime.Kind == DateTimeKind.Local)
                            {
                                reportTime = reportTime.ToUniversalTime();
                            }
                        }
                        else
                        {
                            continue;
                        }

                        // Only consider future earnings
                        if (reportTime > now && (nextEarnings == null || reportTime < nextEarnings))
                        {
                            nextEarnings = reportTime;
                            _logger.LogDebug("Found future earnings date for {Symbol}: {Date} from field {Field}",
                                symbol, reportTime, dateField);

                            // Log additional Benzinga-specific fields for debugging
                            if (result.TryGetProperty("company_name", out var companyElement))
                            {
                                _logger.LogDebug("Company: {Company}", companyElement.GetString());
                            }
                            if (result.TryGetProperty("fiscal_period", out var periodElement))
                            {
                                _logger.LogDebug("Fiscal Period: {Period}", periodElement.GetString());
                            }
                            if (result.TryGetProperty("importance", out var importanceElement))
                            {
                                _logger.LogDebug("Importance: {Importance}", importanceElement.GetInt32());
                            }
                        }
                        break; // Found a valid date, move to next result
                    }
                }
            }
        }

        return nextEarnings;
    }
}
