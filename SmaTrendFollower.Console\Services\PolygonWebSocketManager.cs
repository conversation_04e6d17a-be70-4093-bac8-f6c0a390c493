using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Advanced Polygon WebSocket manager that handles multiple channels with rate limiting,
/// batch subscriptions, Redis persistence, and automatic reconnection
/// </summary>
public sealed class PolygonWebSocketManager : IDisposable
{
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ILogger<PolygonWebSocketManager> _logger;
    private readonly ITradingMetricsService? _metricsService;
    internal readonly AnomalyDetectorService? _anomalyDetector;
    internal readonly QuoteVolatilityGuard? _quoteVolatilityGuard;
    private readonly string _apiKey;
    
    // Connection management
    private readonly ConcurrentDictionary<PolygonWsChannel, WebSocketConnection> _connections = new();
    private readonly SemaphoreSlim _connectionSemaphore = new(1, 1);
    private bool _disposed;

    // Configuration constants
    private const int MaxSymbolsPerBatch = 500;
    private const int MaxReconnectAttempts = 10;
    private const int BaseReconnectDelayMs = 1000;
    private const int MaxReconnectDelayMs = 32000;
    private const int CircuitBreakerThreshold = 3;
    private const int RateLimitRetryDelayMs = 2000;

    public PolygonWebSocketManager(
        IOptimizedRedisConnectionService redisService,
        ILogger<PolygonWebSocketManager> logger,
        string apiKey,
        ITradingMetricsService? metricsService = null,
        AnomalyDetectorService? anomalyDetector = null,
        QuoteVolatilityGuard? quoteVolatilityGuard = null)
    {
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _metricsService = metricsService; // Optional dependency
        _anomalyDetector = anomalyDetector; // Optional dependency
        _quoteVolatilityGuard = quoteVolatilityGuard; // Optional dependency
        _apiKey = apiKey ?? throw new ArgumentNullException(nameof(apiKey));
    }

    /// <summary>
    /// Creates or gets a WebSocket client for the specified channel
    /// </summary>
    /// <param name="channel">The WebSocket channel to connect to</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The WebSocket connection for the channel</returns>
    public async Task<WebSocketConnection> CreateClientAsync(PolygonWsChannel channel, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(PolygonWebSocketManager));

        if (_connections.TryGetValue(channel, out var existingConnection) && 
            existingConnection.State == WebSocketState.Open)
        {
            return existingConnection;
        }

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            // Double-check pattern
            if (_connections.TryGetValue(channel, out existingConnection) && 
                existingConnection.State == WebSocketState.Open)
            {
                return existingConnection;
            }

            _logger.LogInformation("Creating new WebSocket connection for channel {Channel}", channel);

            var connection = new WebSocketConnection(channel, _apiKey, _redisService, _logger, this);
            await connection.ConnectAsync(cancellationToken);

            _connections.AddOrUpdate(channel, connection, (_, old) =>
            {
                old?.Dispose();
                return connection;
            });

            _logger.LogInformation("Successfully created WebSocket connection for channel {Channel}", channel);
            return connection;
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Batch subscribe to multiple symbols on a specific channel
    /// Automatically chunks symbols into groups of ≤500 to respect rate limits
    /// </summary>
    /// <param name="channel">The WebSocket channel</param>
    /// <param name="symbols">The symbols to subscribe to</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task BatchSubscribeAsync(PolygonWsChannel channel, IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(PolygonWebSocketManager));

        var symbolList = symbols.ToList();
        if (symbolList.Count == 0)
        {
            _logger.LogWarning("No symbols provided for batch subscription to channel {Channel}", channel);
            return;
        }

        _logger.LogInformation("Starting batch subscription for {SymbolCount} symbols on channel {Channel}", 
            symbolList.Count, channel);

        var connection = await CreateClientAsync(channel, cancellationToken);
        
        // Split symbols into chunks of MaxSymbolsPerBatch
        var chunks = symbolList
            .Select((symbol, index) => new { symbol, index })
            .GroupBy(x => x.index / MaxSymbolsPerBatch)
            .Select(g => g.Select(x => x.symbol).ToList())
            .ToList();

        _logger.LogInformation("Batch subscribing {SymbolCount} symbols in {ChunkCount} chunk(s) for channel {Channel}",
            symbolList.Count, chunks.Count, channel);

        foreach (var chunk in chunks)
        {
            await connection.SubscribeToSymbolsAsync(chunk, cancellationToken);
            
            // Small delay between chunks to avoid overwhelming the server
            if (chunks.Count > 1)
            {
                await Task.Delay(100, cancellationToken);
            }
        }

        // Persist subscription list to Redis
        await PersistSubscriptionsAsync(channel, symbolList);

        // Record metrics
        await RecordSubscribedSymbolsAsync(channel, symbolList.Count);

        _logger.LogInformation("Completed batch subscription for {SymbolCount} symbols on channel {Channel}",
            symbolList.Count, channel);
    }

    /// <summary>
    /// Gets the current connection status for a channel
    /// </summary>
    /// <param name="channel">The WebSocket channel</param>
    /// <returns>The current WebSocket state</returns>
    public WebSocketState GetConnectionState(PolygonWsChannel channel)
    {
        return _connections.TryGetValue(channel, out var connection) 
            ? connection.State 
            : WebSocketState.None;
    }

    /// <summary>
    /// Gets the count of subscribed symbols for a channel
    /// </summary>
    /// <param name="channel">The WebSocket channel</param>
    /// <returns>The number of subscribed symbols</returns>
    public int GetSubscribedSymbolCount(PolygonWsChannel channel)
    {
        return _connections.TryGetValue(channel, out var connection) 
            ? connection.SubscribedSymbolCount 
            : 0;
    }

    /// <summary>
    /// Persists subscription list to Redis for auto re-subscription on reconnect
    /// </summary>
    private async Task PersistSubscriptionsAsync(PolygonWsChannel channel, List<string> symbols)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var redisKey = channel.GetRedisSubscriptionKey();
            var symbolsString = string.Join(",", symbols);

            await database.StringSetAsync(redisKey, symbolsString, RedisKeyConstants.RedisKeyTTL.WebSocketSubscriptions);
            
            _logger.LogDebug("Persisted {SymbolCount} subscriptions to Redis key {RedisKey}", 
                symbols.Count, redisKey);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to persist subscriptions to Redis for channel {Channel}", channel);
        }
    }

    /// <summary>
    /// Loads persisted subscriptions from Redis
    /// </summary>
    private async Task<List<string>> LoadPersistedSubscriptionsAsync(PolygonWsChannel channel)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var redisKey = channel.GetRedisSubscriptionKey();
            var symbolsString = await database.StringGetAsync(redisKey);

            if (symbolsString.HasValue)
            {
                var symbols = symbolsString.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
                _logger.LogDebug("Loaded {SymbolCount} persisted subscriptions from Redis key {RedisKey}", 
                    symbols.Count, redisKey);
                return symbols;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to load persisted subscriptions from Redis for channel {Channel}", channel);
        }

        return new List<string>();
    }

    /// <summary>
    /// Disconnects and disposes all WebSocket connections
    /// </summary>
    public async Task DisconnectAllAsync()
    {
        if (_disposed)
            return;

        _logger.LogInformation("Disconnecting all WebSocket connections");

        var disconnectTasks = _connections.Values.Select(async connection =>
        {
            try
            {
                await connection.DisconnectAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error disconnecting WebSocket connection");
            }
        });

        await Task.WhenAll(disconnectTasks);
        _connections.Clear();

        _logger.LogInformation("All WebSocket connections disconnected");
    }

    /// <summary>
    /// Records WebSocket metrics for monitoring
    /// </summary>
    private async Task RecordWebSocketMetricsAsync(PolygonWsChannel channel, string metricType, decimal value)
    {
        if (_metricsService == null)
            return;

        try
        {
            var metricName = $"websocket_{metricType}_{channel}";
            await _metricsService.RecordSystemMetricAsync(metricName, value, "count");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to record WebSocket metric {MetricType} for channel {Channel}", metricType, channel);
        }
    }

    /// <summary>
    /// Records reconnection event
    /// </summary>
    internal async Task RecordReconnectionAsync(PolygonWsChannel channel)
    {
        await RecordWebSocketMetricsAsync(channel, "reconnect_total", 1);

        // Record Prometheus metric
        MetricsRegistry.WsReconnects.WithLabels(channel.ToString(), "connection_lost").Inc();
    }

    /// <summary>
    /// Records subscribed symbol count
    /// </summary>
    internal async Task RecordSubscribedSymbolsAsync(PolygonWsChannel channel, int symbolCount)
    {
        await RecordWebSocketMetricsAsync(channel, "subscribed_symbols", symbolCount);
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            DisconnectAllAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during disposal");
        }

        foreach (var connection in _connections.Values)
        {
            connection?.Dispose();
        }

        _connections.Clear();
        _connectionSemaphore?.Dispose();
    }
}

/// <summary>
/// Represents a single WebSocket connection for a specific Polygon channel
/// Handles connection lifecycle, authentication, subscriptions, and reconnection
/// </summary>
public sealed class WebSocketConnection : IDisposable
{
    private readonly PolygonWsChannel _channel;
    private readonly string _apiKey;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ILogger _logger;
    private readonly PolygonWebSocketManager _manager;

    private ClientWebSocket? _webSocket;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _receiveTask;
    private readonly SemaphoreSlim _connectionLock = new(1, 1);
    private readonly HashSet<string> _subscribedSymbols = new();
    private bool _disposed;

    // Reconnection state
    private int _reconnectAttempts;
    private int _consecutiveRateLimitErrors;
    private bool _circuitBreakerOpen;
    private DateTime _lastRateLimitError = DateTime.MinValue;

    // Configuration constants (same as manager)
    private const int MaxReconnectAttempts = 10;
    private const int BaseReconnectDelayMs = 1000;
    private const int MaxReconnectDelayMs = 32000;
    private const int CircuitBreakerThreshold = 3;
    private const int RateLimitRetryDelayMs = 2000;

    public WebSocketConnection(
        PolygonWsChannel channel,
        string apiKey,
        IOptimizedRedisConnectionService redisService,
        ILogger logger,
        PolygonWebSocketManager manager)
    {
        _channel = channel;
        _apiKey = apiKey ?? throw new ArgumentNullException(nameof(apiKey));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _manager = manager ?? throw new ArgumentNullException(nameof(manager));
    }

    public WebSocketState State => _webSocket?.State ?? WebSocketState.None;
    public int SubscribedSymbolCount => _subscribedSymbols.Count;
    public bool IsCircuitBreakerOpen => _circuitBreakerOpen;

    /// <summary>
    /// Connects to the Polygon WebSocket for this channel
    /// </summary>
    public async Task ConnectAsync(CancellationToken cancellationToken = default)
    {
        await _connectionLock.WaitAsync(cancellationToken);
        try
        {
            if (_webSocket?.State == WebSocketState.Open)
                return;

            await DisconnectInternalAsync();

            _logger.LogInformation("Connecting to {Channel} WebSocket at {Url}",
                _channel, _channel.GetWebSocketUrl());

            _cancellationTokenSource = new CancellationTokenSource();
            _webSocket = new ClientWebSocket();

            var uri = new Uri(_channel.GetWebSocketUrl());
            await _webSocket.ConnectAsync(uri, cancellationToken);

            _logger.LogInformation("Connected to {Channel} WebSocket", _channel);

            // Start receiving messages
            _receiveTask = ReceiveMessagesAsync(_cancellationTokenSource.Token);

            // Authenticate
            await AuthenticateAsync(cancellationToken);

            // Reset reconnection state on successful connection
            _reconnectAttempts = 0;
            _consecutiveRateLimitErrors = 0;
            _circuitBreakerOpen = false;

            // Auto re-subscribe to persisted symbols
            await AutoResubscribeAsync(cancellationToken);
        }
        finally
        {
            _connectionLock.Release();
        }
    }

    /// <summary>
    /// Subscribes to symbols on this WebSocket connection
    /// </summary>
    public async Task SubscribeToSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_webSocket?.State != WebSocketState.Open)
            throw new InvalidOperationException($"WebSocket for channel {_channel} is not connected");

        if (_circuitBreakerOpen)
        {
            _logger.LogWarning("Circuit breaker is open for channel {Channel}, skipping subscription", _channel);
            return;
        }

        var symbolList = symbols.ToList();
        if (symbolList.Count == 0)
            return;

        try
        {
            var subscriptionParams = _channel.FormatSubscriptionParams(symbolList);
            var subscribeMessage = new
            {
                action = "subscribe",
                @params = subscriptionParams
            };

            await SendMessageAsync(subscribeMessage, cancellationToken);

            // Add to our tracking set
            foreach (var symbol in symbolList)
            {
                _subscribedSymbols.Add(symbol);
            }

            _logger.LogDebug("Subscribed to {SymbolCount} symbols on channel {Channel}",
                symbolList.Count, _channel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to symbols on channel {Channel}", _channel);
            throw;
        }
    }

    /// <summary>
    /// Disconnects the WebSocket connection
    /// </summary>
    public async Task DisconnectAsync()
    {
        await _connectionLock.WaitAsync();
        try
        {
            await DisconnectInternalAsync();
        }
        finally
        {
            _connectionLock.Release();
        }
    }

    private async Task DisconnectInternalAsync()
    {
        _cancellationTokenSource?.Cancel();

        if (_webSocket?.State == WebSocketState.Open)
        {
            try
            {
                await _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Disconnecting", CancellationToken.None);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error closing WebSocket for channel {Channel}", _channel);
            }
        }

        _webSocket?.Dispose();
        _webSocket = null;
        _cancellationTokenSource?.Dispose();
        _cancellationTokenSource = null;

        if (_receiveTask != null)
        {
            try
            {
                await _receiveTask;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Receive task completed with exception for channel {Channel}", _channel);
            }
            _receiveTask = null;
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            DisconnectAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during WebSocket connection disposal for channel {Channel}", _channel);
        }

        _connectionLock?.Dispose();
    }

    /// <summary>
    /// Authenticates with the Polygon WebSocket
    /// </summary>
    private async Task AuthenticateAsync(CancellationToken cancellationToken)
    {
        var authMessage = new
        {
            action = "auth",
            @params = _apiKey
        };

        await SendMessageAsync(authMessage, cancellationToken);
        _logger.LogDebug("Sent authentication message for channel {Channel}", _channel);
    }

    /// <summary>
    /// Auto re-subscribes to persisted symbols from Redis
    /// </summary>
    private async Task AutoResubscribeAsync(CancellationToken cancellationToken)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var redisKey = _channel.GetRedisSubscriptionKey();
            var symbolsString = await database.StringGetAsync(redisKey);

            if (symbolsString.HasValue)
            {
                var symbols = symbolsString.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (symbols.Length > 0)
                {
                    _logger.LogInformation("Auto re-subscribing to {SymbolCount} persisted symbols for channel {Channel}",
                        symbols.Length, _channel);

                    await SubscribeToSymbolsAsync(symbols, cancellationToken);

                    _logger.LogInformation("Successfully re-subscribed to {SymbolCount} symbols for channel {Channel}",
                        symbols.Length, _channel);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to auto re-subscribe for channel {Channel}", _channel);
        }
    }

    /// <summary>
    /// Sends a message to the WebSocket
    /// </summary>
    private async Task SendMessageAsync(object message, CancellationToken cancellationToken)
    {
        if (_webSocket?.State != WebSocketState.Open)
            throw new InvalidOperationException($"WebSocket for channel {_channel} is not connected");

        var json = JsonSerializer.Serialize(message);
        var bytes = Encoding.UTF8.GetBytes(json);
        var buffer = new ArraySegment<byte>(bytes);

        await _webSocket.SendAsync(buffer, WebSocketMessageType.Text, true, cancellationToken);
        _logger.LogTrace("Sent message to {Channel}: {Message}", _channel, json);
    }

    /// <summary>
    /// Receives messages from the WebSocket
    /// </summary>
    private async Task ReceiveMessagesAsync(CancellationToken cancellationToken)
    {
        var buffer = new byte[4096];

        try
        {
            while (!cancellationToken.IsCancellationRequested && _webSocket?.State == WebSocketState.Open)
            {
                var result = await _webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);

                if (result.MessageType == WebSocketMessageType.Text)
                {
                    var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    await ProcessMessageAsync(message);
                }
                else if (result.MessageType == WebSocketMessageType.Close)
                {
                    _logger.LogInformation("WebSocket closed by server for channel {Channel}: {CloseStatus} - {CloseDescription}",
                        _channel, result.CloseStatus, result.CloseStatusDescription);
                    break;
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("WebSocket receive operation cancelled for channel {Channel}", _channel);
        }
        catch (WebSocketException ex)
        {
            _logger.LogWarning(ex, "WebSocket error for channel {Channel}, attempting reconnection", _channel);
            _ = Task.Run(() => AttemptReconnectionAsync(CancellationToken.None));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in WebSocket receive loop for channel {Channel}", _channel);
        }
    }

    /// <summary>
    /// Processes incoming WebSocket messages
    /// FIXED: Added circuit breaker check to prevent processing when circuit is open
    /// </summary>
    private async Task ProcessMessageAsync(string message)
    {
        try
        {
            // FIXED: Skip processing if circuit breaker is open
            if (_circuitBreakerOpen)
            {
                _logger.LogDebug("Circuit breaker is open for channel {Channel}, skipping message processing", _channel);
                return;
            }

            _logger.LogTrace("Received message on {Channel}: {Message}", _channel, message);

            // Check for rate limit errors - be more specific to avoid false positives
            // Rate limit errors from Polygon typically come as HTTP status codes or specific error messages
            // NOT as part of normal market data which may contain "429" in timestamps
            if (IsRateLimitError(message))
            {
                await HandleRateLimitErrorAsync(message);
                return;
            }

            // Check for authentication success
            if (message.Contains("\"status\":\"auth_success\""))
            {
                _logger.LogInformation("Authentication successful for channel {Channel}", _channel);
                return;
            }

            // Check for subscription confirmations
            if (message.Contains("\"status\":\"success\"") && message.Contains("subscribe"))
            {
                _logger.LogDebug("Subscription confirmed for channel {Channel}", _channel);
                return;
            }

            // Process actual market data messages
            await ProcessMarketDataAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing WebSocket message for channel {Channel}: {Message}", _channel, message);
        }
    }

    /// <summary>
    /// Processes market data messages and forwards to anomaly detector
    /// </summary>
    private async Task ProcessMarketDataAsync(string message)
    {
        try
        {
            if (_manager._anomalyDetector == null)
                return; // No anomaly detection configured

            // Parse JSON message
            using var document = JsonDocument.Parse(message);
            var root = document.RootElement;

            // Handle array of messages
            if (root.ValueKind == JsonValueKind.Array)
            {
                foreach (var element in root.EnumerateArray())
                {
                    await ProcessSingleMarketDataMessage(element);
                }
            }
            else
            {
                await ProcessSingleMarketDataMessage(root);
            }
        }
        catch (JsonException ex)
        {
            _logger.LogDebug(ex, "Failed to parse market data JSON for channel {Channel}", _channel);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing market data for channel {Channel}", _channel);
        }
    }

    /// <summary>
    /// Processes a single market data message element
    /// FIXED: Added better error handling to prevent valid trade messages from being misidentified as rate limit errors
    /// </summary>
    private async Task ProcessSingleMarketDataMessage(JsonElement element)
    {
        try
        {
            // Check if this is a quote message (Q)
            if (element.TryGetProperty("ev", out var eventType) && eventType.GetString() == "Q")
            {
                if (element.TryGetProperty("sym", out var symbolProp) &&
                    element.TryGetProperty("bp", out var bidProp) &&
                    element.TryGetProperty("ap", out var askProp) &&
                    element.TryGetProperty("t", out var timestampProp))
                {
                    var symbol = symbolProp.GetString();
                    var bid = bidProp.GetDecimal();
                    var ask = askProp.GetDecimal();
                    var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampProp.GetInt64()).DateTime;

                    if (!string.IsNullOrEmpty(symbol))
                    {
                        try
                        {
                            _manager._anomalyDetector!.OnQuote(symbol, bid, ask, timestamp);
                            var midPrice = (bid + ask) / 2;
                            _manager._quoteVolatilityGuard?.OnQuote(midPrice);
                        }
                        catch (Exception anomalyEx)
                        {
                            _logger.LogDebug(anomalyEx, "Error in anomaly detector for quote {Symbol}", symbol);
                        }
                    }
                }
            }
            // Check if this is a trade message (T)
            else if (element.TryGetProperty("ev", out eventType) && eventType.GetString() == "T")
            {
                if (element.TryGetProperty("sym", out var symbolProp) &&
                    element.TryGetProperty("p", out var priceProp) &&
                    element.TryGetProperty("t", out var timestampProp))
                {
                    var symbol = symbolProp.GetString();
                    var price = priceProp.GetDecimal();
                    var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampProp.GetInt64()).DateTime;

                    if (!string.IsNullOrEmpty(symbol))
                    {
                        try
                        {
                            _manager._anomalyDetector!.OnTrade(symbol, price, timestamp);
                        }
                        catch (Exception anomalyEx)
                        {
                            _logger.LogDebug(anomalyEx, "Error in anomaly detector for trade {Symbol} at {Price}", symbol, price);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // FIXED: More specific error logging to help identify the real issue
            _logger.LogDebug(ex, "Error processing individual market data message for channel {Channel}. " +
                           "This is NOT a rate limit error - it's a data processing issue.", _channel);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// Determines if a message represents an actual rate limit error
    /// FIXED: Proper rate limit detection to avoid false positives from market data
    /// </summary>
    private static bool IsRateLimitError(string message)
    {
        // Avoid false positives from market data containing "429" in timestamps
        // Rate limit errors should be structured error messages, not market data arrays
        if (string.IsNullOrWhiteSpace(message))
            return false;

        // If the message starts with '[' it's likely market data, not an error
        var trimmed = message.Trim();
        if (trimmed.StartsWith('[') || trimmed.StartsWith('{'))
        {
            // Check if this looks like market data (contains "ev" field which is event type)
            if (trimmed.Contains("\"ev\":") || trimmed.Contains("'ev':"))
                return false;
        }

        // Look for actual rate limit indicators in proper error message format
        return message.Contains("HTTP 429", StringComparison.OrdinalIgnoreCase) ||
               message.Contains("TooManyRequests", StringComparison.OrdinalIgnoreCase) ||
               message.Contains("Rate limit exceeded", StringComparison.OrdinalIgnoreCase) ||
               message.Contains("Too many requests", StringComparison.OrdinalIgnoreCase) ||
               (message.Contains("429") && message.Contains("error", StringComparison.OrdinalIgnoreCase)) ||
               (message.Contains("status") && message.Contains("429"));
    }

    /// <summary>
    /// Handles rate limit errors with circuit breaker logic
    /// FIXED: Added circuit breaker reset logic and better rate limit handling
    /// </summary>
    private async Task HandleRateLimitErrorAsync(string message)
    {
        // FIXED: Use improved rate limit detection to avoid false positives
        if (!IsRateLimitError(message))
        {
            _logger.LogError("HandleRateLimitErrorAsync called with non-rate-limit message for channel {Channel}. " +
                           "This indicates a bug in the error handling logic. Message: {Message}", _channel, message);
            return; // Don't process as rate limit error
        }

        _consecutiveRateLimitErrors++;
        _lastRateLimitError = DateTime.UtcNow;

        _logger.LogWarning("Rate limit error #{ErrorCount} for channel {Channel}: {Message}",
            _consecutiveRateLimitErrors, _channel, message);

        if (_consecutiveRateLimitErrors >= CircuitBreakerThreshold && !_circuitBreakerOpen)
        {
            _circuitBreakerOpen = true;
            _logger.LogError("Circuit breaker opened for channel {Channel} after {ErrorCount} consecutive rate limit errors. " +
                           "Will attempt reset after 5 minutes.", _channel, _consecutiveRateLimitErrors);

            // Schedule circuit breaker reset after 5 minutes
            _ = Task.Run(async () =>
            {
                await Task.Delay(TimeSpan.FromMinutes(5));
                if (_circuitBreakerOpen && DateTime.UtcNow - _lastRateLimitError > TimeSpan.FromMinutes(5))
                {
                    _circuitBreakerOpen = false;
                    _consecutiveRateLimitErrors = 0;
                    _logger.LogInformation("Circuit breaker reset for channel {Channel} - attempting to resume operations", _channel);
                }
            });
        }

        // Only wait if circuit breaker is not open (to prevent endless delays)
        if (!_circuitBreakerOpen)
        {
            await Task.Delay(RateLimitRetryDelayMs);
        }
    }

    /// <summary>
    /// Attempts reconnection with exponential backoff
    /// FIXED: Now respects circuit breaker state to prevent endless reconnection attempts
    /// </summary>
    private async Task AttemptReconnectionAsync(CancellationToken cancellationToken)
    {
        if (_disposed || _reconnectAttempts >= MaxReconnectAttempts)
            return;

        // FIXED: Check circuit breaker state before attempting reconnection
        if (_circuitBreakerOpen)
        {
            _logger.LogWarning("Circuit breaker is open for channel {Channel}, skipping reconnection attempt #{Attempt}. " +
                             "Will retry after circuit breaker resets.", _channel, _reconnectAttempts + 1);

            // Schedule a retry after circuit breaker timeout (5 minutes)
            _ = Task.Run(async () =>
            {
                await Task.Delay(TimeSpan.FromMinutes(5), cancellationToken);
                if (!_circuitBreakerOpen && !_disposed)
                {
                    _logger.LogInformation("Circuit breaker timeout expired for channel {Channel}, attempting reconnection", _channel);
                    await AttemptReconnectionAsync(cancellationToken);
                }
            });
            return;
        }

        _reconnectAttempts++;
        var delay = Math.Min(BaseReconnectDelayMs * (int)Math.Pow(2, _reconnectAttempts - 1), MaxReconnectDelayMs);

        _logger.LogInformation("Attempting reconnection #{Attempt} for channel {Channel} in {Delay}ms",
            _reconnectAttempts, _channel, delay);

        await Task.Delay(delay, cancellationToken);

        try
        {
            await ConnectAsync(cancellationToken);
            await _manager.RecordReconnectionAsync(_channel);
            _logger.LogInformation("Reconnection successful for channel {Channel}", _channel);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Reconnection attempt #{Attempt} failed for channel {Channel}",
                _reconnectAttempts, _channel);

            // Check if this failure is due to rate limiting
            if (IsRateLimitError(ex.Message))
            {
                await HandleRateLimitErrorAsync(ex.Message);
                return; // Don't schedule another reconnection if rate limited
            }

            if (_reconnectAttempts < MaxReconnectAttempts && !_circuitBreakerOpen)
            {
                _ = Task.Run(() => AttemptReconnectionAsync(cancellationToken));
            }
            else
            {
                _logger.LogError("Max reconnection attempts reached for channel {Channel} or circuit breaker is open", _channel);
            }
        }
    }
}
