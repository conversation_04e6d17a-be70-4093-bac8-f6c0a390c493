using Microsoft.Data.Sqlite;
using System.Data;
using System.Data.Common;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Wrapper for SQLite connections that automatically returns connections to the pool when disposed
/// Provides the same interface as SqliteConnection but with pooling behavior
/// </summary>
public sealed class PooledSqliteConnection : IDisposable, IAsyncDisposable
{
    private readonly SqliteConnection _connection;
    private readonly SqliteConnectionPool _pool;
    private readonly bool _isWrite;
    private readonly bool _holdsSemaphore;
    private bool _disposed;

    internal PooledSqliteConnection(
        SqliteConnection connection, 
        SqliteConnectionPool pool, 
        bool isWrite, 
        bool holdsSemaphore = false)
    {
        _connection = connection ?? throw new ArgumentNullException(nameof(connection));
        _pool = pool ?? throw new ArgumentNullException(nameof(pool));
        _isWrite = isWrite;
        _holdsSemaphore = holdsSemaphore;
    }

    /// <summary>
    /// The underlying SQLite connection
    /// </summary>
    public SqliteConnection Connection => _connection;

    /// <summary>
    /// Connection state
    /// </summary>
    public ConnectionState State => _connection.State;

    /// <summary>
    /// Connection string
    /// </summary>
    public string ConnectionString => _connection.ConnectionString;

    /// <summary>
    /// Database name
    /// </summary>
    public string Database => _connection.Database;

    /// <summary>
    /// Data source
    /// </summary>
    public string DataSource => _connection.DataSource;

    /// <summary>
    /// Server version
    /// </summary>
    public string ServerVersion => _connection.ServerVersion;

    /// <summary>
    /// Create a command using this connection
    /// </summary>
    public SqliteCommand CreateCommand()
    {
        ThrowIfDisposed();
        return _connection.CreateCommand();
    }

    /// <summary>
    /// Create a command with the specified command text
    /// </summary>
    public SqliteCommand CreateCommand(string commandText)
    {
        ThrowIfDisposed();
        var command = _connection.CreateCommand();
        command.CommandText = commandText;
        return command;
    }

    /// <summary>
    /// Begin a database transaction
    /// </summary>
    public SqliteTransaction BeginTransaction()
    {
        ThrowIfDisposed();
        return _connection.BeginTransaction();
    }

    /// <summary>
    /// Begin a database transaction with the specified isolation level
    /// </summary>
    public SqliteTransaction BeginTransaction(IsolationLevel isolationLevel)
    {
        ThrowIfDisposed();
        return _connection.BeginTransaction(isolationLevel);
    }

    /// <summary>
    /// Begin a database transaction asynchronously
    /// </summary>
    public async Task<SqliteTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        return (SqliteTransaction)await _connection.BeginTransactionAsync(cancellationToken);
    }

    /// <summary>
    /// Begin a database transaction asynchronously with the specified isolation level
    /// </summary>
    public async Task<SqliteTransaction> BeginTransactionAsync(IsolationLevel isolationLevel, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        return (SqliteTransaction)await _connection.BeginTransactionAsync(isolationLevel, cancellationToken);
    }

    /// <summary>
    /// Execute a SQL command and return the number of affected rows
    /// </summary>
    public async Task<int> ExecuteNonQueryAsync(string sql, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        using var command = CreateCommand(sql);
        return await command.ExecuteNonQueryAsync(cancellationToken);
    }

    /// <summary>
    /// Execute a SQL command and return a scalar value
    /// </summary>
    public async Task<object?> ExecuteScalarAsync(string sql, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        using var command = CreateCommand(sql);
        return await command.ExecuteScalarAsync(cancellationToken);
    }

    /// <summary>
    /// Execute a SQL command and return a data reader
    /// </summary>
    public async Task<SqliteDataReader> ExecuteReaderAsync(string sql, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        using var command = CreateCommand(sql);
        return await command.ExecuteReaderAsync(cancellationToken);
    }

    /// <summary>
    /// Execute multiple SQL commands in a transaction
    /// </summary>
    public async Task ExecuteInTransactionAsync(Func<SqliteTransaction, Task> operation, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        using var transaction = await BeginTransactionAsync(cancellationToken);
        try
        {
            await operation(transaction);
            await transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }

    /// <summary>
    /// Execute multiple SQL commands in a transaction with return value
    /// </summary>
    public async Task<T> ExecuteInTransactionAsync<T>(Func<SqliteTransaction, Task<T>> operation, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        using var transaction = await BeginTransactionAsync(cancellationToken);
        try
        {
            var result = await operation(transaction);
            await transaction.CommitAsync(cancellationToken);
            return result;
        }
        catch
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }

    /// <summary>
    /// Check if the connection is healthy
    /// </summary>
    public bool IsHealthy()
    {
        try
        {
            return !_disposed && _connection.State == ConnectionState.Open;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Get connection statistics
    /// </summary>
    public ConnectionStats GetStats()
    {
        return new ConnectionStats
        {
            IsWrite = _isWrite,
            State = _connection.State,
            DataSource = _connection.DataSource,
            HoldsSemaphore = _holdsSemaphore
        };
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(PooledSqliteConnection));
    }

    /// <summary>
    /// Return the connection to the pool
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;
        
        // Return connection to pool instead of disposing it
        _pool.ReturnConnection(_connection, _isWrite, _holdsSemaphore);
    }

    /// <summary>
    /// Return the connection to the pool asynchronously
    /// </summary>
    public ValueTask DisposeAsync()
    {
        Dispose();
        return ValueTask.CompletedTask;
    }

    /// <summary>
    /// Implicit conversion to SqliteConnection for compatibility
    /// </summary>
    public static implicit operator SqliteConnection(PooledSqliteConnection pooledConnection)
    {
        return pooledConnection._connection;
    }
}

/// <summary>
/// Statistics about a pooled connection
/// </summary>
public class ConnectionStats
{
    public bool IsWrite { get; set; }
    public ConnectionState State { get; set; }
    public string DataSource { get; set; } = string.Empty;
    public bool HoldsSemaphore { get; set; }
}
