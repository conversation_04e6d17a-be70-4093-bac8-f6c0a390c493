using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Portfolio optimization objectives
/// </summary>
public enum PortfolioObjective
{
    MaxSharpe,
    <PERSON><PERSON><PERSON><PERSON>,
    MaxReturn,
    EqualWeight
}

/// <summary>
/// Interface for Modern Portfolio Theory optimization
/// </summary>
public interface IPortfolioOptimizer
{
    /// <summary>
    /// Optimizes portfolio allocation using Modern Portfolio Theory
    /// </summary>
    Task<OptimizedPortfolio> OptimizePortfolioAsync(
        IEnumerable<TradingSignal> signals, 
        decimal totalCapital, 
        PortfolioObjective objective = PortfolioObjective.MaxSharpe);

    /// <summary>
    /// Calculates portfolio risk metrics (VaR, Expected Shortfall, etc.)
    /// </summary>
    Task<PortfolioRiskMetrics> CalculateRiskMetricsAsync(OptimizedPortfolio portfolio);

    /// <summary>
    /// Performs portfolio rebalancing analysis
    /// </summary>
    Task<RebalancingRecommendation> AnalyzeRebalancingAsync(
        OptimizedPortfolio currentPortfolio, 
        IEnumerable<TradingSignal> newSignals);

    /// <summary>
    /// Calculates efficient frontier points for risk-return analysis
    /// </summary>
    Task<EfficientFrontier> CalculateEfficientFrontierAsync(IEnumerable<TradingSignal> signals);
}

/// <summary>
/// Modern Portfolio Theory optimizer with risk-return optimization
/// </summary>
public sealed class PortfolioOptimizer : IPortfolioOptimizer
{
    private readonly ILogger<PortfolioOptimizer> _logger;
    private readonly Random _random;

    public PortfolioOptimizer(ILogger<PortfolioOptimizer> logger)
    {
        _logger = logger;
        _random = new Random(42); // Fixed seed for reproducibility
    }

    public async Task<OptimizedPortfolio> OptimizePortfolioAsync(
        IEnumerable<TradingSignal> signals, 
        decimal totalCapital, 
        PortfolioObjective objective = PortfolioObjective.MaxSharpe)
    {
        var signalsList = signals.ToList();
        _logger.LogInformation("Optimizing portfolio with {Count} signals, capital: ${Capital:N0}, objective: {Objective}", 
            signalsList.Count, totalCapital, objective);

        try
        {
            // Simulate optimization computation time
            await Task.Delay(500);

            var positions = objective switch
            {
                PortfolioObjective.MaxSharpe => OptimizeForMaxSharpe(signalsList, totalCapital),
                PortfolioObjective.MinVariance => OptimizeForMinVariance(signalsList, totalCapital),
                PortfolioObjective.MaxReturn => OptimizeForMaxReturn(signalsList, totalCapital),
                PortfolioObjective.EqualWeight => OptimizeEqualWeight(signalsList, totalCapital),
                _ => OptimizeForMaxSharpe(signalsList, totalCapital)
            };

            var portfolio = CalculatePortfolioMetrics(positions, totalCapital, objective);
            
            _logger.LogInformation("Portfolio optimization completed. Expected return: {Return:P2}, Risk: {Risk:P2}, Sharpe: {Sharpe:F2}",
                portfolio.ExpectedReturn, portfolio.Risk, portfolio.SharpeRatio);

            return portfolio;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Portfolio optimization failed");
            throw;
        }
    }

    public async Task<PortfolioRiskMetrics> CalculateRiskMetricsAsync(OptimizedPortfolio portfolio)
    {
        _logger.LogInformation("Calculating risk metrics for portfolio with {Count} positions", portfolio.Positions.Count);

        try
        {
            await Task.Delay(300); // Simulate computation time

            // Calculate Value at Risk (VaR) and Expected Shortfall
            var dailyReturns = SimulateDailyReturns(portfolio, 252); // 1 year of daily returns
            var sortedReturns = dailyReturns.OrderBy(r => r).ToArray();

            var var95 = sortedReturns[(int)(sortedReturns.Length * 0.05)];
            var var99 = sortedReturns[(int)(sortedReturns.Length * 0.01)];
            
            var expectedShortfall = sortedReturns.Take((int)(sortedReturns.Length * 0.05)).Average();
            var maxDrawdown = CalculateMaxDrawdown(dailyReturns);

            var riskMetrics = new PortfolioRiskMetrics(
                var95,
                var99,
                expectedShortfall,
                maxDrawdown,
                portfolio.Risk,
                CalculateBeta(portfolio),
                DateTime.UtcNow
            );

            _logger.LogInformation("Risk metrics calculated. VaR(95%): {VaR:P2}, Max DD: {DD:P2}", var95, maxDrawdown);
            return riskMetrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Risk metrics calculation failed");
            throw;
        }
    }

    public async Task<RebalancingRecommendation> AnalyzeRebalancingAsync(
        OptimizedPortfolio currentPortfolio, 
        IEnumerable<TradingSignal> newSignals)
    {
        _logger.LogInformation("Analyzing rebalancing for portfolio with {Count} positions", currentPortfolio.Positions.Count);

        try
        {
            await Task.Delay(200);

            var newOptimal = await OptimizePortfolioAsync(newSignals, currentPortfolio.TotalValue);
            var rebalancingTrades = CalculateRebalancingTrades(currentPortfolio, newOptimal);
            
            var recommendation = new RebalancingRecommendation(
                rebalancingTrades,
                newOptimal.SharpeRatio - currentPortfolio.SharpeRatio,
                CalculateTransactionCosts(rebalancingTrades),
                DateTime.UtcNow
            );

            _logger.LogInformation("Rebalancing analysis completed. Sharpe improvement: {Improvement:F3}", 
                recommendation.SharpeImprovement);

            return recommendation;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Rebalancing analysis failed");
            throw;
        }
    }

    public async Task<EfficientFrontier> CalculateEfficientFrontierAsync(IEnumerable<TradingSignal> signals)
    {
        var signalsList = signals.ToList();
        _logger.LogInformation("Calculating efficient frontier for {Count} assets", signalsList.Count);

        try
        {
            await Task.Delay(800); // Simulate computation time

            var frontierPoints = new List<FrontierPoint>();
            
            // Generate efficient frontier points
            for (int i = 0; i <= 20; i++)
            {
                var targetReturn = 0.05m + (decimal)i * 0.01m; // 5% to 25% annual return
                var risk = CalculateRiskForTargetReturn(signalsList, targetReturn);
                var sharpe = (targetReturn - 0.02m) / risk; // Assuming 2% risk-free rate
                
                frontierPoints.Add(new FrontierPoint(targetReturn, risk, sharpe));
            }

            var efficientFrontier = new EfficientFrontier(
                frontierPoints,
                frontierPoints.OrderByDescending(p => p.SharpeRatio).First(),
                frontierPoints.OrderBy(p => p.Risk).First(),
                DateTime.UtcNow
            );

            _logger.LogInformation("Efficient frontier calculated with {Count} points", frontierPoints.Count);
            return efficientFrontier;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Efficient frontier calculation failed");
            throw;
        }
    }

    private List<PortfolioPosition> OptimizeForMaxSharpe(List<TradingSignal> signals, decimal totalCapital)
    {
        // Simplified Sharpe optimization - weight by risk-adjusted returns
        var positions = new List<PortfolioPosition>();
        var totalScore = signals.Sum(s => Math.Max(0, (double)s.SixMonthReturn / Math.Max(0.01, (double)s.Atr)));

        foreach (var signal in signals)
        {
            var score = Math.Max(0, (double)signal.SixMonthReturn / Math.Max(0.01, (double)signal.Atr));
            var weight = (decimal)(score / totalScore);
            var value = totalCapital * weight;
            var quantity = Math.Floor(value / signal.Price);

            if (quantity > 0)
            {
                positions.Add(new PortfolioPosition(
                    signal.Symbol,
                    quantity,
                    signal.Price,
                    value,
                    weight
                ));
            }
        }

        return positions;
    }

    private List<PortfolioPosition> OptimizeForMinVariance(List<TradingSignal> signals, decimal totalCapital)
    {
        // Simplified minimum variance - weight inversely by volatility
        var positions = new List<PortfolioPosition>();
        var totalInverseVol = signals.Sum(s => 1.0 / Math.Max(0.01, (double)s.Atr));

        foreach (var signal in signals)
        {
            var inverseVol = 1.0 / Math.Max(0.01, (double)signal.Atr);
            var weight = (decimal)(inverseVol / totalInverseVol);
            var value = totalCapital * weight;
            var quantity = Math.Floor(value / signal.Price);

            if (quantity > 0)
            {
                positions.Add(new PortfolioPosition(
                    signal.Symbol,
                    quantity,
                    signal.Price,
                    value,
                    weight
                ));
            }
        }

        return positions;
    }

    private List<PortfolioPosition> OptimizeForMaxReturn(List<TradingSignal> signals, decimal totalCapital)
    {
        // Weight by expected returns
        var positions = new List<PortfolioPosition>();
        var totalReturn = signals.Sum(s => Math.Max(0, (double)s.SixMonthReturn));

        foreach (var signal in signals)
        {
            var expectedReturn = Math.Max(0, (double)signal.SixMonthReturn);
            var weight = (decimal)(expectedReturn / totalReturn);
            var value = totalCapital * weight;
            var quantity = Math.Floor(value / signal.Price);

            if (quantity > 0)
            {
                positions.Add(new PortfolioPosition(
                    signal.Symbol,
                    quantity,
                    signal.Price,
                    value,
                    weight
                ));
            }
        }

        return positions;
    }

    private List<PortfolioPosition> OptimizeEqualWeight(List<TradingSignal> signals, decimal totalCapital)
    {
        var positions = new List<PortfolioPosition>();
        var weight = 1.0m / signals.Count();
        var valuePerPosition = totalCapital * weight;

        foreach (var signal in signals)
        {
            var quantity = Math.Floor(valuePerPosition / signal.Price);
            if (quantity > 0)
            {
                var actualValue = quantity * signal.Price;
                positions.Add(new PortfolioPosition(
                    signal.Symbol,
                    quantity,
                    signal.Price,
                    actualValue,
                    weight
                ));
            }
        }

        return positions;
    }

    private OptimizedPortfolio CalculatePortfolioMetrics(List<PortfolioPosition> positions, decimal totalCapital, PortfolioObjective objective)
    {
        var totalValue = positions.Sum(p => p.CurrentValue);
        var expectedReturn = positions.Sum(p => p.Weight * 0.12m); // Assume 12% average return
        var risk = Math.Sqrt(positions.Sum(p => (double)(p.Weight * p.Weight) * 0.04)); // Simplified risk calc
        var sharpeRatio = (double)(expectedReturn - 0.02m) / Math.Max(0.01, risk); // 2% risk-free rate

        return new OptimizedPortfolio(
            positions,
            totalValue,
            expectedReturn,
            (decimal)risk,
            (decimal)sharpeRatio,
            objective,
            DateTime.UtcNow
        );
    }

    private decimal[] SimulateDailyReturns(OptimizedPortfolio portfolio, int days)
    {
        var returns = new decimal[days];
        var dailyVol = (double)portfolio.Risk / Math.Sqrt(252); // Convert annual to daily volatility
        var dailyExpectedReturn = (double)portfolio.ExpectedReturn / 252.0; // Convert annual to daily expected return

        for (int i = 0; i < days; i++)
        {
            // Generate return = expected return + random volatility component
            var randomComponent = _random.NextGaussian() * dailyVol;
            returns[i] = (decimal)(dailyExpectedReturn + randomComponent);
        }
        return returns;
    }

    private decimal CalculateMaxDrawdown(decimal[] returns)
    {
        decimal peak = 0;
        decimal maxDrawdown = 0;
        decimal cumulative = 0;

        foreach (var ret in returns)
        {
            cumulative += ret;
            if (cumulative > peak) peak = cumulative;
            var drawdown = peak - cumulative;
            if (drawdown > maxDrawdown) maxDrawdown = drawdown;
        }

        return maxDrawdown;
    }

    private decimal CalculateBeta(OptimizedPortfolio portfolio)
    {
        // Simplified beta calculation - assume market beta of 1.0 for most stocks
        return 0.95m + (decimal)(_random.NextDouble() * 0.3); // 0.95 to 1.25
    }

    private decimal CalculateRiskForTargetReturn(List<TradingSignal> signals, decimal targetReturn)
    {
        // Simplified risk calculation for target return
        return targetReturn * 0.8m + 0.05m; // Risk increases with return
    }

    private List<RebalancingTrade> CalculateRebalancingTrades(OptimizedPortfolio current, OptimizedPortfolio target)
    {
        var trades = new List<RebalancingTrade>();
        
        // Simplified rebalancing logic
        foreach (var targetPos in target.Positions)
        {
            var currentPos = current.Positions.FirstOrDefault(p => p.Symbol == targetPos.Symbol);
            var currentQty = currentPos?.Quantity ?? 0;
            var targetQty = targetPos.Quantity;
            
            if (currentQty != targetQty)
            {
                trades.Add(new RebalancingTrade(
                    targetPos.Symbol,
                    targetQty - currentQty,
                    targetPos.Price,
                    (targetQty - currentQty) * targetPos.Price
                ));
            }
        }

        return trades;
    }

    private decimal CalculateTransactionCosts(List<RebalancingTrade> trades)
    {
        // Assume $1 per trade + 0.1% of trade value
        return trades.Count * 1.0m + trades.Sum(t => Math.Abs(t.Value) * 0.001m);
    }
}

/// <summary>
/// Optimized portfolio with positions and metrics
/// </summary>
public record OptimizedPortfolio(
    List<PortfolioPosition> Positions,
    decimal TotalValue,
    decimal ExpectedReturn,
    decimal Risk,
    decimal SharpeRatio,
    PortfolioObjective Objective,
    DateTime CreatedAt
);

/// <summary>
/// Individual position in optimized portfolio
/// </summary>
public record PortfolioPosition(
    string Symbol,
    decimal Quantity,
    decimal Price,
    decimal CurrentValue,
    decimal Weight
);

/// <summary>
/// Portfolio risk metrics
/// </summary>
public record PortfolioRiskMetrics(
    decimal VaR95,
    decimal VaR99,
    decimal ExpectedShortfall,
    decimal MaxDrawdown,
    decimal Volatility,
    decimal Beta,
    DateTime CalculatedAt
);

/// <summary>
/// Rebalancing recommendation
/// </summary>
public record RebalancingRecommendation(
    List<RebalancingTrade> Trades,
    decimal SharpeImprovement,
    decimal TransactionCosts,
    DateTime GeneratedAt
);

/// <summary>
/// Individual rebalancing trade
/// </summary>
public record RebalancingTrade(
    string Symbol,
    decimal QuantityChange,
    decimal Price,
    decimal Value
);

/// <summary>
/// Efficient frontier analysis
/// </summary>
public record EfficientFrontier(
    List<FrontierPoint> Points,
    FrontierPoint OptimalPoint,
    FrontierPoint MinVariancePoint,
    DateTime CalculatedAt
);

/// <summary>
/// Point on efficient frontier
/// </summary>
public record FrontierPoint(
    decimal ExpectedReturn,
    decimal Risk,
    decimal SharpeRatio
);


