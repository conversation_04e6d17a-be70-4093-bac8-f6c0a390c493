using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using SmaTrendFollower.Data;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that monitors PostgreSQL connection usage and logs warnings
/// when connection counts approach dangerous levels to prevent "too many clients" errors.
/// </summary>
public class PostgreSQLConnectionMonitorService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<PostgreSQLConnectionMonitorService> _logger;
    private readonly PeriodicTimer _timer;
    private const int MonitoringIntervalMinutes = 2;
    private const int WarningThreshold = 60; // Warn when total connections exceed 60 (out of ~100 max)
    private const int CriticalThreshold = 80; // Critical when total connections exceed 80

    public PostgreSQLConnectionMonitorService(
        IServiceProvider serviceProvider,
        ILogger<PostgreSQLConnectionMonitorService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _timer = new PeriodicTimer(TimeSpan.FromMinutes(MonitoringIntervalMinutes));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("PostgreSQL Connection Monitor started, checking every {Minutes} minutes", MonitoringIntervalMinutes);

        try
        {
            while (await _timer.WaitForNextTickAsync(stoppingToken))
            {
                await MonitorConnectionsAsync(stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("PostgreSQL Connection Monitor stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PostgreSQL Connection Monitor encountered an error");
        }
    }

    private async Task MonitorConnectionsAsync(CancellationToken cancellationToken)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var stockContextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<StockBarCacheDbContext>>();

            using var context = await stockContextFactory.CreateDbContextAsync(cancellationToken);

            // Query PostgreSQL system tables to get connection information
            var connectionStats = await GetConnectionStatsAsync(context);

            if (connectionStats != null)
            {
                LogConnectionStats(connectionStats);
                CheckConnectionThresholds(connectionStats);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to monitor PostgreSQL connections");
        }
    }

    private async Task<ConnectionStats?> GetConnectionStatsAsync(StockBarCacheDbContext context)
    {
        try
        {
            // Query to get connection statistics from PostgreSQL
            var sql = @"
                SELECT 
                    (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections,
                    (SELECT count(*) FROM pg_stat_activity) as total_connections,
                    (SELECT count(*) FROM pg_stat_activity WHERE application_name LIKE '%SmaTrendFollower%' OR application_name LIKE '%tradingbot%') as app_connections
            ";

            using var command = context.Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            if (context.Database.GetDbConnection().State != System.Data.ConnectionState.Open)
            {
                await context.Database.GetDbConnection().OpenAsync();
            }

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new ConnectionStats
                {
                    MaxConnections = Convert.ToInt32(reader["max_connections"]),
                    ActiveConnections = Convert.ToInt32(reader["active_connections"]),
                    IdleConnections = Convert.ToInt32(reader["idle_connections"]),
                    TotalConnections = Convert.ToInt32(reader["total_connections"]),
                    AppConnections = Convert.ToInt32(reader["app_connections"])
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Failed to query PostgreSQL connection stats");
        }

        return null;
    }

    private void LogConnectionStats(ConnectionStats stats)
    {
        var utilizationPercent = (double)stats.TotalConnections / stats.MaxConnections * 100;

        _logger.LogDebug(
            "PostgreSQL Connections: {Total}/{Max} ({Utilization:F1}%) - Active: {Active}, Idle: {Idle}, App: {App}",
            stats.TotalConnections,
            stats.MaxConnections,
            utilizationPercent,
            stats.ActiveConnections,
            stats.IdleConnections,
            stats.AppConnections);
    }

    private void CheckConnectionThresholds(ConnectionStats stats)
    {
        var utilizationPercent = (double)stats.TotalConnections / stats.MaxConnections * 100;

        if (stats.TotalConnections >= CriticalThreshold)
        {
            _logger.LogError(
                "🚨 CRITICAL: PostgreSQL connection usage is dangerously high! {Total}/{Max} ({Utilization:F1}%) - " +
                "Risk of 'too many clients' errors. Consider reducing connection pool sizes or restarting services.",
                stats.TotalConnections,
                stats.MaxConnections,
                utilizationPercent);
        }
        else if (stats.TotalConnections >= WarningThreshold)
        {
            _logger.LogWarning(
                "⚠️ WARNING: PostgreSQL connection usage is high: {Total}/{Max} ({Utilization:F1}%) - " +
                "Monitor for potential connection leaks.",
                stats.TotalConnections,
                stats.MaxConnections,
                utilizationPercent);
        }

        // Log if our application is using too many connections
        if (stats.AppConnections > 50) // Should be around 75 max with our 3×25 pool configuration
        {
            _logger.LogWarning(
                "⚠️ SmaTrendFollower application is using {AppConnections} connections - " +
                "Expected maximum is ~75 (3 contexts × 25 pool size)",
                stats.AppConnections);
        }
    }

    public override void Dispose()
    {
        _timer?.Dispose();
        base.Dispose();
    }

    private class ConnectionStats
    {
        public int MaxConnections { get; set; }
        public int ActiveConnections { get; set; }
        public int IdleConnections { get; set; }
        public int TotalConnections { get; set; }
        public int AppConnections { get; set; }
    }
}
