using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Redis connection management
/// </summary>
public interface IRedisConnection
{
    /// <summary>
    /// Gets a database instance
    /// </summary>
    /// <param name="db">Database number (default: 0)</param>
    /// <returns>IDatabase instance</returns>
    IDatabase GetDb(int db = 0);
}

/// <summary>
/// Redis connection service with optimized database caching
/// </summary>
public class RedisConnectionService : IRedisConnection
{
    private readonly IConnectionMultiplexer _muxer;
    private readonly ILogger<RedisConnectionService> _log;
    private readonly IDatabase _db0;

    public RedisConnectionService(IConnectionMultiplexer muxer, ILogger<RedisConnectionService> log)
    {
        _muxer = muxer;
        _db0   = _muxer.GetDatabase();      // cache once
        _log   = log;
    }

    public IDatabase GetDb(int db = 0)
        => db == 0 ? _db0 : _muxer.GetDatabase(db);
}
