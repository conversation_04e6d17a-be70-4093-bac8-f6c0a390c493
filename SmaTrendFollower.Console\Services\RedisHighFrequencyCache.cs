using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Redis-based implementation of high-frequency cache for real-time trading data
/// Provides sub-millisecond access to frequently accessed data during signal generation
/// Reduces SQLite lock contention by caching hot data in Redis
/// </summary>
public sealed class RedisHighFrequencyCache : IHighFrequencyCache
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    private readonly ILogger<RedisHighFrequencyCache> _logger;

    // Redis key prefixes for different data types
    private const string BarSnapshotPrefix = "hf:bar:";
    private const string SignalStatusPrefix = "hf:signal:";
    private const string PositionSizePrefix = "hf:position:";

    // Default expiry times optimized for trading frequency
    private static readonly TimeSpan DefaultBarExpiry = TimeSpan.FromMinutes(5);
    private static readonly TimeSpan DefaultSignalExpiry = TimeSpan.FromMinutes(2);
    private static readonly TimeSpan DefaultPositionExpiry = TimeSpan.FromMinutes(10);

    public RedisHighFrequencyCache(IConnectionMultiplexer redis, ILogger<RedisHighFrequencyCache> logger)
    {
        _redis = redis ?? throw new ArgumentNullException(nameof(redis));
        _database = _redis.GetDatabase();
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task CacheBarSnapshotAsync(string symbol, IBar bar, TimeSpan? expiry = null)
    {
        try
        {
            var key = BarSnapshotPrefix + symbol.ToUpperInvariant();
            var barData = new CachedBarData
            {
                Symbol = symbol,
                TimeUtc = bar.TimeUtc,
                Open = bar.Open,
                High = bar.High,
                Low = bar.Low,
                Close = bar.Close,
                Volume = bar.Volume
            };

            var value = JsonSerializer.Serialize(barData);
            var ttl = expiry ?? DefaultBarExpiry;

            await _database.StringSetAsync(key, value, ttl);
            _logger.LogDebug("Cached bar snapshot for {Symbol}: Close={Close:F2}, Volume={Volume:N0}", 
                symbol, bar.Close, bar.Volume);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache bar snapshot for {Symbol}", symbol);
        }
    }

    public async Task<IBar?> GetBarSnapshotAsync(string symbol)
    {
        try
        {
            var key = BarSnapshotPrefix + symbol.ToUpperInvariant();
            var value = await _database.StringGetAsync(key);

            if (!value.HasValue)
            {
                _logger.LogDebug("No cached bar snapshot found for {Symbol}", symbol);
                return null;
            }

            var barData = JsonSerializer.Deserialize<CachedBarData>(value!);
            if (barData == null)
            {
                _logger.LogWarning("Failed to deserialize cached bar data for {Symbol}", symbol);
                return null;
            }

            _logger.LogDebug("Retrieved cached bar snapshot for {Symbol}: Close={Close:F2}", 
                symbol, barData.Close);

            return new CachedBar(barData);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve cached bar snapshot for {Symbol}", symbol);
            return null;
        }
    }

    public async Task CacheBatchBarSnapshotsAsync(Dictionary<string, IBar> barSnapshots, TimeSpan? expiry = null)
    {
        try
        {
            var ttl = expiry ?? DefaultBarExpiry;
            var batch = _database.CreateBatch();
            var tasks = new List<Task>();

            foreach (var kvp in barSnapshots)
            {
                var key = BarSnapshotPrefix + kvp.Key.ToUpperInvariant();
                var barData = new CachedBarData
                {
                    Symbol = kvp.Key,
                    TimeUtc = kvp.Value.TimeUtc,
                    Open = kvp.Value.Open,
                    High = kvp.Value.High,
                    Low = kvp.Value.Low,
                    Close = kvp.Value.Close,
                    Volume = kvp.Value.Volume
                };

                var value = JsonSerializer.Serialize(barData);
                tasks.Add(batch.StringSetAsync(key, value, ttl));
            }

            batch.Execute();
            await Task.WhenAll(tasks);

            _logger.LogDebug("Cached {Count} bar snapshots in batch operation", barSnapshots.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache batch bar snapshots for {Count} symbols", barSnapshots.Count);
        }
    }

    public async Task<Dictionary<string, IBar?>> GetBatchBarSnapshotsAsync(IEnumerable<string> symbols)
    {
        var result = new Dictionary<string, IBar?>();

        try
        {
            var symbolsList = symbols.ToList();
            var keys = symbolsList.Select(s => (RedisKey)(BarSnapshotPrefix + s.ToUpperInvariant())).ToArray();
            var values = await _database.StringGetAsync(keys);

            for (int i = 0; i < symbolsList.Count; i++)
            {
                var symbol = symbolsList[i];
                var value = values[i];

                if (value.HasValue)
                {
                    try
                    {
                        var barData = JsonSerializer.Deserialize<CachedBarData>(value!);
                        result[symbol] = barData != null ? new CachedBar(barData) : null;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to deserialize cached bar data for {Symbol}", symbol);
                        result[symbol] = null;
                    }
                }
                else
                {
                    result[symbol] = null;
                }
            }

            var hitCount = result.Values.Count(v => v != null);
            _logger.LogDebug("Retrieved {HitCount}/{TotalCount} cached bar snapshots in batch operation", 
                hitCount, symbolsList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve batch bar snapshots for {Count} symbols", symbols.Count());
            
            // Return empty results for all symbols on error
            foreach (var symbol in symbols)
            {
                result[symbol] = null;
            }
        }

        return result;
    }

    public async Task CacheSignalStatusAsync(string symbol, string status, TimeSpan? expiry = null)
    {
        try
        {
            var key = SignalStatusPrefix + symbol.ToUpperInvariant();
            var ttl = expiry ?? DefaultSignalExpiry;

            await _database.StringSetAsync(key, status, ttl);
            _logger.LogDebug("Cached signal status for {Symbol}: {Status}", symbol, status);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache signal status for {Symbol}", symbol);
        }
    }

    public async Task<string?> GetSignalStatusAsync(string symbol)
    {
        try
        {
            var key = SignalStatusPrefix + symbol.ToUpperInvariant();
            var value = await _database.StringGetAsync(key);

            if (value.HasValue)
            {
                _logger.LogDebug("Retrieved cached signal status for {Symbol}: {Status}", symbol, value.ToString());
                return value.ToString();
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve cached signal status for {Symbol}", symbol);
            return null;
        }
    }

    public async Task CachePositionSizeAsync(string symbol, decimal positionSize, TimeSpan? expiry = null)
    {
        try
        {
            var key = PositionSizePrefix + symbol.ToUpperInvariant();
            var ttl = expiry ?? DefaultPositionExpiry;

            await _database.StringSetAsync(key, positionSize.ToString("F2"), ttl);
            _logger.LogDebug("Cached position size for {Symbol}: {PositionSize:F2}", symbol, positionSize);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache position size for {Symbol}", symbol);
        }
    }

    public async Task<decimal?> GetPositionSizeAsync(string symbol)
    {
        try
        {
            var key = PositionSizePrefix + symbol.ToUpperInvariant();
            var value = await _database.StringGetAsync(key);

            if (value.HasValue && decimal.TryParse(value.ToString(), out var positionSize))
            {
                _logger.LogDebug("Retrieved cached position size for {Symbol}: {PositionSize:F2}", symbol, positionSize);
                return positionSize;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve cached position size for {Symbol}", symbol);
            return null;
        }
    }

    public async Task ClearSymbolCacheAsync(string symbol)
    {
        try
        {
            var symbolUpper = symbol.ToUpperInvariant();
            var keys = new[]
            {
                BarSnapshotPrefix + symbolUpper,
                SignalStatusPrefix + symbolUpper,
                PositionSizePrefix + symbolUpper
            };

            await _database.KeyDeleteAsync(keys.Select(k => (RedisKey)k).ToArray());
            _logger.LogDebug("Cleared all cached data for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to clear cached data for {Symbol}", symbol);
        }
    }

    public async Task ClearAllCacheAsync()
    {
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            var keys = server.Keys(pattern: "hf:*");
            
            if (keys.Any())
            {
                await _database.KeyDeleteAsync(keys.ToArray());
                _logger.LogInformation("Cleared all high-frequency cache data");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to clear all high-frequency cache data");
        }
    }

    public async Task<HighFrequencyCacheStats> GetCacheStatsAsync()
    {
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            var allKeys = server.Keys(pattern: "hf:*").ToList();
            
            var stats = new HighFrequencyCacheStats
            {
                TotalKeys = allKeys.Count,
                BarSnapshotKeys = allKeys.Count(k => k.ToString().StartsWith(BarSnapshotPrefix)),
                SignalStatusKeys = allKeys.Count(k => k.ToString().StartsWith(SignalStatusPrefix)),
                PositionSizeKeys = allKeys.Count(k => k.ToString().StartsWith(PositionSizePrefix)),
                MemoryUsageBytes = await server.DatabaseSizeAsync()
            };

            _logger.LogDebug("Cache stats: {TotalKeys} total keys, {MemoryUsage:N0} bytes", 
                stats.TotalKeys, stats.MemoryUsageBytes);

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve cache statistics");
            return new HighFrequencyCacheStats();
        }
    }
}

/// <summary>
/// Internal data structure for caching bar data in Redis
/// </summary>
internal class CachedBarData
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime TimeUtc { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public decimal Volume { get; set; }
}

/// <summary>
/// IBar implementation for cached bar data
/// </summary>
internal class CachedBar : IBar
{
    public CachedBar(CachedBarData data)
    {
        Symbol = data.Symbol;
        TimeUtc = data.TimeUtc;
        Open = data.Open;
        High = data.High;
        Low = data.Low;
        Close = data.Close;
        Volume = data.Volume;
        Vwap = (data.High + data.Low + data.Close) / 3; // Approximate VWAP
        TradeCount = 0; // Not available in cached data
    }

    public string Symbol { get; }
    public DateTime TimeUtc { get; }
    public decimal Open { get; }
    public decimal High { get; }
    public decimal Low { get; }
    public decimal Close { get; }
    public decimal Volume { get; }
    public decimal Vwap { get; }
    public ulong TradeCount { get; }
}
