using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Exceptions;
using System.Diagnostics;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Focused service for core equity trading cycle operations.
/// Handles signal generation, risk management, and equity trade execution with Phase 6 filters.
/// </summary>
public sealed class EquityTradingCycleService : IEquityTradingCycleService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IPortfolioGate _portfolioGate;
    private readonly ITradeExecutor _tradeExecutor;
    private readonly IVWAPMonitorService _vwapMonitor;
    private readonly ITickVolatilityGuard _volatilityGuard;
    private readonly IRealTimeBreakoutSignal _breakoutSignal;
    private readonly IMicrostructurePatternDetector _microstructureDetector;
    private readonly IIndexRegimeService _indexRegimeService;
    private readonly IVIXResolverService _vixResolverService;
    private readonly IBreadthMonitorService _breadthMonitorService;
    private readonly IRealTimeExecutionService _realTimeExecutionService;
    private readonly IMarketDataService _marketDataService;
    private readonly IDiscordNotificationService _discordService;
    private readonly ILogger<EquityTradingCycleService> _logger;
    private readonly int _tradeDelayMs;

    public EquityTradingStatus Status { get; private set; } = EquityTradingStatus.Idle;

    public EquityTradingCycleService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IPortfolioGate portfolioGate,
        ITradeExecutor tradeExecutor,
        IVWAPMonitorService vwapMonitor,
        ITickVolatilityGuard volatilityGuard,
        IRealTimeBreakoutSignal breakoutSignal,
        IMicrostructurePatternDetector microstructureDetector,
        IIndexRegimeService indexRegimeService,
        IVIXResolverService vixResolverService,
        IBreadthMonitorService breadthMonitorService,
        IRealTimeExecutionService realTimeExecutionService,
        IMarketDataService marketDataService,
        IDiscordNotificationService discordService,
        ILogger<EquityTradingCycleService> logger,
        int tradeDelayMs = 1000)
    {
        _signalGenerator = signalGenerator ?? throw new ArgumentNullException(nameof(signalGenerator));
        _riskManager = riskManager ?? throw new ArgumentNullException(nameof(riskManager));
        _portfolioGate = portfolioGate ?? throw new ArgumentNullException(nameof(portfolioGate));
        _tradeExecutor = tradeExecutor ?? throw new ArgumentNullException(nameof(tradeExecutor));
        _vwapMonitor = vwapMonitor ?? throw new ArgumentNullException(nameof(vwapMonitor));
        _volatilityGuard = volatilityGuard ?? throw new ArgumentNullException(nameof(volatilityGuard));
        _breakoutSignal = breakoutSignal ?? throw new ArgumentNullException(nameof(breakoutSignal));
        _microstructureDetector = microstructureDetector ?? throw new ArgumentNullException(nameof(microstructureDetector));
        _indexRegimeService = indexRegimeService ?? throw new ArgumentNullException(nameof(indexRegimeService));
        _vixResolverService = vixResolverService ?? throw new ArgumentNullException(nameof(vixResolverService));
        _breadthMonitorService = breadthMonitorService ?? throw new ArgumentNullException(nameof(breadthMonitorService));
        _realTimeExecutionService = realTimeExecutionService ?? throw new ArgumentNullException(nameof(realTimeExecutionService));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _discordService = discordService ?? throw new ArgumentNullException(nameof(discordService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tradeDelayMs = tradeDelayMs;
    }

    public async Task<EquityTradingCycleResult> ExecuteEquityTradingCycleAsync(VolatilityRegime vixRegime, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var errors = new List<string>();
        var signalsGenerated = 0;
        var tradesExecuted = 0;
        var totalTradeValue = 0m;

        try
        {
            Status = EquityTradingStatus.GeneratingSignals;
            _logger.LogInformation("Starting equity trading cycle");

            // Step 1: Check portfolio gate (SPY SMA200)
            if (!await _portfolioGate.ShouldTradeAsync())
            {
                _logger.LogInformation("Portfolio gate blocked trading - SPY below SMA200");

                // Send portfolio snapshot notification
                await SendPortfolioSnapshotNotification("Portfolio gate blocked");

                Status = EquityTradingStatus.Completed;
                return new EquityTradingCycleResult
                {
                    Success = true,
                    Message = "Portfolio gate blocked trading - SPY below SMA200",
                    ExecutionTime = stopwatch.Elapsed
                };
            }

            // Step 2: Generate trading signals with VIX-based position count
            if (cancellationToken.IsCancellationRequested)
            {
                Status = EquityTradingStatus.Idle;
                return CreateCancelledResult(stopwatch.Elapsed);
            }

            var topN = GetAdjustedPositionCount(vixRegime);
            var signals = await _signalGenerator.RunAsync(topN);
            var signalList = signals.ToList();
            signalsGenerated = signalList.Count;

            _logger.LogInformation("Generated {Count} trading signals for {Regime} regime", signalsGenerated, vixRegime.RegimeName);

            // Step 3: Start Phase 6 monitoring for signal symbols
            if (cancellationToken.IsCancellationRequested)
            {
                Status = EquityTradingStatus.Idle;
                return CreateCancelledResult(stopwatch.Elapsed);
            }
            await StartPhase6MonitoringAsync(signalList.Select(s => s.Symbol));

            // Step 4: Execute equity trades with Phase 6 filters
            Status = EquityTradingStatus.ExecutingTrades;

            foreach (var signal in signalList)
            {
                if (cancellationToken.IsCancellationRequested) break;

                try
                {
                    // Phase 6 Filter 1: Check if trading is blocked for this symbol
                    if (_volatilityGuard.IsTradingBlocked(signal.Symbol))
                    {
                        _logger.LogInformation("Skipping {Symbol} - trading blocked due to volatility", signal.Symbol);
                        continue;
                    }

                    // Phase 6 Filter 2: Check VWAP condition for trending regime entry
                    var isPriceAboveVWAP = await _vwapMonitor.IsPriceAboveVWAPAsync(signal.Symbol, signal.Price);
                    if (!isPriceAboveVWAP)
                    {
                        _logger.LogInformation("Skipping {Symbol} - price {Price:F2} not above VWAP", signal.Symbol, signal.Price);
                        continue;
                    }

                    // Phase 6 Filter 3: Check microstructure conditions
                    var isFavorableForEntry = await _microstructureDetector.IsFavorableForEntryAsync(signal.Symbol, MicrostructureOrderSide.Buy);
                    if (!isFavorableForEntry)
                    {
                        _logger.LogInformation("Skipping {Symbol} - unfavorable microstructure conditions", signal.Symbol);
                        continue;
                    }

                    // Phase 6 Filter 4: Check index regime conditions
                    var indexRegime = await _indexRegimeService.GetCurrentRegimeAsync();
                    if (indexRegime == IndexMarketRegime.Panic || indexRegime == IndexMarketRegime.Volatile)
                    {
                        _logger.LogInformation("Skipping {Symbol} - unfavorable index regime: {Regime}", signal.Symbol, indexRegime);
                        continue;
                    }

                    // Phase 6 Filter 5: Check VIX data freshness and level
                    var vixResult = await _vixResolverService.GetCurrentVixAsync();
                    if (vixResult.ShouldHaltTrading)
                    {
                        _logger.LogWarning("Halting trading due to VIX data issues: {Error}", vixResult.ErrorMessage);
                        break; // Exit the entire trading loop
                    }
                    if (vixResult.VixValue > 30m) // High VIX threshold
                    {
                        _logger.LogInformation("Skipping {Symbol} - VIX too high: {VIX:F1}", signal.Symbol, vixResult.VixValue);
                        continue;
                    }

                    // Phase 6 Filter 6: Check market breadth conditions
                    var breadthSupportsTrading = await _breadthMonitorService.SupportsBullishSignalsAsync();
                    if (!breadthSupportsTrading)
                    {
                        _logger.LogInformation("Skipping {Symbol} - market breadth does not support bullish signals", signal.Symbol);
                        continue;
                    }

                    // Phase 6 Filter 7: Check real-time execution conditions
                    var executionAllowed = await _realTimeExecutionService.IsExecutionAllowedAsync(signal.Symbol);
                    if (!executionAllowed)
                    {
                        _logger.LogInformation("Skipping {Symbol} - real-time execution not allowed", signal.Symbol);
                        continue;
                    }

                    // Phase 6 Filter 8: Check for breakout confirmation (optional enhancement)
                    var isInBreakout = await _breakoutSignal.IsInBreakoutAsync(signal.Symbol);
                    var signalStrength = await _breakoutSignal.GetSignalStrengthAsync(signal.Symbol);

                    var baseQuantity = await _riskManager.CalculateQuantityAsync(signal);
                    if (baseQuantity > 0)
                    {
                        // Phase 6 Enhancement: Adjust position size based on breadth
                        var adjustedQuantity = await _breadthMonitorService.GetBreadthAdjustedPositionSizeAsync(baseQuantity);

                        // Phase 6 Enhancement: Get optimal execution strategy
                        var executionStrategy = await _realTimeExecutionService.EvaluateExecutionStrategyAsync(
                            signal.Symbol, SmaOrderSide.Buy, adjustedQuantity);

                        // Enhance signal with Phase 6 data
                        _logger.LogInformation("Executing trade for {Symbol}: VWAP✓, Microstructure✓, Breakout: {Breakout} (strength: {Strength:F1}%), " +
                            "IndexRegime: {IndexRegime}, VIX: {VIX:F1}, Breadth: ✓, Execution: {ExecutionType}",
                            signal.Symbol, isInBreakout ? "✓" : "○", signalStrength, indexRegime, vixResult.VixValue, executionStrategy.RecommendedOrderType);

                        // Use enhanced execution if available, otherwise fall back to standard execution
                        if (executionStrategy.ConfidenceScore > 0.5m)
                        {
                            // Create enhanced signal with execution strategy
                            var enhancedSignal = new TradingSignal(
                                signal.Symbol,
                                executionStrategy.LimitPrice ?? signal.Price,
                                signal.Atr,
                                signal.SixMonthReturn,
                                signal.Momentum,
                                signal.Timestamp
                            );

                            await _tradeExecutor.ExecuteTradeAsync(enhancedSignal, adjustedQuantity);
                        }
                        else
                        {
                            await _tradeExecutor.ExecuteTradeAsync(signal, adjustedQuantity);
                        }

                        tradesExecuted++;
                        totalTradeValue += signal.Price * adjustedQuantity;

                        // Small delay between trades to avoid overwhelming the API
                        if (_tradeDelayMs > 0)
                        {
                            await Task.Delay(_tradeDelayMs, cancellationToken);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Error executing trade for {signal.Symbol}: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Error executing trade for {Symbol}", signal.Symbol);
                }
            }

            Status = EquityTradingStatus.Completed;
            _logger.LogInformation("Completed equity trading cycle: {TradesExecuted} trades executed, total value: {TotalValue:C}",
                tradesExecuted, totalTradeValue);

            return new EquityTradingCycleResult
            {
                Success = true,
                Message = $"Successfully executed {tradesExecuted} trades",
                SignalsGenerated = signalsGenerated,
                TradesExecuted = tradesExecuted,
                TotalTradeValue = totalTradeValue,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors,
                TradedSymbols = signalList.Take(tradesExecuted).Select(s => s.Symbol).ToList()
            };
        }
        catch (Exception ex)
        {
            Status = EquityTradingStatus.Error;
            var error = $"Equity trading cycle failed: {ex.Message}";
            errors.Add(error);
            _logger.LogError(ex, "Equity trading cycle failed");

            return new EquityTradingCycleResult
            {
                Success = false,
                Message = error,
                SignalsGenerated = signalsGenerated,
                TradesExecuted = tradesExecuted,
                TotalTradeValue = totalTradeValue,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
    }

    private static EquityTradingCycleResult CreateCancelledResult(TimeSpan executionTime)
    {
        return new EquityTradingCycleResult
        {
            Success = false,
            Message = "Equity trading cycle was cancelled",
            ExecutionTime = executionTime,
            Errors = new List<string> { "Operation was cancelled" }
        };
    }

    private static int GetAdjustedPositionCount(VolatilityRegime vixRegime)
    {
        // Adjust position count based on VIX level for more granular control
        var baseCount = 10;
        var vixLevel = vixRegime.CurrentVix;

        // VIX-based position adjustment with smooth scaling
        return vixLevel switch
        {
            >= 50.0m => 3,  // Extreme VIX -> Minimal positions
            >= 40.0m => 4,  // Very high VIX -> Very few positions
            >= 35.0m => 5,  // High VIX -> Fewer positions
            >= 30.0m => 6,  // Elevated VIX -> Reduced positions
            >= 25.0m => 7,  // Medium VIX -> Moderately reduced positions
            >= 20.0m => baseCount, // Normal VIX -> Standard positions
            >= 15.0m => baseCount, // Low VIX -> Standard positions
            _ => Math.Min(12, baseCount + 2) // Very low VIX -> Slightly more positions
        };
    }

    /// <summary>
    /// Start Phase 6 monitoring services for the given symbols
    /// </summary>
    private async Task StartPhase6MonitoringAsync(IEnumerable<string> symbols)
    {
        try
        {
            var symbolList = symbols.ToList();
            if (!symbolList.Any())
                return;

            _logger.LogInformation("Starting Phase 6 monitoring for {Count} symbols", symbolList.Count);

            // Start existing Phase 6 services
            await _vwapMonitor.AddSymbolsAsync(symbolList);
            await _volatilityGuard.StartMonitoringAsync(symbolList);
            await _breakoutSignal.AddSymbolsAsync(symbolList);
            await _microstructureDetector.AddSymbolsAsync(symbolList);

            // Start new Phase 6: Real-Time Intelligence & Signal Architecture services
            await _indexRegimeService.StartMonitoringAsync();
            await _breadthMonitorService.StartMonitoringAsync();
            await _realTimeExecutionService.StartMonitoringAsync(symbolList);

            _logger.LogDebug("Phase 6 monitoring started for symbols: {Symbols}", string.Join(", ", symbolList));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting Phase 6 monitoring");
        }
    }

    /// <summary>
    /// Sends portfolio snapshot notification to Discord
    /// </summary>
    private async Task SendPortfolioSnapshotNotification(string context)
    {
        try
        {
            var account = await _marketDataService.GetAccountAsync();
            var positions = await _marketDataService.GetPositionsAsync();

            var totalEquity = account.Equity ?? 0m;
            var dayPnl = account.DayTradeCount > 0 ? (account.Equity - account.LastEquity) ?? 0m : 0m;
            var totalPnl = positions.Sum(p => p.UnrealizedProfitLoss ?? 0m);
            var positionCount = positions.Count;

            await _discordService.SendPortfolioSnapshotAsync(totalEquity, dayPnl, totalPnl, positionCount);

            _logger.LogInformation("Portfolio snapshot - Context: {Context}, Equity: {Equity:C}, Day P&L: {DayPnl:C}, Positions: {Count}",
                context, totalEquity, dayPnl, positionCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending portfolio snapshot");
        }
    }
}
