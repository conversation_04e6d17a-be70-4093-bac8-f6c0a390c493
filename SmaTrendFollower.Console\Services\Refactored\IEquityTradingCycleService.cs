using SmaTrendFollower.Models;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Interface for core equity trading cycle operations.
/// Handles signal generation, risk management, and equity trade execution.
/// </summary>
public interface IEquityTradingCycleService
{
    /// <summary>
    /// Executes the core equity trading cycle: signal generation, risk management, and trade execution.
    /// </summary>
    /// <param name="vixRegime">Current volatility regime for position sizing and filtering</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the equity trading cycle</returns>
    Task<EquityTradingCycleResult> ExecuteEquityTradingCycleAsync(VolatilityRegime vixRegime, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the current equity trading status
    /// </summary>
    EquityTradingStatus Status { get; }
}

/// <summary>
/// Result of an equity trading cycle execution
/// </summary>
public record EquityTradingCycleResult
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public int SignalsGenerated { get; init; }
    public int TradesExecuted { get; init; }
    public decimal TotalTradeValue { get; init; }
    public TimeSpan ExecutionTime { get; init; }
    public List<string> Errors { get; init; } = new();
    public List<string> TradedSymbols { get; init; } = new();
}

/// <summary>
/// Status of equity trading operations
/// </summary>
public enum EquityTradingStatus
{
    Idle,
    GeneratingSignals,
    ExecutingTrades,
    Completed,
    Error
}
