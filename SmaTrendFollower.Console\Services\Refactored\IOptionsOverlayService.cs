using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Interface for options overlay strategies.
/// Handles covered calls, protective puts, and delta-efficient exposure strategies.
/// </summary>
public interface IOptionsOverlayService
{
    /// <summary>
    /// Executes options overlay strategies based on current portfolio and market conditions.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the options overlay execution</returns>
    Task<OptionsOverlayResult> ExecuteOptionsOverlayAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the current options overlay status
    /// </summary>
    OptionsOverlayStatus Status { get; }
}

/// <summary>
/// Result of an options overlay execution
/// </summary>
public record OptionsOverlayResult
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public int CoveredCallsEvaluated { get; init; }
    public int ProtectivePutsEvaluated { get; init; }
    public int DeltaEfficientOpportunities { get; init; }
    public decimal TotalOptionsValue { get; init; }
    public TimeSpan ExecutionTime { get; init; }
    public List<string> Errors { get; init; } = new();
}

/// <summary>
/// Status of options overlay operations
/// </summary>
public enum OptionsOverlayStatus
{
    Idle,
    EvaluatingCoveredCalls,
    EvaluatingProtectivePuts,
    EvaluatingDeltaEfficient,
    ManagingExistingPositions,
    Completed,
    Error
}
