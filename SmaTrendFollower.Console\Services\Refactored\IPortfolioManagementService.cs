using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Interface for portfolio management operations.
/// Handles position monitoring, Discord notifications, and portfolio snapshots.
/// </summary>
public interface IPortfolioManagementService
{
    /// <summary>
    /// Executes portfolio management tasks including notifications and monitoring.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of the portfolio management execution</returns>
    Task<PortfolioManagementResult> ExecutePortfolioManagementAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Sends a portfolio snapshot to Discord
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if snapshot was sent successfully</returns>
    Task<bool> SendPortfolioSnapshotAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the current portfolio management status
    /// </summary>
    PortfolioManagementStatus Status { get; }
}

/// <summary>
/// Result of a portfolio management execution
/// </summary>
public record PortfolioManagementResult
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public int NotificationsSent { get; init; }
    public int PositionsMonitored { get; init; }
    public bool SnapshotSent { get; init; }
    public TimeSpan ExecutionTime { get; init; }
    public List<string> Errors { get; init; } = new();
}

/// <summary>
/// Status of portfolio management operations
/// </summary>
public enum PortfolioManagementStatus
{
    Idle,
    MonitoringPositions,
    SendingNotifications,
    SendingSnapshot,
    Completed,
    Error
}
