using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Interface for real-time market monitoring operations.
/// Coordinates all Phase 6 real-time monitoring services.
/// </summary>
public interface IRealTimeMonitoringService
{
    /// <summary>
    /// Starts real-time monitoring for the specified symbols.
    /// </summary>
    /// <param name="symbols">Symbols to monitor</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of starting monitoring</returns>
    Task<RealTimeMonitoringResult> StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stops real-time monitoring for all symbols.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result of stopping monitoring</returns>
    Task<RealTimeMonitoringResult> StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the current real-time monitoring status
    /// </summary>
    RealTimeMonitoringStatus Status { get; }
    
    /// <summary>
    /// Gets the list of currently monitored symbols
    /// </summary>
    IReadOnlyList<string> MonitoredSymbols { get; }
}

/// <summary>
/// Result of a real-time monitoring operation
/// </summary>
public record RealTimeMonitoringResult
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public int SymbolsMonitored { get; init; }
    public int ServicesStarted { get; init; }
    public TimeSpan ExecutionTime { get; init; }
    public List<string> Errors { get; init; } = new();
}

/// <summary>
/// Status of real-time monitoring operations
/// </summary>
public enum RealTimeMonitoringStatus
{
    Stopped,
    Starting,
    Active,
    Stopping,
    Error
}
