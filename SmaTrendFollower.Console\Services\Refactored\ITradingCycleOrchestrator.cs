using SmaTrendFollower.Models;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Interface for the trading cycle orchestrator.
/// Implements ITradingService by coordinating focused trading services using the Facade pattern.
/// </summary>
public interface ITradingCycleOrchestrator : ITradingService
{
    /// <summary>
    /// Gets the current orchestration status
    /// </summary>
    TradingCycleOrchestratorStatus Status { get; }
    
    /// <summary>
    /// Gets the last execution result
    /// </summary>
    TradingCycleOrchestratorResult? LastResult { get; }
}

/// <summary>
/// Result of a complete trading cycle orchestration
/// </summary>
public record TradingCycleOrchestratorResult
{
    public bool Success { get; init; }
    public string Message { get; init; } = string.Empty;
    public EquityTradingCycleResult? EquityResult { get; init; }
    public OptionsOverlayResult? OptionsResult { get; init; }
    public PortfolioManagementResult? PortfolioResult { get; init; }
    public RealTimeMonitoringResult? MonitoringResult { get; init; }
    public TimeSpan TotalExecutionTime { get; init; }
    public List<string> Errors { get; init; } = new();
}

/// <summary>
/// Status of trading cycle orchestration
/// </summary>
public enum TradingCycleOrchestratorStatus
{
    Idle,
    ExecutingEquityTrading,
    ExecutingOptionsOverlay,
    ExecutingPortfolioManagement,
    StartingRealTimeMonitoring,
    Completed,
    Error
}
