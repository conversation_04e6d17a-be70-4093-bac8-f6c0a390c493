using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Diagnostics;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Focused service for options overlay strategies.
/// Handles covered calls, protective puts, and delta-efficient exposure strategies.
/// </summary>
public sealed class OptionsOverlayService : IOptionsOverlayService
{
    private readonly IOptionsStrategyManager _optionsManager;
    private readonly IVolatilityManager _volatilityManager;
    private readonly IMarketDataService _marketDataService;
    private readonly IDiscordNotificationService _discordService;
    private readonly ILogger<OptionsOverlayService> _logger;

    public OptionsOverlayStatus Status { get; private set; } = OptionsOverlayStatus.Idle;

    public OptionsOverlayService(
        IOptionsStrategyManager optionsManager,
        IVolatilityManager volatilityManager,
        IMarketDataService marketDataService,
        IDiscordNotificationService discordService,
        ILogger<OptionsOverlayService> logger)
    {
        _optionsManager = optionsManager ?? throw new ArgumentNullException(nameof(optionsManager));
        _volatilityManager = volatilityManager ?? throw new ArgumentNullException(nameof(volatilityManager));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _discordService = discordService ?? throw new ArgumentNullException(nameof(discordService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<OptionsOverlayResult> ExecuteOptionsOverlayAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var errors = new List<string>();
        var coveredCallsEvaluated = 0;
        var protectivePutsEvaluated = 0;
        var deltaEfficientOpportunities = 0;
        var totalOptionsValue = 0m;

        try
        {
            Status = OptionsOverlayStatus.EvaluatingProtectivePuts;
            _logger.LogInformation("Starting options overlay execution");

            // Get current volatility regime
            var vixRegime = await _volatilityManager.GetCurrentRegimeAsync();
            var account = await _marketDataService.GetAccountAsync();
            var positions = await _marketDataService.GetPositionsAsync();
            var equityPositions = positions.Where(p => !IsOptionsSymbol(p.Symbol)).ToList();

            // Step 1: Evaluate protective puts for portfolio protection
            if (vixRegime.IsVixSpike || vixRegime.IsHighVol)
            {
                if (cancellationToken.IsCancellationRequested) return CreateCancelledResult(stopwatch.Elapsed);

                try
                {
                    var portfolioValue = account.Equity ?? 0m;
                    var spyResult = await _optionsManager.EvaluateProtectivePutAsync("SPY", portfolioValue, 0);
                    protectivePutsEvaluated++;

                    if (spyResult.ShouldExecute)
                    {
                        await _discordService.SendOptionsNotificationAsync("Protective Put", "SPY", spyResult.Reason);
                        _logger.LogInformation("Protective put recommended for SPY: {Reason}", spyResult.Reason);
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Error evaluating protective puts: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Error evaluating protective puts");
                }
            }

            // Step 2: Evaluate covered calls for income generation
            Status = OptionsOverlayStatus.EvaluatingCoveredCalls;
            
            foreach (var position in equityPositions.Where(p => p.Quantity >= 100))
            {
                if (cancellationToken.IsCancellationRequested) break;

                try
                {
                    var currentPrice = await GetCurrentPrice(position.Symbol);
                    if (currentPrice > 0)
                    {
                        var ccResult = await _optionsManager.EvaluateCoveredCallAsync(
                            position.Symbol, position.Quantity, currentPrice);
                        coveredCallsEvaluated++;

                        if (ccResult.ShouldExecute)
                        {
                            await _discordService.SendOptionsNotificationAsync("Covered Call", position.Symbol, ccResult.Reason);
                            _logger.LogInformation("Covered call opportunity for {Symbol}: {Reason}",
                                position.Symbol, ccResult.Reason);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Error evaluating covered call for {position.Symbol}: {ex.Message}";
                    errors.Add(error);
                    _logger.LogWarning(ex, "Error evaluating covered call for {Symbol}", position.Symbol);
                }
            }

            // Step 3: Evaluate delta-efficient exposure for capital efficiency
            Status = OptionsOverlayStatus.EvaluatingDeltaEfficient;
            
            if (!vixRegime.IsHighVol && account.Equity > 100_000m)
            {
                if (cancellationToken.IsCancellationRequested) return CreateCancelledResult(stopwatch.Elapsed);

                try
                {
                    var deltaResult = await _optionsManager.EvaluateDeltaEfficientExposureAsync("SPY", 50_000m, 0);
                    deltaEfficientOpportunities++;

                    if (deltaResult.ShouldExecute)
                    {
                        await _discordService.SendOptionsNotificationAsync("Delta Efficient", "SPY", deltaResult.Reason);
                        _logger.LogInformation("Delta-efficient exposure opportunity: {Reason}", deltaResult.Reason);
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Error evaluating delta-efficient exposure: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Error evaluating delta-efficient exposure");
                }
            }

            // Step 4: Manage existing options positions
            Status = OptionsOverlayStatus.ManagingExistingPositions;
            
            if (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    await _optionsManager.ManageExistingOptionsAsync();
                    await _optionsManager.ManageExpirationRiskAsync();
                }
                catch (Exception ex)
                {
                    var error = $"Error managing existing options positions: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Error managing existing options positions");
                }
            }

            Status = OptionsOverlayStatus.Completed;
            _logger.LogInformation("Completed options overlay: {CoveredCalls} covered calls, {ProtectivePuts} protective puts, {DeltaEfficient} delta-efficient evaluated",
                coveredCallsEvaluated, protectivePutsEvaluated, deltaEfficientOpportunities);

            return new OptionsOverlayResult
            {
                Success = true,
                Message = $"Successfully evaluated options strategies",
                CoveredCallsEvaluated = coveredCallsEvaluated,
                ProtectivePutsEvaluated = protectivePutsEvaluated,
                DeltaEfficientOpportunities = deltaEfficientOpportunities,
                TotalOptionsValue = totalOptionsValue,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
        catch (Exception ex)
        {
            Status = OptionsOverlayStatus.Error;
            var error = $"Options overlay execution failed: {ex.Message}";
            errors.Add(error);
            _logger.LogError(ex, "Options overlay execution failed");

            return new OptionsOverlayResult
            {
                Success = false,
                Message = error,
                CoveredCallsEvaluated = coveredCallsEvaluated,
                ProtectivePutsEvaluated = protectivePutsEvaluated,
                DeltaEfficientOpportunities = deltaEfficientOpportunities,
                TotalOptionsValue = totalOptionsValue,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
    }

    private async Task<decimal> GetCurrentPrice(string symbol)
    {
        try
        {
            var startDate = DateTime.UtcNow.AddDays(-1);
            var endDate = DateTime.UtcNow;
            var bars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            return bars.Items.LastOrDefault()?.Close ?? 0m;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get current price for {Symbol}", symbol);
            return 0m;
        }
    }

    private static bool IsOptionsSymbol(string symbol)
    {
        return symbol.Contains("C") || symbol.Contains("P");
    }

    private static OptionsOverlayResult CreateCancelledResult(TimeSpan executionTime)
    {
        return new OptionsOverlayResult
        {
            Success = false,
            Message = "Options overlay execution was cancelled",
            ExecutionTime = executionTime,
            Errors = new List<string> { "Operation was cancelled" }
        };
    }
}
