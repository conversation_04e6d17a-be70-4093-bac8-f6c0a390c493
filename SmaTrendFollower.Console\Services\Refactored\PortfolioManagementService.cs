using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Diagnostics;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Focused service for portfolio management operations.
/// Handles position monitoring, Discord notifications, and portfolio snapshots.
/// </summary>
public sealed class PortfolioManagementService : IPortfolioManagementService
{
    private readonly IDiscordNotificationService _discordService;
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<PortfolioManagementService> _logger;

    public PortfolioManagementStatus Status { get; private set; } = PortfolioManagementStatus.Idle;

    public PortfolioManagementService(
        IDiscordNotificationService discordService,
        IMarketDataService marketDataService,
        ILogger<PortfolioManagementService> logger)
    {
        _discordService = discordService ?? throw new ArgumentNullException(nameof(discordService));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<PortfolioManagementResult> ExecutePortfolioManagementAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var errors = new List<string>();
        var notificationsSent = 0;
        var positionsMonitored = 0;
        var snapshotSent = false;

        try
        {
            Status = PortfolioManagementStatus.MonitoringPositions;
            _logger.LogInformation("Starting portfolio management execution");

            // Step 1: Monitor positions
            if (cancellationToken.IsCancellationRequested) return CreateCancelledResult(stopwatch.Elapsed);

            try
            {
                var positions = await _marketDataService.GetPositionsAsync();
                positionsMonitored = positions.Count();

                _logger.LogInformation("Monitoring {Count} positions", positionsMonitored);

                // Check for any position alerts or notifications needed
                foreach (var position in positions)
                {
                    if (cancellationToken.IsCancellationRequested) break;

                    // Add any position-specific monitoring logic here
                    // For now, just count the positions being monitored
                }
            }
            catch (Exception ex)
            {
                var error = $"Error monitoring positions: {ex.Message}";
                errors.Add(error);
                _logger.LogError(ex, "Error monitoring positions");
            }

            // Step 2: Send portfolio snapshot
            Status = PortfolioManagementStatus.SendingSnapshot;
            
            if (!cancellationToken.IsCancellationRequested)
            {
                snapshotSent = await SendPortfolioSnapshotAsync(cancellationToken);
                if (snapshotSent)
                {
                    notificationsSent++;
                }
            }

            Status = PortfolioManagementStatus.Completed;
            _logger.LogInformation("Completed portfolio management: {Positions} positions monitored, {Notifications} notifications sent",
                positionsMonitored, notificationsSent);

            return new PortfolioManagementResult
            {
                Success = true,
                Message = $"Successfully managed portfolio with {positionsMonitored} positions",
                NotificationsSent = notificationsSent,
                PositionsMonitored = positionsMonitored,
                SnapshotSent = snapshotSent,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
        catch (Exception ex)
        {
            Status = PortfolioManagementStatus.Error;
            var error = $"Portfolio management execution failed: {ex.Message}";
            errors.Add(error);
            _logger.LogError(ex, "Portfolio management execution failed");

            return new PortfolioManagementResult
            {
                Success = false,
                Message = error,
                NotificationsSent = notificationsSent,
                PositionsMonitored = positionsMonitored,
                SnapshotSent = snapshotSent,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
    }

    public async Task<bool> SendPortfolioSnapshotAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Status = PortfolioManagementStatus.SendingSnapshot;
            _logger.LogInformation("Sending portfolio snapshot");

            var account = await _marketDataService.GetAccountAsync();
            var positions = await _marketDataService.GetPositionsAsync();

            var totalEquity = account.Equity ?? 0m;
            var dayPnl = account.DayTradeCount > 0 ? (account.Equity - account.LastEquity) ?? 0m : 0m;
            var totalPnl = positions.Sum(p => p.UnrealizedProfitLoss ?? 0m);
            var positionCount = positions.Count();

            await _discordService.SendPortfolioSnapshotAsync(totalEquity, dayPnl, totalPnl, positionCount);

            _logger.LogInformation("Portfolio snapshot sent - Equity: {Equity:C}, Day P&L: {DayPnl:C}, Positions: {Count}",
                totalEquity, dayPnl, positionCount);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending portfolio snapshot");
            return false;
        }
    }

    private static PortfolioManagementResult CreateCancelledResult(TimeSpan executionTime)
    {
        return new PortfolioManagementResult
        {
            Success = false,
            Message = "Portfolio management execution was cancelled",
            ExecutionTime = executionTime,
            Errors = new List<string> { "Operation was cancelled" }
        };
    }
}
