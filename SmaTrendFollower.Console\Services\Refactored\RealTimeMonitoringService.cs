using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Diagnostics;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// Focused service for real-time market monitoring operations.
/// Coordinates all Phase 6 real-time monitoring services.
/// </summary>
public sealed class RealTimeMonitoringService : IRealTimeMonitoringService
{
    private readonly IVWAPMonitorService _vwapMonitor;
    private readonly ITickVolatilityGuard _volatilityGuard;
    private readonly IRealTimeBreakoutSignal _breakoutSignal;
    private readonly IMicrostructurePatternDetector _microstructureDetector;
    private readonly IIndexRegimeService _indexRegimeService;
    private readonly IVIXResolverService _vixResolverService;
    private readonly IBreadthMonitorService _breadthMonitorService;
    private readonly IRealTimeExecutionService _realTimeExecutionService;
    private readonly ILogger<RealTimeMonitoringService> _logger;

    private readonly List<string> _monitoredSymbols = new();
    private readonly object _symbolsLock = new();

    public RealTimeMonitoringStatus Status { get; private set; } = RealTimeMonitoringStatus.Stopped;

    public IReadOnlyList<string> MonitoredSymbols
    {
        get
        {
            lock (_symbolsLock)
            {
                return _monitoredSymbols.ToList();
            }
        }
    }

    public RealTimeMonitoringService(
        IVWAPMonitorService vwapMonitor,
        ITickVolatilityGuard volatilityGuard,
        IRealTimeBreakoutSignal breakoutSignal,
        IMicrostructurePatternDetector microstructureDetector,
        IIndexRegimeService indexRegimeService,
        IVIXResolverService vixResolverService,
        IBreadthMonitorService breadthMonitorService,
        IRealTimeExecutionService realTimeExecutionService,
        ILogger<RealTimeMonitoringService> logger)
    {
        _vwapMonitor = vwapMonitor ?? throw new ArgumentNullException(nameof(vwapMonitor));
        _volatilityGuard = volatilityGuard ?? throw new ArgumentNullException(nameof(volatilityGuard));
        _breakoutSignal = breakoutSignal ?? throw new ArgumentNullException(nameof(breakoutSignal));
        _microstructureDetector = microstructureDetector ?? throw new ArgumentNullException(nameof(microstructureDetector));
        _indexRegimeService = indexRegimeService ?? throw new ArgumentNullException(nameof(indexRegimeService));
        _vixResolverService = vixResolverService ?? throw new ArgumentNullException(nameof(vixResolverService));
        _breadthMonitorService = breadthMonitorService ?? throw new ArgumentNullException(nameof(breadthMonitorService));
        _realTimeExecutionService = realTimeExecutionService ?? throw new ArgumentNullException(nameof(realTimeExecutionService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<RealTimeMonitoringResult> StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var errors = new List<string>();
        var servicesStarted = 0;
        var symbolList = symbols.ToList();

        try
        {
            Status = RealTimeMonitoringStatus.Starting;
            _logger.LogInformation("Starting real-time monitoring for {Count} symbols", symbolList.Count);

            // Update monitored symbols
            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
                _monitoredSymbols.AddRange(symbolList);
            }

            // Start Phase 6 Advanced Filters and Reactive Triggers
            try
            {
                await _vwapMonitor.AddSymbolsAsync(symbolList);
                servicesStarted++;
                _logger.LogDebug("Started VWAP monitoring");
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to start VWAP monitoring: {ex.Message}");
                _logger.LogError(ex, "Failed to start VWAP monitoring");
            }

            try
            {
                await _volatilityGuard.StartMonitoringAsync(symbolList);
                servicesStarted++;
                _logger.LogDebug("Started volatility guard");
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to start volatility guard: {ex.Message}");
                _logger.LogError(ex, "Failed to start volatility guard");
            }

            try
            {
                await _breakoutSignal.AddSymbolsAsync(symbolList);
                servicesStarted++;
                _logger.LogDebug("Started breakout signal monitoring");
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to start breakout signal monitoring: {ex.Message}");
                _logger.LogError(ex, "Failed to start breakout signal monitoring");
            }

            try
            {
                await _microstructureDetector.AddSymbolsAsync(symbolList);
                servicesStarted++;
                _logger.LogDebug("Started microstructure pattern detection");
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to start microstructure pattern detection: {ex.Message}");
                _logger.LogError(ex, "Failed to start microstructure pattern detection");
            }

            // Start Phase 6 Real-Time Intelligence & Signal Architecture services
            try
            {
                await _indexRegimeService.StartMonitoringAsync();
                servicesStarted++;
                _logger.LogDebug("Started index regime monitoring");
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to start index regime monitoring: {ex.Message}");
                _logger.LogError(ex, "Failed to start index regime monitoring");
            }

            try
            {
                await _breadthMonitorService.StartMonitoringAsync();
                servicesStarted++;
                _logger.LogDebug("Started breadth monitoring");
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to start breadth monitoring: {ex.Message}");
                _logger.LogError(ex, "Failed to start breadth monitoring");
            }

            try
            {
                await _realTimeExecutionService.StartMonitoringAsync(symbolList);
                servicesStarted++;
                _logger.LogDebug("Started real-time execution monitoring");
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to start real-time execution monitoring: {ex.Message}");
                _logger.LogError(ex, "Failed to start real-time execution monitoring");
            }

            Status = RealTimeMonitoringStatus.Active;
            _logger.LogInformation("Real-time monitoring started: {ServicesStarted} services active for {SymbolCount} symbols",
                servicesStarted, symbolList.Count);

            return new RealTimeMonitoringResult
            {
                Success = true,
                Message = $"Successfully started {servicesStarted} monitoring services",
                SymbolsMonitored = symbolList.Count,
                ServicesStarted = servicesStarted,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
        catch (Exception ex)
        {
            Status = RealTimeMonitoringStatus.Error;
            var error = $"Failed to start real-time monitoring: {ex.Message}";
            errors.Add(error);
            _logger.LogError(ex, "Failed to start real-time monitoring");

            return new RealTimeMonitoringResult
            {
                Success = false,
                Message = error,
                SymbolsMonitored = symbolList.Count,
                ServicesStarted = servicesStarted,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
    }

    public async Task<RealTimeMonitoringResult> StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var errors = new List<string>();
        var servicesStopped = 0;

        try
        {
            Status = RealTimeMonitoringStatus.Stopping;
            _logger.LogInformation("Stopping real-time monitoring");

            // Stop all services (implement stop methods as needed)
            // For now, just clear the monitored symbols
            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }

            // Add a small delay to simulate async work
            await Task.Delay(10, cancellationToken);

            Status = RealTimeMonitoringStatus.Stopped;
            _logger.LogInformation("Real-time monitoring stopped");

            return new RealTimeMonitoringResult
            {
                Success = true,
                Message = "Successfully stopped real-time monitoring",
                SymbolsMonitored = 0,
                ServicesStarted = servicesStopped,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
        catch (Exception ex)
        {
            Status = RealTimeMonitoringStatus.Error;
            var error = $"Failed to stop real-time monitoring: {ex.Message}";
            errors.Add(error);
            _logger.LogError(ex, "Failed to stop real-time monitoring");

            return new RealTimeMonitoringResult
            {
                Success = false,
                Message = error,
                ExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };
        }
    }
}
