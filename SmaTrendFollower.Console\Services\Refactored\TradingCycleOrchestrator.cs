using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Exceptions;
using System.Diagnostics;

namespace SmaTrendFollower.Services.Refactored;

/// <summary>
/// High-level trading cycle orchestrator that implements ITradingService.
/// Coordinates focused trading services using the Facade pattern to replace EnhancedTradingService.
/// </summary>
public sealed class TradingCycleOrchestrator : ITradingCycleOrchestrator
{
    private readonly IEquityTradingCycleService _equityTradingService;
    private readonly IOptionsOverlayService _optionsOverlayService;
    private readonly IPortfolioManagementService _portfolioManagementService;
    private readonly IRealTimeMonitoringService _realTimeMonitoringService;
    private readonly IVolatilityManager _volatilityManager;
    private readonly ITickVolatilityGuard _volatilityGuard;
    private readonly IDiscordNotificationService _discordService;
    private readonly ILogger<TradingCycleOrchestrator> _logger;

    public TradingCycleOrchestratorStatus Status { get; private set; } = TradingCycleOrchestratorStatus.Idle;
    public TradingCycleOrchestratorResult? LastResult { get; private set; }

    public TradingCycleOrchestrator(
        IEquityTradingCycleService equityTradingService,
        IOptionsOverlayService optionsOverlayService,
        IPortfolioManagementService portfolioManagementService,
        IRealTimeMonitoringService realTimeMonitoringService,
        IVolatilityManager volatilityManager,
        ITickVolatilityGuard volatilityGuard,
        IDiscordNotificationService discordService,
        ILogger<TradingCycleOrchestrator> logger)
    {
        _equityTradingService = equityTradingService ?? throw new ArgumentNullException(nameof(equityTradingService));
        _optionsOverlayService = optionsOverlayService ?? throw new ArgumentNullException(nameof(optionsOverlayService));
        _portfolioManagementService = portfolioManagementService ?? throw new ArgumentNullException(nameof(portfolioManagementService));
        _realTimeMonitoringService = realTimeMonitoringService ?? throw new ArgumentNullException(nameof(realTimeMonitoringService));
        _volatilityManager = volatilityManager ?? throw new ArgumentNullException(nameof(volatilityManager));
        _volatilityGuard = volatilityGuard ?? throw new ArgumentNullException(nameof(volatilityGuard));
        _discordService = discordService ?? throw new ArgumentNullException(nameof(discordService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task ExecuteCycleAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var errors = new List<string>();

        try
        {
            Status = TradingCycleOrchestratorStatus.ExecutingEquityTrading;
            _logger.LogInformation("Starting orchestrated trading cycle");

            // Step 1: Analyze volatility regime and check for VIX spikes
            VolatilityRegime vixRegime;
            try
            {
                vixRegime = await _volatilityManager.GetCurrentRegimeAsync();
                _logger.LogInformation("Current volatility regime: {RegimeName} (VIX: {CurrentVix})",
                    vixRegime.RegimeName, vixRegime.CurrentVix);

                // Check for VIX spike and send alert if needed
                if (vixRegime.IsVixSpike)
                {
                    await _discordService.SendVixSpikeAlertAsync(vixRegime.CurrentVix, 25.0m,
                        "Reducing position sizes and evaluating protective puts");
                }
            }
            catch (VixDataUnavailableException)
            {
                // This exception is already handled in the outer catch block
                throw;
            }

            // Step 1.5: Log any symbols with volatility blocks (informational only - don't halt entire system)
            // FIXED: Previously this would halt the entire trading cycle if ANY symbol was blocked,
            // preventing trading on all other healthy symbols. Now we only log and continue trading.
            // Individual symbol checks in SignalGenerator and TradeExecutor handle per-symbol blocking.
            if (_volatilityGuard.IsAnyTradingBlocked())
            {
                var blockedSymbols = _volatilityGuard.GetBlockedSymbols();
                _logger.LogInformation("Individual symbols blocked due to volatility: {Symbols} (trading continues for other symbols)",
                    string.Join(", ", blockedSymbols));

                // Send informational Discord message but continue trading
                await _discordService.SendMessageAsync($"ℹ️ {blockedSymbols.Count()} symbols temporarily blocked due to volatility spikes. Trading continues for other symbols.");
            }

            // Step 2: Execute equity trading cycle with VIX regime
            EquityTradingCycleResult? equityResult = null;
            if (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    equityResult = await _equityTradingService.ExecuteEquityTradingCycleAsync(vixRegime, cancellationToken);
                    if (!equityResult.Success)
                    {
                        errors.AddRange(equityResult.Errors);
                        _logger.LogWarning("Equity trading cycle completed with errors: {Message}", equityResult.Message);
                    }
                    else
                    {
                        _logger.LogInformation("Equity trading cycle completed successfully: {TradesExecuted} trades executed",
                            equityResult.TradesExecuted);
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Equity trading cycle failed: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Equity trading cycle failed");
                }
            }

            // Step 2: Execute options overlay strategies
            Status = TradingCycleOrchestratorStatus.ExecutingOptionsOverlay;
            OptionsOverlayResult? optionsResult = null;
            if (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    optionsResult = await _optionsOverlayService.ExecuteOptionsOverlayAsync(cancellationToken);
                    if (!optionsResult.Success)
                    {
                        errors.AddRange(optionsResult.Errors);
                        _logger.LogWarning("Options overlay completed with errors: {Message}", optionsResult.Message);
                    }
                    else
                    {
                        _logger.LogInformation("Options overlay completed successfully: {CoveredCalls} covered calls, {ProtectivePuts} protective puts evaluated",
                            optionsResult.CoveredCallsEvaluated, optionsResult.ProtectivePutsEvaluated);
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Options overlay failed: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Options overlay failed");
                }
            }

            // Step 3: Execute portfolio management
            Status = TradingCycleOrchestratorStatus.ExecutingPortfolioManagement;
            PortfolioManagementResult? portfolioResult = null;
            if (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    portfolioResult = await _portfolioManagementService.ExecutePortfolioManagementAsync(cancellationToken);
                    if (!portfolioResult.Success)
                    {
                        errors.AddRange(portfolioResult.Errors);
                        _logger.LogWarning("Portfolio management completed with errors: {Message}", portfolioResult.Message);
                    }
                    else
                    {
                        _logger.LogInformation("Portfolio management completed successfully: {Positions} positions monitored",
                            portfolioResult.PositionsMonitored);
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Portfolio management failed: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Portfolio management failed");
                }
            }

            // Step 4: Start real-time monitoring if we have trades
            Status = TradingCycleOrchestratorStatus.StartingRealTimeMonitoring;
            RealTimeMonitoringResult? monitoringResult = null;
            if (!cancellationToken.IsCancellationRequested && equityResult?.TradesExecuted > 0)
            {
                try
                {
                    // Use symbols from executed trades plus key indices
                    var tradedSymbols = equityResult.TradedSymbols ?? new List<string>();
                    var symbolsToMonitor = tradedSymbols.Concat(new[] { "SPY", "QQQ", "IWM" }).Distinct().ToArray();

                    monitoringResult = await _realTimeMonitoringService.StartMonitoringAsync(symbolsToMonitor, cancellationToken);
                    if (!monitoringResult.Success)
                    {
                        errors.AddRange(monitoringResult.Errors);
                        _logger.LogWarning("Real-time monitoring started with errors: {Message}", monitoringResult.Message);
                    }
                    else
                    {
                        _logger.LogInformation("Real-time monitoring started successfully for {Symbols} symbols: {SymbolList}",
                            monitoringResult.SymbolsMonitored, string.Join(", ", symbolsToMonitor));
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Real-time monitoring failed to start: {ex.Message}";
                    errors.Add(error);
                    _logger.LogError(ex, "Real-time monitoring failed to start");
                }
            }

            Status = TradingCycleOrchestratorStatus.Completed;
            
            var overallSuccess = errors.Count == 0;
            var message = overallSuccess 
                ? "Trading cycle completed successfully" 
                : $"Trading cycle completed with {errors.Count} errors";

            LastResult = new TradingCycleOrchestratorResult
            {
                Success = overallSuccess,
                Message = message,
                EquityResult = equityResult,
                OptionsResult = optionsResult,
                PortfolioResult = portfolioResult,
                MonitoringResult = monitoringResult,
                TotalExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };

            _logger.LogInformation("Orchestrated trading cycle completed: {Message}, Total time: {Duration}ms",
                message, stopwatch.ElapsedMilliseconds);
        }
        catch (VixDataUnavailableException ex)
        {
            Status = TradingCycleOrchestratorStatus.Error;
            _logger.LogCritical("🚨 VIX DATA UNAVAILABLE - HALTING TRADING FOR 15 MINUTES: {Message}", ex.Message);
            await _discordService.SendMessageAsync($"🚨 **TRADING HALTED** - VIX data unavailable from all sources. Waiting 15 minutes before retry. Time: {DateTime.UtcNow:HH:mm:ss UTC}");

            // Wait for 15 minutes before allowing the next trading cycle, but respect cancellation
            try
            {
                await Task.Delay(ex.RecommendedWaitTime, cancellationToken);
                _logger.LogInformation("15-minute VIX data halt completed, trading cycle will retry");
                await _discordService.SendMessageAsync($"⏰ **TRADING RESUMED** - 15-minute VIX data halt completed. Retrying trading cycle. Time: {DateTime.UtcNow:HH:mm:ss UTC}");
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("VIX data halt interrupted by shutdown request");
            }

            LastResult = new TradingCycleOrchestratorResult
            {
                Success = false,
                Message = $"Trading halted due to VIX data unavailability: {ex.Message}",
                TotalExecutionTime = stopwatch.Elapsed,
                Errors = new List<string> { ex.Message }
            };
        }
        catch (Exception ex)
        {
            Status = TradingCycleOrchestratorStatus.Error;
            var error = $"Trading cycle orchestration failed: {ex.Message}";
            errors.Add(error);
            _logger.LogError(ex, "Trading cycle orchestration failed");

            LastResult = new TradingCycleOrchestratorResult
            {
                Success = false,
                Message = error,
                TotalExecutionTime = stopwatch.Elapsed,
                Errors = errors
            };

            await _discordService.SendTradeNotificationAsync("ERROR", "SYSTEM", 0, 0, 0);
        }
    }
}
