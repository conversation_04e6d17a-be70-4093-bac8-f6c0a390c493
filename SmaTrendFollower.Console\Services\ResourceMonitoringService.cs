using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Console.Monitoring;
using System.Diagnostics;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Service for monitoring system resources and performance
/// Addresses memory pressure and resource exhaustion issues seen in logs
/// </summary>
public sealed class ResourceMonitoringService : BackgroundService
{
    private readonly ILogger<ResourceMonitoringService> _logger;
    private readonly ResourceMonitoringConfiguration _config;
    private readonly PerformanceCounter? _cpuCounter;
    private readonly PerformanceCounter? _memoryCounter;
    private long _lastGcMemory;
    private DateTime _lastGcTime = DateTime.UtcNow;

    public ResourceMonitoringService(
        IOptions<ResourceMonitoringConfiguration> config,
        ILogger<ResourceMonitoringService> logger)
    {
        _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        try
        {
            // Initialize performance counters (Windows only)
            if (OperatingSystem.IsWindows())
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
            else
            {
                // On non-Windows platforms, these remain null
                _cpuCounter = null;
                _memoryCounter = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to initialize performance counters, using basic monitoring");
            _cpuCounter = null;
            _memoryCounter = null;
        }

        _lastGcMemory = GC.GetTotalMemory(false);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Resource monitoring service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await MonitorResourcesAsync();
                await Task.Delay(TimeSpan.FromSeconds(_config.MonitoringIntervalSeconds), stoppingToken);
            }
            catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in resource monitoring");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("Resource monitoring service stopped");
    }

    private async Task MonitorResourcesAsync()
    {
        var resourceStats = await CollectResourceStatsAsync();
        
        // Update Prometheus metrics
        UpdateMetrics(resourceStats);
        
        // Check for resource pressure
        CheckResourcePressure(resourceStats);
        
        // Log periodic resource report
        if (_config.EnablePeriodicLogging)
        {
            LogResourceReport(resourceStats);
        }
    }

    private Task<ResourceStats> CollectResourceStatsAsync()
    {
        var stats = new ResourceStats
        {
            Timestamp = DateTime.UtcNow,
            ProcessId = Environment.ProcessId
        };

        try
        {
            // .NET Memory statistics
            var currentMemory = GC.GetTotalMemory(false);
            stats.ManagedMemoryBytes = currentMemory;
            stats.MemoryGrowthBytes = currentMemory - _lastGcMemory;
            
            // GC statistics
            stats.Gen0Collections = GC.CollectionCount(0);
            stats.Gen1Collections = GC.CollectionCount(1);
            stats.Gen2Collections = GC.CollectionCount(2);
            
            // Process statistics
            using var process = Process.GetCurrentProcess();
            stats.WorkingSetBytes = process.WorkingSet64;
            stats.PrivateMemoryBytes = process.PrivateMemorySize64;
            stats.ThreadCount = process.Threads.Count;
            stats.HandleCount = process.HandleCount;
            
            // CPU usage (if available) - Windows only
#if WINDOWS
            if (_cpuCounter != null)
            {
                stats.CpuUsagePercent = _cpuCounter.NextValue();
            }

            // Available memory (if available) - Windows only
            if (_memoryCounter != null)
            {
                stats.AvailableMemoryMB = _memoryCounter.NextValue();
            }
#endif

            // Update tracking variables
            _lastGcMemory = currentMemory;
            _lastGcTime = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect some resource statistics");
        }

        return Task.FromResult(stats);
    }

    private void UpdateMetrics(ResourceStats stats)
    {
        try
        {
            // Update Prometheus metrics if available
            CacheMetrics.RedisCacheMemoryUsage.WithLabels("managed").Set(stats.ManagedMemoryBytes);
            
            // Could add more specific metrics here
            _logger.LogDebug("Updated resource metrics: {ManagedMemoryMB}MB managed, {WorkingSetMB}MB working set",
                stats.ManagedMemoryBytes / 1024 / 1024, stats.WorkingSetBytes / 1024 / 1024);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update resource metrics");
        }
    }

    private void CheckResourcePressure(ResourceStats stats)
    {
        var warnings = new List<string>();
        var criticals = new List<string>();

        // Check memory pressure
        var managedMemoryMB = stats.ManagedMemoryBytes / 1024 / 1024;
        var workingSetMB = stats.WorkingSetBytes / 1024 / 1024;

        if (managedMemoryMB > _config.ManagedMemoryWarningMB)
        {
            if (managedMemoryMB > _config.ManagedMemoryCriticalMB)
            {
                criticals.Add($"Managed memory: {managedMemoryMB}MB (critical threshold: {_config.ManagedMemoryCriticalMB}MB)");
            }
            else
            {
                warnings.Add($"Managed memory: {managedMemoryMB}MB (warning threshold: {_config.ManagedMemoryWarningMB}MB)");
            }
        }

        if (workingSetMB > _config.WorkingSetWarningMB)
        {
            if (workingSetMB > _config.WorkingSetCriticalMB)
            {
                criticals.Add($"Working set: {workingSetMB}MB (critical threshold: {_config.WorkingSetCriticalMB}MB)");
            }
            else
            {
                warnings.Add($"Working set: {workingSetMB}MB (warning threshold: {_config.WorkingSetWarningMB}MB)");
            }
        }

        // Check memory growth
        if (stats.MemoryGrowthBytes > _config.MemoryGrowthWarningBytes)
        {
            var growthMB = stats.MemoryGrowthBytes / 1024 / 1024;
            warnings.Add($"Memory growth: {growthMB}MB since last check");
        }

        // Check thread count
        if (stats.ThreadCount > _config.ThreadCountWarning)
        {
            if (stats.ThreadCount > _config.ThreadCountCritical)
            {
                criticals.Add($"Thread count: {stats.ThreadCount} (critical threshold: {_config.ThreadCountCritical})");
            }
            else
            {
                warnings.Add($"Thread count: {stats.ThreadCount} (warning threshold: {_config.ThreadCountWarning})");
            }
        }

        // Check CPU usage
        if (stats.CpuUsagePercent > _config.CpuUsageWarningPercent)
        {
            if (stats.CpuUsagePercent > _config.CpuUsageCriticalPercent)
            {
                criticals.Add($"CPU usage: {stats.CpuUsagePercent:F1}% (critical threshold: {_config.CpuUsageCriticalPercent}%)");
            }
            else
            {
                warnings.Add($"CPU usage: {stats.CpuUsagePercent:F1}% (warning threshold: {_config.CpuUsageWarningPercent}%)");
            }
        }

        // Log warnings and criticals
        if (criticals.Any())
        {
            _logger.LogError("🚨 CRITICAL resource pressure detected: {Issues}", string.Join(", ", criticals));
            
            // Trigger garbage collection on critical memory pressure
            if (criticals.Any(c => c.Contains("memory", StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogWarning("Triggering garbage collection due to memory pressure");
                GC.Collect(2, GCCollectionMode.Forced, true);
                GC.WaitForPendingFinalizers();
            }
        }
        else if (warnings.Any())
        {
            _logger.LogWarning("⚠️ Resource pressure warning: {Issues}", string.Join(", ", warnings));
        }
    }

    private void LogResourceReport(ResourceStats stats)
    {
        _logger.LogInformation("📊 Resource Report - Managed: {ManagedMB}MB, Working Set: {WorkingSetMB}MB, " +
                             "Threads: {ThreadCount}, Handles: {HandleCount}, CPU: {CpuUsage:F1}%",
            stats.ManagedMemoryBytes / 1024 / 1024,
            stats.WorkingSetBytes / 1024 / 1024,
            stats.ThreadCount,
            stats.HandleCount,
            stats.CpuUsagePercent);

        if (stats.Gen2Collections > 0)
        {
            _logger.LogDebug("GC Collections - Gen0: {Gen0}, Gen1: {Gen1}, Gen2: {Gen2}",
                stats.Gen0Collections, stats.Gen1Collections, stats.Gen2Collections);
        }
    }

    public override void Dispose()
    {
        _cpuCounter?.Dispose();
        _memoryCounter?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Configuration for resource monitoring
/// </summary>
public class ResourceMonitoringConfiguration
{
    public const string SectionName = "ResourceMonitoring";

    /// <summary>
    /// Monitoring interval in seconds
    /// </summary>
    public int MonitoringIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// Enable periodic resource logging
    /// </summary>
    public bool EnablePeriodicLogging { get; set; } = true;

    /// <summary>
    /// Managed memory warning threshold (MB)
    /// </summary>
    public long ManagedMemoryWarningMB { get; set; } = 1024; // 1GB

    /// <summary>
    /// Managed memory critical threshold (MB)
    /// </summary>
    public long ManagedMemoryCriticalMB { get; set; } = 2048; // 2GB

    /// <summary>
    /// Working set warning threshold (MB)
    /// </summary>
    public long WorkingSetWarningMB { get; set; } = 2048; // 2GB

    /// <summary>
    /// Working set critical threshold (MB)
    /// </summary>
    public long WorkingSetCriticalMB { get; set; } = 4096; // 4GB

    /// <summary>
    /// Memory growth warning threshold (bytes)
    /// </summary>
    public long MemoryGrowthWarningBytes { get; set; } = 100 * 1024 * 1024; // 100MB

    /// <summary>
    /// Thread count warning threshold
    /// </summary>
    public int ThreadCountWarning { get; set; } = 100;

    /// <summary>
    /// Thread count critical threshold
    /// </summary>
    public int ThreadCountCritical { get; set; } = 200;

    /// <summary>
    /// CPU usage warning threshold (%)
    /// </summary>
    public float CpuUsageWarningPercent { get; set; } = 80.0f;

    /// <summary>
    /// CPU usage critical threshold (%)
    /// </summary>
    public float CpuUsageCriticalPercent { get; set; } = 95.0f;
}

/// <summary>
/// Resource statistics snapshot
/// </summary>
public class ResourceStats
{
    public DateTime Timestamp { get; set; }
    public int ProcessId { get; set; }
    public long ManagedMemoryBytes { get; set; }
    public long WorkingSetBytes { get; set; }
    public long PrivateMemoryBytes { get; set; }
    public long MemoryGrowthBytes { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
    public float CpuUsagePercent { get; set; }
    public float AvailableMemoryMB { get; set; }
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
}
