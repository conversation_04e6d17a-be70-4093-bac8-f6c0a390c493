using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// SQLite connection pool with write serialization to eliminate database lock contention
/// Provides separate read and write connection pools with a single-writer pattern
/// </summary>
public sealed class SqliteConnectionPool : IDisposable
{
    private readonly ILogger<SqliteConnectionPool> _logger;
    private readonly SqlitePoolConfiguration _config;
    private readonly string _connectionString;
    
    // Connection pools
    private readonly ConcurrentQueue<SqliteConnection> _readConnections = new();
    private readonly ConcurrentQueue<SqliteConnection> _writeConnections = new();
    
    // Write serialization
    private readonly SemaphoreSlim _writeSemaphore;
    private readonly object _writeConnectionLock = new();
    private SqliteConnection? _dedicatedWriteConnection;
    
    // Pool management
    private readonly Timer _maintenanceTimer;
    private volatile bool _disposed;
    private int _totalReadConnections;
    private int _totalWriteConnections = 0;
    private int _activeReadConnections;
    private int _activeWriteConnections;

    public SqliteConnectionPool(
        string connectionString,
        IOptions<SqlitePoolConfiguration> config,
        ILogger<SqliteConnectionPool> logger)
    {
        _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _writeSemaphore = new SemaphoreSlim(1, 1); // Single writer
        
        // Initialize connection pools
        InitializeConnectionPools();
        
        // Start maintenance timer
        _maintenanceTimer = new Timer(PerformMaintenance, null, 
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation("SQLite connection pool initialized: {ReadPoolSize} read connections, {WritePoolSize} write connections",
            _config.MaxReadConnections, _config.MaxWriteConnections);
    }

    /// <summary>
    /// Get a read connection from the pool
    /// Multiple concurrent reads are supported
    /// </summary>
    public async Task<PooledSqliteConnection> GetReadConnectionAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Try to get from pool first
            if (_readConnections.TryDequeue(out var pooledConnection))
            {
                if (IsConnectionHealthy(pooledConnection))
                {
                    Interlocked.Increment(ref _activeReadConnections);
                    _logger.LogDebug("Retrieved read connection from pool in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                    return new PooledSqliteConnection(pooledConnection, this, isWrite: false);
                }
                else
                {
                    // Connection is unhealthy, dispose and create new one
                    await pooledConnection.DisposeAsync();
                    Interlocked.Decrement(ref _totalReadConnections);
                }
            }
            
            // Create new connection if pool is empty or connection was unhealthy
            var newConnection = await CreateConnectionAsync(enableWal: true, cancellationToken);
            Interlocked.Increment(ref _totalReadConnections);
            Interlocked.Increment(ref _activeReadConnections);
            
            _logger.LogDebug("Created new read connection in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            return new PooledSqliteConnection(newConnection, this, isWrite: false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get read connection after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// Get a write connection with serialization
    /// Only one write operation can occur at a time to prevent locks
    /// </summary>
    public async Task<PooledSqliteConnection> GetWriteConnectionAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Wait for write semaphore (serializes all writes)
            await _writeSemaphore.WaitAsync(cancellationToken);
            
            try
            {
                lock (_writeConnectionLock)
                {
                    // Use dedicated write connection if available and healthy
                    if (_dedicatedWriteConnection != null && IsConnectionHealthy(_dedicatedWriteConnection))
                    {
                        Interlocked.Increment(ref _activeWriteConnections);
                        _logger.LogDebug("Using dedicated write connection in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                        return new PooledSqliteConnection(_dedicatedWriteConnection, this, isWrite: true, holdsSemaphore: true);
                    }
                    
                    // Create or recreate dedicated write connection
                    _dedicatedWriteConnection?.Dispose();
                    _dedicatedWriteConnection = CreateConnectionAsync(enableWal: true, cancellationToken).GetAwaiter().GetResult();
                    
                    Interlocked.Increment(ref _activeWriteConnections);
                    _logger.LogDebug("Created dedicated write connection in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                    return new PooledSqliteConnection(_dedicatedWriteConnection, this, isWrite: true, holdsSemaphore: true);
                }
            }
            catch
            {
                _writeSemaphore.Release(); // Release semaphore on error
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get write connection after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    /// <summary>
    /// Get pool statistics for monitoring
    /// </summary>
    public PoolStats GetStats()
    {
        return new PoolStats
        {
            TotalReadConnections = _totalReadConnections,
            ActiveReadConnections = _activeReadConnections,
            AvailableReadConnections = _readConnections.Count,
            TotalWriteConnections = _totalWriteConnections,
            ActiveWriteConnections = _activeWriteConnections,
            HasDedicatedWriteConnection = _dedicatedWriteConnection != null
        };
    }

    /// <summary>
    /// Return a connection to the pool
    /// </summary>
    internal void ReturnConnection(SqliteConnection connection, bool isWrite, bool holdsSemaphore)
    {
        try
        {
            if (isWrite)
            {
                Interlocked.Decrement(ref _activeWriteConnections);
                
                // Don't return write connection to pool - keep as dedicated connection
                // Just release the semaphore to allow next write operation
                if (holdsSemaphore)
                {
                    _writeSemaphore.Release();
                }
            }
            else
            {
                Interlocked.Decrement(ref _activeReadConnections);
                
                // Return read connection to pool if healthy and pool not full
                if (IsConnectionHealthy(connection) && _readConnections.Count < _config.MaxReadConnections)
                {
                    _readConnections.Enqueue(connection);
                    _logger.LogDebug("Returned read connection to pool");
                }
                else
                {
                    // Dispose connection if unhealthy or pool is full
                    connection.Dispose();
                    Interlocked.Decrement(ref _totalReadConnections);
                    _logger.LogDebug("Disposed read connection (unhealthy or pool full)");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error returning connection to pool");
        }
    }

    private void InitializeConnectionPools()
    {
        // Pre-create read connections
        for (int i = 0; i < _config.InitialReadConnections; i++)
        {
            try
            {
                var connection = CreateConnectionAsync(enableWal: true).GetAwaiter().GetResult();
                _readConnections.Enqueue(connection);
                Interlocked.Increment(ref _totalReadConnections);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to create initial read connection {Index}", i);
            }
        }
        
        _logger.LogInformation("Initialized {Count} read connections in pool", _readConnections.Count);
    }

    private async Task<SqliteConnection> CreateConnectionAsync(bool enableWal = true, CancellationToken cancellationToken = default)
    {
        var connectionStringBuilder = new SqliteConnectionStringBuilder(_connectionString)
        {
            // Connection optimizations
            Pooling = false, // We handle our own pooling
            Cache = SqliteCacheMode.Shared,
            
            // Performance settings
            DefaultTimeout = _config.CommandTimeoutSeconds,
        };

        var connection = new SqliteConnection(connectionStringBuilder.ToString());
        await connection.OpenAsync(cancellationToken);
        
        // Configure connection for optimal performance
        if (enableWal)
        {
            await ExecutePragmaAsync(connection, "journal_mode", "WAL");
        }
        
        await ExecutePragmaAsync(connection, "synchronous", "NORMAL");
        await ExecutePragmaAsync(connection, "cache_size", "-64000"); // 64MB cache
        await ExecutePragmaAsync(connection, "temp_store", "MEMORY");
        await ExecutePragmaAsync(connection, "mmap_size", "268435456"); // 256MB mmap
        await ExecutePragmaAsync(connection, "busy_timeout", _config.BusyTimeoutMs.ToString());
        
        return connection;
    }

    private static async Task ExecutePragmaAsync(SqliteConnection connection, string pragma, string value)
    {
        using var command = connection.CreateCommand();
        command.CommandText = $"PRAGMA {pragma} = {value}";
        await command.ExecuteNonQueryAsync();
    }

    private static bool IsConnectionHealthy(SqliteConnection connection)
    {
        try
        {
            return connection.State == System.Data.ConnectionState.Open;
        }
        catch
        {
            return false;
        }
    }

    private void PerformMaintenance(object? state)
    {
        if (_disposed) return;
        
        try
        {
            var readPoolSize = _readConnections.Count;
            var totalReads = _totalReadConnections;
            var activeReads = _activeReadConnections;
            var totalWrites = _totalWriteConnections;
            var activeWrites = _activeWriteConnections;
            
            _logger.LogDebug("Connection pool stats - Read: {ReadPool}/{TotalReads} (Active: {ActiveReads}), Write: {TotalWrites} (Active: {ActiveWrites})",
                readPoolSize, totalReads, activeReads, totalWrites, activeWrites);
            
            // Clean up unhealthy connections from read pool
            var connectionsToRemove = new List<SqliteConnection>();
            var tempConnections = new List<SqliteConnection>();
            
            while (_readConnections.TryDequeue(out var connection))
            {
                if (IsConnectionHealthy(connection))
                {
                    tempConnections.Add(connection);
                }
                else
                {
                    connectionsToRemove.Add(connection);
                }
            }
            
            // Return healthy connections to pool
            foreach (var connection in tempConnections)
            {
                _readConnections.Enqueue(connection);
            }
            
            // Dispose unhealthy connections
            foreach (var connection in connectionsToRemove)
            {
                connection.Dispose();
                Interlocked.Decrement(ref _totalReadConnections);
            }
            
            if (connectionsToRemove.Count > 0)
            {
                _logger.LogInformation("Cleaned up {Count} unhealthy read connections during maintenance", connectionsToRemove.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during connection pool maintenance");
        }
    }

    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SqliteConnectionPool));
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;
        
        _maintenanceTimer?.Dispose();
        _writeSemaphore?.Dispose();
        
        // Dispose all read connections
        while (_readConnections.TryDequeue(out var connection))
        {
            connection.Dispose();
        }
        
        // Dispose dedicated write connection
        lock (_writeConnectionLock)
        {
            _dedicatedWriteConnection?.Dispose();
        }
        
        _logger.LogInformation("SQLite connection pool disposed");
    }
}

/// <summary>
/// Configuration for SQLite connection pool
/// </summary>
public class SqlitePoolConfiguration
{
    public const string SectionName = "SqlitePool";
    
    /// <summary>
    /// Maximum number of read connections in the pool
    /// </summary>
    public int MaxReadConnections { get; set; } = 10;
    
    /// <summary>
    /// Initial number of read connections to create
    /// </summary>
    public int InitialReadConnections { get; set; } = 3;
    
    /// <summary>
    /// Maximum number of write connections (typically 1 for serialization)
    /// </summary>
    public int MaxWriteConnections { get; set; } = 1;
    
    /// <summary>
    /// Command timeout in seconds
    /// </summary>
    public int CommandTimeoutSeconds { get; set; } = 30;
    
    /// <summary>
    /// SQLite busy timeout in milliseconds
    /// </summary>
    public int BusyTimeoutMs { get; set; } = 5000;
}

/// <summary>
/// Statistics for a connection pool
/// </summary>
public class PoolStats
{
    public int TotalReadConnections { get; set; }
    public int ActiveReadConnections { get; set; }
    public int AvailableReadConnections { get; set; }
    public int TotalWriteConnections { get; set; }
    public int ActiveWriteConnections { get; set; }
    public bool HasDedicatedWriteConnection { get; set; }
}
