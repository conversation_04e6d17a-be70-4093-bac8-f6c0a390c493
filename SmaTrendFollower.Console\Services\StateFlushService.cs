using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that periodically flushes Redis state to SQLite for persistence.
/// Ensures important trading state is preserved across restarts.
/// </summary>
public sealed class StateFlushService : BackgroundService
{
    private readonly ILiveStateStore _liveStateStore;
    private readonly IBarStore _historicalStore;
    private readonly ILogger<StateFlushService> _logger;
    private readonly TimeSpan _flushInterval;
    private readonly string _stateBackupPath;

    public StateFlushService(
        ILiveStateStore liveStateStore,
        IBarStore historicalStore,
        IConfiguration configuration,
        ILogger<StateFlushService> logger)
    {
        _liveStateStore = liveStateStore;
        _historicalStore = historicalStore;
        _logger = logger;
        
        _flushInterval = TimeSpan.FromMinutes(int.Parse(configuration["STATE_FLUSH_INTERVAL_MINUTES"] ?? "15"));
        _stateBackupPath = configuration["STATE_BACKUP_PATH"] ?? "Data/state_backup.json";
        
        // Ensure backup directory exists
        var backupDir = Path.GetDirectoryName(_stateBackupPath);
        if (!string.IsNullOrEmpty(backupDir))
        {
            Directory.CreateDirectory(backupDir);
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("StateFlushService started with {FlushInterval} interval", _flushInterval);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await FlushStateAsync(stoppingToken);
                await Task.Delay(_flushInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during state flush operation");
                
                // Wait a shorter interval before retrying on error
                try
                {
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }

        // Perform final flush on shutdown
        try
        {
            _logger.LogInformation("Performing final state flush on shutdown");
            await FlushStateAsync(CancellationToken.None);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during final state flush");
        }
    }

    /// <summary>
    /// Flushes current Redis state to persistent storage
    /// </summary>
    private async Task FlushStateAsync(CancellationToken cancellationToken)
    {
        try
        {
            var stats = await _liveStateStore.GetStatsAsync(cancellationToken);
            
            if (stats.TrailingStopCount == 0 && stats.PositionStateCount == 0 && stats.RetryQueueLength == 0)
            {
                _logger.LogDebug("No state to flush");
                return;
            }

            _logger.LogDebug("Flushing state: {TrailingStops} stops, {Positions} positions, {RetryItems} retry items",
                stats.TrailingStopCount, stats.PositionStateCount, stats.RetryQueueLength);

            var stateSnapshot = await CreateStateSnapshotAsync(cancellationToken);
            await SaveStateSnapshotAsync(stateSnapshot, cancellationToken);

            _logger.LogInformation("State flushed successfully at {Time:yyyy-MM-dd HH:mm:ss} UTC", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to flush state");
            throw;
        }
    }

    /// <summary>
    /// Creates a snapshot of current Redis state
    /// </summary>
    private async Task<StateSnapshot> CreateStateSnapshotAsync(CancellationToken cancellationToken)
    {
        var trailingStops = await _liveStateStore.GetAllTrailingStopsAsync(cancellationToken);
        
        var positionStates = new Dictionary<string, PositionState>();
        foreach (var symbol in trailingStops.Keys)
        {
            var positionState = await _liveStateStore.GetPositionStateAsync(symbol, cancellationToken);
            if (positionState != null)
            {
                positionStates[symbol] = positionState;
            }
        }

        var retryItems = new List<RetryItem>();
        var queueLength = await _liveStateStore.GetRetryQueueLengthAsync(cancellationToken);
        for (int i = 0; i < Math.Min(queueLength, 100); i++) // Limit to prevent memory issues
        {
            var item = await _liveStateStore.DequeueRetryAsync(cancellationToken);
            if (item != null)
            {
                retryItems.Add(item);
                // Re-enqueue the item since we're just peeking
                await _liveStateStore.EnqueueRetryAsync(item, cancellationToken);
            }
        }

        return new StateSnapshot(
            DateTime.UtcNow,
            trailingStops,
            positionStates,
            retryItems
        );
    }

    /// <summary>
    /// Saves state snapshot to persistent storage with enhanced error handling
    /// </summary>
    private async Task SaveStateSnapshotAsync(StateSnapshot snapshot, CancellationToken cancellationToken)
    {
        try
        {
            // Ensure directory exists
            var directory = Path.GetDirectoryName(_stateBackupPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogDebug("📁 Created state backup directory: {Directory}", directory);
            }

            var json = JsonSerializer.Serialize(snapshot, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            // Create backup of existing file with timestamp
            if (File.Exists(_stateBackupPath))
            {
                var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                var backupPath = $"{_stateBackupPath}.{timestamp}.bak";

                try
                {
                    File.Copy(_stateBackupPath, backupPath, overwrite: true);
                    _logger.LogDebug("📄 Created backup of existing state file: {BackupPath}", backupPath);

                    // Clean up old backup files (keep only last 5)
                    CleanupOldBackupFiles();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "⚠️ Failed to create backup of existing state file - continuing with save");
                }
            }

            // Write new state file atomically
            var tempPath = _stateBackupPath + ".tmp";
            await File.WriteAllTextAsync(tempPath, json, cancellationToken);

            // Atomic move to final location
            File.Move(tempPath, _stateBackupPath, overwrite: true);

            var fileInfo = new FileInfo(_stateBackupPath);
            _logger.LogDebug("💾 State snapshot saved to {Path} ({Size} bytes)", _stateBackupPath, fileInfo.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to save state snapshot to {Path}", _stateBackupPath);
            throw; // Re-throw to be handled by caller
        }
    }

    /// <summary>
    /// Cleans up old backup files to prevent disk space issues
    /// </summary>
    private void CleanupOldBackupFiles()
    {
        try
        {
            var directory = Path.GetDirectoryName(_stateBackupPath);
            if (string.IsNullOrEmpty(directory)) return;

            var fileName = Path.GetFileName(_stateBackupPath);
            var backupPattern = $"{fileName}.*.bak";

            var backupFiles = Directory.GetFiles(directory, backupPattern)
                .Select(f => new FileInfo(f))
                .OrderByDescending(f => f.LastWriteTime)
                .Skip(5) // Keep only the 5 most recent backups
                .ToList();

            foreach (var file in backupFiles)
            {
                try
                {
                    file.Delete();
                    _logger.LogDebug("🗑️ Deleted old backup file: {FileName}", file.Name);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "⚠️ Failed to delete old backup file: {FileName}", file.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ Failed to cleanup old backup files");
        }
    }

    /// <summary>
    /// Restores state from persistent storage on startup with enhanced error handling
    /// </summary>
    public async Task RestoreStateAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("🔍 Checking for state backup file at: {BackupPath}", _stateBackupPath);

            if (!File.Exists(_stateBackupPath))
            {
                _logger.LogInformation("📄 No state backup file found at {BackupPath} - starting with clean state", _stateBackupPath);
                return;
            }

            var fileInfo = new FileInfo(_stateBackupPath);
            _logger.LogInformation("📊 Found state backup file: {Size} bytes, modified {LastWrite:yyyy-MM-dd HH:mm:ss} UTC",
                fileInfo.Length, fileInfo.LastWriteTimeUtc);

            // Read file with timeout protection
            string json;
            using var fileTimeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            fileTimeoutCts.CancelAfter(TimeSpan.FromSeconds(10)); // File read timeout

            try
            {
                json = await File.ReadAllTextAsync(_stateBackupPath, fileTimeoutCts.Token);
                _logger.LogDebug("📖 Successfully read {CharCount} characters from state backup file", json.Length);
            }
            catch (OperationCanceledException) when (fileTimeoutCts.Token.IsCancellationRequested && !cancellationToken.IsCancellationRequested)
            {
                _logger.LogWarning("⏰ File read timed out after 10 seconds - state backup file may be too large or disk is slow");
                return;
            }

            // Deserialize with better error handling
            StateSnapshot? snapshot;
            try
            {
                snapshot = JsonSerializer.Deserialize<StateSnapshot>(json, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    AllowTrailingCommas = true,
                    ReadCommentHandling = JsonCommentHandling.Skip
                });
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "📄 Failed to deserialize state snapshot - JSON format error at position {Position}", ex.BytePositionInLine);
                throw; // Re-throw to be handled by outer catch
            }

            if (snapshot == null)
            {
                _logger.LogWarning("📄 State snapshot deserialized to null - backup file may be empty or invalid");
                return;
            }

            _logger.LogInformation("📅 State snapshot timestamp: {Timestamp:yyyy-MM-dd HH:mm:ss} UTC", snapshot.Timestamp);

            // Check if snapshot is too old (configurable, default 24 hours)
            var maxAge = TimeSpan.FromHours(24);
            var age = DateTime.UtcNow - snapshot.Timestamp;
            if (age > maxAge)
            {
                _logger.LogWarning("⏰ State snapshot is too old ({Age:F1} hours > {MaxAge:F1} hours), skipping restore for safety",
                    age.TotalHours, maxAge.TotalHours);
                _logger.LogInformation("💡 Old state snapshots are skipped to prevent restoring stale trading positions");
                return;
            }

            // Restore state with individual error handling
            var restoredStops = 0;
            var restoredPositions = 0;
            var restoredRetryItems = 0;

            // Restore trailing stops with individual error handling
            _logger.LogDebug("🛑 Restoring {Count} trailing stops...", snapshot.TrailingStops.Count);
            foreach (var kvp in snapshot.TrailingStops)
            {
                try
                {
                    await _liveStateStore.SetTrailingStopAsync(kvp.Key, kvp.Value, cancellationToken);
                    restoredStops++;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "⚠️ Failed to restore trailing stop for {Symbol}: {StopPrice:C}", kvp.Key, kvp.Value);
                }
            }

            // Restore position states with individual error handling
            _logger.LogDebug("📊 Restoring {Count} position states...", snapshot.PositionStates.Count);
            foreach (var kvp in snapshot.PositionStates)
            {
                try
                {
                    await _liveStateStore.SetPositionStateAsync(kvp.Key, kvp.Value, cancellationToken);
                    restoredPositions++;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "⚠️ Failed to restore position state for {Symbol}", kvp.Key);
                }
            }

            // Restore retry items with individual error handling
            _logger.LogDebug("🔄 Restoring {Count} retry items...", snapshot.RetryItems.Count);
            foreach (var item in snapshot.RetryItems)
            {
                try
                {
                    await _liveStateStore.EnqueueRetryAsync(item, cancellationToken);
                    restoredRetryItems++;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "⚠️ Failed to restore retry item: {Operation}", item.Operation);
                }
            }

            stopwatch.Stop();
            _logger.LogInformation("✅ State restoration completed in {ElapsedMs}ms: {RestoredStops}/{TotalStops} stops, {RestoredPositions}/{TotalPositions} positions, {RestoredRetryItems}/{TotalRetryItems} retry items",
                stopwatch.ElapsedMilliseconds,
                restoredStops, snapshot.TrailingStops.Count,
                restoredPositions, snapshot.PositionStates.Count,
                restoredRetryItems, snapshot.RetryItems.Count);

            // Log any partial failures
            var failedStops = snapshot.TrailingStops.Count - restoredStops;
            var failedPositions = snapshot.PositionStates.Count - restoredPositions;
            var failedRetryItems = snapshot.RetryItems.Count - restoredRetryItems;

            if (failedStops > 0 || failedPositions > 0 || failedRetryItems > 0)
            {
                _logger.LogWarning("⚠️ Partial restoration failure: {FailedStops} stops, {FailedPositions} positions, {FailedRetryItems} retry items failed to restore",
                    failedStops, failedPositions, failedRetryItems);
                _logger.LogInformation("💡 System will continue with partially restored state - manual verification of positions recommended");
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "❌ Failed to restore state from backup after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            _logger.LogInformation("💡 System will continue with clean state - this is not critical but previous trading state is lost");

            // Don't throw - continue with clean state
            // The calling code will handle this gracefully
        }
    }

    /// <summary>
    /// Performs a health check of the state backup system
    /// </summary>
    public async Task<StateBackupHealthCheck> PerformHealthCheckAsync(CancellationToken cancellationToken = default)
    {
        var healthCheck = new StateBackupHealthCheck();

        try
        {
            // Check if backup directory exists and is writable
            var directory = Path.GetDirectoryName(_stateBackupPath);
            if (!string.IsNullOrEmpty(directory))
            {
                healthCheck.BackupDirectoryExists = Directory.Exists(directory);

                if (!healthCheck.BackupDirectoryExists)
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                        healthCheck.BackupDirectoryExists = true;
                        healthCheck.BackupDirectoryWritable = true;
                    }
                    catch
                    {
                        healthCheck.BackupDirectoryWritable = false;
                    }
                }
                else
                {
                    // Test write access
                    var testFile = Path.Combine(directory, $"test_write_{Guid.NewGuid()}.tmp");
                    try
                    {
                        await File.WriteAllTextAsync(testFile, "test", cancellationToken);
                        File.Delete(testFile);
                        healthCheck.BackupDirectoryWritable = true;
                    }
                    catch
                    {
                        healthCheck.BackupDirectoryWritable = false;
                    }
                }
            }

            // Check if backup file exists and get info
            if (File.Exists(_stateBackupPath))
            {
                var fileInfo = new FileInfo(_stateBackupPath);
                healthCheck.BackupFileExists = true;
                healthCheck.BackupFileSize = fileInfo.Length;
                healthCheck.BackupFileAge = DateTime.UtcNow - fileInfo.LastWriteTimeUtc;

                // Try to read and parse the backup file
                try
                {
                    var json = await File.ReadAllTextAsync(_stateBackupPath, cancellationToken);
                    var snapshot = JsonSerializer.Deserialize<StateSnapshot>(json, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    if (snapshot != null)
                    {
                        healthCheck.BackupFileValid = true;
                        healthCheck.TrailingStopsCount = snapshot.TrailingStops.Count;
                        healthCheck.PositionStatesCount = snapshot.PositionStates.Count;
                        healthCheck.RetryItemsCount = snapshot.RetryItems.Count;
                        healthCheck.SnapshotAge = DateTime.UtcNow - snapshot.Timestamp;
                    }
                }
                catch
                {
                    healthCheck.BackupFileValid = false;
                }
            }

            // Check Redis connectivity
            try
            {
                var stats = await _liveStateStore.GetStatsAsync(cancellationToken);
                healthCheck.RedisConnected = true;
                healthCheck.RedisTrailingStopsCount = stats.TrailingStopCount;
                healthCheck.RedisPositionStatesCount = stats.PositionStateCount;
                healthCheck.RedisRetryQueueLength = stats.RetryQueueLength;
            }
            catch
            {
                healthCheck.RedisConnected = false;
            }

            healthCheck.OverallHealthy = healthCheck.BackupDirectoryExists &&
                                       healthCheck.BackupDirectoryWritable &&
                                       healthCheck.RedisConnected;
        }
        catch (Exception ex)
        {
            healthCheck.HealthCheckError = ex.Message;
            healthCheck.OverallHealthy = false;
        }

        return healthCheck;
    }
}

/// <summary>
/// Health check result for the state backup system
/// </summary>
public class StateBackupHealthCheck
{
    public bool BackupDirectoryExists { get; set; }
    public bool BackupDirectoryWritable { get; set; }
    public bool BackupFileExists { get; set; }
    public bool BackupFileValid { get; set; }
    public long BackupFileSize { get; set; }
    public TimeSpan BackupFileAge { get; set; }
    public TimeSpan SnapshotAge { get; set; }
    public int TrailingStopsCount { get; set; }
    public int PositionStatesCount { get; set; }
    public int RetryItemsCount { get; set; }
    public bool RedisConnected { get; set; }
    public int RedisTrailingStopsCount { get; set; }
    public int RedisPositionStatesCount { get; set; }
    public int RedisRetryQueueLength { get; set; }
    public bool OverallHealthy { get; set; }
    public string? HealthCheckError { get; set; }
}

/// <summary>
/// Represents a snapshot of trading state for persistence
/// </summary>
public record StateSnapshot(
    DateTime Timestamp,
    Dictionary<string, decimal> TrailingStops,
    Dictionary<string, PositionState> PositionStates,
    List<RetryItem> RetryItems
);
