using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced synthetic VIX service with machine learning-based regression weights
/// Provides robust VIX estimation when primary data sources fail
/// Uses weekly-trained regression coefficients for optimal accuracy
/// </summary>
public interface ISyntheticVixService
{
    /// <summary>
    /// Estimates VIX using trained regression weights from ETF proxies
    /// Falls back to static coefficients if training data unavailable
    /// </summary>
    Task<decimal?> EstimateAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the current regression weights used for estimation
    /// </summary>
    Task<SyntheticVixWeights?> GetCurrentWeightsAsync();
    
    /// <summary>
    /// Validates that regression weights are fresh and reliable
    /// </summary>
    Task<bool> AreWeightsFreshAsync();
}

/// <summary>
/// Production-ready synthetic VIX service with ML-trained regression weights
/// Uses MarketDataService for unified ETF price retrieval with Polygon/Alpaca fallback
/// Enforces 15-minute data freshness requirement for all ETF inputs
/// </summary>
public sealed class SyntheticVixService : ISyntheticVixService, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IDatabase _redis;
    private readonly ILogger<SyntheticVixService> _logger;
    private readonly SemaphoreSlim _estimationLock = new(1, 1);
    private bool _disposed;

    // Static fallback coefficients (used when trained weights unavailable)
    // Updated to reflect current market relationships: VIX ≈ 0.35*VXX + 0.89*UVXY
    private static readonly SyntheticVixWeights StaticFallbackWeights = new()
    {
        VxxCoefficient = 0.35m,      // Corrected: VIX ≈ 0.35 * VXX
        UvxyCoefficient = 0.89m,     // Corrected: VIX ≈ 0.89 * UVXY
        SvxyCoefficient = 0.0m,      // Not used in primary calculation
        SpyCoefficient = 0.0m,       // Not used in primary calculation
        Intercept = 0.0m,            // Simplified to zero intercept
        TrainedAt = DateTime.MinValue,
        RSquared = 0.75m,            // Updated estimate
        SampleSize = 0
    };

    public SyntheticVixService(
        IMarketDataService marketDataService,
        ConnectionMultiplexer connectionMultiplexer,
        ILogger<SyntheticVixService> logger)
    {
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _redis = connectionMultiplexer?.GetDatabase() ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<decimal?> EstimateAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SyntheticVixService));

        await _estimationLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("Starting synthetic VIX estimation using trained regression weights");

            // Get current regression weights (trained or fallback)
            var weights = await GetCurrentWeightsAsync();
            if (weights == null)
            {
                _logger.LogWarning("No regression weights available, using static fallback");
                weights = StaticFallbackWeights;
            }

            // Fetch fresh ETF prices with data freshness validation
            var etfPrices = await FetchFreshEtfPricesAsync(cancellationToken);
            if (etfPrices == null)
            {
                _logger.LogError("Failed to fetch fresh ETF prices for synthetic VIX calculation");
                return null;
            }

            // Calculate synthetic VIX using regression formula
            var syntheticVix = CalculateSyntheticVix(weights, etfPrices);

            // Validate result is within reasonable bounds
            if (syntheticVix < 8m || syntheticVix > 80m)
            {
                _logger.LogWarning("Synthetic VIX out of bounds: {VIX:F2}, rejecting estimate", syntheticVix);
                return null;
            }

            // Cache the result with appropriate TTL
            await CacheSyntheticVixAsync(syntheticVix, weights, etfPrices);

            _logger.LogInformation("Synthetic VIX estimated: {VIX:F2} (R²={RSquared:F3}, trained: {TrainedAt:yyyy-MM-dd})",
                syntheticVix, weights.RSquared, weights.TrainedAt);

            return syntheticVix;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating synthetic VIX");
            return null;
        }
        finally
        {
            _estimationLock.Release();
        }
    }

    public async Task<SyntheticVixWeights?> GetCurrentWeightsAsync()
    {
        try
        {
            var weightsJson = await _redis.StringGetAsync("vix:weights");
            if (!weightsJson.HasValue)
            {
                _logger.LogDebug("No trained weights found in Redis, will use static fallback");
                return null;
            }

            var weights = JsonSerializer.Deserialize<SyntheticVixWeights>(weightsJson!);
            
            // Validate weights are not too old (max 14 days)
            if (weights != null && DateTime.UtcNow - weights.TrainedAt > TimeSpan.FromDays(14))
            {
                _logger.LogWarning("Regression weights are stale (age: {Age}), should retrain soon",
                    DateTime.UtcNow - weights.TrainedAt);
            }

            return weights;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving regression weights from Redis");
            return null;
        }
    }

    public async Task<bool> AreWeightsFreshAsync()
    {
        var weights = await GetCurrentWeightsAsync();
        if (weights == null) return false;

        var age = DateTime.UtcNow - weights.TrainedAt;
        return age <= TimeSpan.FromDays(7); // Fresh if trained within last week
    }

    private async Task<EtfPriceSnapshot?> FetchFreshEtfPricesAsync(CancellationToken cancellationToken)
    {
        var symbols = new[] { "VXX", "UVXY", "SVXY", "SPY" };
        var prices = new Dictionary<string, decimal>();
        var timestamps = new Dictionary<string, DateTime>();

        _logger.LogDebug("Fetching fresh ETF prices using MarketDataService with Polygon/Alpaca fallback");

        foreach (var symbol in symbols)
        {
            try
            {
                var price = await GetFreshEtfPriceFromMarketDataAsync(symbol, cancellationToken);
                if (price.HasValue)
                {
                    prices[symbol] = price.Value.Price;
                    timestamps[symbol] = price.Value.Timestamp;
                    _logger.LogDebug("Got fresh price for {Symbol}: {Price:F2} (age: {Age})",
                        symbol, price.Value.Price, DateTime.UtcNow - price.Value.Timestamp);
                }
                else
                {
                    _logger.LogWarning("Failed to get fresh price for {Symbol}", symbol);
                    return null; // Require all ETF prices for reliable estimation
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching price for {Symbol}", symbol);
                return null;
            }
        }

        return new EtfPriceSnapshot
        {
            VxxPrice = prices["VXX"],
            UvxyPrice = prices["UVXY"],
            SvxyPrice = prices["SVXY"],
            SpyPrice = prices["SPY"],
            Timestamp = timestamps.Values.Min(), // Use oldest timestamp for conservative freshness
            DataAge = DateTime.UtcNow - timestamps.Values.Min()
        };
    }

    private async Task<(decimal Price, DateTime Timestamp)?> GetFreshEtfPriceFromMarketDataAsync(
        string symbol, CancellationToken cancellationToken)
    {
        try
        {
            // Use MarketDataService to get recent bars with built-in Polygon/Alpaca fallback
            var endDate = DateTime.UtcNow;
            // During market hours: look back 30 minutes. After hours: look back 8 hours to get most recent trading data
            var lookbackMinutes = IsMarketHours() ? 30 : 480; // 8 hours = 480 minutes
            var startDate = endDate.AddMinutes(-lookbackMinutes);

            _logger.LogDebug("Fetching recent bars for {Symbol} from {StartDate} to {EndDate}",
                symbol, startDate, endDate);

            var bars = await _marketDataService.GetStockMinuteBarsAsync(symbol, startDate, endDate);

            if (bars?.Items?.Any() == true)
            {
                // Get the most recent bar
                var latestBar = bars.Items.OrderByDescending(b => b.TimeUtc).First();
                var price = latestBar.Close;
                var timestamp = latestBar.TimeUtc;

                // Enforce freshness requirement for synthetic VIX based on market hours
                var dataAge = DateTime.UtcNow - timestamp;
                var freshnessThreshold = IsMarketHours()
                    ? TimeSpan.FromMinutes(15)    // During market hours: 15 minutes
                    : TimeSpan.FromHours(8);      // After hours: 8 hours (use most recent from trading day)

                if (dataAge > freshnessThreshold)
                {
                    _logger.LogWarning("ETF {Symbol} data is stale (age: {Age}), rejecting for synthetic VIX (threshold: {Threshold})",
                        symbol, dataAge, freshnessThreshold);
                    return null;
                }

                _logger.LogDebug("Fresh ETF price for {Symbol}: {Price:F2} at {Timestamp} (age: {Age})",
                    symbol, price, timestamp, dataAge);
                return (price, timestamp);
            }
            else
            {
                _logger.LogWarning("No recent bars found for {Symbol}", symbol);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching fresh price for {Symbol} via MarketDataService", symbol);
            return null;
        }
    }

    private static decimal CalculateSyntheticVix(SyntheticVixWeights weights, EtfPriceSnapshot prices)
    {
        // Linear regression formula: VIX = a*VXX + b*UVXY + c*SVXY + d*SPY + e
        return weights.VxxCoefficient * prices.VxxPrice +
               weights.UvxyCoefficient * prices.UvxyPrice +
               weights.SvxyCoefficient * prices.SvxyPrice +
               weights.SpyCoefficient * prices.SpyPrice +
               weights.Intercept;
    }

    /// <summary>
    /// Determines if the current time is during market hours (9:30 AM - 4:00 PM ET, Monday-Friday).
    /// </summary>
    private bool IsMarketHours()
    {
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow,
            TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // Check if it's within market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);
        var currentTime = easternTime.TimeOfDay;

        return currentTime >= marketOpen && currentTime <= marketClose;
    }

    private async Task CacheSyntheticVixAsync(decimal vixValue, SyntheticVixWeights weights, EtfPriceSnapshot prices)
    {
        try
        {
            // Cache the synthetic VIX value
            await _redis.StringSetAsync("vix:synthetic", (double)vixValue, TimeSpan.FromMinutes(10));
            await _redis.StringSetAsync("vix:source", "synthetic", TimeSpan.FromMinutes(10));

            // Cache metadata for debugging
            var metadata = new
            {
                Value = vixValue,
                Weights = weights,
                Prices = prices,
                CalculatedAt = DateTime.UtcNow
            };

            await _redis.StringSetAsync("vix:synthetic:metadata", 
                JsonSerializer.Serialize(metadata), TimeSpan.FromMinutes(30));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching synthetic VIX data");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _estimationLock?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Regression weights for synthetic VIX calculation
/// </summary>
public record SyntheticVixWeights
{
    public decimal VxxCoefficient { get; init; }
    public decimal UvxyCoefficient { get; init; }
    public decimal SvxyCoefficient { get; init; }
    public decimal SpyCoefficient { get; init; }
    public decimal Intercept { get; init; }
    public DateTime TrainedAt { get; init; }
    public decimal RSquared { get; init; }
    public int SampleSize { get; init; }
}

/// <summary>
/// Snapshot of ETF prices used for synthetic VIX calculation
/// </summary>
public record EtfPriceSnapshot
{
    public decimal VxxPrice { get; init; }
    public decimal UvxyPrice { get; init; }
    public decimal SvxyPrice { get; init; }
    public decimal SpyPrice { get; init; }
    public DateTime Timestamp { get; init; }
    public TimeSpan DataAge { get; init; }
}
