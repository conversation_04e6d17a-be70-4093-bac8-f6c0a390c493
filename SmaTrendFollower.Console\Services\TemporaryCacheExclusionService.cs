using StackExchange.Redis;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for temporarily excluding symbols from database caching operations.
/// Uses Redis with TTL to automatically expire exclusions after a configurable period.
/// This allows problematic symbols to be retried after a cooling-off period.
/// </summary>
public interface ITemporaryCacheExclusionService
{
    /// <summary>
    /// Check if a symbol is currently excluded from database caching
    /// </summary>
    Task<bool> IsExcludedAsync(string symbol);

    /// <summary>
    /// Add a symbol to the temporary exclusion list
    /// </summary>
    Task AddExclusionAsync(string symbol, string reason, TimeSpan? duration = null);

    /// <summary>
    /// Remove a symbol from the exclusion list (manual override)
    /// </summary>
    Task RemoveExclusionAsync(string symbol);

    /// <summary>
    /// Get exclusion details for a symbol
    /// </summary>
    Task<CacheExclusionInfo?> GetExclusionInfoAsync(string symbol);

    /// <summary>
    /// Get all currently excluded symbols
    /// </summary>
    Task<List<CacheExclusionInfo>> GetAllExclusionsAsync();
}

/// <summary>
/// Information about a cache exclusion
/// </summary>
public class CacheExclusionInfo
{
    public string Symbol { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
    public DateTime ExcludedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public string ExcludedBy { get; set; } = string.Empty;

    public string ToJson() => JsonSerializer.Serialize(this);

    public static CacheExclusionInfo? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json)) return null;
        try
        {
            return JsonSerializer.Deserialize<CacheExclusionInfo>(json);
        }
        catch
        {
            return null;
        }
    }
}

public class TemporaryCacheExclusionService : ITemporaryCacheExclusionService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    private readonly ILogger<TemporaryCacheExclusionService> _logger;
    
    private const string ExclusionKeyPrefix = "cache_exclusion:";
    private static readonly TimeSpan DefaultExclusionDuration = TimeSpan.FromHours(24); // 24 hours default

    public TemporaryCacheExclusionService(
        IConnectionMultiplexer redis,
        ILogger<TemporaryCacheExclusionService> logger)
    {
        _redis = redis;
        _database = redis.GetDatabase();
        _logger = logger;
    }

    public async Task<bool> IsExcludedAsync(string symbol)
    {
        if (string.IsNullOrWhiteSpace(symbol))
            return false;

        try
        {
            var key = GetExclusionKey(symbol);
            var exists = await _database.KeyExistsAsync(key);

            if (exists)
            {
                _logger.LogDebug("Symbol {Symbol} is temporarily excluded from database caching", symbol);
            }

            return exists;
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Redis") || ex.Message.Contains("connection"))
        {
            _logger.LogDebug(ex, "Redis connection issue checking exclusion for {Symbol}, defaulting to not excluded", symbol);
            return false; // Default to not excluded when Redis is unavailable
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking cache exclusion for symbol {Symbol}: {ErrorType}", symbol, ex.GetType().Name);
            return false; // Default to not excluded on error
        }
    }

    public async Task AddExclusionAsync(string symbol, string reason, TimeSpan? duration = null)
    {
        if (string.IsNullOrWhiteSpace(symbol))
            return;

        try
        {
            var exclusionDuration = duration ?? DefaultExclusionDuration;
            var now = DateTime.UtcNow;
            
            var exclusionInfo = new CacheExclusionInfo
            {
                Symbol = symbol.ToUpperInvariant(),
                Reason = reason,
                ExcludedAt = now,
                ExpiresAt = now.Add(exclusionDuration),
                ExcludedBy = "system"
            };

            var key = GetExclusionKey(symbol);
            await _database.StringSetAsync(key, exclusionInfo.ToJson(), exclusionDuration);

            _logger.LogWarning("Added temporary cache exclusion for {Symbol}: {Reason} (expires in {Duration})",
                symbol, reason, exclusionDuration);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("Redis") || ex.Message.Contains("connection"))
        {
            _logger.LogWarning(ex, "Redis connection issue adding exclusion for {Symbol}: {Reason}. Symbol will not be excluded.", symbol, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding cache exclusion for symbol {Symbol}: {ErrorType}", symbol, ex.GetType().Name);
        }
    }

    public async Task RemoveExclusionAsync(string symbol)
    {
        if (string.IsNullOrWhiteSpace(symbol))
            return;

        try
        {
            var key = GetExclusionKey(symbol);
            var removed = await _database.KeyDeleteAsync(key);

            if (removed)
            {
                _logger.LogInformation("Manually removed cache exclusion for {Symbol}", symbol);
            }
            else
            {
                _logger.LogDebug("No cache exclusion found for {Symbol} to remove", symbol);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache exclusion for symbol {Symbol}", symbol);
        }
    }

    public async Task<CacheExclusionInfo?> GetExclusionInfoAsync(string symbol)
    {
        if (string.IsNullOrWhiteSpace(symbol))
            return null;

        try
        {
            var key = GetExclusionKey(symbol);
            var json = await _database.StringGetAsync(key);
            
            if (!json.HasValue)
                return null;

            return CacheExclusionInfo.FromJson(json!);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting exclusion info for symbol {Symbol}", symbol);
            return null;
        }
    }

    public async Task<List<CacheExclusionInfo>> GetAllExclusionsAsync()
    {
        var exclusions = new List<CacheExclusionInfo>();

        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            var pattern = ExclusionKeyPrefix + "*";

            await foreach (var key in server.KeysAsync(pattern: pattern))
            {
                try
                {
                    var json = await _database.StringGetAsync(key);
                    if (json.HasValue)
                    {
                        var exclusion = CacheExclusionInfo.FromJson(json!);
                        if (exclusion != null)
                        {
                            exclusions.Add(exclusion);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error reading exclusion key {Key}", key);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all cache exclusions");
        }

        return exclusions.OrderBy(e => e.Symbol).ToList();
    }

    private static string GetExclusionKey(string symbol)
    {
        return ExclusionKeyPrefix + symbol.ToUpperInvariant();
    }
}
