using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Service that monitors and optimizes thread pool usage to prevent thread exhaustion
/// and reduce excessive thread creation across the application
/// </summary>
public sealed class ThreadPoolOptimizationService : BackgroundService
{
    private readonly ILogger<ThreadPoolOptimizationService> _logger;
    private readonly ThreadPoolOptimizationConfiguration _config;
    private readonly Timer _optimizationTimer;
    private int _lastThreadCount = 0;
    private int _lastWorkerThreads = 0;
    private int _lastCompletionPortThreads = 0;

    public ThreadPoolOptimizationService(
        IOptions<ThreadPoolOptimizationConfiguration> config,
        ILogger<ThreadPoolOptimizationService> logger)
    {
        _config = config.Value;
        _logger = logger;

        // Initialize thread pool settings
        InitializeThreadPool();

        // Create optimization timer
        _optimizationTimer = new Timer(OptimizeThreadPool, null, 
            _config.OptimizationInterval, _config.OptimizationInterval);
    }

    private void InitializeThreadPool()
    {
        try
        {
            // Set minimum thread pool sizes to reduce thread creation overhead
            ThreadPool.SetMinThreads(_config.MinWorkerThreads, _config.MinCompletionPortThreads);
            
            // Set maximum thread pool sizes to prevent runaway thread creation
            ThreadPool.SetMaxThreads(_config.MaxWorkerThreads, _config.MaxCompletionPortThreads);

            _logger.LogInformation("Thread pool initialized - Min: {MinWorker}/{MinIO}, Max: {MaxWorker}/{MaxIO}",
                _config.MinWorkerThreads, _config.MinCompletionPortThreads,
                _config.MaxWorkerThreads, _config.MaxCompletionPortThreads);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize thread pool settings");
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("ThreadPoolOptimizationService started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await MonitorThreadUsageAsync();
                await Task.Delay(_config.MonitoringInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in thread pool monitoring");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("ThreadPoolOptimizationService stopped");
    }

    private async Task MonitorThreadUsageAsync()
    {
        try
        {
            // Get current thread pool status
            ThreadPool.GetAvailableThreads(out int availableWorkerThreads, out int availableCompletionPortThreads);
            ThreadPool.GetMaxThreads(out int maxWorkerThreads, out int maxCompletionPortThreads);
            ThreadPool.GetMinThreads(out int minWorkerThreads, out int minCompletionPortThreads);

            var usedWorkerThreads = maxWorkerThreads - availableWorkerThreads;
            var usedCompletionPortThreads = maxCompletionPortThreads - availableCompletionPortThreads;

            // Get process thread count
            using var process = Process.GetCurrentProcess();
            var totalThreadCount = process.Threads.Count;

            // Log thread usage if it has changed significantly
            if (Math.Abs(totalThreadCount - _lastThreadCount) > _config.ThreadCountChangeThreshold ||
                Math.Abs(usedWorkerThreads - _lastWorkerThreads) > _config.WorkerThreadChangeThreshold)
            {
                _logger.LogInformation("Thread Usage - Total: {Total}, Worker: {UsedWorker}/{MaxWorker}, IO: {UsedIO}/{MaxIO}",
                    totalThreadCount, usedWorkerThreads, maxWorkerThreads, usedCompletionPortThreads, maxCompletionPortThreads);

                _lastThreadCount = totalThreadCount;
                _lastWorkerThreads = usedWorkerThreads;
                _lastCompletionPortThreads = usedCompletionPortThreads;
            }

            // Check for thread pressure
            await CheckThreadPressureAsync(totalThreadCount, usedWorkerThreads, maxWorkerThreads);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error monitoring thread usage");
        }
    }

    private Task CheckThreadPressureAsync(int totalThreads, int usedWorkerThreads, int maxWorkerThreads)
    {
        var workerThreadUtilization = (double)usedWorkerThreads / maxWorkerThreads;

        // Check for high thread count
        if (totalThreads > _config.HighThreadCountThreshold)
        {
            _logger.LogWarning("High thread count detected: {ThreadCount} (threshold: {Threshold})", 
                totalThreads, _config.HighThreadCountThreshold);

            if (_config.EnableGarbageCollectionOnHighThreads)
            {
                // Force garbage collection to clean up any unreferenced objects
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                _logger.LogInformation("Forced garbage collection due to high thread count");
            }
        }

        // Check for worker thread pressure
        if (workerThreadUtilization > _config.WorkerThreadPressureThreshold)
        {
            _logger.LogWarning("Worker thread pressure detected: {Utilization:P1} ({Used}/{Max})",
                workerThreadUtilization, usedWorkerThreads, maxWorkerThreads);

            if (_config.EnableDynamicThreadPoolAdjustment)
            {
                AdjustThreadPoolAsync();
            }
        }

        return Task.CompletedTask;
    }

    private void AdjustThreadPoolAsync()
    {
        try
        {
            ThreadPool.GetMaxThreads(out int currentMaxWorker, out int currentMaxIO);
            
            var newMaxWorker = Math.Min(currentMaxWorker + _config.ThreadPoolAdjustmentStep, _config.AbsoluteMaxWorkerThreads);
            var newMaxIO = Math.Min(currentMaxIO + _config.ThreadPoolAdjustmentStep, _config.AbsoluteMaxCompletionPortThreads);

            if (newMaxWorker > currentMaxWorker || newMaxIO > currentMaxIO)
            {
                ThreadPool.SetMaxThreads(newMaxWorker, newMaxIO);
                _logger.LogInformation("Adjusted thread pool limits - Worker: {OldWorker} -> {NewWorker}, IO: {OldIO} -> {NewIO}",
                    currentMaxWorker, newMaxWorker, currentMaxIO, newMaxIO);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to adjust thread pool settings");
        }
    }

    private void OptimizeThreadPool(object? state)
    {
        try
        {
            // Periodically optimize thread pool based on usage patterns
            ThreadPool.GetAvailableThreads(out int availableWorker, out int availableIO);
            ThreadPool.GetMaxThreads(out int maxWorker, out int maxIO);
            
            var usedWorker = maxWorker - availableWorker;
            var usedIO = maxIO - availableIO;
            
            // If thread usage is consistently low, consider reducing max threads
            if (usedWorker < _config.LowUsageThreshold && maxWorker > _config.MinWorkerThreads)
            {
                var newMaxWorker = Math.Max(maxWorker - _config.ThreadPoolReductionStep, _config.MinWorkerThreads);
                ThreadPool.SetMaxThreads(newMaxWorker, maxIO);
                
                _logger.LogDebug("Reduced worker thread pool size from {Old} to {New} due to low usage", 
                    maxWorker, newMaxWorker);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during thread pool optimization");
        }
    }

    public override void Dispose()
    {
        _optimizationTimer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Configuration for thread pool optimization
/// </summary>
public class ThreadPoolOptimizationConfiguration
{
    public const string SectionName = "ThreadPoolOptimization";

    /// <summary>
    /// Monitoring interval for thread usage
    /// </summary>
    public TimeSpan MonitoringInterval { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Optimization interval for thread pool adjustments
    /// </summary>
    public TimeSpan OptimizationInterval { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Minimum worker threads in the thread pool
    /// </summary>
    public int MinWorkerThreads { get; set; } = 25;

    /// <summary>
    /// Minimum completion port threads in the thread pool
    /// </summary>
    public int MinCompletionPortThreads { get; set; } = 25;

    /// <summary>
    /// Maximum worker threads in the thread pool
    /// </summary>
    public int MaxWorkerThreads { get; set; } = 100;

    /// <summary>
    /// Maximum completion port threads in the thread pool
    /// </summary>
    public int MaxCompletionPortThreads { get; set; } = 100;

    /// <summary>
    /// Absolute maximum worker threads (safety limit)
    /// </summary>
    public int AbsoluteMaxWorkerThreads { get; set; } = 200;

    /// <summary>
    /// Absolute maximum completion port threads (safety limit)
    /// </summary>
    public int AbsoluteMaxCompletionPortThreads { get; set; } = 200;

    /// <summary>
    /// Threshold for high total thread count warning
    /// </summary>
    public int HighThreadCountThreshold { get; set; } = 150;

    /// <summary>
    /// Worker thread pressure threshold (0.0 to 1.0)
    /// </summary>
    public double WorkerThreadPressureThreshold { get; set; } = 0.8;

    /// <summary>
    /// Low usage threshold for thread pool reduction
    /// </summary>
    public int LowUsageThreshold { get; set; } = 10;

    /// <summary>
    /// Thread count change threshold for logging
    /// </summary>
    public int ThreadCountChangeThreshold { get; set; } = 10;

    /// <summary>
    /// Worker thread change threshold for logging
    /// </summary>
    public int WorkerThreadChangeThreshold { get; set; } = 5;

    /// <summary>
    /// Thread pool adjustment step size
    /// </summary>
    public int ThreadPoolAdjustmentStep { get; set; } = 10;

    /// <summary>
    /// Thread pool reduction step size
    /// </summary>
    public int ThreadPoolReductionStep { get; set; } = 5;

    /// <summary>
    /// Enable dynamic thread pool adjustment
    /// </summary>
    public bool EnableDynamicThreadPoolAdjustment { get; set; } = true;

    /// <summary>
    /// Enable garbage collection on high thread count
    /// </summary>
    public bool EnableGarbageCollectionOnHighThreads { get; set; } = true;
}
