using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;
using SmaTrendFollower.Interfaces;
using SmaTrendFollower.Services;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Thread-safe implementation of IndexCacheService using DbContextFactory
/// Fixes the critical threading issues that were causing system failures
/// </summary>
public class ThreadSafeIndexCacheService : IIndexCacheService
{
    private readonly IDbContextFactory<IndexCacheDbContext> _contextFactory;
    private readonly ILogger<ThreadSafeIndexCacheService> _logger;

    public ThreadSafeIndexCacheService(
        IDbContextFactory<IndexCacheDbContext> contextFactory,
        ILogger<ThreadSafeIndexCacheService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public async Task<IEnumerable<IndexBar>> GetCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            _logger.LogDebug("Retrieving cached index bars for {Symbol} from {StartDate} to {EndDate}", 
                symbol, startDate, endDate);

            var cachedBars = await context.CachedIndexBars
                .Where(b => b.Symbol == symbol && 
                           b.TimeUtc >= startDate && 
                           b.TimeUtc <= endDate)
                .OrderBy(b => b.TimeUtc)
                .ToListAsync();

            _logger.LogDebug("Found {Count} cached index bars for {Symbol}", cachedBars.Count, symbol);

            return cachedBars.Select(b => b.ToIndexBar()).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached index bars for {Symbol}", symbol);
            return Enumerable.Empty<IndexBar>();
        }
    }

    public async Task CacheBarsAsync(string symbol, IEnumerable<IndexBar> indexBars)
    {
        try
        {
            var barsList = indexBars.ToList();
            if (!barsList.Any())
            {
                _logger.LogDebug("No index bars to cache for {Symbol}", symbol);
                return;
            }

            _logger.LogDebug("Caching {Count} index bars for {Symbol}", barsList.Count, symbol);

            using var context = await _contextFactory.CreateDbContextAsync();
            await context.AddOrUpdateCachedBarsAsync(symbol, barsList);

            _logger.LogInformation("Successfully cached {Count} index bars for {Symbol}", barsList.Count, symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching index bars for {Symbol}: {ErrorMessage}", symbol, ex.Message);

            // Don't throw - caching failures shouldn't break the trading system
            // The system can continue without caching, just with more API calls
            _logger.LogWarning("Continuing without caching for {Symbol} due to cache error", symbol);
        }
    }

    public async Task<bool> HasCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var hasData = await context.CachedIndexBars
                .AnyAsync(b => b.Symbol == symbol && 
                              b.TimeUtc >= startDate && 
                              b.TimeUtc <= endDate);

            _logger.LogDebug("Index cache check for {Symbol}: {HasData}", symbol, hasData);
            return hasData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cached index bars for {Symbol}", symbol);
            return false;
        }
    }

    public async Task<DateTime?> GetLatestBarDateAsync(string symbol)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            // Add timeout to prevent hanging queries
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

            var latestDate = await context.CachedIndexBars
                .Where(b => b.Symbol == symbol)
                .MaxAsync(b => (DateTime?)b.TimeUtc, cts.Token);

            _logger.LogDebug("Latest index bar date for {Symbol}: {LatestDate}", symbol, latestDate);
            return latestDate;
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Database query timeout getting latest index bar date for {Symbol} - returning null to force API fetch", symbol);
            return null;
        }
        catch (Npgsql.NpgsqlException npgsqlEx) when (npgsqlEx.Message.Contains("timeout") ||
                                                      npgsqlEx.Message.Contains("connection") ||
                                                      npgsqlEx.InnerException is System.Net.Sockets.SocketException)
        {
            _logger.LogWarning(npgsqlEx, "Database connection/timeout error getting latest index bar date for {Symbol}: {ErrorType} - {Message}. Returning null to force API fetch.",
                symbol, npgsqlEx.GetType().Name, npgsqlEx.Message);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Database error getting latest index bar date for {Symbol}: {ErrorType} - {ErrorMessage}. Returning null to force API fetch.",
                symbol, ex.GetType().Name, ex.Message);
            return null;
        }
    }

    public async Task ClearCacheAsync(string symbol)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            var barsToRemove = context.CachedIndexBars
                .Where(b => b.Symbol == symbol);

            context.CachedIndexBars.RemoveRange(barsToRemove);
            await context.SaveChangesAsync();

            _logger.LogInformation("Cleared index cache for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing index cache for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<long> GetCacheSizeAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.CachedIndexBars.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting index cache size");
            return 0;
        }
    }

    public async Task<IEnumerable<string>> GetCachedSymbolsAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            return await context.CachedIndexBars
                .Select(b => b.Symbol)
                .Distinct()
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cached index symbols");
            return Enumerable.Empty<string>();
        }
    }

    public async Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, DateTime requestedStartDate, DateTime requestedEndDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            // Add timeout to prevent hanging queries
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

            var latestDate = await context.CachedIndexBars
                .Where(b => b.Symbol == symbol)
                .MaxAsync(b => (DateTime?)b.TimeUtc, cts.Token);

            if (latestDate == null)
            {
                // No data cached, need to fetch entire range
                return (requestedStartDate, requestedEndDate);
            }

            if (latestDate >= requestedEndDate)
            {
                // Cache is up to date
                return null;
            }

            // FIXED: Improved date logic to handle edge cases properly
            var fetchStartDate = latestDate.Value.AddDays(1);

            // FIXED: Better handling of date range validation
            if (fetchStartDate > requestedEndDate)
            {
                // Check if we already have current data (no fetch needed)
                if (latestDate.Value.Date >= requestedEndDate.Date)
                {
                    _logger.LogDebug("Cache for {Symbol} is already up-to-date. Latest: {Latest:yyyy-MM-dd}, Requested: {Requested:yyyy-MM-dd}",
                        symbol, latestDate.Value, requestedEndDate);
                    return null; // No fetch needed - cache is current
                }

                _logger.LogWarning("Fetch start date \"{FetchStart:yyyy-MM-dd}\" would be after end date \"{EndDate:yyyy-MM-dd}\" for \"{Symbol}\" - using safe date range",
                    fetchStartDate, requestedEndDate, symbol);

                // Use safe date range instead of returning null
                var safeEndDate = DateTime.UtcNow.Date.AddDays(-1);
                var safeStartDate = safeEndDate.AddDays(-300);
                return (safeStartDate, safeEndDate);
            }

            return (fetchStartDate, requestedEndDate);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Database query timeout during date range calculation for index {Symbol} - returning full range to force API fetch", symbol);
            return (requestedStartDate, requestedEndDate);
        }
        catch (Npgsql.NpgsqlException npgsqlEx) when (npgsqlEx.Message.Contains("timeout") ||
                                                      npgsqlEx.Message.Contains("connection") ||
                                                      npgsqlEx.InnerException is System.Net.Sockets.SocketException)
        {
            _logger.LogWarning(npgsqlEx, "Database connection/timeout error during date range calculation for index {Symbol}: {ErrorType} - {Message}. Returning full range to force API fetch.",
                symbol, npgsqlEx.GetType().Name, npgsqlEx.Message);
            return (requestedStartDate, requestedEndDate);
        }
        catch (Exception ex)
        {
            // Log as Warning instead of Error to reduce Discord spam
            _logger.LogWarning(ex, "Date range calculation failed for index {Symbol}: {ErrorType} - {ErrorMessage}. Returning full range to force API fetch.",
                symbol, ex.GetType().Name, ex.Message);
            return (requestedStartDate, requestedEndDate);
        }
    }

    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            // Add timeout to prevent hanging queries
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));

            return await context.CachedIndexBars
                .Where(b => b.Symbol == symbol)
                .MaxAsync(b => (DateTime?)b.TimeUtc, cts.Token);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Database query timeout getting latest cached date for index {Symbol} - returning null to force API fetch", symbol);
            return null;
        }
        catch (Npgsql.NpgsqlException npgsqlEx) when (npgsqlEx.Message.Contains("timeout") ||
                                                      npgsqlEx.Message.Contains("connection") ||
                                                      npgsqlEx.InnerException is System.Net.Sockets.SocketException)
        {
            _logger.LogWarning(npgsqlEx, "Database connection/timeout error getting latest cached date for index {Symbol}: {ErrorType} - {Message}. Returning null to force API fetch.",
                symbol, npgsqlEx.GetType().Name, npgsqlEx.Message);
            return null;
        }
        catch (Exception ex)
        {
            // Log as Warning instead of Error to reduce Discord spam
            _logger.LogWarning(ex, "Failed to get latest cached date for index {Symbol}: {ErrorType} - {ErrorMessage}. Returning null to force API fetch.",
                symbol, ex.GetType().Name, ex.Message);
            return null;
        }
    }

    public async Task<bool> IsCacheFreshAsync(string symbol, DateTime requestedEndDate)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var latestDate = await context.CachedIndexBars
                .Where(b => b.Symbol == symbol)
                .MaxAsync(b => (DateTime?)b.TimeUtc);

            return latestDate >= requestedEndDate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache freshness for {Symbol}", symbol);
            return false;
        }
    }

    public async Task PerformMaintenanceAsync(int retainDays = 365)
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);
            var oldBars = context.CachedIndexBars.Where(b => b.TimeUtc < cutoffDate);

            context.CachedIndexBars.RemoveRange(oldBars);
            await context.SaveChangesAsync();

            _logger.LogInformation("Cache maintenance completed for index bars, removed data older than {CutoffDate}", cutoffDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            throw;
        }
    }

    public async Task InitializeCacheAsync()
    {
        try
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.Database.EnsureCreatedAsync();
            _logger.LogDebug("Index cache database initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing cache");
            throw;
        }
    }
}
