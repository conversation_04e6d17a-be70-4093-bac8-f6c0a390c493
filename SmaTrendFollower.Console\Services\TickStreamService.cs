using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time tick streaming service implementation
/// Provides live trade, quote, and aggregate data with Redis caching
/// </summary>
public sealed class TickStreamService : ITickStreamService, IDisposable
{
    private readonly IPolygonWebSocketClient _polygonClient;
    private readonly PolygonWebSocketManager _polygonWsManager;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ILogger<TickStreamService> _logger;
    
    // In-memory caches for fast access
    private readonly ConcurrentDictionary<string, TradeTick> _latestTrades = new();
    private readonly ConcurrentDictionary<string, QuoteTick> _latestQuotes = new();
    private readonly ConcurrentDictionary<string, ConcurrentQueue<TradeTick>> _recentTrades = new();
    private readonly ConcurrentDictionary<string, ConcurrentQueue<QuoteTick>> _recentQuotes = new();
    private readonly ConcurrentDictionary<string, ConcurrentQueue<AggregateTick>> _recentAggregates = new();
    
    private readonly HashSet<string> _subscribedSymbols = new();
    private readonly SemaphoreSlim _subscriptionLock = new(1, 1);
    
    private TickStreamStatus _status = TickStreamStatus.Disconnected;
    private bool _disposed;
    
    // Redis key patterns
    private const string LatestTradeKeyPattern = "tick:trade:{0}";
    private const string LatestQuoteKeyPattern = "tick:quote:{0}";
    private const string RecentTradesKeyPattern = "tick:trades:{0}";
    private const string RecentQuotesKeyPattern = "tick:quotes:{0}";
    private const string AggregatesKeyPattern = "tick:aggs:{0}";
    
    // Cache settings
    private const int MaxRecentTicks = 1000;
    private const int DefaultRecentCount = 100;
    private static readonly TimeSpan CacheExpiry = TimeSpan.FromHours(1);

    public TickStreamService(
        IPolygonWebSocketClient polygonClient,
        PolygonWebSocketManager polygonWsManager,
        IOptimizedRedisConnectionService redisService,
        ILogger<TickStreamService> logger)
    {
        _polygonClient = polygonClient ?? throw new ArgumentNullException(nameof(polygonClient));
        _polygonWsManager = polygonWsManager ?? throw new ArgumentNullException(nameof(polygonWsManager));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        SetupEventHandlers();
    }

    // === Events ===
    
    public event EventHandler<TradeTickEventArgs>? TradeReceived;
    public event EventHandler<QuoteTickEventArgs>? QuoteReceived;
    public event EventHandler<AggregateTickEventArgs>? AggregateReceived;
    public event EventHandler<TickStreamStatusEventArgs>? StatusChanged;

    // === Properties ===
    
    public TickStreamStatus Status => _status;
    public IReadOnlyList<string> SubscribedSymbols => _subscribedSymbols.ToList();

    // === Connection Management ===
    
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(TickStreamService));

        if (_status == TickStreamStatus.Active || _status == TickStreamStatus.Connecting || _status == TickStreamStatus.Connected)
            return;

        try
        {
            SetStatus(TickStreamStatus.Connecting, "Starting tick stream service...");
            _logger.LogInformation("Starting TickStreamService...");

            // Ensure Redis connection by getting a database instance
            await _redisService.GetDatabaseAsync();

            // Subscribe to universe symbols using the WebSocket manager
            await SubscribeToUniverseSymbolsAsync(cancellationToken);

            SetStatus(TickStreamStatus.Connected, "Connected to Polygon WebSocket");
            _logger.LogInformation("TickStreamService started successfully");
        }
        catch (Exception ex)
        {
            SetStatus(TickStreamStatus.Error, "Failed to start tick stream service", ex);
            _logger.LogError(ex, "Failed to start TickStreamService");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_status == TickStreamStatus.Stopped || _status == TickStreamStatus.Disconnected)
            return;

        try
        {
            SetStatus(TickStreamStatus.Disconnected, "Stopping tick stream service...");
            _logger.LogInformation("Stopping TickStreamService...");

            // Unsubscribe from all symbols
            await UnsubscribeAllAsync(cancellationToken);
            
            // Disconnect from Polygon WebSocket
            await _polygonClient.DisconnectAsync(cancellationToken);
            
            SetStatus(TickStreamStatus.Stopped, "Tick stream service stopped");
            _logger.LogInformation("TickStreamService stopped successfully");
        }
        catch (Exception ex)
        {
            SetStatus(TickStreamStatus.Error, "Error stopping tick stream service", ex);
            _logger.LogError(ex, "Error stopping TickStreamService");
            throw;
        }
    }

    // === Subscription Management ===
    
    public async Task SubscribeAsync(IEnumerable<string> symbols, TickDataTypes dataTypes, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(TickStreamService));
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;

        await _subscriptionLock.WaitAsync(cancellationToken);
        try
        {
            SetStatus(TickStreamStatus.Subscribing, $"Subscribing to {symbolList.Count} symbols...");
            _logger.LogInformation("Subscribing to tick data for {Count} symbols: {Symbols}", 
                symbolList.Count, string.Join(", ", symbolList));

            // Subscribe to different data types based on flags using the WebSocket manager
            if (dataTypes.HasFlag(TickDataTypes.Trades))
            {
                await _polygonWsManager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbolList, cancellationToken);
            }

            if (dataTypes.HasFlag(TickDataTypes.Quotes))
            {
                await _polygonWsManager.BatchSubscribeAsync(PolygonWsChannel.EquityQuotes, symbolList, cancellationToken);
            }

            if (dataTypes.HasFlag(TickDataTypes.Aggregates))
            {
                await _polygonWsManager.BatchSubscribeAsync(PolygonWsChannel.EquityMinute, symbolList, cancellationToken);
            }

            // Add to subscribed symbols
            foreach (var symbol in symbolList)
            {
                _subscribedSymbols.Add(symbol);
                
                // Initialize collections for new symbols
                _recentTrades.TryAdd(symbol, new ConcurrentQueue<TradeTick>());
                _recentQuotes.TryAdd(symbol, new ConcurrentQueue<QuoteTick>());
                _recentAggregates.TryAdd(symbol, new ConcurrentQueue<AggregateTick>());
            }

            SetStatus(TickStreamStatus.Active, $"Subscribed to {symbolList.Count} symbols");
            _logger.LogInformation("Successfully subscribed to tick data for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            SetStatus(TickStreamStatus.Error, "Failed to subscribe to symbols", ex);
            _logger.LogError(ex, "Failed to subscribe to tick data for symbols: {Symbols}", string.Join(", ", symbolList));
            throw;
        }
        finally
        {
            _subscriptionLock.Release();
        }
    }

    public async Task UnsubscribeAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;

        await _subscriptionLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Unsubscribing from tick data for {Count} symbols: {Symbols}", 
                symbolList.Count, string.Join(", ", symbolList));

            // Remove from subscribed symbols
            foreach (var symbol in symbolList)
            {
                _subscribedSymbols.Remove(symbol);
            }

            // Note: Polygon WebSocket client doesn't have individual unsubscribe methods yet
            // This would need to be implemented in the WebSocket client
            
            _logger.LogInformation("Unsubscribed from tick data for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unsubscribe from symbols: {Symbols}", string.Join(", ", symbolList));
            throw;
        }
        finally
        {
            _subscriptionLock.Release();
        }
    }

    public async Task UnsubscribeAllAsync(CancellationToken cancellationToken = default)
    {
        await _subscriptionLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Unsubscribing from all tick data subscriptions");
            
            await _polygonClient.UnsubscribeAllAsync(cancellationToken);
            _subscribedSymbols.Clear();
            
            _logger.LogInformation("Unsubscribed from all tick data subscriptions");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unsubscribe from all symbols");
            throw;
        }
        finally
        {
            _subscriptionLock.Release();
        }
    }

    // === Data Retrieval ===
    
    public async Task<TradeTick?> GetLatestTradeAsync(string symbol)
    {
        // Try in-memory cache first
        if (_latestTrades.TryGetValue(symbol, out var latestTrade))
        {
            return latestTrade;
        }

        // Try Redis cache
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(LatestTradeKeyPattern, symbol);
            var value = await database.StringGetAsync(key);
            
            if (value.HasValue)
            {
                var tradeTick = JsonSerializer.Deserialize<TradeTick>(value!);
                _latestTrades.TryAdd(symbol, tradeTick);
                return tradeTick;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve latest trade for {Symbol} from Redis", symbol);
        }

        return null;
    }

    public async Task<QuoteTick?> GetLatestQuoteAsync(string symbol)
    {
        // Try in-memory cache first
        if (_latestQuotes.TryGetValue(symbol, out var latestQuote))
        {
            return latestQuote;
        }

        // Try Redis cache
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(LatestQuoteKeyPattern, symbol);
            var value = await database.StringGetAsync(key);
            
            if (value.HasValue)
            {
                var quoteTick = JsonSerializer.Deserialize<QuoteTick>(value!);
                _latestQuotes.TryAdd(symbol, quoteTick);
                return quoteTick;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to retrieve latest quote for {Symbol} from Redis", symbol);
        }

        return null;
    }

    public async Task<IReadOnlyList<TradeTick>> GetRecentTradesAsync(string symbol, int count = DefaultRecentCount)
    {
        var result = new List<TradeTick>();

        // Get from in-memory cache
        if (_recentTrades.TryGetValue(symbol, out var trades))
        {
            result.AddRange(trades.TakeLast(count));
        }

        // If not enough data, try Redis
        if (result.Count < count)
        {
            try
            {
                var database = await _redisService.GetDatabaseAsync();
                var key = string.Format(RecentTradesKeyPattern, symbol);
                var values = await database.ListRangeAsync(key, -count, -1);

                foreach (var value in values)
                {
                    if (value.HasValue)
                    {
                        var tradeTick = JsonSerializer.Deserialize<TradeTick>(value!);
                        result.Add(tradeTick);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to retrieve recent trades for {Symbol} from Redis", symbol);
            }
        }

        return result.OrderByDescending(t => t.Timestamp).Take(count).ToList();
    }

    public async Task<IReadOnlyList<QuoteTick>> GetRecentQuotesAsync(string symbol, int count = DefaultRecentCount)
    {
        var result = new List<QuoteTick>();

        // Get from in-memory cache
        if (_recentQuotes.TryGetValue(symbol, out var quotes))
        {
            result.AddRange(quotes.TakeLast(count));
        }

        // If not enough data, try Redis
        if (result.Count < count)
        {
            try
            {
                var database = await _redisService.GetDatabaseAsync();
                var key = string.Format(RecentQuotesKeyPattern, symbol);
                var values = await database.ListRangeAsync(key, -count, -1);

                foreach (var value in values)
                {
                    if (value.HasValue)
                    {
                        var quoteTick = JsonSerializer.Deserialize<QuoteTick>(value!);
                        result.Add(quoteTick);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to retrieve recent quotes for {Symbol} from Redis", symbol);
            }
        }

        return result.OrderByDescending(q => q.Timestamp).Take(count).ToList();
    }

    // === Real-time Analysis ===

    public async Task<bool> IsAboveFiveMinuteHighAsync(string symbol)
    {
        try
        {
            var latestTrade = await GetLatestTradeAsync(symbol);
            if (latestTrade == null)
                return false;

            var fiveMinutesAgo = DateTime.UtcNow.AddMinutes(-5);
            var recentAggregates = _recentAggregates.GetValueOrDefault(symbol, new ConcurrentQueue<AggregateTick>());

            var recentHigh = recentAggregates
                .Where(a => a.Timestamp >= fiveMinutesAgo)
                .Select(a => a.High)
                .DefaultIfEmpty(0)
                .Max();

            return latestTrade.Value.Price > recentHigh;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check 5-minute high for {Symbol}", symbol);
            return false;
        }
    }

    public async Task<decimal?> CalculateVwapAsync(string symbol, int minutes = 5)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow.AddMinutes(-minutes);
            var recentTrades = await GetRecentTradesAsync(symbol, 1000);

            var relevantTrades = recentTrades.Where(t => t.Timestamp >= cutoffTime).ToList();
            if (!relevantTrades.Any())
                return null;

            var totalValue = relevantTrades.Sum(t => t.Price * t.Size);
            var totalVolume = relevantTrades.Sum(t => t.Size);

            return totalVolume > 0 ? totalValue / totalVolume : null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to calculate VWAP for {Symbol}", symbol);
            return null;
        }
    }

    public async Task<decimal?> GetBidAskSpreadAsync(string symbol)
    {
        try
        {
            var latestQuote = await GetLatestQuoteAsync(symbol);
            if (latestQuote == null)
                return null;

            return latestQuote.Value.AskPrice - latestQuote.Value.BidPrice;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get bid-ask spread for {Symbol}", symbol);
            return null;
        }
    }

    // === Private Methods ===

    private void SetupEventHandlers()
    {
        _polygonClient.TradeUpdated += OnTradeUpdated;
        _polygonClient.QuoteUpdated += OnQuoteUpdated;
        _polygonClient.AggregateUpdated += OnAggregateUpdated;
        _polygonClient.ConnectionStatusChanged += OnConnectionStatusChanged;
        _polygonClient.ErrorOccurred += OnErrorOccurred;
    }

    private void SetStatus(TickStreamStatus status, string? message = null, Exception? exception = null)
    {
        _status = status;
        StatusChanged?.Invoke(this, new TickStreamStatusEventArgs
        {
            Status = status,
            Message = message,
            Exception = exception
        });
    }

    private void OnTradeUpdated(object? sender, PolygonTradeUpdateEventArgs e)
    {
        try
        {
            var tradeTick = new TradeTick(
                e.Symbol,
                e.Price,
                e.Size,
                e.Timestamp,
                e.Exchange,
                e.Conditions
            );

            // Update in-memory caches
            _latestTrades.AddOrUpdate(e.Symbol, tradeTick, (_, _) => tradeTick);

            if (_recentTrades.TryGetValue(e.Symbol, out var trades))
            {
                trades.Enqueue(tradeTick);

                // Keep only recent ticks
                while (trades.Count > MaxRecentTicks)
                {
                    trades.TryDequeue(out _);
                }
            }

            // Cache in Redis
            _ = Task.Run(async () =>
            {
                try
                {
                    var database = await _redisService.GetDatabaseAsync();
                    var latestKey = string.Format(LatestTradeKeyPattern, e.Symbol);
                    var recentKey = string.Format(RecentTradesKeyPattern, e.Symbol);

                    var json = JsonSerializer.Serialize(tradeTick);

                    // Update latest trade
                    await database.StringSetAsync(latestKey, json, CacheExpiry);

                    // Add to recent trades list
                    await database.ListLeftPushAsync(recentKey, json);
                    await database.ListTrimAsync(recentKey, 0, MaxRecentTicks - 1);
                    await database.KeyExpireAsync(recentKey, CacheExpiry);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cache trade tick for {Symbol}", e.Symbol);
                }
            });

            // Fire event
            TradeReceived?.Invoke(this, new TradeTickEventArgs { TradeTick = tradeTick });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trade update for {Symbol}", e.Symbol);
        }
    }

    private void OnQuoteUpdated(object? sender, PolygonQuoteUpdateEventArgs e)
    {
        try
        {
            var quoteTick = new QuoteTick(
                e.Symbol,
                e.BidPrice,
                e.AskPrice,
                e.BidSize,
                e.AskSize,
                e.Timestamp,
                e.Exchange
            );

            // Update in-memory caches
            _latestQuotes.AddOrUpdate(e.Symbol, quoteTick, (_, _) => quoteTick);

            if (_recentQuotes.TryGetValue(e.Symbol, out var quotes))
            {
                quotes.Enqueue(quoteTick);

                // Keep only recent ticks
                while (quotes.Count > MaxRecentTicks)
                {
                    quotes.TryDequeue(out _);
                }
            }

            // Cache in Redis
            _ = Task.Run(async () =>
            {
                try
                {
                    var database = await _redisService.GetDatabaseAsync();
                    var latestKey = string.Format(LatestQuoteKeyPattern, e.Symbol);
                    var recentKey = string.Format(RecentQuotesKeyPattern, e.Symbol);

                    var json = JsonSerializer.Serialize(quoteTick);

                    // Update latest quote
                    await database.StringSetAsync(latestKey, json, CacheExpiry);

                    // Add to recent quotes list
                    await database.ListLeftPushAsync(recentKey, json);
                    await database.ListTrimAsync(recentKey, 0, MaxRecentTicks - 1);
                    await database.KeyExpireAsync(recentKey, CacheExpiry);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cache quote tick for {Symbol}", e.Symbol);
                }
            });

            // Fire event
            QuoteReceived?.Invoke(this, new QuoteTickEventArgs { QuoteTick = quoteTick });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing quote update for {Symbol}", e.Symbol);
        }
    }

    private void OnAggregateUpdated(object? sender, PolygonAggregateUpdateEventArgs e)
    {
        try
        {
            var aggregateTick = new AggregateTick(
                e.Symbol,
                e.Open,
                e.High,
                e.Low,
                e.Close,
                e.Volume,
                e.Vwap,
                e.Timestamp,
                e.TradeCount
            );

            // Update in-memory cache
            if (_recentAggregates.TryGetValue(e.Symbol, out var aggregates))
            {
                aggregates.Enqueue(aggregateTick);

                // Keep only recent aggregates (last hour)
                var cutoffTime = DateTime.UtcNow.AddHours(-1);
                while (aggregates.TryPeek(out var oldest) && oldest.Timestamp < cutoffTime)
                {
                    aggregates.TryDequeue(out _);
                }
            }

            // Cache in Redis
            _ = Task.Run(async () =>
            {
                try
                {
                    var database = await _redisService.GetDatabaseAsync();
                    var key = string.Format(AggregatesKeyPattern, e.Symbol);
                    var json = JsonSerializer.Serialize(aggregateTick);

                    await database.ListLeftPushAsync(key, json);
                    await database.ListTrimAsync(key, 0, 60); // Keep last 60 minutes
                    await database.KeyExpireAsync(key, CacheExpiry);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cache aggregate tick for {Symbol}", e.Symbol);
                }
            });

            // Fire event
            AggregateReceived?.Invoke(this, new AggregateTickEventArgs { AggregateTick = aggregateTick });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing aggregate update for {Symbol}", e.Symbol);
        }
    }

    private void OnConnectionStatusChanged(object? sender, PolygonConnectionStatusEventArgs e)
    {
        var tickStatus = e.Status switch
        {
            PolygonConnectionStatus.Disconnected => TickStreamStatus.Disconnected,
            PolygonConnectionStatus.Connecting => TickStreamStatus.Connecting,
            PolygonConnectionStatus.Connected => TickStreamStatus.Connected,
            PolygonConnectionStatus.Authenticated => TickStreamStatus.Active,
            PolygonConnectionStatus.Reconnecting => TickStreamStatus.Reconnecting,
            PolygonConnectionStatus.Error => TickStreamStatus.Error,
            _ => TickStreamStatus.Disconnected
        };

        SetStatus(tickStatus, e.Message, e.Exception);
    }

    private void OnErrorOccurred(object? sender, PolygonErrorEventArgs e)
    {
        SetStatus(TickStreamStatus.Error, e.Message, e.Exception);
        _logger.LogError(e.Exception, "Polygon WebSocket error: {Message}", e.Message);
    }

    // === IDisposable ===

    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            // Unsubscribe from events
            _polygonClient.TradeUpdated -= OnTradeUpdated;
            _polygonClient.QuoteUpdated -= OnQuoteUpdated;
            _polygonClient.AggregateUpdated -= OnAggregateUpdated;
            _polygonClient.ConnectionStatusChanged -= OnConnectionStatusChanged;
            _polygonClient.ErrorOccurred -= OnErrorOccurred;

            // Stop the service
            StopAsync().GetAwaiter().GetResult();

            // Dispose resources
            _subscriptionLock?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing TickStreamService");
        }
        finally
        {
            _disposed = true;
        }
    }

    /// <summary>
    /// Subscribes to universe symbols using the advanced WebSocket manager
    /// </summary>
    private async Task SubscribeToUniverseSymbolsAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Get universe symbols from Redis
            var database = await _redisService.GetDatabaseAsync();
            var universeKey = "universe:today"; // Standard universe key
            var universeData = await database.StringGetAsync(universeKey);

            if (!universeData.HasValue)
            {
                _logger.LogWarning("No universe data found in Redis key {UniverseKey}, skipping WebSocket subscriptions", universeKey);
                return;
            }

            var symbols = universeData.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);
            if (symbols.Length == 0)
            {
                _logger.LogWarning("Empty universe data found, skipping WebSocket subscriptions");
                return;
            }

            _logger.LogInformation("Subscribing to {SymbolCount} universe symbols via WebSocket manager", symbols.Length);

            // Subscribe to equity minute aggregates for all universe symbols
            await _polygonWsManager.BatchSubscribeAsync(PolygonWsChannel.EquityMinute, symbols, cancellationToken);

            // Subscribe to equity trades for real-time execution data
            await _polygonWsManager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols, cancellationToken);

            _logger.LogInformation("Successfully subscribed to universe symbols via WebSocket manager");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to universe symbols via WebSocket manager");
            throw;
        }
    }
}
