using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Time-based rolling statistics calculator for real-time quote volatility detection.
/// Maintains a sliding time window of values and computes mean, standard deviation, and z-scores efficiently.
/// Thread-safe implementation using lock-free operations where possible.
/// Unlike count-based rolling stats, this uses time-based windowing to handle variable tick frequencies.
/// </summary>
public sealed class TimeBasedRollingStats
{
    private readonly TimeSpan _windowDuration;
    private readonly Queue<(double Value, DateTime Timestamp)> _values = new();
    private readonly object _lock = new();
    private double _sum = 0.0;
    private double _sumOfSquares = 0.0;

    public TimeBasedRollingStats(TimeSpan windowDuration)
    {
        _windowDuration = windowDuration;
    }

    /// <summary>
    /// Adds a new value with timestamp to the rolling window
    /// </summary>
    /// <param name="value">Value to add</param>
    /// <param name="timestamp">Timestamp of the value</param>
    public void Add(double value, DateTime timestamp)
    {
        lock (_lock)
        {
            // Remove expired values first
            RemoveExpiredValues(timestamp);

            // Add new value
            _values.Enqueue((value, timestamp));
            _sum += value;
            _sumOfSquares += value * value;
        }
    }

    /// <summary>
    /// Gets the current count of values in the window
    /// </summary>
    public int Count
    {
        get
        {
            lock (_lock)
            {
                RemoveExpiredValues(DateTime.UtcNow);
                return _values.Count;
            }
        }
    }

    /// <summary>
    /// Gets the current mean of values in the window
    /// </summary>
    public double Mean
    {
        get
        {
            lock (_lock)
            {
                RemoveExpiredValues(DateTime.UtcNow);
                return _values.Count > 0 ? _sum / _values.Count : 0.0;
            }
        }
    }

    /// <summary>
    /// Gets the current standard deviation of values in the window
    /// </summary>
    public double StandardDeviation
    {
        get
        {
            lock (_lock)
            {
                RemoveExpiredValues(DateTime.UtcNow);
                
                if (_values.Count < 2)
                    return 0.0;

                var mean = _sum / _values.Count;
                var variance = (_sumOfSquares / _values.Count) - (mean * mean);
                
                // Handle numerical precision issues
                if (variance < 0)
                    variance = 0;

                return Math.Sqrt(variance);
            }
        }
    }

    /// <summary>
    /// Checks if there's sufficient data for reliable statistics (at least 10 values)
    /// </summary>
    public bool HasSufficientData
    {
        get
        {
            lock (_lock)
            {
                RemoveExpiredValues(DateTime.UtcNow);
                return _values.Count >= 10; // Minimum for reliable statistics
            }
        }
    }

    /// <summary>
    /// Calculates the z-score for a given value
    /// </summary>
    /// <param name="value">Value to calculate z-score for</param>
    /// <returns>Z-score or 0 if insufficient data</returns>
    public double CalculateZScore(double value)
    {
        lock (_lock)
        {
            RemoveExpiredValues(DateTime.UtcNow);
            
            if (!HasSufficientData || StandardDeviation == 0)
                return 0.0;

            return (value - Mean) / StandardDeviation;
        }
    }

    /// <summary>
    /// Gets a snapshot of current statistics
    /// </summary>
    public TimeBasedRollingStatsSnapshot GetSnapshot()
    {
        lock (_lock)
        {
            RemoveExpiredValues(DateTime.UtcNow);
            
            return new TimeBasedRollingStatsSnapshot(
                Count: _values.Count,
                Mean: Mean,
                StandardDeviation: StandardDeviation,
                WindowDuration: _windowDuration,
                HasSufficientData: HasSufficientData,
                OldestTimestamp: _values.Count > 0 ? _values.Peek().Timestamp : DateTime.MinValue,
                NewestTimestamp: _values.Count > 0 ? _values.Last().Timestamp : DateTime.MinValue
            );
        }
    }

    /// <summary>
    /// Removes values that are outside the time window
    /// Must be called within lock
    /// </summary>
    private void RemoveExpiredValues(DateTime currentTime)
    {
        var cutoffTime = currentTime - _windowDuration;

        while (_values.Count > 0 && _values.Peek().Timestamp < cutoffTime)
        {
            var (value, _) = _values.Dequeue();
            _sum -= value;
            _sumOfSquares -= value * value;
        }
    }
}

/// <summary>
/// Immutable snapshot of time-based rolling statistics at a point in time
/// </summary>
public readonly record struct TimeBasedRollingStatsSnapshot(
    int Count,
    double Mean,
    double StandardDeviation,
    TimeSpan WindowDuration,
    bool HasSufficientData,
    DateTime OldestTimestamp,
    DateTime NewestTimestamp
);
