using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Comprehensive trading diagnostics service for monitoring and debugging
/// </summary>
public sealed class TradingDiagnosticsService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<TradingDiagnosticsService> _logger;

    public TradingDiagnosticsService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILogger<TradingDiagnosticsService> logger)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _logger = logger;
    }

    /// <summary>
    /// Runs comprehensive trading pipeline diagnostics
    /// </summary>
    public async Task<TradingDiagnosticsReport> RunDiagnosticsAsync(CancellationToken cancellationToken = default)
    {
        var report = new TradingDiagnosticsReport
        {
            Timestamp = DateTime.UtcNow,
            DiagnosticsVersion = "1.0"
        };

        _logger.LogInformation("🔍 Starting comprehensive trading diagnostics...");

        try
        {
            // Step 1: Universe diagnostics
            await DiagnoseUniverseAsync(report, cancellationToken);

            // Step 2: Market data diagnostics
            await DiagnoseMarketDataAsync(report, cancellationToken);

            // Step 3: Signal generation diagnostics
            await DiagnoseSignalGenerationAsync(report, cancellationToken);

            // Step 4: Risk management diagnostics
            await DiagnoseRiskManagementAsync(report, cancellationToken);

            // Step 5: VIX data diagnostics
            await DiagnoseVixDataAsync(report, cancellationToken);

            // Step 6: System health diagnostics
            await DiagnoseSystemHealthAsync(report, cancellationToken);

            report.OverallStatus = DetermineOverallStatus(report);
            
            _logger.LogInformation("✅ Trading diagnostics completed. Overall status: {Status}", report.OverallStatus);
            
            // Log summary
            LogDiagnosticsSummary(report);

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Trading diagnostics failed");
            report.OverallStatus = DiagnosticStatus.Critical;
            report.Errors.Add($"Diagnostics failed: {ex.Message}");
            return report;
        }
    }

    private async Task DiagnoseUniverseAsync(TradingDiagnosticsReport report, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🌍 Diagnosing trading universe...");
            
            var universe = await _universeProvider.GetSymbolsAsync();
            var universeList = universe.ToList();

            report.UniverseSize = universeList.Count;
            report.UniverseStatus = universeList.Count >= 10 ? DiagnosticStatus.Healthy : DiagnosticStatus.Warning;

            if (universeList.Count == 0)
            {
                report.Errors.Add("Trading universe is empty");
                report.UniverseStatus = DiagnosticStatus.Critical;
            }
            else if (universeList.Count < 50)
            {
                report.Warnings.Add($"Small trading universe: {universeList.Count} symbols (recommended: 50+)");
            }

            _logger.LogInformation("🌍 Universe: {Count} symbols, Status: {Status}", 
                universeList.Count, report.UniverseStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Universe diagnostics failed");
            report.UniverseStatus = DiagnosticStatus.Critical;
            report.Errors.Add($"Universe diagnostics failed: {ex.Message}");
        }
    }

    private async Task DiagnoseMarketDataAsync(TradingDiagnosticsReport report, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("📊 Diagnosing market data connectivity...");
            
            // Test SPY data retrieval
            var testSymbol = "SPY";
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-5);
            
            var barsResponse = await _marketDataService.GetStockBarsAsync(testSymbol, startDate, endDate);
            var bars = barsResponse.Items.ToList();

            report.MarketDataStatus = bars.Count > 0 ? DiagnosticStatus.Healthy : DiagnosticStatus.Critical;
            report.TestDataPoints = bars.Count;

            if (bars.Count == 0)
            {
                report.Errors.Add("No market data available for test symbol SPY");
            }
            else
            {
                var latestBar = bars.Last();
                var dataAge = DateTime.UtcNow - latestBar.TimeUtc;
                
                if (dataAge.TotalHours > 24)
                {
                    report.Warnings.Add($"Market data is stale: {dataAge.TotalHours:F1} hours old");
                    report.MarketDataStatus = DiagnosticStatus.Warning;
                }
            }

            _logger.LogInformation("📊 Market Data: {Count} bars for {Symbol}, Status: {Status}", 
                bars.Count, testSymbol, report.MarketDataStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Market data diagnostics failed");
            report.MarketDataStatus = DiagnosticStatus.Critical;
            report.Errors.Add($"Market data diagnostics failed: {ex.Message}");
        }
    }

    private async Task DiagnoseSignalGenerationAsync(TradingDiagnosticsReport report, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🎯 Diagnosing signal generation...");
            
            var signals = await _signalGenerator.RunAsync(10, cancellationToken);
            var signalsList = signals.ToList();

            report.SignalsGenerated = signalsList.Count;
            report.SignalGenerationStatus = signalsList.Count > 0 ? DiagnosticStatus.Healthy : DiagnosticStatus.Warning;

            if (signalsList.Count == 0)
            {
                report.Warnings.Add("No trading signals generated - check market conditions and filters");
            }
            else
            {
                // Analyze signal quality
                var avgMomentum = signalsList.Average(s => s.Momentum);
                var avgReturn = signalsList.Average(s => s.SixMonthReturn);
                
                report.AverageSignalMomentum = (decimal)avgMomentum;
                report.AverageSignalReturn = avgReturn;

                if (avgMomentum < 0.3m)
                {
                    report.Warnings.Add($"Low average signal momentum: {avgMomentum:F2}");
                }
            }

            _logger.LogInformation("🎯 Signals: {Count} generated, Avg Momentum: {Momentum:F2}, Status: {Status}", 
                signalsList.Count, signalsList.Count > 0 ? signalsList.Average(s => s.Momentum) : 0, 
                report.SignalGenerationStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Signal generation diagnostics failed");
            report.SignalGenerationStatus = DiagnosticStatus.Critical;
            report.Errors.Add($"Signal generation diagnostics failed: {ex.Message}");
        }
    }

    private async Task DiagnoseRiskManagementAsync(TradingDiagnosticsReport report, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🛡️ Diagnosing risk management...");
            
            // Test risk calculation with a sample signal
            var testSignal = new TradingSignal("SPY", 500m, 5m, 0.15m, 0.7m);
            var quantity = await _riskManager.CalculateQuantityAsync(testSignal);

            report.RiskManagementStatus = quantity > 0 ? DiagnosticStatus.Healthy : DiagnosticStatus.Warning;
            report.TestPositionSize = quantity;

            if (quantity == 0)
            {
                report.Warnings.Add("Risk manager returned zero position size for test signal");
            }
            else if (quantity * testSignal.Price > 10000) // More than $10k position
            {
                report.Warnings.Add($"Large position size calculated: {quantity} shares (${quantity * testSignal.Price:F0})");
            }

            _logger.LogInformation("🛡️ Risk Management: Test position size: {Quantity} shares, Status: {Status}", 
                quantity, report.RiskManagementStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Risk management diagnostics failed");
            report.RiskManagementStatus = DiagnosticStatus.Critical;
            report.Errors.Add($"Risk management diagnostics failed: {ex.Message}");
        }
    }

    private async Task DiagnoseVixDataAsync(TradingDiagnosticsReport report, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("📈 Diagnosing VIX data availability...");
            
            var vixAnalysis = await _marketDataService.GetVixAnalysisAsync();
            var vixValue = vixAnalysis.CurrentVix;
            
            report.VixDataStatus = vixValue > 0 ? DiagnosticStatus.Healthy : DiagnosticStatus.Critical;
            report.CurrentVix = vixValue;

            if (vixValue <= 0)
            {
                report.Errors.Add("VIX data unavailable or invalid");
            }
            else if (vixValue > 40)
            {
                report.Warnings.Add($"High VIX level: {vixValue:F2} (market stress indicator)");
            }
            else if (vixValue < 10)
            {
                report.Warnings.Add($"Very low VIX level: {vixValue:F2} (potential complacency)");
            }

            _logger.LogInformation("📈 VIX: {Value:F2}, Status: {Status}", vixValue, report.VixDataStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ VIX data diagnostics failed");
            report.VixDataStatus = DiagnosticStatus.Critical;
            report.Errors.Add($"VIX data diagnostics failed: {ex.Message}");
        }
    }

    private Task DiagnoseSystemHealthAsync(TradingDiagnosticsReport report, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("⚡ Diagnosing system health...");
            
            // Check memory usage
            var memoryUsage = GC.GetTotalMemory(false);
            report.MemoryUsageMB = memoryUsage / (1024 * 1024);

            // Check if memory usage is concerning
            if (report.MemoryUsageMB > 1000) // More than 1GB
            {
                report.Warnings.Add($"High memory usage: {report.MemoryUsageMB} MB");
                report.SystemHealthStatus = DiagnosticStatus.Warning;
            }
            else
            {
                report.SystemHealthStatus = DiagnosticStatus.Healthy;
            }

            _logger.LogInformation("⚡ System Health: Memory: {Memory} MB, Status: {Status}",
                report.MemoryUsageMB, report.SystemHealthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ System health diagnostics failed");
            report.SystemHealthStatus = DiagnosticStatus.Critical;
            report.Errors.Add($"System health diagnostics failed: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    private static DiagnosticStatus DetermineOverallStatus(TradingDiagnosticsReport report)
    {
        var statuses = new[]
        {
            report.UniverseStatus,
            report.MarketDataStatus,
            report.SignalGenerationStatus,
            report.RiskManagementStatus,
            report.VixDataStatus,
            report.SystemHealthStatus
        };

        if (statuses.Any(s => s == DiagnosticStatus.Critical))
            return DiagnosticStatus.Critical;
        
        if (statuses.Any(s => s == DiagnosticStatus.Warning))
            return DiagnosticStatus.Warning;
        
        return DiagnosticStatus.Healthy;
    }

    private void LogDiagnosticsSummary(TradingDiagnosticsReport report)
    {
        _logger.LogInformation("📋 TRADING DIAGNOSTICS SUMMARY");
        _logger.LogInformation("═══════════════════════════════");
        _logger.LogInformation("🌍 Universe: {Size} symbols ({Status})", report.UniverseSize, report.UniverseStatus);
        _logger.LogInformation("📊 Market Data: {Points} test points ({Status})", report.TestDataPoints, report.MarketDataStatus);
        _logger.LogInformation("🎯 Signals: {Count} generated ({Status})", report.SignalsGenerated, report.SignalGenerationStatus);
        _logger.LogInformation("🛡️ Risk Mgmt: {Size} test position ({Status})", report.TestPositionSize, report.RiskManagementStatus);
        _logger.LogInformation("📈 VIX: {Value:F2} ({Status})", report.CurrentVix, report.VixDataStatus);
        _logger.LogInformation("⚡ System: {Memory} MB memory ({Status})", report.MemoryUsageMB, report.SystemHealthStatus);
        _logger.LogInformation("═══════════════════════════════");
        _logger.LogInformation("🎯 OVERALL STATUS: {Status}", report.OverallStatus);
        
        if (report.Errors.Count > 0)
        {
            _logger.LogError("❌ ERRORS ({Count}):", report.Errors.Count);
            foreach (var error in report.Errors)
                _logger.LogError("  • {Error}", error);
        }
        
        if (report.Warnings.Count > 0)
        {
            _logger.LogWarning("⚠️ WARNINGS ({Count}):", report.Warnings.Count);
            foreach (var warning in report.Warnings)
                _logger.LogWarning("  • {Warning}", warning);
        }
    }
}

/// <summary>
/// Comprehensive trading diagnostics report
/// </summary>
public sealed class TradingDiagnosticsReport
{
    public DateTime Timestamp { get; set; }
    public string DiagnosticsVersion { get; set; } = string.Empty;
    public DiagnosticStatus OverallStatus { get; set; }
    
    // Universe diagnostics
    public int UniverseSize { get; set; }
    public DiagnosticStatus UniverseStatus { get; set; }
    
    // Market data diagnostics
    public int TestDataPoints { get; set; }
    public DiagnosticStatus MarketDataStatus { get; set; }
    
    // Signal generation diagnostics
    public int SignalsGenerated { get; set; }
    public decimal AverageSignalMomentum { get; set; }
    public decimal AverageSignalReturn { get; set; }
    public DiagnosticStatus SignalGenerationStatus { get; set; }
    
    // Risk management diagnostics
    public decimal TestPositionSize { get; set; }
    public DiagnosticStatus RiskManagementStatus { get; set; }
    
    // VIX data diagnostics
    public decimal CurrentVix { get; set; }
    public DiagnosticStatus VixDataStatus { get; set; }
    
    // System health diagnostics
    public long MemoryUsageMB { get; set; }
    public DiagnosticStatus SystemHealthStatus { get; set; }
    
    // Issues tracking
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Diagnostic status levels
/// </summary>
public enum DiagnosticStatus
{
    Healthy,
    Warning,
    Critical
}
