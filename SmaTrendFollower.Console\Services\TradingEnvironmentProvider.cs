using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Provides trading environment detection based on APCA_API_ENV environment variable
/// </summary>
public sealed class TradingEnvironmentProvider : ITradingEnvironmentProvider
{
    private readonly ILogger<TradingEnvironmentProvider> _logger;
    private readonly bool _isLive;
    private readonly string _environmentName;

    public TradingEnvironmentProvider(ILogger<TradingEnvironmentProvider> logger)
    {
        _logger = logger;
        
        // Primary check: Use environment variable as the source of truth
        var apiEnv = Environment.GetEnvironmentVariable("APCA_API_ENV");
        _isLive = !string.Equals(apiEnv, "paper", StringComparison.OrdinalIgnoreCase);
        _environmentName = _isLive ? "Live" : "Paper";
        
        _logger.LogInformation("Trading environment detected: {Environment} (APCA_API_ENV={ApiEnv})", 
            _environmentName, apiEnv ?? "null");
    }

    /// <inheritdoc />
    public bool IsLive => _isLive;

    /// <inheritdoc />
    public string EnvironmentName => _environmentName;
}
