using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Specialized monitoring service for trading operations, performance, and risk metrics
/// </summary>
public sealed class TradingMonitoringService : BackgroundService, IDisposable
{
    private readonly ILogger<TradingMonitoringService> _logger;
    private readonly IDiscordNotificationService _discordService;
    private readonly ILiveStateStore _liveStateStore;
    private readonly Timer _tradingMetricsTimer;
    
    private readonly ConcurrentDictionary<string, TradingPerformanceMetric> _tradingMetrics = new();
    private readonly ConcurrentQueue<TradingEvent> _recentTrades = new();
    private readonly object _metricsLock = new();
    
    private volatile bool _isDisposed;
    private DateTime _lastDailyReport = DateTime.MinValue;

    public TradingMonitoringService(
        ILogger<TradingMonitoringService> logger,
        IDiscordNotificationService discordService,
        ILiveStateStore liveStateStore)
    {
        _logger = logger;
        _discordService = discordService;
        _liveStateStore = liveStateStore;
        
        // Check trading metrics every 5 minutes
        _tradingMetricsTimer = new Timer(CheckTradingMetrics, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(5));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("📈 TradingMonitoringService started");
        
        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
                
                // Check if we need to send daily report
                var now = DateTime.UtcNow;
                if (now.Date > _lastDailyReport.Date && now.Hour >= 16) // After market close
                {
                    await SendDailyTradingReportAsync();
                    _lastDailyReport = now;
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error in trading monitoring service");
        }
        finally
        {
            _logger.LogInformation("📈 TradingMonitoringService stopped");
        }
    }

    private async void CheckTradingMetrics(object? state)
    {
        if (_isDisposed) return;
        
        try
        {
            await CheckDrawdownAsync();
            await CheckPositionConcentrationAsync();
            await CheckTradingVelocityAsync();
            await CheckStopLossEffectivenessAsync();
            
            // Clean up old trading events (keep last 100)
            while (_recentTrades.Count > 100)
            {
                _recentTrades.TryDequeue(out _);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking trading metrics");
        }
    }

    private async Task CheckDrawdownAsync()
    {
        try
        {
            // Get current portfolio value and peak value from Redis
            var currentValue = await GetPortfolioValueAsync();
            var peakValue = await GetPeakPortfolioValueAsync();
            
            if (currentValue > 0 && peakValue > 0)
            {
                var drawdown = (peakValue - currentValue) / peakValue;
                RecordMetric("Portfolio.DrawdownPercent", (double)(drawdown * 100));

                // Alert on significant drawdown
                if (drawdown > 0.05m) // 5% drawdown
                {
                    var message = $"📉 **Drawdown Alert**\n" +
                                 $"Current: ${currentValue:N2}\n" +
                                 $"Peak: ${peakValue:N2}\n" +
                                 $"Drawdown: {drawdown:P2}";
                    
                    await _discordService.SendMessageAsync(message);
                    _logger.LogWarning("Portfolio drawdown alert: {DrawdownPercent:P2}", drawdown);
                }
                
                // Update peak if current value is higher
                if (currentValue > peakValue)
                {
                    await SetPeakPortfolioValueAsync(currentValue);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking drawdown");
        }
    }

    private async Task CheckPositionConcentrationAsync()
    {
        try
        {
            // Get all current positions from Redis
            var positions = await GetCurrentPositionsAsync();
            var totalValue = positions.Sum(p => p.Value);
            
            if (totalValue > 0)
            {
                var maxPosition = positions.Max(p => p.Value);
                var concentration = maxPosition / totalValue;

                RecordMetric("Portfolio.MaxConcentrationPercent", (double)(concentration * 100));

                // Alert on high concentration
                if (concentration > 0.15m) // 15% in single position
                {
                    var largestPosition = positions.OrderByDescending(p => p.Value).First();
                    var message = $"⚠️ **Position Concentration Alert**\n" +
                                 $"Symbol: {largestPosition.Symbol}\n" +
                                 $"Value: ${largestPosition.Value:N2}\n" +
                                 $"Concentration: {concentration:P2}";
                    
                    await _discordService.SendMessageAsync(message);
                    _logger.LogWarning("High position concentration: {Symbol} at {Concentration:P2}", 
                        largestPosition.Symbol, concentration);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking position concentration");
        }
    }

    private async Task CheckTradingVelocityAsync()
    {
        try
        {
            var recentTrades = _recentTrades.Where(t => t.Timestamp > DateTime.UtcNow.AddHours(-1)).ToList();
            var tradesPerHour = recentTrades.Count;
            
            RecordMetric("Trading.TradesPerHour", tradesPerHour);
            
            // Alert on excessive trading
            if (tradesPerHour > 20)
            {
                var message = $"🚨 **High Trading Velocity Alert**\n" +
                             $"Trades in last hour: {tradesPerHour}\n" +
                             $"This may indicate system issues or overtrading";
                
                await _discordService.SendMessageAsync(message);
                _logger.LogWarning("High trading velocity: {TradesPerHour} trades in last hour", tradesPerHour);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking trading velocity");
        }
    }

    private async Task CheckStopLossEffectivenessAsync()
    {
        try
        {
            var recentStops = _recentTrades
                .Where(t => t.Type == "STOP" && t.Timestamp > DateTime.UtcNow.AddDays(-7))
                .ToList();
            
            if (recentStops.Count >= 5) // Need minimum sample size
            {
                var avgStopLoss = recentStops.Average(t => t.PnL);
                var stopWinRate = recentStops.Count(t => t.PnL > 0) / (double)recentStops.Count;

                RecordMetric("Trading.AvgStopLossPnL", (double)avgStopLoss);
                RecordMetric("Trading.StopWinRatePercent", stopWinRate * 100);
                
                // Alert on poor stop loss performance
                if (avgStopLoss < -100 || stopWinRate < 0.1) // Avg loss > $100 or win rate < 10%
                {
                    var message = $"⚠️ **Stop Loss Performance Alert**\n" +
                                 $"Recent stops: {recentStops.Count}\n" +
                                 $"Avg P&L: ${avgStopLoss:N2}\n" +
                                 $"Win rate: {stopWinRate:P1}\n" +
                                 $"Consider reviewing stop loss strategy";
                    
                    await _discordService.SendMessageAsync(message);
                    _logger.LogWarning("Poor stop loss performance: Avg P&L ${AvgPnL:N2}, Win rate {WinRate:P1}", 
                        avgStopLoss, stopWinRate);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking stop loss effectiveness");
        }
    }

    private async Task SendDailyTradingReportAsync()
    {
        try
        {
            var today = DateTime.UtcNow.Date;
            var todayTrades = _recentTrades.Where(t => t.Timestamp.Date == today).ToList();
            
            var totalPnL = todayTrades.Sum(t => t.PnL);
            var winningTrades = todayTrades.Count(t => t.PnL > 0);
            var losingTrades = todayTrades.Count(t => t.PnL < 0);
            var winRate = todayTrades.Count > 0 ? winningTrades / (double)todayTrades.Count : 0;
            
            var portfolioValue = await GetPortfolioValueAsync();
            var drawdown = GetMetricValue("Portfolio.DrawdownPercent") ?? 0;
            
            var report = $"📊 **Daily Trading Report - {today:yyyy-MM-dd}**\n\n" +
                        $"💰 **Performance:**\n" +
                        $"• Total P&L: ${totalPnL:N2}\n" +
                        $"• Portfolio Value: ${portfolioValue:N2}\n" +
                        $"• Max Drawdown: {drawdown:F1}%\n\n" +
                        $"📈 **Trading Activity:**\n" +
                        $"• Total Trades: {todayTrades.Count}\n" +
                        $"• Winning Trades: {winningTrades}\n" +
                        $"• Losing Trades: {losingTrades}\n" +
                        $"• Win Rate: {winRate:P1}\n\n" +
                        $"🎯 **Key Metrics:**\n" +
                        $"• Avg Trade P&L: ${(todayTrades.Count > 0 ? totalPnL / todayTrades.Count : 0):N2}\n" +
                        $"• Max Concentration: {GetMetricValue("Portfolio.MaxConcentrationPercent") ?? 0:F1}%";
            
            await _discordService.SendMessageAsync(report);
            _logger.LogInformation("Daily trading report sent: {TradeCount} trades, ${TotalPnL:N2} P&L", 
                todayTrades.Count, totalPnL);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending daily trading report");
        }
    }

    public void RecordTrade(string symbol, string type, decimal quantity, decimal price, decimal pnl)
    {
        _recentTrades.Enqueue(new TradingEvent
        {
            Symbol = symbol,
            Type = type,
            Quantity = quantity,
            Price = price,
            PnL = pnl,
            Timestamp = DateTime.UtcNow
        });
        
        _logger.LogDebug("Recorded trade: {Type} {Quantity} {Symbol} @ ${Price:N2}, P&L: ${PnL:N2}", 
            type, quantity, symbol, price, pnl);
    }

    private void RecordMetric(string name, double value)
    {
        lock (_metricsLock)
        {
            _tradingMetrics[name] = new TradingPerformanceMetric
            {
                Name = name,
                Value = value,
                Timestamp = DateTime.UtcNow
            };
        }
    }

    private double? GetMetricValue(string name)
    {
        lock (_metricsLock)
        {
            return _tradingMetrics.TryGetValue(name, out var metric) ? metric.Value : null;
        }
    }

    // Placeholder methods - these would integrate with actual portfolio tracking
    private Task<decimal> GetPortfolioValueAsync() => Task.FromResult(50000m); // Placeholder
    private Task<decimal> GetPeakPortfolioValueAsync() => Task.FromResult(52000m); // Placeholder
    private Task SetPeakPortfolioValueAsync(decimal value) => Task.CompletedTask;
    private Task<List<PositionInfo>> GetCurrentPositionsAsync() => Task.FromResult(new List<PositionInfo>()); // Placeholder

    public override void Dispose()
    {
        if (_isDisposed) return;
        
        _isDisposed = true;
        _tradingMetricsTimer?.Dispose();
        
        base.Dispose();
    }
}

public class TradingPerformanceMetric
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public DateTime Timestamp { get; set; }
}

public class TradingEvent
{
    public string Symbol { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal Price { get; set; }
    public decimal PnL { get; set; }
    public DateTime Timestamp { get; set; }
}

public class PositionInfo
{
    public string Symbol { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal Quantity { get; set; }
}
