using Microsoft.Extensions.Logging;
using SmaTrendFollower.Monitoring;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Trading pipeline performance monitoring service
/// Tracks end-to-end latency for signal generation, risk management, and trade execution
/// </summary>
public interface ITradingPipelinePerformanceMonitor : IDisposable
{
    /// <summary>
    /// Starts tracking a trading pipeline execution
    /// </summary>
    string StartPipelineExecution(string symbol, string pipelineType);
    
    /// <summary>
    /// Records completion of a pipeline stage
    /// </summary>
    void RecordStageCompletion(string executionId, string stage, bool success, string? details = null);
    
    /// <summary>
    /// Completes a pipeline execution
    /// </summary>
    void CompletePipelineExecution(string executionId, bool success, string? result = null);
    
    /// <summary>
    /// Records a bottleneck in the pipeline
    /// </summary>
    void RecordBottleneck(string stage, string bottleneckType, double latencyMs, string details);
    
    /// <summary>
    /// Gets current pipeline performance metrics
    /// </summary>
    Task<TradingPipelineMetrics> GetPipelineMetricsAsync();
    
    /// <summary>
    /// Gets bottleneck analysis
    /// </summary>
    Task<BottleneckAnalysisResult> AnalyzeBottlenecksAsync();
}

/// <summary>
/// Trading pipeline performance monitoring implementation
/// </summary>
public sealed class TradingPipelinePerformanceMonitor : ITradingPipelinePerformanceMonitor
{
    private readonly ILogger<TradingPipelinePerformanceMonitor> _logger;
    private readonly ConcurrentDictionary<string, PipelineExecution> _activeExecutions;
    private readonly ConcurrentDictionary<string, PipelineStageMetrics> _stageMetrics;
    private readonly ConcurrentQueue<BottleneckRecord> _bottleneckHistory;
    private readonly Timer _metricsTimer;
    private bool _disposed;

    public TradingPipelinePerformanceMonitor(ILogger<TradingPipelinePerformanceMonitor> logger)
    {
        _logger = logger;
        _activeExecutions = new ConcurrentDictionary<string, PipelineExecution>();
        _stageMetrics = new ConcurrentDictionary<string, PipelineStageMetrics>();
        _bottleneckHistory = new ConcurrentQueue<BottleneckRecord>();
        
        // Start metrics collection timer
        _metricsTimer = new Timer(CollectAndReportMetrics, null, TimeSpan.FromSeconds(15), TimeSpan.FromSeconds(15));
    }

    public string StartPipelineExecution(string symbol, string pipelineType)
    {
        var executionId = Guid.NewGuid().ToString("N")[..8];
        var execution = new PipelineExecution
        {
            ExecutionId = executionId,
            Symbol = symbol,
            PipelineType = pipelineType,
            StartTime = DateTime.UtcNow,
            Stages = new List<PipelineStageExecution>()
        };
        
        _activeExecutions[executionId] = execution;
        
        _logger.LogDebug("Started pipeline execution {ExecutionId} for {Symbol} ({PipelineType})",
            executionId, symbol, pipelineType);
        
        return executionId;
    }

    public void RecordStageCompletion(string executionId, string stage, bool success, string? details = null)
    {
        if (!_activeExecutions.TryGetValue(executionId, out var execution))
        {
            _logger.LogWarning("Pipeline execution {ExecutionId} not found for stage {Stage}", executionId, stage);
            return;
        }
        
        var now = DateTime.UtcNow;
        var stageExecution = new PipelineStageExecution
        {
            Stage = stage,
            StartTime = execution.Stages.LastOrDefault()?.EndTime ?? execution.StartTime,
            EndTime = now,
            Success = success,
            Details = details
        };
        
        execution.Stages.Add(stageExecution);
        
        var latencyMs = (stageExecution.EndTime - stageExecution.StartTime).TotalMilliseconds;
        
        // Update stage metrics
        _stageMetrics.AddOrUpdate(stage,
            new PipelineStageMetrics { StageName = stage },
            (_, existing) =>
            {
                existing.TotalExecutions++;
                existing.TotalLatencyMs += latencyMs;
                existing.LastExecutionTime = now;
                
                if (success)
                {
                    existing.SuccessfulExecutions++;
                }
                else
                {
                    existing.FailedExecutions++;
                }
                
                if (latencyMs > existing.MaxLatencyMs)
                {
                    existing.MaxLatencyMs = latencyMs;
                }
                
                if (existing.MinLatencyMs == 0 || latencyMs < existing.MinLatencyMs)
                {
                    existing.MinLatencyMs = latencyMs;
                }
                
                return existing;
            });
        
        // Update Prometheus metrics
        MetricsRegistry.SignalLatencyMs.Observe(latencyMs);
        
        // Check for bottlenecks
        if (latencyMs > GetBottleneckThreshold(stage))
        {
            RecordBottleneck(stage, "high_latency", latencyMs, 
                $"Stage {stage} took {latencyMs:F1}ms (threshold: {GetBottleneckThreshold(stage)}ms)");
        }
        
        _logger.LogDebug("Completed stage {Stage} for execution {ExecutionId} in {LatencyMs:F1}ms (success: {Success})",
            stage, executionId, latencyMs, success);
    }

    public void CompletePipelineExecution(string executionId, bool success, string? result = null)
    {
        if (!_activeExecutions.TryRemove(executionId, out var execution))
        {
            _logger.LogWarning("Pipeline execution {ExecutionId} not found for completion", executionId);
            return;
        }
        
        execution.EndTime = DateTime.UtcNow;
        execution.Success = success;
        execution.Result = result;
        
        var totalLatencyMs = (execution.EndTime.Value - execution.StartTime).TotalMilliseconds;
        
        _logger.LogInformation("Completed pipeline execution {ExecutionId} for {Symbol} in {TotalLatencyMs:F1}ms " +
                             "(stages: {StageCount}, success: {Success})",
            executionId, execution.Symbol, totalLatencyMs, execution.Stages.Count, success);
        
        // Record end-to-end metrics
        if (execution.PipelineType == "signal_generation")
        {
            MetricsRegistry.SignalLatencyMs.Observe(totalLatencyMs);
        }
        
        // Check for end-to-end bottlenecks
        if (totalLatencyMs > GetPipelineBottleneckThreshold(execution.PipelineType))
        {
            RecordBottleneck("end_to_end", "pipeline_slow", totalLatencyMs,
                $"Pipeline {execution.PipelineType} took {totalLatencyMs:F1}ms");
        }
    }

    public void RecordBottleneck(string stage, string bottleneckType, double latencyMs, string details)
    {
        var bottleneck = new BottleneckRecord
        {
            Timestamp = DateTime.UtcNow,
            Stage = stage,
            BottleneckType = bottleneckType,
            LatencyMs = latencyMs,
            Details = details
        };
        
        _bottleneckHistory.Enqueue(bottleneck);
        
        // Keep only recent bottlenecks (last 1000)
        while (_bottleneckHistory.Count > 1000)
        {
            _bottleneckHistory.TryDequeue(out _);
        }
        
        _logger.LogWarning("Bottleneck detected: {Stage} - {BottleneckType} ({LatencyMs:F1}ms) - {Details}",
            stage, bottleneckType, latencyMs, details);
    }

    public async Task<TradingPipelineMetrics> GetPipelineMetricsAsync()
    {
        var metrics = new TradingPipelineMetrics
        {
            Timestamp = DateTime.UtcNow,
            ActiveExecutions = _activeExecutions.Count,
            StageMetrics = _stageMetrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Clone()),
            RecentBottlenecks = _bottleneckHistory.Where(b => b.Timestamp > DateTime.UtcNow.AddMinutes(-30)).ToList()
        };
        
        return await Task.FromResult(metrics);
    }

    public async Task<BottleneckAnalysisResult> AnalyzeBottlenecksAsync()
    {
        var recentBottlenecks = _bottleneckHistory
            .Where(b => b.Timestamp > DateTime.UtcNow.AddHours(-1))
            .ToList();
        
        var analysis = new BottleneckAnalysisResult
        {
            AnalysisTime = DateTime.UtcNow,
            TotalBottlenecks = recentBottlenecks.Count,
            BottlenecksByStage = recentBottlenecks
                .GroupBy(b => b.Stage)
                .ToDictionary(g => g.Key, g => g.Count()),
            BottlenecksByType = recentBottlenecks
                .GroupBy(b => b.BottleneckType)
                .ToDictionary(g => g.Key, g => g.Count()),
            AverageLatencyByStage = _stageMetrics
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value.AverageLatencyMs),
            Recommendations = GenerateRecommendations(recentBottlenecks)
        };
        
        return await Task.FromResult(analysis);
    }

    private double GetBottleneckThreshold(string stage)
    {
        return stage switch
        {
            "signal_generation" => 5000, // 5 seconds
            "risk_management" => 1000,   // 1 second
            "trade_execution" => 2000,   // 2 seconds
            "market_data_fetch" => 3000, // 3 seconds
            _ => 1000 // Default 1 second
        };
    }

    private double GetPipelineBottleneckThreshold(string pipelineType)
    {
        return pipelineType switch
        {
            "signal_generation" => 10000, // 10 seconds
            "trade_execution" => 5000,     // 5 seconds
            "risk_assessment" => 3000,     // 3 seconds
            _ => 5000 // Default 5 seconds
        };
    }

    private List<string> GenerateRecommendations(List<BottleneckRecord> bottlenecks)
    {
        var recommendations = new List<string>();
        
        var stageBottlenecks = bottlenecks.GroupBy(b => b.Stage).ToList();
        
        foreach (var group in stageBottlenecks.OrderByDescending(g => g.Count()))
        {
            var stage = group.Key;
            var count = group.Count();
            var avgLatency = group.Average(b => b.LatencyMs);
            
            if (count > 5)
            {
                recommendations.Add($"Stage '{stage}' has {count} bottlenecks (avg: {avgLatency:F1}ms) - consider optimization");
            }
        }
        
        return recommendations;
    }

    private void CollectAndReportMetrics(object? state)
    {
        try
        {
            var activeCount = _activeExecutions.Count;
            var totalStages = _stageMetrics.Count;
            var recentBottlenecks = _bottleneckHistory.Count(b => b.Timestamp > DateTime.UtcNow.AddMinutes(-5));
            
            _logger.LogDebug("Trading pipeline performance: {ActiveExecutions} active, {TotalStages} stages monitored, " +
                           "{RecentBottlenecks} recent bottlenecks",
                activeCount, totalStages, recentBottlenecks);
            
            // Clean up old executions (older than 1 hour)
            var cutoff = DateTime.UtcNow.AddHours(-1);
            var oldExecutions = _activeExecutions
                .Where(kvp => kvp.Value.StartTime < cutoff)
                .Select(kvp => kvp.Key)
                .ToList();
            
            foreach (var executionId in oldExecutions)
            {
                if (_activeExecutions.TryRemove(executionId, out var execution))
                {
                    _logger.LogWarning("Removed stale pipeline execution {ExecutionId} for {Symbol} (started: {StartTime})",
                        executionId, execution.Symbol, execution.StartTime);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect trading pipeline metrics");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _metricsTimer?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Pipeline execution tracking
/// </summary>
public class PipelineExecution
{
    public string ExecutionId { get; set; } = string.Empty;
    public string Symbol { get; set; } = string.Empty;
    public string PipelineType { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public bool Success { get; set; }
    public string? Result { get; set; }
    public List<PipelineStageExecution> Stages { get; set; } = new();
}

/// <summary>
/// Individual pipeline stage execution
/// </summary>
public class PipelineStageExecution
{
    public string Stage { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool Success { get; set; }
    public string? Details { get; set; }

    public double LatencyMs => (EndTime - StartTime).TotalMilliseconds;
}

/// <summary>
/// Pipeline stage metrics
/// </summary>
public class PipelineStageMetrics
{
    public string StageName { get; set; } = string.Empty;
    public long TotalExecutions { get; set; }
    public long SuccessfulExecutions { get; set; }
    public long FailedExecutions { get; set; }
    public double TotalLatencyMs { get; set; }
    public double MinLatencyMs { get; set; }
    public double MaxLatencyMs { get; set; }
    public DateTime LastExecutionTime { get; set; }

    public double AverageLatencyMs => TotalExecutions > 0 ? TotalLatencyMs / TotalExecutions : 0;
    public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions : 0;

    public PipelineStageMetrics Clone()
    {
        return new PipelineStageMetrics
        {
            StageName = StageName,
            TotalExecutions = TotalExecutions,
            SuccessfulExecutions = SuccessfulExecutions,
            FailedExecutions = FailedExecutions,
            TotalLatencyMs = TotalLatencyMs,
            MinLatencyMs = MinLatencyMs,
            MaxLatencyMs = MaxLatencyMs,
            LastExecutionTime = LastExecutionTime
        };
    }
}

/// <summary>
/// Bottleneck record
/// </summary>
public class BottleneckRecord
{
    public DateTime Timestamp { get; set; }
    public string Stage { get; set; } = string.Empty;
    public string BottleneckType { get; set; } = string.Empty;
    public double LatencyMs { get; set; }
    public string Details { get; set; } = string.Empty;
}

/// <summary>
/// Trading pipeline metrics
/// </summary>
public class TradingPipelineMetrics
{
    public DateTime Timestamp { get; set; }
    public int ActiveExecutions { get; set; }
    public Dictionary<string, PipelineStageMetrics> StageMetrics { get; set; } = new();
    public List<BottleneckRecord> RecentBottlenecks { get; set; } = new();
}

/// <summary>
/// Bottleneck analysis result
/// </summary>
public class BottleneckAnalysisResult
{
    public DateTime AnalysisTime { get; set; }
    public int TotalBottlenecks { get; set; }
    public Dictionary<string, int> BottlenecksByStage { get; set; } = new();
    public Dictionary<string, int> BottlenecksByType { get; set; } = new();
    public Dictionary<string, double> AverageLatencyByStage { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}
