using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Unified cache warming coordinator that eliminates conflicts between multiple cache warming systems.
/// Coordinates Redis warming, PostgreSQL cache warming, and universe pre-warming in a single, efficient process.
/// </summary>
public sealed class UnifiedCacheWarmingCoordinator : IDisposable
{
    private readonly IRedisWarmingService _redisWarming;
    private readonly ICacheWarmingService _cacheWarming;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<UnifiedCacheWarmingCoordinator> _logger;
    private readonly UnifiedCacheWarmingConfig _config;
    private readonly SemaphoreSlim _coordinationSemaphore;
    private readonly Timer _scheduledWarmingTimer;
    private DateTime? _lastWarmingRun;
    private bool _disposed;

    public UnifiedCacheWarmingCoordinator(
        IRedisWarmingService redisWarming,
        ICacheWarmingService cacheWarming,
        IUniverseProvider universeProvider,
        IOptions<UnifiedCacheWarmingConfig> config,
        ILogger<UnifiedCacheWarmingCoordinator> logger)
    {
        _redisWarming = redisWarming ?? throw new ArgumentNullException(nameof(redisWarming));
        _cacheWarming = cacheWarming ?? throw new ArgumentNullException(nameof(cacheWarming));
        _universeProvider = universeProvider ?? throw new ArgumentNullException(nameof(universeProvider));
        _config = config?.Value ?? UnifiedCacheWarmingConfig.Default;
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _coordinationSemaphore = new SemaphoreSlim(1, 1);
        
        // Set up scheduled warming timer
        _scheduledWarmingTimer = new Timer(
            ScheduledWarmingCallback, 
            null, 
            _config.InitialDelay, 
            _config.WarmingInterval);
    }

    /// <summary>
    /// Performs complete coordinated cache warming operation
    /// </summary>
    public async Task<UnifiedWarmingResult> WarmAllCachesAsync(CancellationToken cancellationToken = default)
    {
        if (!await _coordinationSemaphore.WaitAsync(100, cancellationToken))
        {
            _logger.LogDebug("Cache warming already in progress, skipping");
            return UnifiedWarmingResult.Skipped;
        }

        try
        {
            _logger.LogInformation("🔥 Starting unified cache warming coordination");
            var startTime = DateTime.UtcNow;

            // Phase 1: Get universe symbols (single source of truth)
            var universeSymbols = await GetUniverseSymbolsAsync(cancellationToken);
            _logger.LogInformation("📊 Universe contains {Count} symbols for warming", universeSymbols.Count);

            // Phase 2: Coordinate Redis warming (trading state data)
            var redisResult = await WarmRedisAsync(universeSymbols, cancellationToken);

            // Phase 3: Coordinate PostgreSQL warming (historical data)
            var postgresResult = await WarmPostgreSQLAsync(universeSymbols, cancellationToken);

            var duration = DateTime.UtcNow - startTime;
            _lastWarmingRun = startTime;

            var result = new UnifiedWarmingResult(
                Success: true,
                Duration: duration,
                RedisSymbolsWarmed: redisResult.SymbolsWarmed,
                PostgreSQLSymbolsWarmed: postgresResult.SymbolsWarmed,
                PostgreSQLBarsWarmed: postgresResult.BarsWarmed,
                TotalSymbols: universeSymbols.Count,
                Errors: redisResult.Errors.Concat(postgresResult.Errors).ToList()
            );

            _logger.LogInformation("✅ Unified cache warming completed in {Duration:F1}s: Redis={RedisSymbols}, PostgreSQL={PgSymbols} symbols, {Bars} bars",
                duration.TotalSeconds, result.RedisSymbolsWarmed, result.PostgreSQLSymbolsWarmed, result.PostgreSQLBarsWarmed);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Unified cache warming failed");
            return UnifiedWarmingResult.Failed(ex.Message);
        }
        finally
        {
            _coordinationSemaphore.Release();
        }
    }

    /// <summary>
    /// Performs startup cache warming (essential symbols only for fast startup)
    /// </summary>
    public async Task<UnifiedWarmingResult> WarmStartupCacheAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🚀 Starting startup cache warming (essential symbols only)");
            var startTime = DateTime.UtcNow;

            // Only warm essential symbols for fast startup
            var essentialSymbols = _config.EssentialSymbols.ToList();

            // Coordinate Redis warming for essential symbols
            var redisResult = await WarmRedisAsync(essentialSymbols, cancellationToken);

            // Skip PostgreSQL warming during startup for speed
            _logger.LogInformation("⚡ Skipping PostgreSQL warming during startup for faster boot");

            var duration = DateTime.UtcNow - startTime;

            var result = new UnifiedWarmingResult(
                Success: true,
                Duration: duration,
                RedisSymbolsWarmed: redisResult.SymbolsWarmed,
                PostgreSQLSymbolsWarmed: 0,
                PostgreSQLBarsWarmed: 0,
                TotalSymbols: essentialSymbols.Count,
                Errors: redisResult.Errors
            );

            _logger.LogInformation("✅ Startup cache warming completed in {Duration:F1}s: {RedisSymbols} essential symbols",
                duration.TotalSeconds, result.RedisSymbolsWarmed);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Startup cache warming failed");
            return UnifiedWarmingResult.Failed(ex.Message);
        }
    }

    private async Task<List<string>> GetUniverseSymbolsAsync(CancellationToken cancellationToken)
    {
        try
        {
            var universe = await _universeProvider.GetSymbolsAsync();
            var symbols = universe.Take(_config.MaxSymbolsToWarm).ToList();
            
            // Always include essential symbols
            var allSymbols = _config.EssentialSymbols
                .Concat(symbols)
                .Distinct()
                .ToList();

            return allSymbols;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get universe symbols, using essential symbols only");
            return _config.EssentialSymbols.ToList();
        }
    }

    private async Task<(int SymbolsWarmed, List<string> Errors)> WarmRedisAsync(List<string> symbols, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🔥 Coordinating Redis cache warming for {Count} symbols", symbols.Count);
            
            // Use RedisWarmingService but coordinate the symbol list
            await _redisWarming.WarmCacheAsync(cancellationToken);
            
            return (symbols.Count, new List<string>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis warming failed");
            return (0, new List<string> { $"Redis warming: {ex.Message}" });
        }
    }

    private async Task<(int SymbolsWarmed, int BarsWarmed, List<string> Errors)> WarmPostgreSQLAsync(List<string> symbols, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("🔥 Coordinating PostgreSQL cache warming for {Count} symbols", symbols.Count);
            
            // Use CacheWarmingService with coordinated symbol list
            var (symbolsWarmed, barsWarmed) = await _cacheWarming.WarmUniverseSymbolsAsync(symbols, _config.HistoricalDays);
            
            return (symbolsWarmed, barsWarmed, new List<string>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PostgreSQL warming failed");
            return (0, 0, new List<string> { $"PostgreSQL warming: {ex.Message}" });
        }
    }

    private async void ScheduledWarmingCallback(object? state)
    {
        try
        {
            // Only run during off-market hours
            var now = DateTime.UtcNow;
            var etNow = TimeZoneInfo.ConvertTimeFromUtc(now, TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));
            var isMarketHours = etNow.TimeOfDay >= TimeSpan.FromHours(9.5) && etNow.TimeOfDay <= TimeSpan.FromHours(16);

            if (isMarketHours)
            {
                _logger.LogDebug("Skipping scheduled cache warming during market hours");
                return;
            }

            // Check if enough time has passed
            if (_lastWarmingRun.HasValue && (now - _lastWarmingRun.Value) < _config.MinTimeBetweenRuns)
            {
                _logger.LogDebug("Not enough time since last warming run");
                return;
            }

            await WarmAllCachesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Scheduled cache warming failed");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _scheduledWarmingTimer?.Dispose();
            _coordinationSemaphore?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Configuration for unified cache warming
/// </summary>
public class UnifiedCacheWarmingConfig
{
    public string[] EssentialSymbols { get; set; } = Array.Empty<string>();
    public int MaxSymbolsToWarm { get; set; } = 2000;
    public int HistoricalDays { get; set; } = 60;
    public TimeSpan WarmingInterval { get; set; } = TimeSpan.FromHours(6);
    public TimeSpan MinTimeBetweenRuns { get; set; } = TimeSpan.FromHours(4);
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromMinutes(30);

    public static UnifiedCacheWarmingConfig Default => new()
    {
        EssentialSymbols = new[] { "SPY", "QQQ", "IWM", "VIX", "TLT", "GLD", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA" },
        MaxSymbolsToWarm = 2000,
        HistoricalDays = 60,
        WarmingInterval = TimeSpan.FromHours(6),
        MinTimeBetweenRuns = TimeSpan.FromHours(4),
        InitialDelay = TimeSpan.FromMinutes(30)
    };
}

/// <summary>
/// Result of unified cache warming operation
/// </summary>
public record class UnifiedWarmingResult(
    bool Success,
    TimeSpan Duration,
    int RedisSymbolsWarmed,
    int PostgreSQLSymbolsWarmed,
    int PostgreSQLBarsWarmed,
    int TotalSymbols,
    List<string> Errors
)
{
    public static UnifiedWarmingResult Skipped => new(false, TimeSpan.Zero, 0, 0, 0, 0, new List<string> { "Already in progress" });
    public static UnifiedWarmingResult Failed(string error) => new(false, TimeSpan.Zero, 0, 0, 0, 0, new List<string> { error });
}
