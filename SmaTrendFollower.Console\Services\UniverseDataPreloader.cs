using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using SmaTrendFollower.Services;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service to preload historical bar data for the entire trading universe
/// This is what was missing - universe refresh only gets symbol lists, not actual price data
/// </summary>
public interface IUniverseDataPreloader
{
    Task PreloadUniverseDataAsync(CancellationToken cancellationToken = default);
    Task<PreloadResult> GetPreloadStatusAsync();
}

public class UniverseDataPreloader : IUniverseDataPreloader
{
    private readonly IUniverseProvider _universeProvider;
    private readonly IMarketDataService _marketDataService;
    private readonly IStockBarCacheService _stockBarCache;
    private readonly ILogger<UniverseDataPreloader> _logger;
    private readonly SemaphoreSlim _rateLimitSemaphore;

    public UniverseDataPreloader(
        IUniverseProvider universeProvider,
        IMarketDataService marketDataService,
        IStockBarCacheService stockBarCache,
        ILogger<UniverseDataPreloader> logger)
    {
        _universeProvider = universeProvider;
        _marketDataService = marketDataService;
        _stockBarCache = stockBarCache;
        _logger = logger;
        _rateLimitSemaphore = new SemaphoreSlim(10, 10); // Limit concurrent API calls
    }

    public async Task PreloadUniverseDataAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("🚀 Starting universe data preload - downloading historical bars for all symbols");

        try
        {
            // Get the current universe
            var universe = await _universeProvider.GetSymbolsAsync();
            var symbols = universe.ToList();

            _logger.LogInformation("📊 Preloading data for {SymbolCount} symbols", symbols.Count);

            // Calculate date range for historical data (250 trading days for SMA200)
            var endDate = DateTime.UtcNow.Date.AddDays(-1); // Yesterday (avoid today's incomplete data)
            var startDate = endDate.AddDays(-365); // 1 year of data (covers 250 trading days)

            _logger.LogInformation("📅 Date range: {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}", startDate, endDate);

            // Track progress
            var completed = 0;
            var failed = 0;
            var totalBarsLoaded = 0;
            var progressTimer = new System.Timers.Timer(10000); // Progress every 10 seconds
            progressTimer.Elapsed += (s, e) => 
                _logger.LogInformation("📈 Progress: {Completed}/{Total} symbols ({Percentage:F1}%), {TotalBars} bars loaded, {Failed} failed", 
                    completed, symbols.Count, (completed * 100.0) / symbols.Count, totalBarsLoaded, failed);
            progressTimer.Start();

            // Process symbols in parallel with rate limiting
            var tasks = symbols.Select(async symbol =>
            {
                await _rateLimitSemaphore.WaitAsync(cancellationToken);
                try
                {
                    return await PreloadSymbolDataAsync(symbol, startDate, endDate, cancellationToken);
                }
                finally
                {
                    _rateLimitSemaphore.Release();
                }
            });

            var results = await Task.WhenAll(tasks);

            // Calculate final statistics
            completed = results.Count(r => r.Success);
            failed = results.Count(r => !r.Success);
            totalBarsLoaded = results.Sum(r => r.BarsLoaded);

            progressTimer.Stop();
            stopwatch.Stop();

            _logger.LogInformation("✅ Universe data preload completed!");
            _logger.LogInformation("📊 Results: {Completed} successful, {Failed} failed, {TotalBars} total bars loaded", 
                completed, failed, totalBarsLoaded);
            _logger.LogInformation("⏱️ Total time: {ElapsedMinutes:F1} minutes", stopwatch.Elapsed.TotalMinutes);

            if (failed > 0)
            {
                var failureRate = (failed * 100.0) / symbols.Count;
                if (failureRate > 10)
                {
                    _logger.LogWarning("⚠️ High failure rate: {FailureRate:F1}% of symbols failed to load", failureRate);
                }
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "❌ Universe data preload failed after {ElapsedMinutes:F1} minutes", stopwatch.Elapsed.TotalMinutes);
            throw;
        }
    }

    private async Task<SymbolPreloadResult> PreloadSymbolDataAsync(string symbol, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("📥 Loading data for {Symbol}", symbol);

            // Check if we already have recent data cached
            var existingBars = await _stockBarCache.GetCachedBarsAsync(symbol, "Day", startDate, endDate);
            if (existingBars.Count > 200) // We have sufficient data
            {
                _logger.LogDebug("✅ {Symbol} already has {Count} cached bars, skipping", symbol, existingBars.Count);
                return new SymbolPreloadResult(symbol, true, existingBars.Count, "Already cached");
            }

            // Fetch historical data from API
            var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var bars = response.Items.ToList();

            if (!bars.Any())
            {
                _logger.LogDebug("⚠️ No data available for {Symbol}", symbol);
                return new SymbolPreloadResult(symbol, false, 0, "No data available");
            }

            // Cache the data
            await _stockBarCache.CacheBarsAsync(symbol, "Day", bars);

            _logger.LogDebug("✅ Loaded {Count} bars for {Symbol}", bars.Count, symbol);
            return new SymbolPreloadResult(symbol, true, bars.Count, "Loaded from API");
        }
        catch (Exception ex)
        {
            // Reduce Discord noise for common data loading issues
            if (ex.Message.Contains("No data available") || ex.Message.Contains("Invalid date range"))
            {
                _logger.LogDebug("❌ Failed to load data for {Symbol}: {Error}", symbol, ex.Message);
            }
            else
            {
                _logger.LogWarning(ex, "❌ Failed to load data for {Symbol}: {ErrorType}", symbol, ex.GetType().Name);
            }
            return new SymbolPreloadResult(symbol, false, 0, ex.Message);
        }
    }

    public async Task<PreloadResult> GetPreloadStatusAsync()
    {
        try
        {
            var universe = await _universeProvider.GetSymbolsAsync();
            var symbols = universe.ToList();

            var endDate = DateTime.UtcNow.Date.AddDays(-1);
            var startDate = endDate.AddDays(-365);

            var symbolsWithData = 0;
            var totalBars = 0;

            // Check each symbol for cached data
            foreach (var symbol in symbols.Take(100)) // Sample first 100 for performance
            {
                var bars = await _stockBarCache.GetCachedBarsAsync(symbol, "Day", startDate, endDate);
                if (bars.Count > 0)
                {
                    symbolsWithData++;
                    totalBars += bars.Count;
                }
            }

            var completionPercentage = (symbolsWithData * 100.0) / Math.Min(symbols.Count, 100);

            return new PreloadResult
            {
                TotalSymbols = symbols.Count,
                SymbolsWithData = symbolsWithData,
                TotalBars = totalBars,
                CompletionPercentage = completionPercentage,
                IsComplete = completionPercentage > 90
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking preload status");
            return new PreloadResult
            {
                TotalSymbols = 0,
                SymbolsWithData = 0,
                TotalBars = 0,
                CompletionPercentage = 0,
                IsComplete = false
            };
        }
    }
}

public record SymbolPreloadResult(string Symbol, bool Success, int BarsLoaded, string Message);

public class PreloadResult
{
    public int TotalSymbols { get; set; }
    public int SymbolsWithData { get; set; }
    public int TotalBars { get; set; }
    public double CompletionPercentage { get; set; }
    public bool IsComplete { get; set; }
}
