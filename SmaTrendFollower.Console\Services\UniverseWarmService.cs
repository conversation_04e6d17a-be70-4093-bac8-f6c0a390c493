using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NodaTime;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that warms the universe data at startup and schedules daily refreshes after 04:05 ET
/// Ensures universe refresh always runs when pre-market volume is non-zero and Redis keys expire properly
/// </summary>
public sealed class UniverseWarmService : BackgroundService
{
    private readonly IUniverseFetcherService _fetch;
    private readonly ITimeProvider _timeProvider;
    private readonly ILogger<UniverseWarmService> _logger;
    private readonly UniverseCacheOptions _options;
    private readonly DateTimeZone _nyZone = DateTimeZoneProviders.Tzdb["America/New_York"];

    public UniverseWarmService(
        IUniverseFetcherService fetch,
        ITimeProvider timeProvider,
        IOptions<UniverseCacheOptions> options,
        ILogger<UniverseWarmService> logger)
    {
        _fetch = fetch ?? throw new ArgumentNullException(nameof(fetch));
        _timeProvider = timeProvider ?? throw new ArgumentNullException(nameof(timeProvider));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Wait until allowed window (after 04:05 ET)
            await DelayUntilWindowAsync(stoppingToken);

            // First run once we're in-window
            _logger.LogInformation("Starting universe warm service - performing initial refresh");
            await _fetch.RefreshUniverseAsync(stoppingToken);
            _logger.LogInformation("Initial universe refresh completed");

            while (!stoppingToken.IsCancellationRequested)
            {
                // Re-run at configured interval, but stay inside window
                await Task.Delay(_options.RefreshInterval, stoppingToken);
                await DelayUntilWindowAsync(stoppingToken);

                if (!stoppingToken.IsCancellationRequested)
                {
                    _logger.LogInformation("Performing scheduled universe refresh");
                    await _fetch.RefreshUniverseAsync(stoppingToken);
                    _logger.LogInformation("Scheduled universe refresh completed");
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Universe warm service stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in universe warm service");
            throw;
        }
    }

    private async Task DelayUntilWindowAsync(CancellationToken token)
    {
        var nowUtc = SystemClock.Instance.GetCurrentInstant();
        var nyNow = nowUtc.InZone(_nyZone);
        var nyToday = nyNow.Date;

        // 04:05 same day if before, else tomorrow
        var targetLocal = nyNow.TimeOfDay < new LocalTime(4, 5)
            ? nyToday.At(new LocalTime(4, 5))
            : nyToday.PlusDays(1).At(new LocalTime(4, 5));

        var delay = targetLocal.InZoneLeniently(_nyZone).ToInstant() - nowUtc;
        if (delay > Duration.Zero && delay.TotalHours < 10)   // skip long waits during day
        {
            _logger.LogInformation("Universe refresh sleeping {Min:F1} min until 04:05 ET", delay.TotalMinutes);
            await Task.Delay(delay.ToTimeSpan(), token);
        }
    }
}
