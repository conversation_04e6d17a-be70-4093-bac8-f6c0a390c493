using Microsoft.Extensions.Logging;
using SmaTrendFollower.Exceptions;

namespace SmaTrendFollower.Services;

/// <summary>
/// Manages VIX-based volatility analysis and position sizing adjustments
/// </summary>
public sealed class VolatilityManager : IVolatilityManager
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<VolatilityManager> _logger;
    private VolatilityRegime? _cachedRegime;
    private DateTime _lastRegimeUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

    public VolatilityManager(IMarketDataService marketDataService, ILogger<VolatilityManager> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
    }

    public async Task<VolatilityRegime> GetCurrentRegimeAsync()
    {
        // Use cached regime if still valid
        if (_cachedRegime.HasValue && DateTime.UtcNow - _lastRegimeUpdate < _cacheExpiry)
        {
            return _cachedRegime.Value;
        }

        try
        {
            var vixAnalysis = await _marketDataService.GetVixAnalysisAsync();
            var regime = AnalyzeVolatilityRegime(vixAnalysis);

            _cachedRegime = regime;
            _lastRegimeUpdate = DateTime.UtcNow;

            _logger.LogInformation("Updated volatility regime: {RegimeName}, VIX: {CurrentVix}, SMA30: {VixSma30}, " +
                                 "Position multiplier: {Multiplier}",
                regime.RegimeName, regime.CurrentVix, regime.VixSma30, regime.PositionSizeMultiplier);

            return regime;
        }
        catch (VixDataUnavailableException ex)
        {
            _logger.LogError(ex, "VIX data unavailable - cannot determine volatility regime without real data");
            throw; // Re-throw to halt trading cycle
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current volatility regime - cannot proceed without real VIX data");
            throw new VixDataUnavailableException("Volatility regime analysis failed - real VIX data required");
        }
    }

    public async Task<decimal> GetVixPositionAdjustmentAsync()
    {
        var regime = await GetCurrentRegimeAsync();
        return regime.PositionSizeMultiplier;
    }

    public async Task<bool> IsVixSpikeDetectedAsync(decimal threshold = 25.0m)
    {
        try
        {
            return await _marketDataService.IsVixSpikeAsync(threshold);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting VIX spike, assuming no spike");
            return false;
        }
    }

    public async Task<decimal> GetIvAdjustedStopAsync(string symbol, decimal entryPrice, decimal baseAtr)
    {
        try
        {
            // Get options data to determine IV environment
            var optionsData = await _marketDataService.GetOptionsDataAsync(symbol, null);
            var atmOptions = optionsData
                .Where(o => Math.Abs(o.Strike - entryPrice) / entryPrice < 0.05m) // Within 5% of ATM
                .Where(o => o.ImpliedVolatility.HasValue)
                .ToList();

            if (!atmOptions.Any())
            {
                // No options data available, use base ATR
                return entryPrice - (2m * baseAtr);
            }

            // Calculate average IV for ATM options
            var avgIv = atmOptions.Average(o => o.ImpliedVolatility!.Value);
            
            // Adjust stop based on IV environment
            // High IV (>30%) = wider stops, Low IV (<=20%) = tighter stops
            var ivAdjustment = avgIv > 0.30m ? 1.3m :  // High IV: 30% wider stops
                              avgIv <= 0.20m ? 0.8m : // Low IV: 20% or less tighter stops
                              1.0m;                    // Normal IV: standard stops

            var adjustedAtr = baseAtr * ivAdjustment;
            var stopPrice = entryPrice - (2m * adjustedAtr);

            _logger.LogDebug("IV-adjusted stop for {Symbol}: IV={IV:P2}, Adjustment={Adjustment:P1}, " +
                           "Stop={Stop:C} (vs base {BaseStop:C})",
                symbol, avgIv, ivAdjustment - 1, stopPrice, entryPrice - (2m * baseAtr));

            return stopPrice;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating IV-adjusted stop for {Symbol}, using base ATR", symbol);
            return entryPrice - (2m * baseAtr);
        }
    }

    /// <summary>
    /// Analyzes VIX data to determine current volatility regime
    /// </summary>
    private static VolatilityRegime AnalyzeVolatilityRegime(VixAnalysis vixAnalysis)
    {
        var currentVix = vixAnalysis.CurrentVix;
        var vixSma30 = vixAnalysis.VixSma30;
        var isAboveSma = vixAnalysis.IsAboveSma;
        var isSpike = vixAnalysis.IsSpike;

        // Determine regime based on VIX levels and trend
        string regimeName;
        decimal positionMultiplier;
        bool isHighVol;

        if (isSpike || currentVix > 30m)
        {
            // Crisis/Panic regime
            regimeName = "Crisis";
            positionMultiplier = 0.5m; // Reduce positions by 50%
            isHighVol = true;
        }
        else if (currentVix > 25m || (currentVix > 20m && isAboveSma))
        {
            // High volatility regime
            regimeName = "High Volatility";
            positionMultiplier = 0.7m; // Reduce positions by 30%
            isHighVol = true;
        }
        else if (currentVix < 15m && !isAboveSma)
        {
            // Low volatility/complacency regime
            regimeName = "Low Volatility";
            positionMultiplier = 1.2m; // Increase positions by 20%
            isHighVol = false;
        }
        else
        {
            // Normal regime
            regimeName = "Normal";
            positionMultiplier = 1.0m; // Standard position sizing
            isHighVol = false;
        }

        return new VolatilityRegime(
            currentVix, vixSma30, isHighVol, isSpike, positionMultiplier, regimeName);
    }
}
