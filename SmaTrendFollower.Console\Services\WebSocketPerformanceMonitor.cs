using Microsoft.Extensions.Logging;
using SmaTrendFollower.Monitoring;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Net.WebSockets;

namespace SmaTrendFollower.Services;

/// <summary>
/// WebSocket performance monitoring service for real-time trading connections
/// Monitors connection stability, message throughput, latency, and reconnection patterns
/// </summary>
public interface IWebSocketPerformanceMonitor : IDisposable
{
    /// <summary>
    /// Registers a WebSocket connection for monitoring
    /// </summary>
    void RegisterConnection(string service, string channel, WebSocketState state);
    
    /// <summary>
    /// Records a WebSocket message received
    /// </summary>
    void RecordMessageReceived(string service, string messageType, int messageSize, TimeSpan? processingTime = null);
    
    /// <summary>
    /// Records a WebSocket message sent
    /// </summary>
    void RecordMessageSent(string service, string messageType, int messageSize);
    
    /// <summary>
    /// Records a WebSocket reconnection event
    /// </summary>
    void RecordReconnection(string service, string channel, string reason);
    
    /// <summary>
    /// Records a WebSocket error
    /// </summary>
    void RecordError(string service, string errorType, Exception? exception = null);
    
    /// <summary>
    /// Updates subscription count for a service
    /// </summary>
    void UpdateSubscriptionCount(string service, string channel, int count);
    
    /// <summary>
    /// Gets current WebSocket performance metrics
    /// </summary>
    Task<WebSocketPerformanceMetrics> GetPerformanceMetricsAsync();
}

/// <summary>
/// WebSocket performance monitoring service implementation
/// </summary>
public sealed class WebSocketPerformanceMonitor : IWebSocketPerformanceMonitor
{
    private readonly ILogger<WebSocketPerformanceMonitor> _logger;
    private readonly ConcurrentDictionary<string, WebSocketConnectionMetrics> _connectionMetrics;
    private readonly ConcurrentDictionary<string, WebSocketServiceMetrics> _serviceMetrics;
    private readonly Timer _metricsTimer;
    private bool _disposed;

    public WebSocketPerformanceMonitor(ILogger<WebSocketPerformanceMonitor> logger)
    {
        _logger = logger;
        _connectionMetrics = new ConcurrentDictionary<string, WebSocketConnectionMetrics>();
        _serviceMetrics = new ConcurrentDictionary<string, WebSocketServiceMetrics>();
        
        // Start metrics collection timer
        _metricsTimer = new Timer(CollectAndReportMetrics, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
    }

    public void RegisterConnection(string service, string channel, WebSocketState state)
    {
        var key = $"{service}:{channel}";
        var isConnected = state == WebSocketState.Open ? 1.0 : 0.0;
        
        _connectionMetrics.AddOrUpdate(key,
            new WebSocketConnectionMetrics
            {
                Service = service,
                Channel = channel,
                State = state,
                LastStateChange = DateTime.UtcNow
            },
            (_, existing) =>
            {
                existing.State = state;
                existing.LastStateChange = DateTime.UtcNow;
                return existing;
            });
        
        // Update Prometheus metrics
        MetricsRegistry.WebSocketConnectionStatus
            .WithLabels(service, channel)
            .Set(isConnected);
    }

    public void RecordMessageReceived(string service, string messageType, int messageSize, TimeSpan? processingTime = null)
    {
        // Update service metrics
        _serviceMetrics.AddOrUpdate(service,
            new WebSocketServiceMetrics { ServiceName = service },
            (_, existing) =>
            {
                existing.MessagesReceived++;
                existing.BytesReceived += messageSize;
                existing.LastMessageTime = DateTime.UtcNow;
                
                if (processingTime.HasValue)
                {
                    existing.TotalProcessingTimeMs += processingTime.Value.TotalMilliseconds;
                    existing.ProcessedMessages++;
                }
                
                return existing;
            });
        
        // Update Prometheus metrics
        MetricsRegistry.WebSocketMessagesReceived
            .WithLabels(service, messageType)
            .Inc();
        
        if (processingTime.HasValue)
        {
            MetricsRegistry.WebSocketMessageLatencyMs
                .WithLabels(service, messageType)
                .Observe(processingTime.Value.TotalMilliseconds);
        }
    }

    public void RecordMessageSent(string service, string messageType, int messageSize)
    {
        // Update service metrics
        _serviceMetrics.AddOrUpdate(service,
            new WebSocketServiceMetrics { ServiceName = service },
            (_, existing) =>
            {
                existing.MessagesSent++;
                existing.BytesSent += messageSize;
                return existing;
            });
        
        // Update Prometheus metrics
        MetricsRegistry.WebSocketMessagesSent
            .WithLabels(service, messageType)
            .Inc();
    }

    public void RecordReconnection(string service, string channel, string reason)
    {
        var key = $"{service}:{channel}";
        
        _connectionMetrics.AddOrUpdate(key,
            new WebSocketConnectionMetrics
            {
                Service = service,
                Channel = channel,
                ReconnectionCount = 1,
                LastReconnectionReason = reason,
                LastReconnectionTime = DateTime.UtcNow
            },
            (_, existing) =>
            {
                existing.ReconnectionCount++;
                existing.LastReconnectionReason = reason;
                existing.LastReconnectionTime = DateTime.UtcNow;
                return existing;
            });
        
        // Update Prometheus metrics
        MetricsRegistry.WsReconnects
            .WithLabels(channel, reason)
            .Inc();
        
        _logger.LogWarning("WebSocket reconnection: {Service}:{Channel} - {Reason}", service, channel, reason);
    }

    public void RecordError(string service, string errorType, Exception? exception = null)
    {
        _serviceMetrics.AddOrUpdate(service,
            new WebSocketServiceMetrics { ServiceName = service },
            (_, existing) =>
            {
                existing.ErrorCount++;
                existing.LastError = exception?.Message ?? errorType;
                existing.LastErrorTime = DateTime.UtcNow;
                return existing;
            });
        
        // Update Prometheus metrics
        MetricsRegistry.WebSocketErrors
            .WithLabels(service, errorType)
            .Inc();
        
        _logger.LogError(exception, "WebSocket error: {Service} - {ErrorType}", service, errorType);
    }

    public void UpdateSubscriptionCount(string service, string channel, int count)
    {
        MetricsRegistry.WebSocketSubscriptionCount
            .WithLabels(service, channel)
            .Set(count);
    }

    public async Task<WebSocketPerformanceMetrics> GetPerformanceMetricsAsync()
    {
        var metrics = new WebSocketPerformanceMetrics
        {
            Timestamp = DateTime.UtcNow,
            ConnectionMetrics = _connectionMetrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Clone()),
            ServiceMetrics = _serviceMetrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Clone())
        };

        return await Task.FromResult(metrics);
    }

    private void CollectAndReportMetrics(object? state)
    {
        try
        {
            var now = DateTime.UtcNow;
            var totalConnections = _connectionMetrics.Count;
            var activeConnections = _connectionMetrics.Values.Count(c => c.State == WebSocketState.Open);
            var totalMessages = _serviceMetrics.Values.Sum(s => s.MessagesReceived + s.MessagesSent);
            var totalErrors = _serviceMetrics.Values.Sum(s => s.ErrorCount);
            
            // Calculate message rates (messages per second over last minute)
            var recentMessages = _serviceMetrics.Values
                .Where(s => s.LastMessageTime > now.AddMinutes(-1))
                .Sum(s => s.MessagesReceived);
            
            _logger.LogDebug("WebSocket performance: {ActiveConnections}/{TotalConnections} connections, " +
                           "{TotalMessages} messages, {RecentMessages}/min rate, {TotalErrors} errors",
                activeConnections, totalConnections, totalMessages, recentMessages, totalErrors);
            
            // Check for connection health issues
            foreach (var connection in _connectionMetrics.Values)
            {
                if (connection.State != WebSocketState.Open && 
                    connection.LastStateChange < now.AddMinutes(-5))
                {
                    _logger.LogWarning("WebSocket connection {Service}:{Channel} has been disconnected for {Duration}",
                        connection.Service, connection.Channel, now - connection.LastStateChange);
                }
                
                if (connection.ReconnectionCount > 10 && 
                    connection.LastReconnectionTime > now.AddHours(-1))
                {
                    _logger.LogWarning("WebSocket connection {Service}:{Channel} has {ReconnectionCount} reconnections in the last hour",
                        connection.Service, connection.Channel, connection.ReconnectionCount);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect WebSocket performance metrics");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _metricsTimer?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// WebSocket performance metrics data model
/// </summary>
public class WebSocketPerformanceMetrics
{
    public DateTime Timestamp { get; set; }
    public Dictionary<string, WebSocketConnectionMetrics> ConnectionMetrics { get; set; } = new();
    public Dictionary<string, WebSocketServiceMetrics> ServiceMetrics { get; set; } = new();
}

/// <summary>
/// WebSocket connection metrics
/// </summary>
public class WebSocketConnectionMetrics
{
    public string Service { get; set; } = string.Empty;
    public string Channel { get; set; } = string.Empty;
    public WebSocketState State { get; set; }
    public DateTime LastStateChange { get; set; }
    public int ReconnectionCount { get; set; }
    public string? LastReconnectionReason { get; set; }
    public DateTime? LastReconnectionTime { get; set; }
    
    public WebSocketConnectionMetrics Clone()
    {
        return new WebSocketConnectionMetrics
        {
            Service = Service,
            Channel = Channel,
            State = State,
            LastStateChange = LastStateChange,
            ReconnectionCount = ReconnectionCount,
            LastReconnectionReason = LastReconnectionReason,
            LastReconnectionTime = LastReconnectionTime
        };
    }
}

/// <summary>
/// WebSocket service metrics
/// </summary>
public class WebSocketServiceMetrics
{
    public string ServiceName { get; set; } = string.Empty;
    public long MessagesReceived { get; set; }
    public long MessagesSent { get; set; }
    public long BytesReceived { get; set; }
    public long BytesSent { get; set; }
    public long ErrorCount { get; set; }
    public DateTime LastMessageTime { get; set; }
    public DateTime? LastErrorTime { get; set; }
    public string? LastError { get; set; }
    public double TotalProcessingTimeMs { get; set; }
    public long ProcessedMessages { get; set; }
    
    public double AverageProcessingTimeMs => ProcessedMessages > 0 ? TotalProcessingTimeMs / ProcessedMessages : 0;
    public double MessagesPerSecond => (DateTime.UtcNow - LastMessageTime).TotalSeconds > 0 ? 
        (MessagesReceived + MessagesSent) / (DateTime.UtcNow - LastMessageTime).TotalSeconds : 0;
    
    public WebSocketServiceMetrics Clone()
    {
        return new WebSocketServiceMetrics
        {
            ServiceName = ServiceName,
            MessagesReceived = MessagesReceived,
            MessagesSent = MessagesSent,
            BytesReceived = BytesReceived,
            BytesSent = BytesSent,
            ErrorCount = ErrorCount,
            LastMessageTime = LastMessageTime,
            LastErrorTime = LastErrorTime,
            LastError = LastError,
            TotalProcessingTimeMs = TotalProcessingTimeMs,
            ProcessedMessages = ProcessedMessages
        };
    }
}
