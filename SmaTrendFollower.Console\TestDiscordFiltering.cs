using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using SmaTrendFollower.Infrastructure;
using System;

namespace SmaTrendFollower.Console;

/// <summary>
/// Test program to verify Discord message filtering is working correctly
/// </summary>
public static class TestDiscordFiltering
{
    public static async Task RunAsync()
    {
        System.Console.WriteLine("🧪 Testing Discord Message Filtering...\n");

        // Get Discord credentials
        var botToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
        var channelId = Environment.GetEnvironmentVariable("DISCORD_CHANNEL_ID");

        if (string.IsNullOrEmpty(botToken) || string.IsNullOrEmpty(channelId))
        {
            System.Console.WriteLine("❌ Discord not configured - missing DISCORD_BOT_TOKEN or DISCORD_CHANNEL_ID");
            return;
        }

        System.Console.WriteLine($"✅ Bot Token: {botToken.Substring(0, 20)}...");
        System.Console.WriteLine($"✅ Channel ID: {channelId}\n");

        // Configure Serilog with the enhanced DiscordBotSink
        var loggerConfig = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.File("logs/discord-filter-test-.log",
                          rollingInterval: RollingInterval.Day,
                          retainedFileCountLimit: 7);

        // Add the enhanced DiscordBotSink with filtering
        loggerConfig.WriteTo.Sink(new DiscordBotSink(botToken, channelId));

        Log.Logger = loggerConfig.CreateLogger();

        try
        {
            System.Console.WriteLine("🧪 Testing filtered messages (should NOT appear in Discord)...\n");
            
            // These should be filtered out
            Log.Warning("Insufficient bar data for SPY");
            await Task.Delay(1000);

            Log.Warning("No data available for BETZ");
            await Task.Delay(1000);

            Log.Warning("Failed to load data for INVALID_SYMBOL");
            await Task.Delay(1000);

            Log.Warning("Temporary file lock for AAPL Day");
            await Task.Delay(1000);

            Log.Warning("Cache contains 0 symbols, treating as cache miss");
            await Task.Delay(1000);

            System.Console.WriteLine("✅ Filtered messages sent (should be blocked from Discord)\n");

            System.Console.WriteLine("🧪 Testing unfiltered messages (SHOULD appear in Discord)...\n");

            // These should go through
            Log.Warning("🧪 TEST: Critical system warning - this should appear in Discord");
            await Task.Delay(2000);

            Log.Error("🧪 TEST: System error - this should appear in Discord");
            await Task.Delay(2000);

            Log.Warning("🧪 TEST: Trading halt detected - this should appear in Discord");
            await Task.Delay(2000);

            System.Console.WriteLine("✅ Unfiltered messages sent (should appear in Discord)\n");

            System.Console.WriteLine("🎯 Test completed! Check Discord channel to verify filtering is working.");
            System.Console.WriteLine("   - Filtered messages should NOT appear");
            System.Console.WriteLine("   - Test messages should appear with 🧪 prefix");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error during test: {ex.Message}");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }
}
