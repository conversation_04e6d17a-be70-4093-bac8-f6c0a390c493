using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace SmaTrendFollower.Console;

/// <summary>
/// Test program to debug Polygon API response parsing
/// </summary>
public static class TestPolygonParsing
{
    public static async Task RunAsync(IServiceProvider serviceProvider)
    {
        System.Console.WriteLine("🔍 Testing Polygon API Response Parsing...\n");

        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var polygonFactory = serviceProvider.GetRequiredService<IPolygonClientFactory>();
        var marketDataService = serviceProvider.GetRequiredService<IMarketDataService>();

        try
        {
            var httpClient = polygonFactory.CreateClient();

            // Test 1: Raw API response parsing
            System.Console.WriteLine("📊 Test 1: Raw API Response Parsing");
            System.Console.WriteLine("====================================");

            var testUrl = "/v2/aggs/ticker/SPY/range/1/day/2025-01-06/2025-07-24?adjusted=true&sort=asc&limit=50000";
            var urlWithApiKey = polygonFactory.AddApiKeyToUrl(testUrl);
            var response = await httpClient.GetAsync(urlWithApiKey);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                System.Console.WriteLine($"✅ Raw Response Length: {content.Length} characters");

                // Parse JSON manually
                var jsonDoc = JsonDocument.Parse(content);
                
                if (jsonDoc.RootElement.TryGetProperty("results", out var results))
                {
                    System.Console.WriteLine($"✅ Found 'results' property");
                    System.Console.WriteLine($"✅ Results type: {results.ValueKind}");
                    
                    if (results.ValueKind == JsonValueKind.Array)
                    {
                        var arrayLength = results.GetArrayLength();
                        System.Console.WriteLine($"✅ Results array length: {arrayLength}");

                        // Test parsing first few bars
                        int parsedCount = 0;
                        int failedCount = 0;

                        foreach (var bar in results.EnumerateArray())
                        {
                            try
                            {
                                if (bar.TryGetProperty("t", out var timestampElement) &&
                                    bar.TryGetProperty("o", out var openElement) &&
                                    bar.TryGetProperty("h", out var highElement) &&
                                    bar.TryGetProperty("l", out var lowElement) &&
                                    bar.TryGetProperty("c", out var closeElement) &&
                                    bar.TryGetProperty("v", out var volumeElement))
                                {
                                    var timestampMs = timestampElement.GetInt64();
                                    var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampMs).UtcDateTime;

                                    // Handle scientific notation by parsing as double first, then converting to decimal
                                    var open = decimal.Parse(openElement.GetDouble().ToString("F6"));
                                    var close = decimal.Parse(closeElement.GetDouble().ToString("F6"));
                                    var volume = (long)volumeElement.GetDouble(); // Handle scientific notation in volume

                                    parsedCount++;

                                    if (parsedCount <= 3)
                                    {
                                        System.Console.WriteLine($"   Bar {parsedCount}: {timestamp:yyyy-MM-dd} O:{open:F2} C:{close:F2} V:{volume:N0}");
                                    }
                                }
                                else
                                {
                                    failedCount++;
                                    if (failedCount <= 3)
                                    {
                                        System.Console.WriteLine($"   ❌ Failed to parse bar {parsedCount + failedCount}: Missing required properties");
                                        System.Console.WriteLine($"      Bar JSON: {bar.GetRawText()}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                failedCount++;
                                if (failedCount <= 3)
                                {
                                    System.Console.WriteLine($"   ❌ Exception parsing bar {parsedCount + failedCount}: {ex.Message}");
                                    System.Console.WriteLine($"      Bar JSON: {bar.GetRawText()}");
                                }
                            }
                        }

                        System.Console.WriteLine($"✅ Successfully parsed: {parsedCount} bars");
                        System.Console.WriteLine($"❌ Failed to parse: {failedCount} bars");
                    }
                    else
                    {
                        System.Console.WriteLine($"❌ Results is not an array: {results.ValueKind}");
                    }
                }
                else
                {
                    System.Console.WriteLine("❌ No 'results' property found in response");
                    System.Console.WriteLine($"Response preview: {content.Substring(0, Math.Min(500, content.Length))}");
                }
            }
            else
            {
                System.Console.WriteLine($"❌ API call failed: {response.StatusCode}");
            }

            // Test 2: MarketDataService call
            System.Console.WriteLine("\n📊 Test 2: MarketDataService Call");
            System.Console.WriteLine("==================================");

            var startDate = new DateTime(2025, 1, 6);
            var endDate = new DateTime(2025, 7, 24);

            System.Console.WriteLine($"Requesting SPY bars from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

            var bars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
            
            System.Console.WriteLine($"✅ MarketDataService returned: {bars.Items.Count()} bars");
            
            if (bars.Items.Any())
            {
                var firstBar = bars.Items.First();
                var lastBar = bars.Items.Last();
                System.Console.WriteLine($"   First bar: {firstBar.TimeUtc:yyyy-MM-dd} O:{firstBar.Open:F2} C:{firstBar.Close:F2}");
                System.Console.WriteLine($"   Last bar:  {lastBar.TimeUtc:yyyy-MM-dd} O:{lastBar.Open:F2} C:{lastBar.Close:F2}");
            }
            else
            {
                System.Console.WriteLine("   ⚠️  No bars returned from MarketDataService");
            }

            System.Console.WriteLine("\n🎯 Polygon Parsing Test Complete!");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Fatal error during Polygon parsing test: {ex.Message}");
            logger.LogError(ex, "Fatal error during Polygon parsing test");
        }
    }
}
