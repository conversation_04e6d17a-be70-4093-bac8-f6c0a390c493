using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace SmaTrendFollower.Console;

/// <summary>
/// Test program to directly test SPY data retrieval and diagnose regime analysis issues
/// </summary>
public static class TestSpyDataDirect
{
    public static async Task RunAsync(IServiceProvider serviceProvider)
    {
        System.Console.WriteLine("🔍 Testing SPY Data Retrieval for Regime Analysis...\n");

        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var marketDataService = serviceProvider.GetRequiredService<IMarketDataService>();
        var marketRegimeService = serviceProvider.GetRequiredService<IMarketRegimeService>();
        var polygonFactory = serviceProvider.GetRequiredService<IPolygonClientFactory>();

        try
        {
            // Test 1: Direct MarketDataService SPY call (same as MarketRegimeService)
            System.Console.WriteLine("📊 Test 1: MarketDataService SPY Call (100 days)");
            System.Console.WriteLine("=================================================");

            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-100); // Same as MarketRegimeService.AnalysisPeriodDays

            System.Console.WriteLine($"   📅 Date Range: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
            System.Console.WriteLine($"   📅 Days Span: {(endDate - startDate).Days} days");

            try
            {
                var spyBars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
                var barsList = spyBars.Items.ToList();

                System.Console.WriteLine($"   ✅ Retrieved {barsList.Count} SPY bars");

                if (barsList.Count < 50)
                {
                    System.Console.WriteLine($"   ❌ INSUFFICIENT DATA: Only {barsList.Count} bars (need 50+)");
                    System.Console.WriteLine("   🔧 This explains the 'Insufficient SPY data for regime analysis' warning");
                }
                else
                {
                    System.Console.WriteLine($"   ✅ Sufficient data for regime analysis");

                    if (barsList.Any())
                    {
                        var firstBar = barsList.First();
                        var lastBar = barsList.Last();
                        System.Console.WriteLine($"   📅 First Bar: {firstBar.TimeUtc:yyyy-MM-dd} (Price: ${firstBar.Close:F2})");
                        var lastBarDate = lastBar.TimeUtc;
                        var dataAge = DateTime.UtcNow - lastBarDate;
                        System.Console.WriteLine($"   📅 Last Bar:  {lastBarDate:yyyy-MM-dd} (Price: ${lastBar.Close:F2}) - Age: {dataAge.Days} days");

                        if (dataAge.Days > 5)
                        {
                            System.Console.WriteLine($"   ⚠️  Data may be stale - last bar is {dataAge.Days} days old");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"   ❌ ERROR: {ex.Message}");
                logger.LogError(ex, "Error retrieving SPY data via MarketDataService");
            }

            // Test 2: Direct Polygon API call for SPY
            System.Console.WriteLine("\n📊 Test 2: Direct Polygon API SPY Call");
            System.Console.WriteLine("======================================");

            try
            {
                var httpClient = polygonFactory.CreateClient();
                var apiKey = Environment.GetEnvironmentVariable("POLYGON_API_KEY") ?? 
                            Environment.GetEnvironmentVariable("POLY_API_KEY");

                System.Console.WriteLine($"   🔑 API Key: {apiKey?.Substring(0, 10)}...");

                // Test different date ranges
                var testRanges = new[]
                {
                    (days: 100, name: "100 days (regime analysis)"),
                    (days: 50, name: "50 days (minimum)"),
                    (days: 30, name: "30 days (recent)"),
                    (days: 200, name: "200 days (extended)")
                };

                foreach (var range in testRanges)
                {
                    var testEndDate = DateTime.UtcNow;
                    var testStartDate = testEndDate.AddDays(-range.days);

                    var testUrl = $"/v2/aggs/ticker/SPY/range/1/day/{testStartDate:yyyy-MM-dd}/{testEndDate:yyyy-MM-dd}?adjusted=true&sort=asc&limit=50000";
                    System.Console.WriteLine($"\n   🧪 Testing {range.name}: {testUrl}");

                    try
                    {
                        var urlWithApiKey = polygonFactory.AddApiKeyToUrl(testUrl);
                        var response = await httpClient.GetAsync(urlWithApiKey);

                        System.Console.WriteLine($"      Status: {response.StatusCode}");

                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            var jsonDoc = JsonDocument.Parse(content);

                            if (jsonDoc.RootElement.TryGetProperty("results", out var results) && 
                                results.ValueKind == JsonValueKind.Array)
                            {
                                var barCount = results.GetArrayLength();
                                System.Console.WriteLine($"      ✅ Found {barCount} bars");

                                if (barCount < 50)
                                {
                                    System.Console.WriteLine($"      ❌ INSUFFICIENT: Only {barCount} bars (need 50+)");
                                }
                            }
                            else
                            {
                                System.Console.WriteLine($"      ⚠️  No results array in response");
                                if (jsonDoc.RootElement.TryGetProperty("status", out var status))
                                {
                                    System.Console.WriteLine($"      Status: {status.GetString()}");
                                }
                            }
                        }
                        else
                        {
                            var errorContent = await response.Content.ReadAsStringAsync();
                            System.Console.WriteLine($"      ❌ Error: {errorContent}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Console.WriteLine($"      ❌ Exception: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"   ❌ Polygon test failed: {ex.Message}");
            }

            // Test 3: MarketRegimeService direct test
            System.Console.WriteLine("\n📊 Test 3: MarketRegimeService Direct Test");
            System.Console.WriteLine("==========================================");

            try
            {
                var regime = await marketRegimeService.DetectRegimeAsync();
                System.Console.WriteLine($"   ✅ Market Regime: {regime}");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"   ❌ ERROR: {ex.Message}");
                logger.LogError(ex, "Error in MarketRegimeService.DetectRegimeAsync");
            }

            // Test 4: Check cached regime
            System.Console.WriteLine("\n📊 Test 4: Cached Regime Check");
            System.Console.WriteLine("==============================");

            try
            {
                var cachedRegime = await marketRegimeService.GetCachedRegimeAsync();
                System.Console.WriteLine($"   ✅ Cached Regime: {cachedRegime}");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"   ❌ ERROR: {ex.Message}");
                logger.LogError(ex, "Error checking cached regime");
            }

            System.Console.WriteLine("\n🔍 SPY Data Diagnostic Complete");
            System.Console.WriteLine("================================");

            // Recommendations
            System.Console.WriteLine("\n💡 Recommendations:");
            System.Console.WriteLine("   1. If insufficient data: Check Polygon API rate limits and connectivity");
            System.Console.WriteLine("   2. If data is stale: Verify market data is being updated properly");
            System.Console.WriteLine("   3. If API errors: Check API key validity and subscription status");
            System.Console.WriteLine("   4. Consider implementing data refresh mechanism for SPY");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Test failed: {ex.Message}");
            logger.LogError(ex, "SPY data diagnostic test failed");
        }
    }
}
