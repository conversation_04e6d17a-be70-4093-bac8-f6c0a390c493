using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Console;

/// <summary>
/// Test to diagnose SPY data retrieval issues causing "Insufficient SPY data for regime analysis" warning
/// </summary>
public static class TestSpyDataIssue
{
    public static async Task RunAsync(IHost host)
    {
        var logger = host.Services.GetRequiredService<ILogger<Program>>();
        var marketDataService = host.Services.GetRequiredService<IMarketDataService>();
        var marketRegimeService = host.Services.GetRequiredService<IMarketRegimeService>();
        
        System.Console.WriteLine("🔍 SPY Data Retrieval Diagnostic Test");
        System.Console.WriteLine("=====================================");
        
        // Test 1: Direct SPY data retrieval for regime analysis period
        System.Console.WriteLine("\n📊 Test 1: Direct SPY Data Retrieval (100 days)");
        try
        {
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-100); // Same as MarketRegimeService
            
            System.Console.WriteLine($"   📅 Date Range: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
            
            var spyBars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
            var barsList = spyBars.Items.ToList();
            
            System.Console.WriteLine($"   ✅ Retrieved {barsList.Count} SPY bars");
            
            if (barsList.Count < 50)
            {
                System.Console.WriteLine($"   ❌ INSUFFICIENT DATA: Only {barsList.Count} bars (need 50+)");
                System.Console.WriteLine("   🔧 This explains the 'Insufficient SPY data for regime analysis' warning");
            }
            else
            {
                System.Console.WriteLine($"   ✅ Sufficient data for regime analysis");
                
                if (barsList.Any())
                {
                    var firstBar = barsList.First();
                    var lastBar = barsList.Last();
                    System.Console.WriteLine($"   📅 First Bar: {firstBar.TimeUtc:yyyy-MM-dd} (Price: ${firstBar.Close:F2})");
                    System.Console.WriteLine($"   📅 Last Bar:  {lastBar.TimeUtc:yyyy-MM-dd} (Price: ${lastBar.Close:F2})");
                    
                    // Check for data gaps
                    var sortedBars = barsList.OrderBy(b => b.TimeUtc).ToList();
                    var gaps = new List<(DateTime from, DateTime to, int dayGap)>();
                    
                    for (int i = 1; i < sortedBars.Count; i++)
                    {
                        var prevDate = sortedBars[i - 1].TimeUtc.Date;
                        var currDate = sortedBars[i].TimeUtc.Date;
                        var daysDiff = (currDate - prevDate).Days;
                        
                        // Flag gaps > 5 days (accounting for weekends)
                        if (daysDiff > 5)
                        {
                            gaps.Add((prevDate, currDate, daysDiff));
                        }
                    }
                    
                    if (gaps.Any())
                    {
                        System.Console.WriteLine($"   ⚠️  Found {gaps.Count} data gaps:");
                        foreach (var gap in gaps.Take(5)) // Show first 5 gaps
                        {
                            System.Console.WriteLine($"      Gap: {gap.from:yyyy-MM-dd} to {gap.to:yyyy-MM-dd} ({gap.dayGap} days)");
                        }
                    }
                    else
                    {
                        System.Console.WriteLine("   ✅ No significant data gaps found");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"   ❌ ERROR: {ex.Message}");
            logger.LogError(ex, "Error retrieving SPY data for regime analysis test");
        }
        
        // Test 2: Test different date ranges
        System.Console.WriteLine("\n📊 Test 2: Different Date Ranges");
        var testRanges = new[]
        {
            (days: 30, name: "30 days"),
            (days: 60, name: "60 days"),
            (days: 90, name: "90 days"),
            (days: 120, name: "120 days"),
            (days: 180, name: "180 days")
        };
        
        foreach (var range in testRanges)
        {
            try
            {
                var endDate = DateTime.UtcNow;
                var startDate = endDate.AddDays(-range.days);
                
                var spyBars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
                var barsList = spyBars.Items.ToList();
                
                System.Console.WriteLine($"   {range.name}: {barsList.Count} bars");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"   {range.name}: ERROR - {ex.Message}");
            }
        }
        
        // Test 3: Test MarketRegimeService directly
        System.Console.WriteLine("\n📊 Test 3: MarketRegimeService Direct Test");
        try
        {
            var regime = await marketRegimeService.DetectRegimeAsync();
            System.Console.WriteLine($"   ✅ Market Regime: {regime}");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"   ❌ ERROR: {ex.Message}");
            logger.LogError(ex, "Error in MarketRegimeService test");
        }
        
        // Test 4: Check cache status
        System.Console.WriteLine("\n📊 Test 4: Cache Status Check");
        try
        {
            var cachedRegime = await marketRegimeService.GetCachedRegimeAsync();
            System.Console.WriteLine($"   ✅ Cached Regime: {cachedRegime}");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"   ❌ ERROR: {ex.Message}");
            logger.LogError(ex, "Error checking cached regime");
        }
        
        // Test 5: Check data sources
        System.Console.WriteLine("\n📊 Test 5: Data Source Analysis");
        try
        {
            // Test Polygon directly
            System.Console.WriteLine("   Testing Polygon API...");
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-100);
            
            // This will use the primary data source (Polygon for daily bars)
            var spyBars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
            var barsList = spyBars.Items.ToList();
            
            System.Console.WriteLine($"   📊 Primary source returned {barsList.Count} bars");
            
            if (barsList.Any())
            {
                var dataSource = spyBars.GetType().Name;
                System.Console.WriteLine($"   🔧 Data source type: {dataSource}");
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"   ❌ Data source test failed: {ex.Message}");
        }
        
        System.Console.WriteLine("\n🔍 Diagnostic Complete");
        System.Console.WriteLine("======================");
        
        // Recommendations
        System.Console.WriteLine("\n💡 Recommendations:");
        System.Console.WriteLine("   1. Check Polygon API connectivity and rate limits");
        System.Console.WriteLine("   2. Verify Alpaca fallback is working properly");
        System.Console.WriteLine("   3. Check for date range validation issues");
        System.Console.WriteLine("   4. Verify cache is not returning stale/empty data");
        System.Console.WriteLine("   5. Consider refreshing SPY data cache");
    }
}
