using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace SmaTrendFollower.Console;

/// <summary>
/// Test program to diagnose SPY data retrieval issues
/// </summary>
public static class TestSpyDataRetrieval
{
    public static async Task RunAsync(IServiceProvider serviceProvider)
    {
        System.Console.WriteLine("🔍 Diagnosing SPY Data Retrieval Issues...\n");

        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var marketDataService = serviceProvider.GetRequiredService<IMarketDataService>();
        var momentumTrainer = serviceProvider.GetRequiredService<IMomentumModelTrainer>();

        try
        {
            // Test 1: Direct SPY data retrieval with various date ranges
            System.Console.WriteLine("📊 Test 1: Direct SPY Data Retrieval");
            System.Console.WriteLine("=====================================");

            var testCases = new[]
            {
                new { Name = "Today - 200 days", EndDate = DateTime.UtcNow.Date, StartDays = -200 },
                new { Name = "Yesterday - 200 days", EndDate = DateTime.UtcNow.Date.AddDays(-1), StartDays = -200 },
                new { Name = "Last Friday - 200 days", EndDate = GetLastFriday(), StartDays = -200 },
                new { Name = "Today - 300 days", EndDate = DateTime.UtcNow.Date, StartDays = -300 },
                new { Name = "Yesterday - 300 days", EndDate = DateTime.UtcNow.Date.AddDays(-1), StartDays = -300 }
            };

            foreach (var testCase in testCases)
            {
                var endDate = testCase.EndDate;
                var startDate = endDate.AddDays(testCase.StartDays);

                System.Console.WriteLine($"\n🧪 Testing: {testCase.Name}");
                System.Console.WriteLine($"   Date Range: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");
                System.Console.WriteLine($"   Days Span: {(endDate - startDate).Days} days");

                try
                {
                    var response = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
                    var bars = response.Items.ToList();

                    System.Console.WriteLine($"   ✅ Retrieved {bars.Count} bars");
                    
                    if (bars.Any())
                    {
                        var firstBar = bars.First();
                        var lastBar = bars.Last();
                        System.Console.WriteLine($"   📅 First Bar: {firstBar.TimeUtc:yyyy-MM-dd} (Price: ${firstBar.Close:F2})");
                        System.Console.WriteLine($"   📅 Last Bar:  {lastBar.TimeUtc:yyyy-MM-dd} (Price: ${lastBar.Close:F2})");
                    }

                    if (bars.Count < 50)
                    {
                        System.Console.WriteLine($"   ⚠️  INSUFFICIENT DATA: Only {bars.Count} bars (need 50+)");
                    }
                }
                catch (Exception ex)
                {
                    System.Console.WriteLine($"   ❌ ERROR: {ex.Message}");
                    logger.LogError(ex, "Error retrieving SPY data for test case: {TestCase}", testCase.Name);
                }
            }

            // Test 2: MomentumModelTrainer feature vector creation
            System.Console.WriteLine("\n\n📊 Test 2: MomentumModelTrainer Feature Vector Creation");
            System.Console.WriteLine("======================================================");

            var featureTestDates = new[]
            {
                DateTime.UtcNow.Date,
                DateTime.UtcNow.Date.AddDays(-1),
                GetLastFriday(),
                DateTime.UtcNow.Date.AddDays(-7)
            };

            foreach (var testDate in featureTestDates)
            {
                System.Console.WriteLine($"\n🧪 Testing feature vector for date: {testDate:yyyy-MM-dd}");
                
                try
                {
                    var features = await momentumTrainer.CreateFeatureVectorAsync("SPY", testDate);
                    System.Console.WriteLine($"   ✅ Feature Vector: [{string.Join(", ", features.Select(f => f.ToString("F3")))}]");
                    
                    if (features.All(f => f == 0))
                    {
                        System.Console.WriteLine($"   ⚠️  ALL ZEROS: This indicates insufficient data for feature calculation");
                    }
                }
                catch (Exception ex)
                {
                    System.Console.WriteLine($"   ❌ ERROR: {ex.Message}");
                    logger.LogError(ex, "Error creating feature vector for SPY on {Date}", testDate);
                }
            }

            // Test 3: Check market data service configuration
            System.Console.WriteLine("\n\n📊 Test 3: Market Data Service Configuration");
            System.Console.WriteLine("=============================================");

            System.Console.WriteLine($"Market Data Service Type: {marketDataService.GetType().Name}");
            
            // Test 4: Check current market status
            System.Console.WriteLine("\n\n📊 Test 4: Market Status Check");
            System.Console.WriteLine("===============================");

            var now = DateTime.UtcNow;
            var easternTime = TimeZoneInfo.ConvertTimeFromUtc(now, TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));
            
            System.Console.WriteLine($"Current UTC Time: {now:yyyy-MM-dd HH:mm:ss}");
            System.Console.WriteLine($"Current ET Time:  {easternTime:yyyy-MM-dd HH:mm:ss}");
            
            var isMarketHours = easternTime.TimeOfDay >= TimeSpan.FromHours(9.5) && 
                               easternTime.TimeOfDay <= TimeSpan.FromHours(16) &&
                               easternTime.DayOfWeek >= DayOfWeek.Monday && 
                               easternTime.DayOfWeek <= DayOfWeek.Friday;
            
            System.Console.WriteLine($"Is Market Hours: {isMarketHours}");

            System.Console.WriteLine("\n🎯 Diagnosis Complete!");
            System.Console.WriteLine("Check the results above to identify the root cause of SPY data issues.");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Fatal error during SPY diagnosis: {ex.Message}");
            logger.LogError(ex, "Fatal error during SPY data diagnosis");
        }
    }

    private static DateTime GetLastFriday()
    {
        var today = DateTime.UtcNow.Date;
        var daysToSubtract = ((int)today.DayOfWeek + 2) % 7; // Calculate days back to Friday
        if (daysToSubtract == 0 && today.DayOfWeek != DayOfWeek.Friday)
        {
            daysToSubtract = 7; // If today is Saturday, go back to last Friday
        }
        return today.AddDays(-daysToSubtract);
    }
}
