using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test to verify API tracking service can successfully log API calls to the database
/// </summary>
public static class ApiTrackingTest
{
    public static async Task RunAsync(IServiceProvider serviceProvider)
    {
        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger("ApiTrackingTest");
        
        logger.LogInformation("🔧 Starting API Tracking Test...");
        
        try
        {
            var apiTrackingService = serviceProvider.GetRequiredService<IApiTrackingService>();
            
            // Test 1: Track a successful FinBERT API call
            await TestFinBertApiCall(apiTrackingService, logger);
            
            // Test 2: Track a failed API call
            await TestFailedApiCall(apiTrackingService, logger);
            
            // Test 3: Track an API call with metadata
            await TestApiCallWithMetadata(apiTrackingService, logger);
            
            logger.LogInformation("✅ API Tracking Test completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ API Tracking Test failed: {ErrorMessage}", ex.Message);
            throw;
        }
    }
    
    private static async Task TestFinBertApiCall(IApiTrackingService apiTrackingService, ILogger logger)
    {
        logger.LogInformation("🔍 Testing FinBERT API call tracking...");
        
        var request = new ApiCallRequest(
            Provider: "FinBERT",
            Operation: "sentiment_analysis",
            Symbol: "AAPL",
            RequestData: "Apple Inc. reports strong quarterly earnings with revenue growth of 15%"
        );
        
        var result = new ApiCallResult(
            Success: true,
            StatusCode: 200,
            ResponseData: "pos:0.850 neg:0.100 neu:0.050",
            TokensUsed: 25,
            Cost: 0.001m,
            Metadata: new Dictionary<string, object>
            {
                ["confidence"] = 0.850,
                ["sentiment_score"] = 0.750,
                ["model"] = "finbert"
            }
        );
        
        var duration = TimeSpan.FromMilliseconds(1250);
        
        await apiTrackingService.TrackApiCallAsync(request, result, duration);
        
        logger.LogInformation("✅ FinBERT API call tracked successfully");
    }
    
    private static async Task TestFailedApiCall(IApiTrackingService apiTrackingService, ILogger logger)
    {
        logger.LogInformation("🔍 Testing failed API call tracking...");
        
        var request = new ApiCallRequest(
            Provider: "OpenAI",
            Operation: "sentiment_analysis",
            Symbol: "TSLA",
            RequestData: "Tesla stock analysis request"
        );
        
        var result = new ApiCallResult(
            Success: false,
            StatusCode: 429,
            ErrorMessage: "Rate limit exceeded",
            Metadata: new Dictionary<string, object>
            {
                ["retry_after"] = 60,
                ["error_type"] = "rate_limit"
            }
        );
        
        var duration = TimeSpan.FromMilliseconds(500);
        
        await apiTrackingService.TrackApiCallAsync(request, result, duration);
        
        logger.LogInformation("✅ Failed API call tracked successfully");
    }
    
    private static async Task TestApiCallWithMetadata(IApiTrackingService apiTrackingService, ILogger logger)
    {
        logger.LogInformation("🔍 Testing API call with complex metadata...");
        
        var request = new ApiCallRequest(
            Provider: "Gemini",
            Operation: "sentiment_analysis",
            RequestData: "Market analysis for technology sector",
            Metadata: new Dictionary<string, object>
            {
                ["model"] = "gemini-pro",
                ["temperature"] = 0.1,
                ["max_tokens"] = 100
            }
        );
        
        var result = new ApiCallResult(
            Success: true,
            StatusCode: 200,
            ResponseData: "Positive sentiment detected with high confidence",
            TokensUsed: 75,
            Cost: 0.0025m,
            Metadata: new Dictionary<string, object>
            {
                ["sentiment_score"] = 0.82,
                ["confidence"] = 0.95,
                ["processing_time_ms"] = 850
            }
        );
        
        var duration = TimeSpan.FromMilliseconds(850);
        
        await apiTrackingService.TrackApiCallAsync(request, result, duration);
        
        logger.LogInformation("✅ API call with metadata tracked successfully");
    }
}
