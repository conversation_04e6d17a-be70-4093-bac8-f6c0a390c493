using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test class for BarRecorder functionality
/// </summary>
public class BarRecorderTest
{
    /// <summary>
    /// Test to verify BarRecorder handles ObjectDisposedException gracefully
    /// </summary>
    public static async Task RunAsync(IServiceProvider serviceProvider)
    {
        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger("BarRecorderTest");

        logger.LogInformation("🔧 Starting BarRecorder Test...");

        try
        {
            var barRecorder = serviceProvider.GetRequiredService<IBarRecorder>();

            // Test 1: Record bars for CISS symbol (the one that was failing)
            await TestCissBarRecording(barRecorder, logger);

            // Test 2: Test with various symbols to ensure robustness
            await TestMultipleSymbolRecording(barRecorder, logger);

            logger.LogInformation("✅ BarRecorder Test completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ BarRecorder Test failed: {ErrorMessage}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Simple test to verify bar recording works
    /// </summary>
    public static async Task TestBarRecording()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<BarRecorder>();
        var circuitBreakerLogger = loggerFactory.CreateLogger<CompressionCircuitBreakerService>();
        var compressionCircuitBreaker = new CompressionCircuitBreakerService(circuitBreakerLogger);

        var barRecorder = new BarRecorder(logger, compressionCircuitBreaker);

        // Create test bars
        var testBars = new List<TestBar>
        {
            new TestBar("AAPL", DateTime.UtcNow.Date, 150.00m, 152.00m, 149.00m, 151.00m, 1000000),
            new TestBar("AAPL", DateTime.UtcNow.Date.AddMinutes(1), 151.00m, 153.00m, 150.50m, 152.50m, 1200000),
            new TestBar("AAPL", DateTime.UtcNow.Date.AddMinutes(2), 152.50m, 154.00m, 152.00m, 153.75m, 900000)
        };

        System.Console.WriteLine("Recording test bars...");
        barRecorder.Record(testBars, "AAPL", "Minute");

        // Wait for compression to complete
        await Task.Delay(2000);

        System.Console.WriteLine("Bar recording test completed. Check Data/Bars directory for output files.");
    }

    private static async Task TestCissBarRecording(IBarRecorder barRecorder, ILogger logger)
    {
        logger.LogInformation("🔍 Testing CISS bar recording...");

        // Create mock bars for CISS
        var bars = new List<TestBar>
        {
            new TestBar("CISS", DateTime.UtcNow.AddDays(-2), 100.50m, 102.75m, 99.25m, 101.80m, 1500000),
            new TestBar("CISS", DateTime.UtcNow.AddDays(-1), 101.80m, 103.20m, 100.90m, 102.45m, 1750000)
        };

        // This should not throw ObjectDisposedException
        barRecorder.Record(bars, "CISS", "Day");

        // Give background compression a moment to start
        await Task.Delay(100);

        logger.LogInformation("✅ CISS bar recording completed without ObjectDisposedException");
    }

    private static async Task TestMultipleSymbolRecording(IBarRecorder barRecorder, ILogger logger)
    {
        logger.LogInformation("🔍 Testing multiple symbol bar recording...");

        var symbols = new[] { "AAPL", "MSFT", "GOOGL", "TSLA", "SPY" };

        foreach (var symbol in symbols)
        {
            var bars = new List<TestBar>
            {
                new TestBar(symbol, DateTime.UtcNow.AddHours(-1), 150.00m, 152.50m, 149.75m, 151.25m, 1000000)
            };

            // This should not throw ObjectDisposedException
            barRecorder.Record(bars, symbol, "Day");

            logger.LogDebug("Recorded bars for {Symbol}", symbol);
        }

        // Give background compression a moment to process
        await Task.Delay(200);

        logger.LogInformation("✅ Multiple symbol bar recording completed successfully");
    }
}

/// <summary>
/// Simple test implementation of IBar for testing
/// </summary>
public record TestBar(string Symbol, DateTime TimeUtc, decimal Open, decimal High, decimal Low, decimal Close, decimal Volume) : IBar
{
    public decimal Vwap => (High + Low + Close) / 3;
    public ulong TradeCount => (ulong)(Volume / 100);
}
