using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test class for database optimization functionality
/// </summary>
public static class DatabaseOptimizationTest
{
    /// <summary>
    /// Tests database optimization to fix the UPDATE performance issues
    /// </summary>
    public static async Task TestDatabaseOptimization()
    {
        System.Console.WriteLine("🔧 Testing Database Optimization...");
        
        try
        {
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.LocalProd.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
            
            // Create host with minimal services needed for database optimization
            using var host = Host.CreateDefaultBuilder()
                .ConfigureServices(services =>
                {
                    services.AddSingleton<IConfiguration>(configuration);
                    
                    // Add logging
                    services.AddLogging(builder => builder.AddConsole());
                    
                    // Add database services
                    services.AddDataServices(configuration);
                    
                    // Add database optimization service
                    services.AddSingleton<IDatabaseOptimizationService, DatabaseOptimizationService>();
                })
                .Build();
            
            using var scope = host.Services.CreateScope();
            var optimizationService = scope.ServiceProvider.GetRequiredService<IDatabaseOptimizationService>();
            
            System.Console.WriteLine("✅ Services initialized successfully");
            
            // Run database optimization
            System.Console.WriteLine("🔧 Running database optimization...");
            await optimizationService.OptimizeDatabaseAsync();
            
            // Verify optimizations
            System.Console.WriteLine("🔍 Verifying optimizations...");
            await optimizationService.VerifyOptimizationsAsync();
            
            // Get performance report
            System.Console.WriteLine("📊 Getting performance report...");
            var report = await optimizationService.GetPerformanceReportAsync();
            
            System.Console.WriteLine($"✅ Database optimization completed!");
            System.Console.WriteLine($"   - Optimization Status: {report.OptimizationStatus}");
            System.Console.WriteLine($"   - UPDATE Performance: {report.UpdatePerformanceMs}ms");
            System.Console.WriteLine($"   - Report Timestamp: {report.Timestamp}");
            
            if (report.TableStatistics?.Any() == true)
            {
                System.Console.WriteLine("📊 Table Statistics:");
                foreach (var stat in report.TableStatistics)
                {
                    System.Console.WriteLine($"   - {stat.TableName}: {stat.Size}, Inserts: {stat.Inserts:N0}, Updates: {stat.Updates:N0}, Deletes: {stat.Deletes:N0}");
                }
            }
            
            System.Console.WriteLine("✅ Database optimization test completed successfully!");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Database optimization test failed: {ex.Message}");
            System.Console.WriteLine($"🔍 Stack trace: {ex.StackTrace}");
            throw;
        }
    }
}
