using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Interfaces;
using SmaTrendFollower.Data;
using System.Diagnostics;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Comprehensive test for database performance improvements
/// Tests connection pooling, retry policies, context management, and performance monitoring
/// </summary>
public class DatabasePerformanceTest
{
    public static async Task RunAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<DatabasePerformanceTest>>();
        var performanceMonitor = serviceProvider.GetRequiredService<IDatabasePerformanceMonitorService>();
        var stockBarCache = serviceProvider.GetRequiredService<IStockBarCacheService>();
        
        logger.LogInformation("=== Starting Database Performance Test ===");
        
        // Reset metrics for clean test
        performanceMonitor.ResetMetrics();
        
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Test 1: Connection Pool Stress Test
            await TestConnectionPoolStress(logger, stockBarCache, performanceMonitor);
            
            // Test 2: Concurrent Operations Test
            await TestConcurrentOperations(logger, stockBarCache, performanceMonitor);
            
            // Test 3: Error Handling and Retry Test
            await TestErrorHandlingAndRetry(logger, stockBarCache, performanceMonitor);
            
            // Test 4: Performance Monitoring Test
            await TestPerformanceMonitoring(logger, performanceMonitor);
            
            stopwatch.Stop();
            
            // Final performance summary
            var metrics = performanceMonitor.GetCurrentMetrics();
            var summary = performanceMonitor.GetPerformanceSummary();
            
            logger.LogInformation("=== Database Performance Test Results ===");
            logger.LogInformation("Total Test Duration: {Duration}ms", stopwatch.ElapsedMilliseconds);
            logger.LogInformation("Performance Summary: {Summary}", summary);
            logger.LogInformation("Success Rate: {SuccessRate:F2}%", metrics.SuccessRate);
            logger.LogInformation("Average Operation Time: {AvgTime:F2}ms", metrics.AverageOperationTime.TotalMilliseconds);
            logger.LogInformation("Max Operation Time: {MaxTime:F2}ms", metrics.MaxOperationTime.TotalMilliseconds);
            logger.LogInformation("Total Errors: {TotalErrors}", 
                metrics.TimeoutErrors + metrics.ConnectionErrors + metrics.InvalidOperationErrors + metrics.OtherErrors);
            
            if (metrics.SuccessRate >= 95.0)
            {
                logger.LogInformation("✅ Database Performance Test PASSED - Success rate: {SuccessRate:F2}%", metrics.SuccessRate);
            }
            else
            {
                logger.LogWarning("⚠️ Database Performance Test MARGINAL - Success rate: {SuccessRate:F2}% (below 95%)", metrics.SuccessRate);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Database Performance Test FAILED: {ErrorMessage}", ex.Message);
            throw;
        }
    }
    
    private static async Task TestConnectionPoolStress(ILogger logger, IStockBarCacheService stockBarCache, IDatabasePerformanceMonitorService performanceMonitor)
    {
        logger.LogInformation("--- Test 1: Connection Pool Stress Test ---");
        
        var tasks = new List<Task>();
        var symbols = new[] { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "CRM", "ORCL" };
        
        // Create 20 concurrent tasks to stress the connection pool
        for (int i = 0; i < 20; i++)
        {
            var symbol = symbols[i % symbols.Length];
            var task = Task.Run(async () =>
            {
                try
                {
                    var startDate = DateTime.UtcNow.AddDays(-30);
                    var endDate = DateTime.UtcNow;
                    
                    // Test multiple operations per task
                    for (int j = 0; j < 5; j++)
                    {
                        await stockBarCache.GetLatestCachedDateAsync(symbol, "Day");
                        await Task.Delay(10); // Small delay to simulate real usage
                    }
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Connection pool stress test error for {Symbol}: {ErrorMessage}", symbol, ex.Message);
                }
            });
            tasks.Add(task);
        }
        
        await Task.WhenAll(tasks);
        logger.LogInformation("✅ Connection Pool Stress Test completed");
    }
    
    private static async Task TestConcurrentOperations(ILogger logger, IStockBarCacheService stockBarCache, IDatabasePerformanceMonitorService performanceMonitor)
    {
        logger.LogInformation("--- Test 2: Concurrent Operations Test ---");
        
        var tasks = new List<Task>();
        var symbols = new[] { "SPY", "QQQ", "IWM", "VTI", "VOO" };
        
        // Test concurrent read operations
        for (int i = 0; i < 15; i++)
        {
            var symbol = symbols[i % symbols.Length];
            var task = Task.Run(async () =>
            {
                try
                {
                    var startDate = DateTime.UtcNow.AddDays(-7);
                    var endDate = DateTime.UtcNow;
                    
                    // Mix of different operations
                    await stockBarCache.GetLatestCachedDateAsync(symbol, "Day");
                    await stockBarCache.GetCachedBarsAsync(symbol, "Day", startDate, endDate);
                    await stockBarCache.IsCacheFreshAsync(symbol, "Day", endDate);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Concurrent operations test error for {Symbol}: {ErrorMessage}", symbol, ex.Message);
                }
            });
            tasks.Add(task);
        }
        
        await Task.WhenAll(tasks);
        logger.LogInformation("✅ Concurrent Operations Test completed");
    }
    
    private static async Task TestErrorHandlingAndRetry(ILogger logger, IStockBarCacheService stockBarCache, IDatabasePerformanceMonitorService performanceMonitor)
    {
        logger.LogInformation("--- Test 3: Error Handling and Retry Test ---");
        
        // Test with potentially problematic operations
        var testSymbols = new[] { "INVALID_SYMBOL_TEST", "TEST123", "RETRY_TEST" };
        
        foreach (var symbol in testSymbols)
        {
            try
            {
                var startDate = DateTime.UtcNow.AddDays(-1);
                var endDate = DateTime.UtcNow;
                
                // These operations should handle errors gracefully
                await stockBarCache.GetLatestCachedDateAsync(symbol, "Day");
                await stockBarCache.IsCacheFreshAsync(symbol, "Day", endDate);
            }
            catch (Exception ex)
            {
                logger.LogDebug("Expected error for test symbol {Symbol}: {ErrorMessage}", symbol, ex.Message);
            }
        }
        
        logger.LogInformation("✅ Error Handling and Retry Test completed");
    }
    
    private static async Task TestPerformanceMonitoring(ILogger logger, IDatabasePerformanceMonitorService performanceMonitor)
    {
        logger.LogInformation("--- Test 4: Performance Monitoring Test ---");
        
        // Test performance monitoring functionality
        var testOperationName = "TestOperation";
        
        // Record some test metrics
        performanceMonitor.RecordOperationTime(testOperationName, TimeSpan.FromMilliseconds(100), true);
        performanceMonitor.RecordOperationTime(testOperationName, TimeSpan.FromMilliseconds(200), true);
        performanceMonitor.RecordOperationTime(testOperationName, TimeSpan.FromMilliseconds(50), false);
        
        performanceMonitor.RecordConnectionPoolEvent("TestEvent", 25, 50);
        
        // Test error recording
        try
        {
            throw new TimeoutException("Test timeout exception");
        }
        catch (Exception ex)
        {
            performanceMonitor.RecordDatabaseError(testOperationName, ex);
        }
        
        var metrics = performanceMonitor.GetCurrentMetrics();
        var summary = performanceMonitor.GetPerformanceSummary();
        
        logger.LogInformation("Performance Monitoring Metrics: {Summary}", summary);
        logger.LogInformation("Current Active Connections: {ActiveConnections}/{PoolSize}", 
            metrics.CurrentActiveConnections, metrics.ConnectionPoolSize);
        
        logger.LogInformation("✅ Performance Monitoring Test completed");
        
        await Task.CompletedTask;
    }
}
