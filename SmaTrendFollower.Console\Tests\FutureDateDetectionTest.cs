using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Interfaces;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test to verify future date detection logic works correctly
/// </summary>
public static class FutureDateDetectionTest
{
    public static async Task RunAsync(IServiceProvider serviceProvider)
    {
        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger("FutureDateDetectionTest");
        
        logger.LogInformation("🔧 Starting Future Date Detection Test...");
        
        try
        {
            var cacheService = serviceProvider.GetRequiredService<IStockBarCacheService>();
            
            // Test 1: Request data for today (should trigger warning and adjustment)
            await TestTodayDateRequest(cacheService, logger);
            
            // Test 2: Request data for yesterday (should be allowed)
            await TestYesterdayDateRequest(cacheService, logger);
            
            // Test 3: Request data for future date (should trigger warning and adjustment)
            await TestFutureDateRequest(cacheService, logger);
            
            // Test 4: Request data for valid historical range (should work without warnings)
            await TestValidHistoricalRange(cacheService, logger);
            
            logger.LogInformation("✅ Future Date Detection Test completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Future Date Detection Test failed: {ErrorMessage}", ex.Message);
            throw;
        }
    }
    
    private static async Task TestTodayDateRequest(IStockBarCacheService cacheService, ILogger logger)
    {
        logger.LogInformation("🔍 Testing request for today's date (should trigger warning)...");
        
        var today = DateTime.UtcNow.Date;
        var startDate = today.AddDays(-30);
        var endDate = today; // This should trigger the warning
        
        logger.LogInformation("Requesting data for ALMS from {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}", 
            startDate, endDate);
        
        // This should trigger the future date warning and adjust the endDate to yesterday
        var bars = await cacheService.GetCachedBarsAsync("ALMS", "Day", startDate, endDate);
        
        logger.LogInformation("✅ Today's date request completed (warning expected)");
    }
    
    private static async Task TestYesterdayDateRequest(IStockBarCacheService cacheService, ILogger logger)
    {
        logger.LogInformation("🔍 Testing request for yesterday's date (should be allowed)...");

        var yesterday = DateTime.UtcNow.Date.AddDays(-1);
        var startDate = yesterday.AddDays(-30);
        var endDate = yesterday;

        logger.LogInformation("Requesting data for AAPL from {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}",
            startDate, endDate);

        // This should NOT trigger any warnings
        var bars = await cacheService.GetCachedBarsAsync("AAPL", "Day", startDate, endDate);

        logger.LogInformation("✅ Yesterday's date request completed (no warnings expected)");
    }

    private static async Task TestFutureDateRequest(IStockBarCacheService cacheService, ILogger logger)
    {
        logger.LogInformation("🔍 Testing request for future date (should trigger warning)...");

        var futureDate = DateTime.UtcNow.Date.AddDays(7); // One week in the future
        var startDate = futureDate.AddDays(-30);
        var endDate = futureDate;

        logger.LogInformation("Requesting data for MSFT from {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}",
            startDate, endDate);

        // This should trigger the future date warning and adjust both dates
        var bars = await cacheService.GetCachedBarsAsync("MSFT", "Day", startDate, endDate);

        logger.LogInformation("✅ Future date request completed (warning expected)");
    }

    private static async Task TestValidHistoricalRange(IStockBarCacheService cacheService, ILogger logger)
    {
        logger.LogInformation("🔍 Testing valid historical date range (should work without warnings)...");
        
        var endDate = DateTime.UtcNow.Date.AddDays(-7); // One week ago
        var startDate = endDate.AddDays(-30); // 30 days before that
        
        logger.LogInformation("Requesting data for SPY from {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}", 
            startDate, endDate);
        
        // This should NOT trigger any warnings
        var bars = await cacheService.GetCachedBarsAsync("SPY", "Day", startDate, endDate);
        
        logger.LogInformation("✅ Valid historical range request completed (no warnings expected)");
    }
}
