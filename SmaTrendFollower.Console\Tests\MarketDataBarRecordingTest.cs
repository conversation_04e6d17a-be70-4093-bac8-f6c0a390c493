using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test class for MarketDataService bar recording integration
/// </summary>
public static class MarketDataBarRecordingTest
{
    /// <summary>
    /// Tests that MarketDataService properly records bars when fetching data
    /// </summary>
    public static async Task TestMarketDataServiceIntegration()
    {
        System.Console.WriteLine("🔄 Testing MarketDataService bar recording integration...");
        
        try
        {
            // Build a minimal service collection for testing
            var services = new ServiceCollection();
            
            // Add configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.LocalProd.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
            
            services.AddSingleton<IConfiguration>(configuration);
            
            // Add logging
            services.AddLogging(builder => builder.AddConsole());
            
            // Add core infrastructure and market data services
            services.AddCoreInfrastructure();
            services.AddMarketDataServices();
            
            var serviceProvider = services.BuildServiceProvider();
            
            // Get the market data service
            var marketDataService = serviceProvider.GetRequiredService<IMarketDataService>();
            var barRecorder = serviceProvider.GetRequiredService<IBarRecorder>();
            
            System.Console.WriteLine("✅ Services initialized successfully");
            
            // Test with a small date range to avoid hitting rate limits
            var endDate = DateTime.UtcNow.Date.AddDays(-1); // Yesterday
            var startDate = endDate.AddDays(-2); // Day before yesterday
            
            System.Console.WriteLine($"📊 Fetching bars for SPY from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}...");
            
            // This should trigger bar recording automatically
            var bars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
            
            System.Console.WriteLine($"✅ Retrieved {bars.Items.Count()} bars for SPY");
            
            // Wait for compression to complete
            await Task.Delay(2000);
            
            // Check if files were created
            var expectedDir = Path.Combine("Data", "Bars", startDate.ToString("yyyy"), startDate.ToString("MM"), startDate.ToString("dd"));
            if (Directory.Exists(expectedDir))
            {
                var files = Directory.GetFiles(expectedDir, "SPY_*.csv.zst");
                System.Console.WriteLine($"📄 Found {files.Length} recorded files for SPY");
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    System.Console.WriteLine($"   - {Path.GetFileName(file)} ({fileInfo.Length} bytes)");
                }
            }
            else
            {
                System.Console.WriteLine($"⚠️ Expected directory not found: {expectedDir}");
            }
            
            System.Console.WriteLine("✅ MarketDataService bar recording integration test completed!");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error testing MarketDataService integration: {ex.Message}");
            System.Console.WriteLine($"🔍 Stack trace: {ex.StackTrace}");
        }
    }
}
