{"ConnectionStrings": {"StockBarCache": "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;", "IndexCache": "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;", "MLFeatures": "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;"}, "Redis": {"ConnectionString": "*************:6379", "UniverseCacheConnection": "*************:6379"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "SmaTrendFollower": "Information"}}, "Alpaca": {"KeyId": "AKGBPW5HD8LVI5C6NJUJ", "SecretKey": "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM", "Environment": "live"}, "Polygon": {"ApiKey": "********************************"}, "Discord": {"BotToken": "MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo", "ChannelId": "1385057459814797383"}, "Brave": {"SearchApiKey": "BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz", "AiApiKey": "BSAacxwbtDjQfP4151QopodZgaSS8jS", "RateLimitPerSecond": 1, "MaxRequestsPerMonth": 2000, "TimeoutSeconds": 30, "PageSize": 20, "LookbackMins": 20, "Symbols": ["AAPL", "SPY", "QQQ", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "NFLX"]}, "STATE_FLUSH_INTERVAL_MINUTES": "10", "STATE_BACKUP_PATH": "Data/state_backup.json", "Monitoring": {"MemoryThresholdMB": 1500, "EnableDiscordAlerts": true, "EnablePerformanceMonitoring": true, "HealthCheckInterval": "00:02:00"}, "Safety": {"AllowedEnvironment": "Live", "MaxDailyLoss": 500, "MaxPositions": 8, "MaxSingleTradeValue": 3000, "MinAccountEquity": 5000, "MaxPositionSizePercent": 0.12, "MaxDailyTrades": 25, "RequireConfirmation": false, "DryRunMode": false}, "Strategy": {"UniverseSize": 500, "TopNSymbols": 5, "VixThreshold": 25.0, "EnableRegimeFilter": true, "EnableVolatilityFilter": true}, "Options": {"EnableOptionsOverlay": false, "EnableProtectivePuts": false, "EnableCoveredCalls": false}, "WheelStrategy": {"Enabled": true, "MaxAllocationPercent": 0.2, "MinPremiumPercent": 0.01, "MinDaysToExpiration": 7, "MaxDaysToExpiration": 45, "MaxDeltaForPuts": 0.3, "MaxDeltaForCalls": 0.3, "MinLiquidity": 100, "MaxBidAskSpreadPercent": 0.05, "EnableRolling": true, "RollThreshold": 0.5, "MaxRollAttempts": 2, "RequireHighIV": true, "MinIVPercentile": 30, "AllowedSymbols": ["SPY", "QQQ", "AAPL", "MSFT", "TSLA", "NVDA", "AMD", "AMZN"], "ExcludedSymbols": [], "Timing": {"EntryWindowStart": "09:35:00", "EntryWindowEnd": "15:30:00", "CycleInterval": "00:15:00", "EnableExtendedHours": false, "EnablePreMarketEntry": false}, "Risk": {"MaxDrawdownPercent": 0.1, "MaxDailyLossPercent": 0.05, "MaxActivePositions": 10, "MaxSinglePositionPercent": 0.05, "EnableEmergencyStop": true, "EmergencyStopThreshold": 0.15, "EnablePositionSizing": true, "VolatilityAdjustmentFactor": 1.0}}, "UsePolygonUniverse": true, "USE_DYNAMIC_UNIVERSE": true, "PolygonUniverse": {"PageSize": 500, "MaxSymbols": 0, "IncludedMarkets": ["stocks"], "IncludedTypes": ["CS", "ETF"], "ActiveOnly": true, "DelayBetweenCalls": 300, "CacheTtlHours": 168}, "PolygonSnapshot": {"BatchSize": 30, "MaxConcurrency": 8, "DelayBetweenCalls": 200}, "UniverseRefresh": {"RefreshTimeUtc": "12:30:00", "MinPrice": 2.0, "MinAverageVolume": 50000, "MinVolatilityPercent": 0.3, "MaxCandidates": 1000, "AnalysisPeriodDays": 5, "MinMarketCap": null, "CacheTtlHours": 24, "ExcludedExchanges": [], "ExcludedTypes": []}, "SubscriptionManager": {"SubscriptionTimeUtc": "13:25:00", "MaxSubscriptions": 200, "BatchSize": 50, "DelayBetweenBatches": 1000}, "DynamicUniverse": {"UsePolygonIntegration": true, "FallbackToStaticSymbols": true, "MaxConcurrentBatches": 15, "BatchSize": 100, "DefaultCriteria": {"MinPrice": 2.0, "MinAverageVolume": 50000, "MinVolatilityPercent": 0.3, "AnalysisPeriodDays": 30, "MaxSymbols": 5000}}, "OptimizedUniverse": {"MaxConcurrentRequests": 50, "SymbolTimeoutMs": 10000, "SlowRequestThresholdMs": 2000, "MinBarsRequired": 5}, "UniverseCache": {"RefreshInterval": "00:10:00"}, "RedisWarming": {"EssentialSymbols": ["SPY", "QQQ", "IWM", "VIX", "TLT", "GLD", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA"], "HistoricalDays": 7, "WarmSignalFlags": true, "WarmThrottleFlags": true, "DefaultTtlHours": 24, "MaxConcurrency": 10}, "CacheWarming": {"EssentialSymbols": ["SPY", "QQQ", "IWM", "VIX", "TLT", "GLD", "AAPL", "MSFT", "GOOGL", "AMZN"], "EssentialSymbolsDays": 365, "UniverseSymbolsDays": 250, "WarmingInterval": "06:00:00", "PreferredStartTime": "18:00:00", "PreferredEndTime": "06:00:00", "MaxConcurrentSymbols": 5, "BatchSize": 50}, "UnifiedCacheWarming": {"EssentialSymbols": ["SPY", "QQQ", "IWM", "VIX", "TLT", "GLD", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA"], "MaxSymbolsToWarm": 2000, "HistoricalDays": 60, "WarmingInterval": "06:00:00", "MinTimeBetweenRuns": "04:00:00", "InitialDelay": "00:30:00"}, "BulkData": {"MaxConcurrentRequests": 100, "SymbolTimeoutMs": 10000, "SlowRequestThresholdMs": 2000, "UseBulkEndpoints": true, "BulkThreshold": 100}, "EnhancedServices": {"EnableEnhancedDataRetrieval": true, "EnableAdaptiveRateLimit": true, "EnableAdaptiveSignalGeneration": true, "EnableEnhancedMetrics": true, "EnableSyntheticData": false, "EnableEmergencyMode": true}, "EnhancedDataRetrieval": {"MaxConcurrentRequests": 120, "PrimaryApiTimeout": "00:01:00", "BatchTimeout": "00:05:00", "RelaxedStalenessThreshold": "01:00:00", "EmergencyModeMaxStaleness": "04:00:00", "EmergencyModeTimeout": "00:10:00", "EnableSyntheticData": false, "MinimumBatchSuccessRate": 0.8, "MaxFailedAttempts": 2}, "AdaptiveRateLimit": {"Providers": {"Alpaca": {"InitialLimit": 15, "MinLimit": 5, "MaxLimit": 30, "CircuitBreakerConfig": {"FailureThreshold": 3, "OpenTimeout": "00:01:00", "SuccessThreshold": 2}}, "Polygon": {"InitialLimit": 20, "MinLimit": 8, "MaxLimit": 40, "CircuitBreakerConfig": {"FailureThreshold": 3, "OpenTimeout": "00:01:00", "SuccessThreshold": 2}}}, "AdjustmentInterval": "00:01:30", "AdjustmentWindow": "00:03:00", "SuccessRateThreshold": 0.9, "AcquisitionTimeout": "00:00:45"}, "RobustSignal": {"MaxConcurrentGenerations": 4, "MinimumAcceptableSignals": 3, "MinimumConfidenceScore": 0.5, "EnableFallbackGeneration": true, "EnableEmergencyGeneration": true, "AdaptiveGenerationTimeout": "00:00:45", "FallbackGenerationTimeout": "00:00:30", "EmergencyModeDuration": "00:03:00", "MaxErrorsPerSymbol": 2, "ErrorCooldownPeriod": "00:10:00"}, "OptimizedSignal": {"CacheValidityPeriod": "00:05:00", "OptimalBatchSize": 8, "MaxConcurrentBatches": 2, "SlowBatchThresholdMs": 20000, "SlowSymbolThresholdMs": 3000}, "ConcurrentProcessing": {"GlobalMaxConcurrency": 15, "ProviderMaxConcurrency": 3, "DefaultBatchSize": 8, "MaxBatchSize": 25, "SlowBatchThresholdMs": 20000, "SlowItemThresholdMs": 2500, "HighLatencyThresholdMs": 4000, "LowLatencyThresholdMs": 800, "OverloadLatencyThresholdMs": 8000, "AdaptiveAdjustmentInterval": "00:02:00"}}