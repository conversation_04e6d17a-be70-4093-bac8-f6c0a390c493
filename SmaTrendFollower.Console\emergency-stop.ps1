#!/usr/bin/env pwsh
# Emergency Stop Script
# Immediately halts all trading and closes positions if needed

param(
    [switch]$ClosePositions,
    [switch]$Force
)

$ErrorActionPreference = "Stop"

Write-Host "🚨 EMERGENCY STOP ACTIVATED" -ForegroundColor Red
Write-Host "===========================" -ForegroundColor Red

# Set environment variables
$env:APCA_API_ENV = "live"
$env:APCA_API_KEY_ID_LIVE = "AKGBPW5HD8LVI5C6NJUJ"
$env:APCA_API_SECRET_KEY_LIVE = "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"

# Navigate to executable directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$exeDir = Join-Path $scriptDir "bin\Release\net8.0"
Set-Location $exeDir

Write-Host "🕐 Emergency Stop Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss ET')" -ForegroundColor Yellow

# Step 1: Stop the trading system
Write-Host "1️⃣  Stopping trading system..." -ForegroundColor White
try {
    # Find and kill SmaTrendFollower processes
    $processes = Get-Process -Name "SmaTrendFollower.Console" -ErrorAction SilentlyContinue
    if ($processes) {
        foreach ($proc in $processes) {
            Write-Host "   🔪 Killing process ID: $($proc.Id)" -ForegroundColor Yellow
            Stop-Process -Id $proc.Id -Force
        }
        Write-Host "   ✅ Trading system stopped" -ForegroundColor Green
    } else {
        Write-Host "   ℹ️  No trading processes found running" -ForegroundColor Cyan
    }
} catch {
    Write-Host "   ⚠️  Error stopping processes: $_" -ForegroundColor Yellow
}

# Step 2: Check current positions
Write-Host "2️⃣  Checking current positions..." -ForegroundColor White
try {
    $positionsResult = & .\SmaTrendFollower.Console.exe check-positions 2>&1
    Write-Host "   📊 Position Status:" -ForegroundColor Cyan
    Write-Host "   $positionsResult" -ForegroundColor White
} catch {
    Write-Host "   ⚠️  Could not check positions: $_" -ForegroundColor Yellow
}

# Step 3: Close positions if requested
if ($ClosePositions) {
    Write-Host "3️⃣  CLOSING ALL POSITIONS..." -ForegroundColor Red
    
    if (-not $Force) {
        Write-Host "   ⚠️  This will close ALL open positions at market prices" -ForegroundColor Yellow
        $confirmation = Read-Host "   Type 'CLOSE ALL' to confirm"
        if ($confirmation -ne "CLOSE ALL") {
            Write-Host "   ❌ Position closure cancelled" -ForegroundColor Yellow
            exit 0
        }
    }
    
    try {
        Write-Host "   🔄 Executing market sell orders for all positions..." -ForegroundColor Yellow
        $closeResult = & .\SmaTrendFollower.Console.exe close-all-positions --market-orders 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ All positions closed successfully" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Error closing positions: $closeResult" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ Failed to close positions: $_" -ForegroundColor Red
    }
} else {
    Write-Host "3️⃣  Positions left open (use -ClosePositions to close)" -ForegroundColor Yellow
}

# Step 4: Set emergency flag in Redis
Write-Host "4️⃣  Setting emergency stop flag..." -ForegroundColor White
try {
    redis-cli -h ************* -p 6379 set "emergency_stop" "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" EX 3600 2>$null
    Write-Host "   ✅ Emergency flag set (expires in 1 hour)" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not set emergency flag: $_" -ForegroundColor Yellow
}

# Step 5: Generate emergency report
Write-Host "5️⃣  Generating emergency report..." -ForegroundColor White
$reportFile = "emergency_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
try {
    $report = @"
EMERGENCY STOP REPORT
=====================
Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss ET')
Reason: Manual emergency stop activated
Positions Closed: $ClosePositions
Force Mode: $Force

System Status:
- Trading processes stopped
- Emergency flag set in Redis
- Report generated: $reportFile

Next Steps:
1. Review positions in Alpaca dashboard
2. Check system logs for any errors
3. Clear emergency flag when ready to resume: redis-cli -h ************* -p 6379 del emergency_stop
4. Restart system with: .\start-live-trading.ps1

"@
    
    $report | Out-File -FilePath $reportFile -Encoding UTF8
    Write-Host "   ✅ Report saved: $reportFile" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not generate report: $_" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🛑 EMERGENCY STOP COMPLETED" -ForegroundColor Red
Write-Host "============================" -ForegroundColor Red
Write-Host "📊 Check Alpaca dashboard for position status" -ForegroundColor Yellow
Write-Host "📋 Review report: $reportFile" -ForegroundColor Yellow
Write-Host "🔄 To resume trading: .\start-live-trading.ps1" -ForegroundColor Yellow
Write-Host ""
