#!/usr/bin/env pwsh
# Get the top 10 symbols from the current universe

Write-Host "🏆 Getting Top 10 Symbols from Current Universe" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

try {
    # Set environment variables
    $env:POLY_API_KEY = "stffXZCR90K0YULLv7zoUMq1k4JWiyHD"
    $env:APCA_API_KEY_ID_LIVE = "AKGBPW5HD8LVI5C6NJUJ"
    $env:APCA_API_SECRET_KEY_LIVE = "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"
    $env:APCA_API_ENV = "live"
    
    # Get the Redis data
    $output = & ".\bin\Release\net8.0\SmaTrendFollower.Console.exe" "check-redis" 2>&1
    
    # Find the content preview line
    $previewLine = $output | Where-Object { $_ -match "Content preview:" }
    
    if ($previewLine) {
        # Extract the JSON part
        $jsonStart = $previewLine.IndexOf('{"Candidates"')
        if ($jsonStart -ge 0) {
            $jsonPart = $previewLine.Substring($jsonStart)
            
            # Try to parse what we can see
            Write-Host "📊 Raw JSON Preview:" -ForegroundColor Yellow
            Write-Host $jsonPart -ForegroundColor Gray
            
            # Extract symbol names from the visible part
            $symbols = [regex]::Matches($jsonPart, '"Symbol":"([^"]+)"') | ForEach-Object { $_.Groups[1].Value }
            
            if ($symbols.Count -gt 0) {
                Write-Host "`n🎯 Top Symbols Found:" -ForegroundColor Green
                for ($i = 0; $i -lt [Math]::Min($symbols.Count, 10); $i++) {
                    Write-Host "  #$($i + 1): $($symbols[$i])" -ForegroundColor White
                }
                
                Write-Host "`n🏆 #1 RANKED SYMBOL: $($symbols[0])" -ForegroundColor Green -BackgroundColor Black
            } else {
                Write-Host "❌ No symbols found in preview" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ No JSON data found in preview" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ No content preview found" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📝 Note: This shows the order symbols appear in Redis, which reflects the ranking algorithm" -ForegroundColor Yellow
