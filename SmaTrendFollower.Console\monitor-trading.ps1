#!/usr/bin/env pwsh
# Real-time Trading Monitor
# Displays live trading metrics and system health

param(
    [int]$RefreshSeconds = 30,
    [switch]$Compact
)

$ErrorActionPreference = "SilentlyContinue"

# Set environment variables
$env:APCA_API_ENV = "live"
$env:APCA_API_KEY_ID_LIVE = "AKGBPW5HD8LVI5C6NJUJ"
$env:APCA_API_SECRET_KEY_LIVE = "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"

# Navigate to executable directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$exeDir = Join-Path $scriptDir "bin\Release\net8.0"
Set-Location $exeDir

function Get-TradingStatus {
    try {
        $status = & .\SmaTrendFollower.Console.exe get-status --json 2>$null | ConvertFrom-Json
        return $status
    } catch {
        return $null
    }
}

function Get-PositionSummary {
    try {
        $positions = & .\SmaTrendFollower.Console.exe get-positions --json 2>$null | ConvertFrom-Json
        return $positions
    } catch {
        return $null
    }
}

function Get-SystemHealth {
    try {
        $health = & .\SmaTrendFollower.Console.exe get-health --json 2>$null | ConvertFrom-Json
        return $health
    } catch {
        return $null
    }
}

function Format-Currency($value) {
    if ($value -eq $null) { return "N/A" }
    return "${value:C2}"
}

function Format-Percentage($value) {
    if ($value -eq $null) { return "N/A" }
    return "${value:P2}"
}

function Get-ColorForPnL($value) {
    if ($value -eq $null) { return "White" }
    if ($value -gt 0) { return "Green" }
    if ($value -lt 0) { return "Red" }
    return "Yellow"
}

Write-Host "🖥️  SMATRENDFOLLOWER LIVE TRADING MONITOR" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "🔄 Refresh Rate: $RefreshSeconds seconds" -ForegroundColor Cyan
Write-Host "⏹️  Press Ctrl+C to stop monitoring" -ForegroundColor Yellow
Write-Host ""

$startTime = Get-Date
$iteration = 0

while ($true) {
    $iteration++
    $currentTime = Get-Date
    $uptime = $currentTime - $startTime
    
    Clear-Host
    
    # Header
    Write-Host "🖥️  SMATRENDFOLLOWER LIVE TRADING MONITOR" -ForegroundColor Green
    Write-Host "==========================================" -ForegroundColor Green
    Write-Host "🕐 Time: $($currentTime.ToString('yyyy-MM-dd HH:mm:ss ET'))" -ForegroundColor White
    Write-Host "⏱️  Monitor Uptime: $($uptime.Hours)h $($uptime.Minutes)m $($uptime.Seconds)s" -ForegroundColor Cyan
    Write-Host "🔄 Refresh #$iteration (every $RefreshSeconds sec)" -ForegroundColor Cyan
    Write-Host ""
    
    # Market Status
    $marketOpen = Get-Date -Hour 9 -Minute 30 -Second 0
    $marketClose = Get-Date -Hour 16 -Minute 0 -Second 0
    
    if ($currentTime -ge $marketOpen -and $currentTime -le $marketClose) {
        Write-Host "📈 MARKET STATUS: OPEN" -ForegroundColor Green
        $timeToClose = $marketClose - $currentTime
        Write-Host "⏰ Time to Close: $($timeToClose.Hours)h $($timeToClose.Minutes)m" -ForegroundColor Green
    } else {
        Write-Host "📉 MARKET STATUS: CLOSED" -ForegroundColor Red
        if ($currentTime -lt $marketOpen) {
            $timeToOpen = $marketOpen - $currentTime
            Write-Host "⏰ Time to Open: $($timeToOpen.Hours)h $($timeToOpen.Minutes)m" -ForegroundColor Yellow
        }
    }
    Write-Host ""
    
    # Trading Status
    Write-Host "📊 TRADING STATUS" -ForegroundColor Yellow
    Write-Host "=================" -ForegroundColor Yellow
    
    $status = Get-TradingStatus
    if ($status) {
        Write-Host "🤖 System Status: $($status.SystemStatus)" -ForegroundColor $(if ($status.SystemStatus -eq "Running") { "Green" } else { "Red" })
        Write-Host "💰 Account Balance: $(Format-Currency $status.AccountBalance)" -ForegroundColor White
        Write-Host "💵 Buying Power: $(Format-Currency $status.BuyingPower)" -ForegroundColor White
        Write-Host "📈 Day P&L: $(Format-Currency $status.DayPnL)" -ForegroundColor $(Get-ColorForPnL $status.DayPnL)
        Write-Host "📊 Total P&L: $(Format-Currency $status.TotalPnL)" -ForegroundColor $(Get-ColorForPnL $status.TotalPnL)
        Write-Host "🎯 Win Rate: $(Format-Percentage $status.WinRate)" -ForegroundColor White
        Write-Host "🔢 Signals Generated: $($status.SignalsGenerated)" -ForegroundColor White
        Write-Host "⚡ Last Signal Time: $($status.LastSignalTime)" -ForegroundColor White
    } else {
        Write-Host "❌ Could not retrieve trading status" -ForegroundColor Red
    }
    Write-Host ""
    
    # Positions Summary
    Write-Host "📋 POSITIONS SUMMARY" -ForegroundColor Yellow
    Write-Host "====================" -ForegroundColor Yellow
    
    $positions = Get-PositionSummary
    if ($positions -and $positions.Count -gt 0) {
        Write-Host "📊 Open Positions: $($positions.Count)/8" -ForegroundColor White
        
        foreach ($pos in $positions | Select-Object -First 5) {
            $pnlColor = Get-ColorForPnL $pos.UnrealizedPnL
            Write-Host "   $($pos.Symbol): $($pos.Qty) @ $(Format-Currency $pos.AvgPrice) | P&L: $(Format-Currency $pos.UnrealizedPnL)" -ForegroundColor $pnlColor
        }
        
        if ($positions.Count -gt 5) {
            Write-Host "   ... and $($positions.Count - 5) more positions" -ForegroundColor Gray
        }
    } else {
        Write-Host "📭 No open positions" -ForegroundColor Gray
    }
    Write-Host ""
    
    # System Health
    Write-Host "🏥 SYSTEM HEALTH" -ForegroundColor Yellow
    Write-Host "================" -ForegroundColor Yellow
    
    $health = Get-SystemHealth
    if ($health) {
        Write-Host "💾 Memory Usage: $($health.MemoryUsageMB) MB" -ForegroundColor $(if ($health.MemoryUsageMB -gt 2000) { "Red" } elseif ($health.MemoryUsageMB -gt 1000) { "Yellow" } else { "Green" })
        Write-Host "🗄️  Database Status: $($health.DatabaseStatus)" -ForegroundColor $(if ($health.DatabaseStatus -eq "Healthy") { "Green" } else { "Red" })
        Write-Host "🔄 Redis Status: $($health.RedisStatus)" -ForegroundColor $(if ($health.RedisStatus -eq "Connected") { "Green" } else { "Red" })
        Write-Host "📡 Market Data: $($health.MarketDataStatus)" -ForegroundColor $(if ($health.MarketDataStatus -eq "Connected") { "Green" } else { "Red" })
        Write-Host "⚡ Signal Gen Speed: $($health.LastSignalGenTimeMs) ms" -ForegroundColor $(if ($health.LastSignalGenTimeMs -gt 300000) { "Red" } elseif ($health.LastSignalGenTimeMs -gt 60000) { "Yellow" } else { "Green" })
    } else {
        Write-Host "❌ Could not retrieve system health" -ForegroundColor Red
    }
    Write-Host ""
    
    # Quick Actions
    Write-Host "🎮 QUICK ACTIONS" -ForegroundColor Yellow
    Write-Host "================" -ForegroundColor Yellow
    Write-Host "🚨 Emergency Stop: .\emergency-stop.ps1" -ForegroundColor Red
    Write-Host "🛑 Stop + Close Positions: .\emergency-stop.ps1 -ClosePositions" -ForegroundColor Red
    Write-Host "📊 Detailed Logs: Get-Content logs\trading.log -Tail 20" -ForegroundColor Cyan
    Write-Host ""
    
    # Wait for next refresh
    Start-Sleep -Seconds $RefreshSeconds
}
