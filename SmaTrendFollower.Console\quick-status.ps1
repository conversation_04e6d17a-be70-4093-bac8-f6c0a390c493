#!/usr/bin/env pwsh
# Quick Status Check - Mobile Friendly
# Provides essential trading status in compact format

$ErrorActionPreference = "SilentlyContinue"

# Set environment variables
$env:APCA_API_ENV = "live"
$env:APCA_API_KEY_ID_LIVE = "AKGBPW5HD8LVI5C6NJUJ"
$env:APCA_API_SECRET_KEY_LIVE = "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"

# Navigate to executable directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$exeDir = Join-Path $scriptDir "bin\Release\net8.0"
Set-Location $exeDir

$currentTime = Get-Date

Write-Host "📱 QUICK STATUS - $($currentTime.ToString('HH:mm:ss'))" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Market Status
$marketOpen = Get-Date -Hour 9 -Minute 30 -Second 0
$marketClose = Get-Date -Hour 16 -Minute 0 -Second 0

if ($currentTime -ge $marketOpen -and $currentTime -le $marketClose) {
    Write-Host "📈 MARKET: OPEN" -ForegroundColor Green
} else {
    Write-Host "📉 MARKET: CLOSED" -ForegroundColor Red
}

# System Status
$processes = Get-Process -Name "SmaTrendFollower.Console" -ErrorAction SilentlyContinue
if ($processes) {
    Write-Host "🤖 SYSTEM: RUNNING" -ForegroundColor Green
} else {
    Write-Host "🤖 SYSTEM: STOPPED" -ForegroundColor Red
}

# Account Status
try {
    $accountResult = & .\SmaTrendFollower.Console.exe get-account-summary --json 2>$null | ConvertFrom-Json
    if ($accountResult) {
        Write-Host "💰 BALANCE: $($accountResult.Balance)" -ForegroundColor White
        Write-Host "📊 DAY P&L: $($accountResult.DayPnL)" -ForegroundColor $(if ($accountResult.DayPnL -gt 0) { "Green" } elseif ($accountResult.DayPnL -lt 0) { "Red" } else { "Yellow" })
        Write-Host "📋 POSITIONS: $($accountResult.PositionCount)/8" -ForegroundColor White
    }
} catch {
    Write-Host "💰 ACCOUNT: ERROR" -ForegroundColor Red
}

# Recent Activity
try {
    $lastTrade = & .\SmaTrendFollower.Console.exe get-last-trade --json 2>$null | ConvertFrom-Json
    if ($lastTrade) {
        Write-Host "🔄 LAST TRADE: $($lastTrade.Symbol) @ $($lastTrade.Time)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "🔄 LAST TRADE: N/A" -ForegroundColor Gray
}

# Alerts
try {
    $alerts = & .\SmaTrendFollower.Console.exe get-alerts --count 3 --json 2>$null | ConvertFrom-Json
    if ($alerts -and $alerts.Count -gt 0) {
        Write-Host "🚨 ALERTS:" -ForegroundColor Yellow
        foreach ($alert in $alerts) {
            Write-Host "   $($alert.Message)" -ForegroundColor Yellow
        }
    }
} catch {
    # No alerts or error - silent
}

Write-Host ""
Write-Host "📱 For full monitor: .\monitor-trading.ps1" -ForegroundColor Cyan
Write-Host "🚨 Emergency stop: .\emergency-stop.ps1" -ForegroundColor Red
