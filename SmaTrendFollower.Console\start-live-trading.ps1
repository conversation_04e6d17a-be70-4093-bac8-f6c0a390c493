#!/usr/bin/env pwsh
# Live Trading Startup Script
# Prepares and launches SmaTrendFollower for live trading

param(
    [switch]$SkipChecks,
    [switch]$Verbose,
    [int]$MaxSymbols = 0
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 SMATRENDFOLLOWER LIVE TRADING STARTUP" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host "⚠️  LIVE TRADING MODE - REAL MONEY AT RISK" -ForegroundColor Red
Write-Host ""

# Set environment variables for live trading
$env:APCA_API_ENV = "live"
$env:APCA_API_KEY_ID_LIVE = "AKGBPW5HD8LVI5C6NJUJ"
$env:APCA_API_SECRET_KEY_LIVE = "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"
$env:POLY_API_KEY = "stffXZCR90K0YULLv7zoUMq1k4JWiyHD"

# Navigate to executable directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$exeDir = Join-Path $scriptDir "bin\Release\net8.0"
Set-Location $exeDir

Write-Host "📍 Working Directory: $exeDir" -ForegroundColor Cyan
Write-Host "🕐 Current Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss ET')" -ForegroundColor Cyan
Write-Host ""

if (-not $SkipChecks) {
    Write-Host "🔍 PRE-FLIGHT SYSTEM CHECKS" -ForegroundColor Yellow
    Write-Host "============================" -ForegroundColor Yellow
    
    # Check 1: Database connectivity
    Write-Host "1️⃣  Testing PostgreSQL connection..." -ForegroundColor White
    try {
        $dbResult = & .\SmaTrendFollower.Console.exe test-postgres 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ PostgreSQL: Connected and operational" -ForegroundColor Green
        } else {
            Write-Host "   ❌ PostgreSQL: Connection failed" -ForegroundColor Red
            Write-Host "   Error: $dbResult" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "   ❌ PostgreSQL: Test failed - $_" -ForegroundColor Red
        exit 1
    }
    
    # Check 2: Account access
    Write-Host "2️⃣  Verifying Alpaca live account access..." -ForegroundColor White
    try {
        $accountResult = & .\SmaTrendFollower.Console.exe check-account 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ Alpaca: Live account accessible" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Alpaca: Account access failed" -ForegroundColor Red
            Write-Host "   Error: $accountResult" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "   ❌ Alpaca: Account test failed - $_" -ForegroundColor Red
        exit 1
    }
    
    # Check 3: Market data
    Write-Host "3️⃣  Testing Polygon market data feed..." -ForegroundColor White
    try {
        $marketResult = & .\SmaTrendFollower.Console.exe test-market-data 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ Polygon: Market data feed active" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Polygon: Market data failed" -ForegroundColor Red
            Write-Host "   Error: $marketResult" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "   ❌ Polygon: Market data test failed - $_" -ForegroundColor Red
        exit 1
    }
    
    # Check 4: System health
    Write-Host "4️⃣  Running system health check..." -ForegroundColor White
    try {
        $healthResult = & .\SmaTrendFollower.Console.exe check-system-health 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ System: All components healthy" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  System: Health check warnings" -ForegroundColor Yellow
            if ($Verbose) {
                Write-Host "   Details: $healthResult" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Host "   ❌ System: Health check failed - $_" -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
    Write-Host "✅ ALL PRE-FLIGHT CHECKS PASSED" -ForegroundColor Green
    Write-Host ""
}

# Market hours check
$currentTime = Get-Date
$marketOpen = Get-Date -Hour 9 -Minute 30 -Second 0
$marketClose = Get-Date -Hour 16 -Minute 0 -Second 0

if ($currentTime -lt $marketOpen) {
    $timeToOpen = $marketOpen - $currentTime
    Write-Host "⏰ Market opens in: $($timeToOpen.Hours)h $($timeToOpen.Minutes)m" -ForegroundColor Yellow
    Write-Host "   System will start and wait for market open" -ForegroundColor Yellow
} elseif ($currentTime -gt $marketClose) {
    Write-Host "⏰ Market is closed (after 4:00 PM ET)" -ForegroundColor Yellow
    Write-Host "   System will start in after-hours mode" -ForegroundColor Yellow
} else {
    Write-Host "⏰ Market is OPEN - Live trading will begin immediately" -ForegroundColor Green
}

Write-Host ""
Write-Host "🚀 LAUNCHING LIVE TRADING SYSTEM" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Build launch command
$launchArgs = @("start-trading")
if ($MaxSymbols -gt 0) {
    $launchArgs += "--max-symbols=$MaxSymbols"
}
if ($Verbose) {
    $launchArgs += "--verbose"
}

Write-Host "📋 Launch Command: .\SmaTrendFollower.Console.exe $($launchArgs -join ' ')" -ForegroundColor Cyan
Write-Host "📊 Account Balance: $17,066.39" -ForegroundColor Cyan
Write-Host "🛡️  Max Daily Loss: $682.68 (4%)" -ForegroundColor Cyan
Write-Host "📈 Max Positions: 8" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚠️  Press Ctrl+C to stop trading at any time" -ForegroundColor Yellow
Write-Host ""

# Final confirmation for live trading
Write-Host "⚠️  FINAL CONFIRMATION REQUIRED" -ForegroundColor Red
Write-Host "You are about to start LIVE TRADING with REAL MONEY" -ForegroundColor Red
Write-Host "Account Balance: $17,066.39" -ForegroundColor Yellow
Write-Host "Max Daily Loss: $682.68" -ForegroundColor Yellow
Write-Host ""
$confirmation = Read-Host "Type 'START LIVE TRADING' to confirm"
if ($confirmation -ne "START LIVE TRADING") {
    Write-Host "❌ Live trading cancelled" -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "🚀 STARTING LIVE TRADING..." -ForegroundColor Green

# Launch the trading system
try {
    & .\SmaTrendFollower.Console.exe @launchArgs
} catch {
    Write-Host "❌ Trading system crashed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🏁 Trading session ended" -ForegroundColor Yellow
