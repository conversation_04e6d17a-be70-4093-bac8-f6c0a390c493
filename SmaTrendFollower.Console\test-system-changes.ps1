#!/usr/bin/env pwsh
# Comprehensive Test Suite for SmaTrendFollower System Changes
# Tests all optimizations and fixes implemented today

param(
    [switch]$SkipBuild,
    [switch]$QuickTest,
    [switch]$Verbose
)

$ErrorActionPreference = "Continue"
$WarningPreference = "Continue"

# Test results tracking
$TestResults = @{
    Passed = 0
    Failed = 0
    Warnings = 0
    Tests = @()
}

function Write-TestResult {
    param($TestName, $Status, $Message, $Details = "")
    
    $result = @{
        Name = $TestName
        Status = $Status
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
    
    $TestResults.Tests += $result
    
    switch ($Status) {
        "PASS" { 
            $TestResults.Passed++
            Write-Host "✅ $TestName - $Message" -ForegroundColor Green
        }
        "FAIL" { 
            $TestResults.Failed++
            Write-Host "❌ $TestName - $Message" -ForegroundColor Red
            if ($Details) { Write-Host "   Details: $Details" -ForegroundColor Yellow }
        }
        "WARN" { 
            $TestResults.Warnings++
            Write-Host "⚠️  $TestName - $Message" -ForegroundColor Yellow
        }
    }
}

function Test-JsonConfiguration {
    Write-Host "`n🔧 Testing Configuration Files..." -ForegroundColor Cyan
    
    # Test appsettings.LocalProd.json
    try {
        $config = Get-Content "appsettings.LocalProd.json" | ConvertFrom-Json
        Write-TestResult "JSON Validity" "PASS" "appsettings.LocalProd.json is valid JSON"
        
        # Test specific optimizations
        if ($config.AdaptiveRateLimit.Providers.Polygon.InitialLimit -eq 20) {
            Write-TestResult "Rate Limiting Config" "PASS" "Polygon rate limit optimized to 20"
        } else {
            Write-TestResult "Rate Limiting Config" "FAIL" "Polygon rate limit not optimized"
        }
        
        if ($config.OptimizedUniverse.MaxConcurrentRequests -eq 25) {
            Write-TestResult "Universe Config" "PASS" "Universe concurrency optimized to 25"
        } else {
            Write-TestResult "Universe Config" "FAIL" "Universe concurrency not optimized"
        }
        
        if ($config.RobustSignal.MaxConcurrentGenerations -eq 4) {
            Write-TestResult "Signal Config" "PASS" "Signal generation optimized to 4 concurrent"
        } else {
            Write-TestResult "Signal Config" "FAIL" "Signal generation not optimized"
        }
        
        if ($config.ConcurrentProcessing) {
            Write-TestResult "Concurrent Processing" "PASS" "ConcurrentProcessing configuration present"
        } else {
            Write-TestResult "Concurrent Processing" "FAIL" "ConcurrentProcessing configuration missing"
        }
        
    } catch {
        Write-TestResult "JSON Validity" "FAIL" "appsettings.LocalProd.json is invalid" $_.Exception.Message
    }
}

function Test-ServiceFiles {
    Write-Host "`n📁 Testing Service Files..." -ForegroundColor Cyan
    
    $requiredFiles = @(
        "Services/OptimizedSignalGenerationService.cs",
        "Services/ConcurrentProcessingManager.cs",
        "Services/TradingMonitoringService.cs",
        "Services/ComprehensiveMonitoringService.cs"
    )
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-TestResult "File Existence" "PASS" "$file exists"
            
            # Check for key optimizations in the file
            $content = Get-Content $file -Raw
            if ($file -like "*OptimizedSignal*" -and $content -match "CacheValidityPeriod") {
                Write-TestResult "Optimization Features" "PASS" "$file contains caching logic"
            }
            if ($file -like "*ConcurrentProcessing*" -and $content -match "AdaptiveControl") {
                Write-TestResult "Optimization Features" "PASS" "$file contains adaptive processing"
            }
        } else {
            Write-TestResult "File Existence" "FAIL" "$file missing"
        }
    }
}

function Test-BuildCompilation {
    if ($SkipBuild) {
        Write-Host "`n⏭️  Skipping build test..." -ForegroundColor Yellow
        return
    }
    
    Write-Host "`n🔨 Testing Build Compilation..." -ForegroundColor Cyan
    
    try {
        $buildResult = dotnet build SmaTrendFollower.Console --configuration Release --verbosity quiet 2>&1
        $buildExitCode = $LASTEXITCODE
        
        if ($buildExitCode -eq 0) {
            Write-TestResult "Build Compilation" "PASS" "Project builds successfully"
        } else {
            Write-TestResult "Build Compilation" "FAIL" "Build failed" ($buildResult -join "`n")
        }
    } catch {
        Write-TestResult "Build Compilation" "FAIL" "Build process error" $_.Exception.Message
    }
}

function Test-SystemStartup {
    Write-Host "`n🚀 Testing System Startup..." -ForegroundColor Cyan
    
    if (-not (Test-Path "bin/Release/net8.0/SmaTrendFollower.Console.exe")) {
        Write-TestResult "Startup Test" "FAIL" "Executable not found - build required"
        return
    }
    
    try {
        # Set environment variables for test
        $env:POLY_API_KEY = "test_key"
        $env:APCA_API_KEY_ID_LIVE = "test_key"
        $env:APCA_API_SECRET_KEY_LIVE = "test_secret"
        $env:APCA_API_ENV = "paper"
        
        # Test startup with timeout
        $startupTest = Start-Process -FilePath "bin/Release/net8.0/SmaTrendFollower.Console.exe" -ArgumentList "--validate-environment", "--no-confirm" -PassThru -WindowStyle Hidden
        
        # Wait up to 30 seconds for startup
        $timeout = 30
        $elapsed = 0
        while (-not $startupTest.HasExited -and $elapsed -lt $timeout) {
            Start-Sleep -Seconds 1
            $elapsed++
        }
        
        if (-not $startupTest.HasExited) {
            $startupTest.Kill()
            Write-TestResult "Startup Test" "WARN" "Startup test timed out after 30 seconds (may be normal for full initialization)"
        } else {
            if ($startupTest.ExitCode -eq 0) {
                Write-TestResult "Startup Test" "PASS" "System starts successfully"
            } else {
                Write-TestResult "Startup Test" "FAIL" "System startup failed with exit code $($startupTest.ExitCode)"
            }
        }
    } catch {
        Write-TestResult "Startup Test" "FAIL" "Startup test error" $_.Exception.Message
    }
}

function Test-PerformanceConfigurations {
    Write-Host "`n⚡ Testing Performance Configurations..." -ForegroundColor Cyan
    
    try {
        $config = Get-Content "appsettings.LocalProd.json" | ConvertFrom-Json
        
        # Test rate limiting optimizations
        $alpacaLimit = $config.AdaptiveRateLimit.Providers.Alpaca.InitialLimit
        $polygonLimit = $config.AdaptiveRateLimit.Providers.Polygon.InitialLimit
        
        if ($alpacaLimit -le 20 -and $polygonLimit -le 25) {
            Write-TestResult "Rate Limits" "PASS" "Conservative rate limits configured (Alpaca: $alpacaLimit, Polygon: $polygonLimit)"
        } else {
            Write-TestResult "Rate Limits" "WARN" "Rate limits may be too aggressive (Alpaca: $alpacaLimit, Polygon: $polygonLimit)"
        }
        
        # Test timeout optimizations
        $adaptiveTimeout = $config.RobustSignal.AdaptiveGenerationTimeout
        if ($adaptiveTimeout -eq "00:00:45") {
            Write-TestResult "Timeouts" "PASS" "Optimized timeouts configured (45 seconds)"
        } else {
            Write-TestResult "Timeouts" "WARN" "Timeout not optimized: $adaptiveTimeout"
        }
        
        # Test concurrent processing limits
        $maxConcurrent = $config.OptimizedUniverse.MaxConcurrentRequests
        if ($maxConcurrent -le 30) {
            Write-TestResult "Concurrency" "PASS" "Conservative concurrency configured ($maxConcurrent)"
        } else {
            Write-TestResult "Concurrency" "WARN" "Concurrency may be too high ($maxConcurrent)"
        }
        
    } catch {
        Write-TestResult "Performance Config" "FAIL" "Error reading performance configuration" $_.Exception.Message
    }
}

function Test-MonitoringServices {
    Write-Host "`n📊 Testing Monitoring Services..." -ForegroundColor Cyan
    
    # Check if monitoring services are properly configured
    $serviceConfigFile = "Configuration/ServiceConfiguration.cs"
    if (Test-Path $serviceConfigFile) {
        $content = Get-Content $serviceConfigFile -Raw
        
        if ($content -match "TradingMonitoringService") {
            Write-TestResult "Trading Monitoring" "PASS" "TradingMonitoringService registered"
        } else {
            Write-TestResult "Trading Monitoring" "FAIL" "TradingMonitoringService not registered"
        }
        
        if ($content -match "ComprehensiveMonitoringService") {
            Write-TestResult "Comprehensive Monitoring" "PASS" "ComprehensiveMonitoringService registered"
        } else {
            Write-TestResult "Comprehensive Monitoring" "FAIL" "ComprehensiveMonitoringService not registered"
        }
        
        if ($content -match "ConcurrentProcessingManager") {
            Write-TestResult "Concurrent Processing" "PASS" "ConcurrentProcessingManager registered"
        } else {
            Write-TestResult "Concurrent Processing" "FAIL" "ConcurrentProcessingManager not registered"
        }
    } else {
        Write-TestResult "Service Configuration" "FAIL" "ServiceConfiguration.cs not found"
    }
}

function Show-TestSummary {
    Write-Host "`n" + "="*60 -ForegroundColor Cyan
    Write-Host "📋 TEST SUMMARY" -ForegroundColor Cyan
    Write-Host "="*60 -ForegroundColor Cyan
    
    Write-Host "✅ Passed: $($TestResults.Passed)" -ForegroundColor Green
    Write-Host "❌ Failed: $($TestResults.Failed)" -ForegroundColor Red
    Write-Host "⚠️  Warnings: $($TestResults.Warnings)" -ForegroundColor Yellow
    Write-Host "📊 Total Tests: $($TestResults.Tests.Count)" -ForegroundColor White
    
    $successRate = if ($TestResults.Tests.Count -gt 0) { 
        [math]::Round(($TestResults.Passed / $TestResults.Tests.Count) * 100, 1) 
    } else { 0 }
    
    Write-Host "🎯 Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })
    
    if ($TestResults.Failed -gt 0) {
        Write-Host "`n❌ FAILED TESTS:" -ForegroundColor Red
        $TestResults.Tests | Where-Object { $_.Status -eq "FAIL" } | ForEach-Object {
            Write-Host "   • $($_.Name): $($_.Message)" -ForegroundColor Red
        }
    }
    
    if ($TestResults.Warnings -gt 0) {
        Write-Host "`n⚠️  WARNINGS:" -ForegroundColor Yellow
        $TestResults.Tests | Where-Object { $_.Status -eq "WARN" } | ForEach-Object {
            Write-Host "   • $($_.Name): $($_.Message)" -ForegroundColor Yellow
        }
    }
    
    Write-Host "`n🚀 SYSTEM READINESS:" -ForegroundColor Cyan
    if ($TestResults.Failed -eq 0) {
        Write-Host "✅ System is ready for live trading!" -ForegroundColor Green
    } elseif ($TestResults.Failed -le 2) {
        Write-Host "⚠️  System has minor issues but may be ready for trading" -ForegroundColor Yellow
    } else {
        Write-Host "❌ System has significant issues - review before trading" -ForegroundColor Red
    }
}

# Main execution
Write-Host "🧪 SmaTrendFollower System Validation Test Suite" -ForegroundColor Cyan
Write-Host "Testing all changes made on $(Get-Date -Format 'yyyy-MM-dd')" -ForegroundColor White
Write-Host ""

# Run all tests
Test-JsonConfiguration
Test-ServiceFiles
Test-PerformanceConfigurations
Test-MonitoringServices

if (-not $QuickTest) {
    Test-BuildCompilation
    Test-SystemStartup
}

Show-TestSummary

# Exit with appropriate code
if ($TestResults.Failed -eq 0) {
    exit 0
} else {
    exit 1
}
