#!/usr/bin/env pwsh
# Simple validation script for SmaTrendFollower system changes

Write-Host "🧪 SmaTrendFollower System Validation" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

$passed = 0
$failed = 0

function Test-Item {
    param($Name, $Condition, $Message)
    if ($Condition) {
        Write-Host "✅ $Name - $Message" -ForegroundColor Green
        $script:passed++
    } else {
        Write-Host "❌ $Name - $Message" -ForegroundColor Red
        $script:failed++
    }
}

# Test 1: Configuration File Validity
Write-Host "`n📋 Testing Configuration..." -ForegroundColor Yellow
try {
    $config = Get-Content "appsettings.LocalProd.json" | ConvertFrom-Json
    Test-Item "JSON Config" $true "appsettings.LocalProd.json is valid JSON"
    
    # Test specific optimizations
    Test-Item "Rate Limiting" ($config.AdaptiveRateLimit.Providers.Polygon.InitialLimit -eq 20) "Polygon rate limit optimized to 20"
    Test-Item "Universe Config" ($config.OptimizedUniverse.MaxConcurrentRequests -eq 25) "Universe concurrency optimized to 25"
    Test-Item "Signal Config" ($config.RobustSignal.MaxConcurrentGenerations -eq 4) "Signal generation optimized to 4"
    Test-Item "New Config Section" ($config.ConcurrentProcessing -ne $null) "ConcurrentProcessing configuration added"
} catch {
    Test-Item "JSON Config" $false "Configuration file has errors: $($_.Exception.Message)"
}

# Test 2: Service Files
Write-Host "`n📁 Testing Service Files..." -ForegroundColor Yellow
Test-Item "Optimized Signal Service" (Test-Path "Services/OptimizedSignalGenerationService.cs") "OptimizedSignalGenerationService.cs exists"
Test-Item "Concurrent Processing" (Test-Path "Services/ConcurrentProcessingManager.cs") "ConcurrentProcessingManager.cs exists"
Test-Item "Trading Monitoring" (Test-Path "Services/TradingMonitoringService.cs") "TradingMonitoringService.cs exists"
Test-Item "Comprehensive Monitoring" (Test-Path "Services/ComprehensiveMonitoringService.cs") "ComprehensiveMonitoringService.cs exists"

# Test 3: Service Registration
Write-Host "`n🔧 Testing Service Registration..." -ForegroundColor Yellow
if (Test-Path "Configuration/ServiceConfiguration.cs") {
    $serviceConfig = Get-Content "Configuration/ServiceConfiguration.cs" -Raw
    Test-Item "Trading Monitor Registration" ($serviceConfig -match "TradingMonitoringService") "TradingMonitoringService registered"
    Test-Item "Comprehensive Monitor Registration" ($serviceConfig -match "ComprehensiveMonitoringService") "ComprehensiveMonitoringService registered"
    Test-Item "Concurrent Processing Registration" ($serviceConfig -match "ConcurrentProcessingManager") "ConcurrentProcessingManager registered"
} else {
    Test-Item "Service Configuration" $false "ServiceConfiguration.cs not found"
}

# Test 4: Performance Optimizations
Write-Host "`n⚡ Testing Performance Optimizations..." -ForegroundColor Yellow
try {
    $config = Get-Content "appsettings.LocalProd.json" | ConvertFrom-Json
    
    # Rate limiting optimizations
    $alpacaLimit = $config.AdaptiveRateLimit.Providers.Alpaca.InitialLimit
    $polygonLimit = $config.AdaptiveRateLimit.Providers.Polygon.InitialLimit
    Test-Item "Conservative Rate Limits" ($alpacaLimit -le 20 -and $polygonLimit -le 25) "Rate limits are conservative (Alpaca: $alpacaLimit, Polygon: $polygonLimit)"
    
    # Timeout optimizations
    Test-Item "Optimized Timeouts" ($config.RobustSignal.AdaptiveGenerationTimeout -eq "00:00:45") "Signal generation timeout optimized to 45 seconds"
    
    # Concurrency optimizations
    Test-Item "Optimized Concurrency" ($config.OptimizedUniverse.MaxConcurrentRequests -le 30) "Universe concurrency is conservative ($($config.OptimizedUniverse.MaxConcurrentRequests))"
    
} catch {
    Test-Item "Performance Config" $false "Error reading performance configuration"
}

# Test 5: File Integrity
Write-Host "`n📄 Testing File Integrity..." -ForegroundColor Yellow
$criticalFiles = @(
    "appsettings.LocalProd.json",
    "Configuration/ServiceConfiguration.cs",
    "Services/TradingMonitoringService.cs",
    "Services/ComprehensiveMonitoringService.cs"
)

foreach ($file in $criticalFiles) {
    Test-Item "File Integrity" (Test-Path $file) "$file exists"
}

# Test 6: Configuration Consistency
Write-Host "`n🔄 Testing Configuration Consistency..." -ForegroundColor Yellow
try {
    $config = Get-Content "appsettings.LocalProd.json" | ConvertFrom-Json
    
    # Check for duplicate keys (should not throw error if valid)
    Test-Item "No Duplicate Keys" $true "Configuration has no duplicate keys"
    
    # Check for required sections
    $requiredSections = @("AdaptiveRateLimit", "OptimizedUniverse", "RobustSignal", "ConcurrentProcessing")
    foreach ($section in $requiredSections) {
        $hasSection = $config.PSObject.Properties.Name -contains $section
        Test-Item "Required Section" $hasSection "$section configuration section exists"
    }
    
} catch {
    Test-Item "Configuration Consistency" $false "Configuration consistency check failed"
}

# Summary
Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "📊 VALIDATION SUMMARY" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan
Write-Host "✅ Passed: $passed" -ForegroundColor Green
Write-Host "❌ Failed: $failed" -ForegroundColor Red

$total = $passed + $failed
$successRate = if ($total -gt 0) { [math]::Round(($passed / $total) * 100, 1) } else { 0 }
Write-Host "🎯 Success Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 75) { "Yellow" } else { "Red" })

Write-Host "`n🚀 SYSTEM STATUS:" -ForegroundColor Cyan
if ($failed -eq 0) {
    Write-Host "✅ All validations passed! System is ready for trading." -ForegroundColor Green
    exit 0
} elseif ($failed -le 2) {
    Write-Host "⚠️  Minor issues detected. Review before live trading." -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "❌ Significant issues detected. Fix before trading." -ForegroundColor Red
    exit 2
}
