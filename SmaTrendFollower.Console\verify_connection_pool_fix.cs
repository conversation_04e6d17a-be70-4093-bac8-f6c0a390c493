using Npgsql;
using SmaTrendFollower.Services;

namespace SmaTrendFollower;

/// <summary>
/// Verification utility to test PostgreSQL connection pool optimization
/// </summary>
public static class VerifyConnectionPoolFix
{
    public static void VerifyOptimizedConnectionStrings()
    {
        System.Console.WriteLine("🔍 Verifying PostgreSQL Connection Pool Optimization...");
        System.Console.WriteLine();

        // Test base connection string
        var baseConnectionString = "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;";

        System.Console.WriteLine("📋 Base Connection String:");
        System.Console.WriteLine($"   {baseConnectionString}");
        System.Console.WriteLine();

        // Test optimized connection string using the service
        var dbConfigService = new DatabaseConfigurationService(null!, null!);
        var optimizedConnectionString = dbConfigService.GetOptimizedPostgreSQLConnectionString(baseConnectionString);

        System.Console.WriteLine("⚡ Optimized Connection String:");
        System.Console.WriteLine($"   {optimizedConnectionString}");
        System.Console.WriteLine();

        // Parse and verify the optimized settings
        var builder = new NpgsqlConnectionStringBuilder(optimizedConnectionString);

        System.Console.WriteLine("🔧 Verified Connection Pool Settings:");
        System.Console.WriteLine($"   MaxPoolSize: {builder.MaxPoolSize} (Expected: 200)");
        System.Console.WriteLine($"   MinPoolSize: {builder.MinPoolSize} (Expected: 20)");
        System.Console.WriteLine($"   Pooling: {builder.Pooling} (Expected: True)");
        System.Console.WriteLine($"   ConnectionIdleLifetime: {builder.ConnectionIdleLifetime} (Expected: 300)");
        System.Console.WriteLine($"   CommandTimeout: {builder.CommandTimeout} (Expected: 45)");
        System.Console.WriteLine($"   Timeout: {builder.Timeout} (Expected: 45)");
        System.Console.WriteLine($"   Multiplexing: {builder.Multiplexing} (Expected: True)");
        System.Console.WriteLine($"   ReadBufferSize: {builder.ReadBufferSize} (Expected: 8192)");
        System.Console.WriteLine($"   WriteBufferSize: {builder.WriteBufferSize} (Expected: 8192)");
        System.Console.WriteLine();

        // Verify critical settings
        var issues = new List<string>();
        
        if (builder.MaxPoolSize != 200)
            issues.Add($"MaxPoolSize is {builder.MaxPoolSize}, expected 200");
            
        if (builder.MinPoolSize != 20)
            issues.Add($"MinPoolSize is {builder.MinPoolSize}, expected 20");
            
        if (!builder.Pooling)
            issues.Add("Pooling is disabled, expected enabled");
            
        if (!builder.Multiplexing)
            issues.Add("Multiplexing is disabled, expected enabled");

        if (issues.Any())
        {
            System.Console.WriteLine("❌ Issues Found:");
            foreach (var issue in issues)
            {
                System.Console.WriteLine($"   - {issue}");
            }
        }
        else
        {
            System.Console.WriteLine("✅ All connection pool settings are correctly optimized!");
            System.Console.WriteLine("   The fix should resolve the 'connection pool exhausted' errors.");
        }

        System.Console.WriteLine();
        System.Console.WriteLine("📊 Expected Impact:");
        System.Console.WriteLine("   - 2x increase in max connections (100 → 200)");
        System.Console.WriteLine("   - 20x increase in min connections (1 → 20)");
        System.Console.WriteLine("   - Connection multiplexing enabled for better throughput");
        System.Console.WriteLine("   - Optimized buffer sizes for high-frequency trading");
        System.Console.WriteLine("   - Extended timeouts (30s → 45s) for network resilience");
        System.Console.WriteLine("   - TCP keepalive enabled to detect dead connections");
        System.Console.WriteLine();
        System.Console.WriteLine("🎯 This should eliminate Discord errors:");
        System.Console.WriteLine("   'Database error caching bars for \"AVNW\" \"Day\": connection pool exhausted'");
        System.Console.WriteLine("   'Database error caching bars for \"BBW\" \"Day\": Exception while reading from stream'");
    }
}
