using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using NSubstitute;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Guards;

public class AnomalyDetectorTests
{
    [Fact]
    public void AnomalyDetectorService_CanBeCreated()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        // Act & Assert - should create without throwing
        var act = () => new AnomalyDetectorService(mockMux, NullLogger<AnomalyDetectorService>.Instance);
        act.Should().NotThrow();
    }

    [Fact]
    public void OnTrade_HandlesValidInputs_WithoutThrowing()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        var anom = new AnomalyDetectorService(mockMux, NullLogger<AnomalyDetectorService>.Instance);

        // Act & Assert - should process trades without throwing
        var act = () =>
        {
            for (int i = 0; i < 10; i++)
                anom.OnTrade("XYZ", 100m + i, System.DateTime.UtcNow.AddMilliseconds(i));
        };

        act.Should().NotThrow();
    }

    [Fact]
    public void OnQuote_HandlesValidInputs_WithoutThrowing()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        var anom = new AnomalyDetectorService(mockMux, NullLogger<AnomalyDetectorService>.Instance);

        // Act & Assert - should process quotes without throwing
        var act = () =>
        {
            for (int i = 0; i < 10; i++)
                anom.OnQuote("ABC", 100m, 100.10m, System.DateTime.UtcNow.AddMilliseconds(i));
        };

        act.Should().NotThrow();
    }

    [Fact]
    public void OnQuote_HandlesInvalidInputs_Gracefully()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        var anom = new AnomalyDetectorService(mockMux, NullLogger<AnomalyDetectorService>.Instance);

        // Act & Assert - should not throw exceptions with invalid inputs
        var act = () =>
        {
            anom.OnQuote("", 100m, 100.10m, System.DateTime.UtcNow);     // empty symbol
            anom.OnQuote("GHI", 0m, 100.10m, System.DateTime.UtcNow);    // zero bid
            anom.OnQuote("GHI", 100m, 0m, System.DateTime.UtcNow);       // zero ask
            anom.OnQuote("GHI", 100m, 99m, System.DateTime.UtcNow);      // ask < bid
        };

        act.Should().NotThrow();
    }

    [Fact]
    public void OnTrade_HandlesInvalidInputs_Gracefully()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        var anom = new AnomalyDetectorService(mockMux, NullLogger<AnomalyDetectorService>.Instance);

        // Act & Assert - should not throw exceptions with invalid inputs
        var act = () =>
        {
            anom.OnTrade("", 100m, System.DateTime.UtcNow);              // empty symbol
            anom.OnTrade("JKL", 0m, System.DateTime.UtcNow);             // zero price
            anom.OnTrade("JKL", -100m, System.DateTime.UtcNow);          // negative price
        };

        act.Should().NotThrow();
    }

    [Fact]
    public void AnomalyDetectorService_RequiresValidDependencies()
    {
        // Act & Assert - should allow null Redis (degraded mode) but require logger
        var act1 = () => new AnomalyDetectorService(null, NullLogger<AnomalyDetectorService>.Instance);
        var act2 = () => new AnomalyDetectorService(Substitute.For<IConnectionMultiplexer>(), null!);

        act1.Should().NotThrow(); // Null Redis is allowed (degraded mode)
        act2.Should().Throw<ArgumentNullException>(); // Null logger is not allowed
    }
}
