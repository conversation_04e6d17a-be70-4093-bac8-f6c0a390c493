using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using NSubstitute;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Guards;

public class QuoteVolatilityGuardTests
{
    [Fact]
    public void QuoteVolatilityGuard_CanBeCreated()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        // Act & Assert - should create without throwing
        var act = () => new QuoteVolatilityGuard(mockMux, NullLogger<QuoteVolatilityGuard>.Instance);
        act.Should().NotThrow();
    }

    [Fact]
    public void OnQuote_HandlesValidInputs_WithoutThrowing()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        var guard = new QuoteVolatilityGuard(mockMux, NullLogger<QuoteVolatilityGuard>.Instance);

        // Act & Assert - should process quotes without throwing
        var act = () =>
        {
            for (int i = 0; i < 10; i++)
                guard.OnQuote("TEST", 10m, 10.02m);
        };

        act.Should().NotThrow();
    }

    [Fact]
    public void OnQuote_HandlesInvalidInputs_Gracefully()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        var guard = new QuoteVolatilityGuard(mockMux, NullLogger<QuoteVolatilityGuard>.Instance);

        // Act & Assert - should not throw exceptions with invalid inputs
        var act = () =>
        {
            guard.OnQuote("", 10m, 10.02m);           // empty symbol
            guard.OnQuote("TEST3", 0m, 10.02m);       // zero bid
            guard.OnQuote("TEST3", 10m, 0m);          // zero ask
            guard.OnQuote("TEST3", 10m, 9m);          // ask < bid
        };

        act.Should().NotThrow();
    }

    [Fact]
    public void QuoteVolatilityGuard_RequiresValidDependencies()
    {
        // Act & Assert - should allow null Redis (degraded mode) but require logger
        var act1 = () => new QuoteVolatilityGuard(null, NullLogger<QuoteVolatilityGuard>.Instance);
        var act2 = () => new QuoteVolatilityGuard(Substitute.For<IConnectionMultiplexer>(), null!);

        act1.Should().NotThrow(); // Null Redis is allowed (degraded mode)
        act2.Should().Throw<ArgumentNullException>(); // Null logger is not allowed
    }
}
