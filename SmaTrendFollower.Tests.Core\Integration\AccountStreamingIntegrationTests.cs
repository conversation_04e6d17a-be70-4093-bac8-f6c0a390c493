using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Configuration;

namespace SmaTrendFollower.Tests.Core.Integration;

public class AccountStreamingIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly IHost _host;
    private readonly IAlpacaClientFactory _mockClientFactory;
    private readonly IAlpacaTradingClient _mockTradingClient;
    private readonly IAccount _mockAccount;
    private readonly IAlpacaRateLimitHelper _mockRateLimitHelper;

    public AccountStreamingIntegrationTests()
    {
        // Create mock dependencies
        _mockClientFactory = Substitute.For<IAlpacaClientFactory>();
        _mockTradingClient = Substitute.For<IAlpacaTradingClient>();
        _mockAccount = Substitute.For<IAccount>();
        _mockRateLimitHelper = Substitute.For<IAlpacaRateLimitHelper>();

        // Setup mock account data with proper values
        _mockAccount.Equity.Returns(100000m);
        _mockAccount.BuyingPower.Returns(200000m);
        _mockAccount.TradableCash.Returns(50000m);

        // Setup mock rate limit helper to execute functions directly
        _mockRateLimitHelper.ExecuteAsync(Arg.Any<Func<Task<IAccount>>>())
            .Returns(callInfo => callInfo.Arg<Func<Task<IAccount>>>()());

        _mockRateLimitHelper.ExecuteAsync(Arg.Any<Func<Task<decimal>>>(), Arg.Any<string>())
            .Returns(callInfo => callInfo.Arg<Func<Task<decimal>>>()());

        // Setup mock trading client
        _mockTradingClient.GetAccountAsync(Arg.Any<CancellationToken>())
            .Returns(_mockAccount);

        // Setup mock client factory
        _mockClientFactory.CreateTradingClient().Returns(_mockTradingClient);
        _mockClientFactory.GetRateLimitHelper().Returns(_mockRateLimitHelper);

        // Configure services
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder().Build();
        
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Register our services
        services.AddSingleton(_mockClientFactory);
        services.AddSingleton<ITradingEnvironmentProvider>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<TradingEnvironmentProvider>>();
            Environment.SetEnvironmentVariable("APCA_API_ENV", "paper");
            return new TradingEnvironmentProvider(logger);
        });

        services.AddSingleton<IAccountSnapshotService>(provider =>
        {
            var factory = provider.GetRequiredService<IAlpacaClientFactory>();
            var envProvider = provider.GetRequiredService<ITradingEnvironmentProvider>();
            var config = provider.GetRequiredService<IConfiguration>();
            var logger = provider.GetRequiredService<ILogger<AccountStreamingService>>();
            return new AccountStreamingService(factory, envProvider, config, logger);
        });
        services.AddHostedService(sp => (AccountStreamingService)sp.GetRequiredService<IAccountSnapshotService>());
        
        services.AddScoped<IRiskManager>(provider =>
        {
            var clientFactory = provider.GetRequiredService<IAlpacaClientFactory>();
            var accountSnapshot = provider.GetRequiredService<IAccountSnapshotService>();
            var logger = provider.GetRequiredService<ILogger<RiskManager>>();
            return new RiskManager(clientFactory, accountSnapshot, logger);
        });

        _serviceProvider = services.BuildServiceProvider();
        
        // Create host for background services
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices(services => 
            {
                foreach (var service in _serviceProvider.GetServices<IHostedService>())
                {
                    services.AddSingleton(service);
                }
            })
            .Build();
    }

    [Fact]
    public async Task AccountStreamingService_CachesAccountData_Successfully()
    {
        // Arrange
        var accountService = _serviceProvider.GetRequiredService<IAccountSnapshotService>();
        
        // Initially no cached data
        accountService.Latest.Should().BeNull();

        // Act - Start the background service
        await _host.StartAsync();
        
        // Wait a bit for the service to fetch initial data
        await Task.Delay(TimeSpan.FromSeconds(2));

        // Assert
        accountService.Latest.Should().NotBeNull();
        accountService.Latest!.Equity.Should().Be(100000m);
        accountService.Latest.BuyingPower.Should().Be(200000m);
    }

    [Fact]
    public async Task RiskManager_UsesCachedAccountData_WhenAvailable()
    {
        // Arrange
        var accountService = _serviceProvider.GetRequiredService<IAccountSnapshotService>();
        var riskManager = _serviceProvider.GetRequiredService<IRiskManager>();
        
        // Start the background service and wait for initial data
        await _host.StartAsync();
        await Task.Delay(TimeSpan.FromSeconds(2));
        
        // Verify we have cached data
        accountService.Latest.Should().NotBeNull();

        // Create a test signal
        var signal = new TradingSignal(
            Symbol: "AAPL",
            Price: 150.00m,
            Atr: 3.50m,
            SixMonthReturn: 0.15m,
            RankingProbability: 0.85f
        );

        // Act - Calculate quantity (this should use cached data)
        var quantity = await riskManager.CalculateQuantityAsync(signal);

        // Assert
        quantity.Should().BeGreaterThan(0);
        
        // Verify that the trading client was called initially for caching
        // but not called again during risk calculation
        await _mockTradingClient.Received(1).GetAccountAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task RiskManager_FallsBackToRestApi_WhenNoCachedData()
    {
        // Arrange
        var riskManager = _serviceProvider.GetRequiredService<IRiskManager>();
        
        // Don't start the background service, so no cached data
        var signal = new TradingSignal(
            Symbol: "MSFT",
            Price: 300.00m,
            Atr: 5.00m,
            SixMonthReturn: 0.20m,
            RankingProbability: 0.90f
        );

        // Act - Calculate quantity (should fallback to REST API)
        var quantity = await riskManager.CalculateQuantityAsync(signal);

        // Assert
        quantity.Should().BeGreaterThan(0);
        
        // Verify that the trading client was called for the fallback
        await _mockTradingClient.Received().GetAccountAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task AccountStreamingService_HandlesApiErrors_Gracefully()
    {
        // Arrange
        var accountService = _serviceProvider.GetRequiredService<IAccountSnapshotService>();
        
        // Setup the mock to throw an exception
        _mockTradingClient.GetAccountAsync(Arg.Any<CancellationToken>())
            .Returns<IAccount>(_ => throw new Exception("API Error"));

        // Act - Start the service (should handle the error gracefully)
        await _host.StartAsync();
        await Task.Delay(TimeSpan.FromSeconds(2));

        // Assert - Service should still be running, just with no cached data
        accountService.Latest.Should().BeNull();
        
        // The service should have attempted to call the API
        await _mockTradingClient.Received().GetAccountAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public void TradingEnvironmentProvider_DetectsCorrectEnvironment()
    {
        // Arrange & Act
        var envProvider = _serviceProvider.GetRequiredService<ITradingEnvironmentProvider>();

        // Assert
        envProvider.IsLive.Should().BeFalse(); // We set APCA_API_ENV=paper
        envProvider.EnvironmentName.Should().Be("Paper");
    }

    public void Dispose()
    {
        _host?.StopAsync().Wait();
        _host?.Dispose();
        _serviceProvider?.Dispose();
        Environment.SetEnvironmentVariable("APCA_API_ENV", null);
    }
}
