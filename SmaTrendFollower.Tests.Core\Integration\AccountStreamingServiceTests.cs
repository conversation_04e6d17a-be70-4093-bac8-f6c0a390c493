using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Services;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests.Core.Integration;

public class AccountStreamingServiceTests
{
    [Fact]
    public void AccountStreamingService_CanBeInstantiated()
    {
        // Arrange
        var factory = Substitute.For<IAlpacaClientFactory>();
        var envProvider = Substitute.For<ITradingEnvironmentProvider>();
        var configuration = Substitute.For<IConfiguration>();
        var logger = Substitute.For<ILogger<AccountStreamingService>>();

        envProvider.IsLive.Returns(false); // Paper trading for tests
        configuration.GetValue<int?>("AccountStreaming:RefreshIntervalSeconds").Returns((int?)null);

        // Act
        var service = new AccountStreamingService(factory, envProvider, configuration, logger);

        // Assert
        service.Should().NotBeNull();
        service.Latest.Should().BeNull(); // No data initially
    }

    [Fact]
    public void AccountStreamingService_ImplementsIAccountSnapshotService()
    {
        // Arrange
        var factory = Substitute.For<IAlpacaClientFactory>();
        var envProvider = Substitute.For<ITradingEnvironmentProvider>();
        var configuration = Substitute.For<IConfiguration>();
        var logger = Substitute.For<ILogger<AccountStreamingService>>();

        envProvider.IsLive.Returns(false);
        configuration.GetValue<int?>("AccountStreaming:RefreshIntervalSeconds").Returns((int?)null);

        // Act
        var service = new AccountStreamingService(factory, envProvider, configuration, logger);

        // Assert
        service.Should().BeAssignableTo<IAccountSnapshotService>();
    }

    [Fact]
    public void TradingEnvironmentProvider_DetectsEnvironmentCorrectly()
    {
        // Arrange
        var logger = Substitute.For<ILogger<TradingEnvironmentProvider>>();
        
        // Set paper trading environment
        Environment.SetEnvironmentVariable("APCA_API_ENV", "paper");

        try
        {
            // Act
            var provider = new TradingEnvironmentProvider(logger);

            // Assert
            provider.IsLive.Should().BeFalse();
            provider.EnvironmentName.Should().Be("Paper");
        }
        finally
        {
            // Cleanup
            Environment.SetEnvironmentVariable("APCA_API_ENV", null);
        }
    }

    [Fact]
    public void TradingEnvironmentProvider_DetectsLiveEnvironment()
    {
        // Arrange
        var logger = Substitute.For<ILogger<TradingEnvironmentProvider>>();
        
        // Set live trading environment
        Environment.SetEnvironmentVariable("APCA_API_ENV", "live");

        try
        {
            // Act
            var provider = new TradingEnvironmentProvider(logger);

            // Assert
            provider.IsLive.Should().BeTrue();
            provider.EnvironmentName.Should().Be("Live");
        }
        finally
        {
            // Cleanup
            Environment.SetEnvironmentVariable("APCA_API_ENV", null);
        }
    }
}
