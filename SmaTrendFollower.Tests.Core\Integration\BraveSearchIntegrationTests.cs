using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;

namespace SmaTrendFollower.Tests.Core.Integration;

/// <summary>
/// Integration tests for Brave Search API functionality
/// These tests use real API keys and make actual HTTP requests
/// </summary>
public class BraveSearchIntegrationTests
{
    private readonly ITestOutputHelper _output;
    private readonly ILogger<BraveSearchService> _logger;

    public BraveSearchIntegrationTests(ITestOutputHelper output)
    {
        _output = output;
        
        var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        _logger = loggerFactory.CreateLogger<BraveSearchService>();
    }

    [Fact]
    public void BraveSearchService_Should_Be_Registered_In_DI_Container()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Brave:SearchApiKey"] = "BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz",
                ["Brave:AiApiKey"] = "BSAacxwbtDjQfP4151QopodZgaSS8jS"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient("brave-search");
        services.AddSingleton<IBraveSearchService, BraveSearchService>();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();

        // Act
        var braveService = serviceProvider.GetService<IBraveSearchService>();

        // Assert
        braveService.Should().NotBeNull();
        braveService.Should().BeOfType<BraveSearchService>();
        _output.WriteLine("BraveSearchService successfully registered and resolved from DI container");
    }

    [Fact]
    public async Task BraveSearchService_Should_Handle_Rate_Limiting_Gracefully()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Brave:SearchApiKey"] = "BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz",
                ["Brave:RateLimitPerSecond"] = "1",
                ["Brave:TimeoutSeconds"] = "10"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Act & Assert
        var startTime = DateTime.UtcNow;
        
        // Make two requests in quick succession to test rate limiting
        var task1 = braveService.SearchWebAsync("test query 1", 1);
        var task2 = braveService.SearchWebAsync("test query 2", 1);
        
        await Task.WhenAll(task1, task2);
        
        var endTime = DateTime.UtcNow;
        var duration = endTime - startTime;

        // Should take at least 1 second due to rate limiting
        duration.Should().BeGreaterThan(TimeSpan.FromMilliseconds(900));
        _output.WriteLine($"Rate limiting test completed in {duration.TotalMilliseconds:F0}ms");
    }

    [Fact]
    public void BraveSearchService_Configuration_Should_Load_From_Environment_Variables()
    {
        // Arrange
        Environment.SetEnvironmentVariable("BRAVE_SEARCH_API_KEY", "BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz");
        Environment.SetEnvironmentVariable("BRAVE_AI_API_KEY", "BSAacxwbtDjQfP4151QopodZgaSS8jS");

        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        // Act
        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Assert
        braveService.Should().NotBeNull();
        _output.WriteLine("BraveSearchService successfully loaded configuration from environment variables");

        // Cleanup
        Environment.SetEnvironmentVariable("BRAVE_SEARCH_API_KEY", null);
        Environment.SetEnvironmentVariable("BRAVE_AI_API_KEY", null);
    }

    [Fact]
    public void BraveSearchService_Should_Prefer_Structured_Config_Over_Environment_Variables()
    {
        // Arrange
        Environment.SetEnvironmentVariable("BRAVE_SEARCH_API_KEY", "env-key");
        Environment.SetEnvironmentVariable("BRAVE_AI_API_KEY", "env-ai-key");

        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Brave:SearchApiKey"] = "BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz",
                ["Brave:AiApiKey"] = "BSAacxwbtDjQfP4151QopodZgaSS8jS"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        // Act
        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Assert
        braveService.Should().NotBeNull();
        _output.WriteLine("BraveSearchService correctly prioritized structured config over environment variables");

        // Cleanup
        Environment.SetEnvironmentVariable("BRAVE_SEARCH_API_KEY", null);
        Environment.SetEnvironmentVariable("BRAVE_AI_API_KEY", null);
    }

    [Fact]
    public async Task BraveSearchService_Should_Handle_Invalid_Api_Key_Gracefully()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Brave:SearchApiKey"] = "invalid-api-key",
                ["Brave:TimeoutSeconds"] = "5"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Act
        var result = await braveService.SearchWebAsync("test query");

        // Assert
        result.Should().BeNull();
        _output.WriteLine("BraveSearchService correctly handled invalid API key by returning null");
    }

    [Fact]
    public void BraveSearchService_Should_Dispose_Resources_Properly()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Brave:SearchApiKey"] = "BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Act & Assert
        Action disposeAction = () => braveService.Dispose();
        disposeAction.Should().NotThrow();
        _output.WriteLine("BraveSearchService disposed successfully without throwing exceptions");
    }
}
