using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using NSubstitute;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Risk;

public class RiskManagerTests
{
    private readonly RiskManager _risk;

    public RiskManagerTests()
    {
        // Create RiskManager with mocked dependencies
        var clientFactory = Substitute.For<IAlpacaClientFactory>();
        var accountSnapshot = Substitute.For<IAccountSnapshotService>();
        _risk = new RiskManager(clientFactory, accountSnapshot, NullLogger<RiskManager>.Instance);
    }

    [Fact]
    public async Task ValidateTradeAsync_AllowsWhenNoExistingExposure()
    {
        // Arrange
        var signal = new TradingSignal("MSFT", 300m, 8m, 0.12m);

        // Act
        var result = await _risk.ValidateTradeAsync(signal);

        // Assert
        result.Should().BeTrue();
    }

    [Theory]
    [InlineData("AAPL", 150.0, 3.0, 0.15)]
    [InlineData("MSFT", 300.0, 8.0, 0.12)]
    [InlineData("GOOGL", 2500.0, 50.0, 0.08)]
    public async Task ValidateTradeAsync_HandlesVariousSymbols(string symbol, double price, double atr, double sixMonthReturn)
    {
        // Arrange
        var signal = new TradingSignal(symbol, (decimal)price, (decimal)atr, (decimal)sixMonthReturn);

        // Act
        var result = await _risk.ValidateTradeAsync(signal);

        // Assert
        result.Should().BeTrue(); // Should pass when no Redis dependency
    }

    [Fact]
    public void RiskManager_CanBeInstantiated()
    {
        // Arrange & Act
        var clientFactory = Substitute.For<IAlpacaClientFactory>();
        var accountSnapshot = Substitute.For<IAccountSnapshotService>();
        var riskManager = new RiskManager(clientFactory, accountSnapshot, NullLogger<RiskManager>.Instance);

        // Assert
        riskManager.Should().NotBeNull();
    }
}
