using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;

namespace SmaTrendFollower.Tests.Core.Services;

public class BraveSearchServiceTests
{
    private readonly ITestOutputHelper _output;
    private readonly ILogger<BraveSearchService> _logger;

    public BraveSearchServiceTests(ITestOutputHelper output)
    {
        _output = output;
        
        var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        _logger = loggerFactory.CreateLogger<BraveSearchService>();
    }

    [Fact]
    public void BraveSearchService_Should_Load_Configuration_From_AppSettings()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Brave:SearchApiKey"] = "test-search-key",
                ["Brave:AiApiKey"] = "test-ai-key",
                ["Brave:RateLimitPerSecond"] = "1",
                ["Brave:MaxRequestsPerMonth"] = "2000",
                ["Brave:TimeoutSeconds"] = "30"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        // Act
        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Assert
        braveService.Should().NotBeNull();
        _output.WriteLine("BraveSearchService created successfully with configuration");
    }

    [Fact]
    public void BraveSearchService_Should_Load_Configuration_From_Environment_Variables()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["BRAVE_SEARCH_API_KEY"] = "env-search-key",
                ["BRAVE_AI_API_KEY"] = "env-ai-key"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        // Act
        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Assert
        braveService.Should().NotBeNull();
        _output.WriteLine("BraveSearchService created successfully with environment variable configuration");
    }

    [Fact]
    public void BraveSearchService_Should_Handle_Missing_Configuration_Gracefully()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>())
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        // Act
        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Assert
        braveService.Should().NotBeNull();
        _output.WriteLine("BraveSearchService created successfully even without API keys");
    }

    [Fact]
    public async Task SearchVixValueAsync_Should_Return_Null_When_No_Api_Key()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>())
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Act
        var result = await braveService.SearchVixValueAsync();

        // Assert
        result.Should().BeNull();
        _output.WriteLine("SearchVixValueAsync correctly returned null when no API key is configured");
    }

    [Fact]
    public async Task SearchSecFilingsAsync_Should_Return_Empty_List_When_No_Api_Key()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>())
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Act
        var result = await braveService.SearchSecFilingsAsync("AAPL");

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
        _output.WriteLine("SearchSecFilingsAsync correctly returned empty list when no API key is configured");
    }

    [Theory]
    [InlineData("BSAzbdfJM7ozNY6D6D9Cs_OccWRoRWz")]
    [InlineData("BSAacxwbtDjQfP4151QopodZgaSS8jS")]
    public void BraveSearchService_Should_Accept_Real_Api_Keys(string apiKey)
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Brave:SearchApiKey"] = apiKey,
                ["Brave:AiApiKey"] = apiKey
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        // Act
        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Assert
        braveService.Should().NotBeNull();
        _output.WriteLine($"BraveSearchService created successfully with API key: {apiKey[..10]}...");
    }

    [Fact]
    public void BraveSearchConfig_Should_Have_Correct_Default_Values()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>())
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddHttpClient();
        services.AddSingleton<ILogger<BraveSearchService>>(_logger);

        var serviceProvider = services.BuildServiceProvider();
        var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();

        // Act
        var braveService = new BraveSearchService(_logger, httpClientFactory, configuration);

        // Assert
        braveService.Should().NotBeNull();
        _output.WriteLine("BraveSearchService uses correct default configuration values");
    }
}
