using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Threading.Channels;

namespace SmaTrendFollower.Tests.Core.Services;

public class FinbertWorkerLlmOverlayTests
{
    [Fact]
    public void FinbertWorker_WithLlmOverlay_CanBeConstructed()
    {
        // Arrange
        var httpClientFactory = Substitute.For<IHttpClientFactory>();
        var redisService = Substitute.For<IOptimizedRedisConnectionService>();
        var redisDatabase = Substitute.For<IDatabase>();
        var llmOptions = Options.Create(new LlmSentimentOptions
        {
            Provider = "OpenAI",
            Model = "gpt-4o-mini",
            BlendWeight = 0.30,
            ConfidenceCutoff = 0.25,
            TimeoutSeconds = 10
        });

        var apiTrackingService = Substitute.For<IApiTrackingService>();
        var llmService = Substitute.For<LlmSentimentService>(
            Substitute.For<IHttpClientFactory>(),
            llmOptions,
            apiTrackingService,
            Substitute.For<ILogger<LlmSentimentService>>());
        var logger = NullLogger<FinbertWorker>.Instance;
        var channel = Channel.CreateUnbounded<HeadlineItem>();

        var finbertOptions = Options.Create(new FinbertOptions
        {
            BaseUrl = "http://localhost:5000/predict",
            Parallelism = 2,
            TtlDays = 3
        });

        redisService.GetDatabaseAsync().Returns(redisDatabase);

        // Act & Assert - Test that we can construct the worker without exceptions
        var act = () => new FinbertWorker(
            channel,
            httpClientFactory,
            redisService,
            finbertOptions,
            llmService,
            llmOptions,
            apiTrackingService,
            logger);

        act.Should().NotThrow();
    }

    [Fact]
    public void LlmSentimentOptions_BlendingLogic_IsCorrect()
    {
        // Arrange
        var options = new LlmSentimentOptions
        {
            BlendWeight = 0.30,
            ConfidenceCutoff = 0.33 // Updated to match new configuration
        };

        var finbertScore = 0.25; // Low confidence (below 0.33 threshold)
        var llmScore = 0.6;      // High confidence

        // Act - simulate the blending formula from FinbertWorker
        var blendedScore = (1.0 - options.BlendWeight) * finbertScore + options.BlendWeight * llmScore;

        // Assert
        var expectedScore = 0.7 * 0.25 + 0.3 * 0.6; // = 0.175 + 0.18 = 0.355
        blendedScore.Should().BeApproximately(expectedScore, 0.001);
        Math.Abs(finbertScore).Should().BeLessThan(options.ConfidenceCutoff); // Should trigger LLM
    }
}
