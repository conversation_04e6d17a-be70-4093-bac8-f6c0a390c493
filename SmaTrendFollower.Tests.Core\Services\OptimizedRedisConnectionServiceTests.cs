using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging.Abstractions;
using NSubstitute;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Services;

public class OptimizedRedisConnectionServiceTests
{
    [Fact]
    public void GetDatabase_WithDefaultDatabase_ReturnsCachedInstance()
    {
        // Arrange
        var mockMuxer = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        var mockConfiguration = Substitute.For<IConfiguration>();
        
        mockMuxer.GetDatabase(0).Returns(mockDb);
        
        var service = new OptimizedRedisConnectionService(
            mockMuxer, 
            mockConfiguration, 
            NullLogger<OptimizedRedisConnectionService>.Instance);

        // Act
        var result1 = service.GetDatabase(0);
        var result2 = service.GetDatabase(0);

        // Assert
        result1.Should().BeSameAs(mockDb);
        result2.Should().BeSameAs(mockDb);
        result1.Should().BeSameAs(result2); // Should return the same cached instance
        
        // Verify that GetDatabase was called only once during construction (for caching)
        mockMuxer.Received(1).GetDatabase(0);
    }

    [Fact]
    public void GetDatabase_WithNonDefaultDatabase_CallsMuxerDirectly()
    {
        // Arrange
        var mockMuxer = Substitute.For<IConnectionMultiplexer>();
        var mockDb0 = Substitute.For<IDatabase>();
        var mockDb1 = Substitute.For<IDatabase>();
        var mockConfiguration = Substitute.For<IConfiguration>();
        
        mockMuxer.GetDatabase(0).Returns(mockDb0);
        mockMuxer.GetDatabase(1).Returns(mockDb1);
        
        var service = new OptimizedRedisConnectionService(
            mockMuxer, 
            mockConfiguration, 
            NullLogger<OptimizedRedisConnectionService>.Instance);

        // Act
        var result = service.GetDatabase(1);

        // Assert
        result.Should().BeSameAs(mockDb1);
        
        // Verify that GetDatabase was called for both database 0 (during construction) and database 1
        mockMuxer.Received(1).GetDatabase(0); // Called during construction for caching
        mockMuxer.Received(1).GetDatabase(1); // Called for the non-default database
    }

    [Fact]
    public void Constructor_CachesDefaultDatabase()
    {
        // Arrange
        var mockMuxer = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        var mockConfiguration = Substitute.For<IConfiguration>();
        
        mockMuxer.GetDatabase(0).Returns(mockDb);

        // Act
        var service = new OptimizedRedisConnectionService(
            mockMuxer, 
            mockConfiguration, 
            NullLogger<OptimizedRedisConnectionService>.Instance);

        // Assert
        // Verify that the constructor called GetDatabase to cache the default database
        mockMuxer.Received(1).GetDatabase(0);
    }

    [Fact]
    public void GetDatabase_MultipleCallsToDefaultDatabase_OnlyCallsMuxerOnce()
    {
        // Arrange
        var mockMuxer = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        var mockConfiguration = Substitute.For<IConfiguration>();
        
        mockMuxer.GetDatabase(0).Returns(mockDb);
        
        var service = new OptimizedRedisConnectionService(
            mockMuxer, 
            mockConfiguration, 
            NullLogger<OptimizedRedisConnectionService>.Instance);

        // Act - Call GetDatabase multiple times
        var result1 = service.GetDatabase(0);
        var result2 = service.GetDatabase(0);
        var result3 = service.GetDatabase(0);

        // Assert
        result1.Should().BeSameAs(mockDb);
        result2.Should().BeSameAs(mockDb);
        result3.Should().BeSameAs(mockDb);
        
        // All results should be the same cached instance
        result1.Should().BeSameAs(result2);
        result2.Should().BeSameAs(result3);
        
        // Verify that GetDatabase was called only once (during construction)
        mockMuxer.Received(1).GetDatabase(0);
    }
}
