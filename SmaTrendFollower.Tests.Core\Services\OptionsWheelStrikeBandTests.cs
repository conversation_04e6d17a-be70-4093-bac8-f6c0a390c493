using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Services;

/// <summary>
/// Tests for the Options Wheel Strike Band filtering functionality
/// </summary>
public class OptionsWheelStrikeBandTests
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<WheelStrategyEngine> _logger;
    private readonly IOptionsMonitor<WheelStrategyConfig> _configMonitor;
    private readonly IOptions<OptionsWheelOptions> _optionsWheelOptions;
    private readonly WheelStrategyConfig _defaultConfig;

    public OptionsWheelStrikeBandTests()
    {
        _marketDataService = Substitute.For<IMarketDataService>();
        _logger = Substitute.For<ILogger<WheelStrategyEngine>>();
        _configMonitor = Substitute.For<IOptionsMonitor<WheelStrategyConfig>>();
        _optionsWheelOptions = Substitute.For<IOptions<OptionsWheelOptions>>();

        _defaultConfig = new WheelStrategyConfig
        {
            Enabled = true,
            MaxAllocationPercent = 0.20m,
            MinPremiumPercent = 0.01m,
            MinDaysToExpiration = 7,
            MaxDaysToExpiration = 45,
            MaxDeltaForPuts = 0.30m,
            MaxDeltaForCalls = 0.30m,
            MinLiquidity = 100m,
            MaxBidAskSpreadPercent = 0.05m,
            EnableRolling = true,
            RollThreshold = 0.50m,
            MaxRollAttempts = 2,
            RequireHighIV = false,
            MinIVPercentile = 30m,
            AllowedSymbols = new[] { "SPY", "AAPL", "MSFT" },
            ExcludedSymbols = null
        };

        _configMonitor.CurrentValue.Returns(_defaultConfig);
        _optionsWheelOptions.Value.Returns(new OptionsWheelOptions { StrikeBandPercent = 15.0 });
    }

    [Fact]
    public void OptionsWheelOptions_DefaultStrikeBandPercent_ShouldBe15()
    {
        // Arrange
        var options = new OptionsWheelOptions();

        // Act & Assert
        options.StrikeBandPercent.Should().Be(15.0);
    }

    [Fact]
    public void OptionsWheelOptions_CustomStrikeBandPercent_ShouldBeConfigurable()
    {
        // Arrange & Act
        var options = new OptionsWheelOptions { StrikeBandPercent = 20.0 };

        // Assert
        options.StrikeBandPercent.Should().Be(20.0);
    }

    [Fact]
    public async Task MarketDataService_GetOptionsDataAsync_WithStrikeBandFiltering_ShouldCallCorrectOverload()
    {
        // Arrange
        var marketDataService = Substitute.For<IMarketDataService>();
        var symbol = "SPY";
        var currentPrice = 450.00m;
        var strikeBandPercent = 15.0;

        var expectedOptions = new List<OptionData>
        {
            new OptionData("SPY240101C00450000", "SPY", DateTime.Today.AddDays(30), 450m, "call", 5.50m, 5.45m, 5.55m, 1000, 500, 0.25m, 0.50m, 0.02m, -0.05m, 0.10m),
            new OptionData("SPY240101P00450000", "SPY", DateTime.Today.AddDays(30), 450m, "put", 5.25m, 5.20m, 5.30m, 800, 400, 0.24m, -0.50m, 0.02m, -0.04m, 0.09m)
        };

        marketDataService.GetOptionsDataAsync(symbol, null, currentPrice, strikeBandPercent)
            .Returns(expectedOptions);

        // Act
        var result = await marketDataService.GetOptionsDataAsync(symbol, null, currentPrice, strikeBandPercent);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        await marketDataService.Received(1).GetOptionsDataAsync(symbol, null, currentPrice, strikeBandPercent);
    }

    [Theory]
    [InlineData(100.0, 15.0, 85.0, 115.0)]
    [InlineData(450.0, 15.0, 382.5, 517.5)]
    [InlineData(200.0, 10.0, 180.0, 220.0)]
    [InlineData(50.0, 20.0, 40.0, 60.0)]
    public void StrikeBandCalculation_ShouldCalculateCorrectRange(decimal currentPrice, double bandPercent, decimal expectedLower, decimal expectedUpper)
    {
        // Arrange
        var bandMultiplier = (decimal)(bandPercent / 100.0);

        // Act
        var lowerStrike = currentPrice * (1 - bandMultiplier);
        var upperStrike = currentPrice * (1 + bandMultiplier);

        // Assert
        lowerStrike.Should().Be(expectedLower);
        upperStrike.Should().Be(expectedUpper);
    }
}
