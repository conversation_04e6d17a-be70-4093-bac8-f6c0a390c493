using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Services;

public class QuoteGuardSigmaMetricTests
{
    private readonly ILogger<QuoteVolatilityGuard> _mockLogger;
    private readonly IConnectionMultiplexer _mockRedis;
    private readonly QuoteVolatilityOptions _options;

    public QuoteGuardSigmaMetricTests()
    {
        _mockLogger = Substitute.For<ILogger<QuoteVolatilityGuard>>();
        _mockRedis = Substitute.For<IConnectionMultiplexer>();
        _options = new QuoteVolatilityOptions
        {
            WindowSeconds = 120,
            StdDevSigma = 2.0,
            HaltDurationSeconds = 120
        };
    }

    [Fact]
    public void QuoteGuardSigmaRatio_Metric_ShouldExist()
    {
        // Arrange & Act
        var metric = MetricsRegistry.QuoteGuardSigmaRatio;

        // Assert
        metric.Should().NotBeNull();
        metric.Name.Should().Be("quote_guard_sigma");
        metric.Help.Should().Be("Current σ divided by halt threshold");
    }

    [Fact]
    public void QuoteVolatilityGuard_WithNormalVolatility_ShouldSetLowSigmaRatio()
    {
        // Arrange
        var guard = new QuoteVolatilityGuard(_options, _mockLogger, _mockRedis);
        var symbol = "TEST";
        var basePrice = 100.00m;

        // Reset metric to ensure clean test
        MetricsRegistry.QuoteGuardSigmaRatio.Set(0);

        // Act - Send 25 quotes with low volatility (±0.1% variation)
        for (int i = 0; i < 25; i++)
        {
            var variation = (decimal)(0.001 * Math.Sin(i * 0.1)); // Small sine wave variation
            var bid = basePrice + variation - 0.05m;
            var ask = basePrice + variation + 0.05m;
            guard.OnQuote(symbol, bid, ask);
        }

        // Assert - Should have low sigma ratio (well below 1.0)
        var currentRatio = MetricsRegistry.QuoteGuardSigmaRatio.Value;
        currentRatio.Should().BeLessThan(0.5, "normal volatility should result in low sigma ratio");
    }

    [Fact]
    public void QuoteVolatilityGuard_WithHighVolatility_ShouldSetHighSigmaRatio()
    {
        // Arrange
        var guard = new QuoteVolatilityGuard(_options, _mockLogger, _mockRedis);
        var symbol = "TEST";

        // Reset metric to ensure clean test
        MetricsRegistry.QuoteGuardSigmaRatio.Set(0);

        // Act - Send 25 quotes with extreme volatility to trigger high sigma ratio
        // With threshold = 2.0 * mean / 100 = 2.0 * 100 / 100 = 2.0
        // We need sigma > 2.0, so create price swings that will generate high standard deviation
        var prices = new decimal[] { 95m, 105m, 90m, 110m, 85m, 115m, 80m, 120m, 75m, 125m,
                                   95m, 105m, 90m, 110m, 85m, 115m, 80m, 120m, 75m, 125m,
                                   95m, 105m, 90m, 110m, 85m };

        foreach (var price in prices)
        {
            guard.OnQuote(symbol, price - 0.05m, price + 0.05m);
        }

        // Assert - Should have high sigma ratio (above 1.0 due to extreme price swings)
        var currentRatio = MetricsRegistry.QuoteGuardSigmaRatio.Value;
        currentRatio.Should().BeGreaterThan(1.0, "extreme volatility should result in sigma ratio > 1.0");
    }

    [Fact]
    public void QuoteVolatilityGuard_WithInsufficientData_ShouldNotUpdateMetric()
    {
        // Arrange
        var guard = new QuoteVolatilityGuard(_options, _mockLogger, _mockRedis);
        var symbol = "TEST";
        var initialValue = 0.5;

        // Set initial metric value
        MetricsRegistry.QuoteGuardSigmaRatio.Set(initialValue);

        // Act - Send only 10 quotes (below 20 minimum threshold)
        for (int i = 0; i < 10; i++)
        {
            guard.OnQuote(symbol, 100.00m, 100.10m);
        }

        // Assert - Metric should remain unchanged
        var currentRatio = MetricsRegistry.QuoteGuardSigmaRatio.Value;
        currentRatio.Should().Be(initialValue, "insufficient data should not update the metric");
    }

    [Fact]
    public void QuoteVolatilityGuard_SigmaRatioCalculation_ShouldBeAccurate()
    {
        // Arrange
        var guard = new QuoteVolatilityGuard(_options, _mockLogger, _mockRedis);
        var symbol = "TEST";

        // Reset metric
        MetricsRegistry.QuoteGuardSigmaRatio.Set(0);

        // Act - Send quotes with known volatility pattern
        var prices = new decimal[] { 100.0m, 101.0m, 99.0m, 102.0m, 98.0m, 103.0m, 97.0m, 104.0m, 96.0m, 105.0m,
                                   100.0m, 101.0m, 99.0m, 102.0m, 98.0m, 103.0m, 97.0m, 104.0m, 96.0m, 105.0m,
                                   100.0m, 101.0m, 99.0m, 102.0m, 98.0m };

        foreach (var price in prices)
        {
            guard.OnQuote(symbol, price - 0.05m, price + 0.05m);
        }

        // Assert - Ratio should be calculated and set
        var currentRatio = MetricsRegistry.QuoteGuardSigmaRatio.Value;
        currentRatio.Should().BeGreaterThan(0, "sigma ratio should be calculated for sufficient data");
        currentRatio.Should().BeLessThan(10, "sigma ratio should be reasonable for normal test data");
    }

    [Fact]
    public void QuoteVolatilityOptions_DefaultValues_ShouldBeReasonable()
    {
        // Arrange & Act
        var defaultOptions = new QuoteVolatilityOptions();

        // Assert
        defaultOptions.WindowSeconds.Should().BeGreaterThan(0);
        defaultOptions.StdDevSigma.Should().BeGreaterThan(0);
        defaultOptions.HaltDurationSeconds.Should().BeGreaterThan(0);
    }
}
