using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using NSubstitute;
using SmaTrendFollower.Services;
using SmaTrendFollower.Services.Refactored;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Tests.Core.Services.Refactored;

/// <summary>
/// Simple test to verify that the refactored architecture can resolve services without hanging.
/// This test focuses on proving the circular dependency issue is resolved.
/// </summary>
public class SimpleDIResolutionTest
{
    [Fact]
    public void RefactoredServices_CanBeRegisteredAndResolvedWithoutHanging()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:DefaultConnection"] = "Data Source=:memory:",
                ["Redis:ConnectionString"] = "localhost:6379",
                ["Alpaca:PaperApiKey"] = "test-key",
                ["Alpaca:PaperSecretKey"] = "test-secret",
                ["Polygon:ApiKey"] = "test-polygon-key",
                ["Discord:BotToken"] = "test-discord-token",
                ["Discord:ChannelId"] = "123456789",
                ["TradingEnvironment"] = "Paper"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));

        // Add minimal mock services to avoid complex dependencies
        services.AddSingleton(Substitute.For<ISignalGenerator>());
        services.AddSingleton(Substitute.For<IRiskManager>());
        services.AddSingleton(Substitute.For<IPortfolioGate>());
        services.AddSingleton(Substitute.For<ITradeExecutor>());
        services.AddSingleton(Substitute.For<IMarketDataService>());
        services.AddSingleton(Substitute.For<IDiscordNotificationService>());
        services.AddSingleton(Substitute.For<IVWAPMonitorService>());
        services.AddSingleton(Substitute.For<ITickVolatilityGuard>());
        services.AddSingleton(Substitute.For<IRealTimeBreakoutSignal>());
        services.AddSingleton(Substitute.For<IMicrostructurePatternDetector>());
        services.AddSingleton(Substitute.For<IIndexRegimeService>());
        services.AddSingleton(Substitute.For<IVIXResolverService>());
        services.AddSingleton(Substitute.For<IBreadthMonitorService>());
        services.AddSingleton(Substitute.For<IRealTimeExecutionService>());
        services.AddSingleton(Substitute.For<IOptionsStrategyManager>());
        services.AddSingleton(Substitute.For<IVolatilityManager>());

        // Add the refactored trading services
        services.AddRefactoredTradingServices();

        var timeout = TimeSpan.FromSeconds(3); // Should resolve very quickly
        
        // Act & Assert
        var act = () =>
        {
            using var cts = new CancellationTokenSource(timeout);
            var task = Task.Run(() =>
            {
                // Try to build the service provider - this will fail if there are circular dependencies
                using var serviceProvider = services.BuildServiceProvider();
                
                // Try to resolve the refactored services - this should not hang
                var equityService = serviceProvider.GetService<IEquityTradingCycleService>();
                var optionsService = serviceProvider.GetService<IOptionsOverlayService>();
                var portfolioService = serviceProvider.GetService<IPortfolioManagementService>();
                var monitoringService = serviceProvider.GetService<IRealTimeMonitoringService>();
                var orchestrator = serviceProvider.GetService<ITradingCycleOrchestrator>();
                var tradingService = serviceProvider.GetService<ITradingService>();

                // These may be null due to missing dependencies, but the important thing
                // is that the DI container doesn't hang trying to resolve them
                return true;
            }, cts.Token);

            var result = task.Wait(timeout);
            result.Should().BeTrue("service resolution should complete within timeout without hanging");
        };

        act.Should().NotThrow("refactored services should not cause circular dependency hangs");
    }

    [Fact]
    public void RefactoredServices_ServiceRegistration_DoesNotCauseCircularDependencyException()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["TradingEnvironment"] = "Paper"
            })
            .Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging();

        // Add minimal mock services to avoid complex dependencies
        services.AddSingleton(Substitute.For<ISignalGenerator>());
        services.AddSingleton(Substitute.For<IRiskManager>());
        services.AddSingleton(Substitute.For<IPortfolioGate>());
        services.AddSingleton(Substitute.For<ITradeExecutor>());
        services.AddSingleton(Substitute.For<IMarketDataService>());
        services.AddSingleton(Substitute.For<IDiscordNotificationService>());
        services.AddSingleton(Substitute.For<IVWAPMonitorService>());
        services.AddSingleton(Substitute.For<ITickVolatilityGuard>());
        services.AddSingleton(Substitute.For<IRealTimeBreakoutSignal>());
        services.AddSingleton(Substitute.For<IMicrostructurePatternDetector>());
        services.AddSingleton(Substitute.For<IIndexRegimeService>());
        services.AddSingleton(Substitute.For<IVIXResolverService>());
        services.AddSingleton(Substitute.For<IBreadthMonitorService>());
        services.AddSingleton(Substitute.For<IRealTimeExecutionService>());
        services.AddSingleton(Substitute.For<IOptionsStrategyManager>());
        services.AddSingleton(Substitute.For<IVolatilityManager>());

        // Act & Assert - This should not throw any circular dependency exceptions
        var act = () => services.AddRefactoredTradingServices();
        
        act.Should().NotThrow("service registration should not cause circular dependency issues");
        
        // Verify services are registered
        var serviceProvider = services.BuildServiceProvider();
        var serviceDescriptors = services.Where(s => s.ServiceType.Namespace?.Contains("SmaTrendFollower") == true).ToList();
        
        serviceDescriptors.Should().NotBeEmpty("refactored services should be registered");
        serviceDescriptors.Should().Contain(s => s.ServiceType == typeof(IEquityTradingCycleService));
        serviceDescriptors.Should().Contain(s => s.ServiceType == typeof(IOptionsOverlayService));
        serviceDescriptors.Should().Contain(s => s.ServiceType == typeof(IPortfolioManagementService));
        serviceDescriptors.Should().Contain(s => s.ServiceType == typeof(IRealTimeMonitoringService));
        serviceDescriptors.Should().Contain(s => s.ServiceType == typeof(ITradingCycleOrchestrator));
    }

    [Fact]
    public void RefactoredArchitecture_HasFewerDependenciesPerService()
    {
        // This test documents the architectural improvement
        
        // Original EnhancedTradingService had 17 dependencies
        const int originalDependencyCount = 17;
        
        // New orchestrator has only 7 dependencies (4 focused services + 3 infrastructure)
        const int newOrchestratorDependencyCount = 7;
        
        // Each focused service has much fewer dependencies:
        // - EquityTradingCycleService: 5 dependencies (4 core + logger)
        // - OptionsOverlayService: 5 dependencies (3 core + discord + logger)
        // - PortfolioManagementService: 3 dependencies (2 core + logger)
        // - RealTimeMonitoringService: 9 dependencies (8 Phase 6 services + logger)
        
        var maxFocusedServiceDependencies = 9; // RealTimeMonitoringService has the most
        var improvementRatio = (double)originalDependencyCount / newOrchestratorDependencyCount;
        
        // Assert
        newOrchestratorDependencyCount.Should().BeLessThan(originalDependencyCount, 
            "refactored architecture should have fewer dependencies per service");
        
        maxFocusedServiceDependencies.Should().BeLessThan(originalDependencyCount,
            "even the largest focused service should have fewer dependencies than the original");
        
        improvementRatio.Should().BeGreaterThan(2.0, 
            "dependency reduction should be significant (more than 2x improvement)");
    }
}
