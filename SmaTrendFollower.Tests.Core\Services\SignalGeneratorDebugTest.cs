using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Console.Extensions;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests.Core.Services;

public class SignalGeneratorDebugTest
{
    [Fact]
    public void TestTechnicalIndicators_WithMockData()
    {
        // Create realistic uptrending data (need at least 200 bars for SMA200)
        var bars = CreateStrongUptrendBars(250);
        
        // Test technical indicators directly
        try
        {
            var sma50 = bars.GetSma50();
            var sma200 = bars.GetSma200();
            var atr14 = bars.GetAtr14();
            var rsi14 = bars.GetRsi14();
            var macd = bars.GetMacd();
            
            System.Console.WriteLine($"SMA50: {sma50:F2}");
            System.Console.WriteLine($"SMA200: {sma200:F2}");
            System.Console.WriteLine($"ATR14: {atr14:F4}");
            System.Console.WriteLine($"RSI14: {rsi14:F2}");
            System.Console.WriteLine($"MACD: {macd.Macd:F4}, Signal: {macd.Signal:F4}, Histogram: {macd.Histogram:F4}");
            
            var currentPrice = bars.Last().Close;
            System.Console.WriteLine($"Current Price: {currentPrice:F2}");
            
            // Check conditions
            var trendCondition = currentPrice > (decimal)sma50 && currentPrice > (decimal)sma200;
            var rsiCondition = rsi14 > 55;
            var macdCondition = macd.Histogram > 0;
            
            System.Console.WriteLine($"Trend condition (price > SMA50 > SMA200): {trendCondition}");
            System.Console.WriteLine($"RSI condition (RSI > 55): {rsiCondition}");
            System.Console.WriteLine($"MACD condition (histogram > 0): {macdCondition}");
            
            // All should be true for a strong uptrend
            trendCondition.Should().BeTrue();
            rsiCondition.Should().BeTrue();
            macdCondition.Should().BeTrue();
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"Exception: {ex.Message}");
            throw;
        }
    }

    private static List<IBar> CreateStrongUptrendBars(int count)
    {
        var bars = new List<IBar>();
        var baseDate = DateTime.UtcNow.AddDays(-count);
        var basePrice = 50m;

        // Create a strong but reasonable uptrend
        for (int i = 0; i < count; i++)
        {
            var bar = Substitute.For<IBar>();

            // Linear growth with some acceleration - more reasonable than exponential
            var growthComponent = (decimal)i * 0.2m; // 20 cents per day base growth
            var accelerationComponent = (decimal)(i * i) * 0.001m; // Small acceleration
            var noiseComponent = (decimal)(Math.Sin(i * 0.1) * 1.0); // Some noise

            var price = basePrice + growthComponent + accelerationComponent + noiseComponent;

            // Ensure we have realistic OHLC data
            var open = price * 0.998m;
            var high = price * 1.005m;
            var low = price * 0.995m;
            var close = price;

            bar.TimeUtc.Returns(baseDate.AddDays(i));
            bar.Open.Returns(open);
            bar.High.Returns(high);
            bar.Low.Returns(low);
            bar.Close.Returns(close);
            bar.Volume.Returns((ulong)(2000000 + i * 10000)); // Increasing volume

            bars.Add(bar);
        }

        return bars;
    }
}
