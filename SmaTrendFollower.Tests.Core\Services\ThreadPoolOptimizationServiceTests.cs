using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Console.Services;
using Xunit;
using FluentAssertions;

namespace SmaTrendFollower.Tests.Core.Services;

public class ThreadPoolOptimizationServiceTests
{
    private readonly ILogger<ThreadPoolOptimizationService> _logger;
    private readonly ThreadPoolOptimizationConfiguration _config;
    private readonly IOptions<ThreadPoolOptimizationConfiguration> _options;

    public ThreadPoolOptimizationServiceTests()
    {
        _logger = new TestLogger<ThreadPoolOptimizationService>();
        _config = new ThreadPoolOptimizationConfiguration
        {
            MonitoringInterval = TimeSpan.FromSeconds(1),
            OptimizationInterval = TimeSpan.FromSeconds(2),
            MinWorkerThreads = 10,
            MinCompletionPortThreads = 10,
            MaxWorkerThreads = 50,
            MaxCompletionPortThreads = 50,
            HighThreadCountThreshold = 100,
            WorkerThreadPressureThreshold = 0.8,
            EnableDynamicThreadPoolAdjustment = true,
            EnableGarbageCollectionOnHighThreads = true
        };
        _options = Options.Create(_config);
    }

    [Fact]
    public void Constructor_ShouldInitializeThreadPool()
    {
        // Act
        using var service = new ThreadPoolOptimizationService(_options, _logger);

        // Assert
        ThreadPool.GetMinThreads(out int minWorker, out int minIO);
        ThreadPool.GetMaxThreads(out int maxWorker, out int maxIO);

        minWorker.Should().BeGreaterOrEqualTo(_config.MinWorkerThreads);
        minIO.Should().BeGreaterOrEqualTo(_config.MinCompletionPortThreads);
    }

    [Fact]
    public void Configuration_ShouldHaveReasonableDefaults()
    {
        // Arrange
        var defaultConfig = new ThreadPoolOptimizationConfiguration();

        // Assert
        defaultConfig.MonitoringInterval.Should().Be(TimeSpan.FromSeconds(30));
        defaultConfig.OptimizationInterval.Should().Be(TimeSpan.FromMinutes(5));
        defaultConfig.MinWorkerThreads.Should().Be(25);
        defaultConfig.MinCompletionPortThreads.Should().Be(25);
        defaultConfig.MaxWorkerThreads.Should().Be(100);
        defaultConfig.MaxCompletionPortThreads.Should().Be(100);
        defaultConfig.HighThreadCountThreshold.Should().Be(150);
        defaultConfig.WorkerThreadPressureThreshold.Should().Be(0.8);
        defaultConfig.EnableDynamicThreadPoolAdjustment.Should().BeTrue();
        defaultConfig.EnableGarbageCollectionOnHighThreads.Should().BeTrue();
    }

    [Fact]
    public async Task StartAsync_ShouldStartSuccessfully()
    {
        // Arrange
        using var service = new ThreadPoolOptimizationService(_options, _logger);
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(2));

        // Act
        var startTask = service.StartAsync(cts.Token);
        await Task.Delay(100, cts.Token); // Let it start
        var stopTask = service.StopAsync(cts.Token);

        // Assert
        await startTask;
        await stopTask;

        // Service should start and stop without throwing
        Assert.True(true);
    }

    [Fact]
    public void SectionName_ShouldBeCorrect()
    {
        // Assert
        ThreadPoolOptimizationConfiguration.SectionName.Should().Be("ThreadPoolOptimization");
    }
}

/// <summary>
/// Simple test logger implementation
/// </summary>
public class TestLogger<T> : ILogger<T>
{
    public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;
    public bool IsEnabled(LogLevel logLevel) => true;
    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        // Simple test implementation - just ignore logging
    }
}
