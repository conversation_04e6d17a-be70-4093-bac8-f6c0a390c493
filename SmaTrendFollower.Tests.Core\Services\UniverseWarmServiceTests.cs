using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using NodaTime;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Services;

public class UniverseWarmServiceTests
{
    private readonly IUniverseFetcherService _mockFetcher;
    private readonly ITimeProvider _mockTimeProvider;
    private readonly ILogger<UniverseWarmService> _mockLogger;
    private readonly UniverseCacheOptions _options;

    public UniverseWarmServiceTests()
    {
        _mockFetcher = Substitute.For<IUniverseFetcherService>();
        _mockTimeProvider = Substitute.For<ITimeProvider>();
        _mockLogger = Substitute.For<ILogger<UniverseWarmService>>();
        _options = new UniverseCacheOptions { RefreshInterval = TimeSpan.FromMinutes(10) };
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldNotThrow()
    {
        // Arrange & Act
        var action = () => new UniverseWarmService(
            _mockFetcher,
            _mockTimeProvider,
            Options.Create(_options),
            _mockLogger);

        // Assert
        action.Should().NotThrow();
    }

    [Fact]
    public void Constructor_WithNullFetcher_ShouldThrowArgumentNullException()
    {
        // Arrange & Act
        var action = () => new UniverseWarmService(
            null!,
            _mockTimeProvider,
            Options.Create(_options),
            _mockLogger);

        // Assert
        action.Should().Throw<ArgumentNullException>().WithParameterName("fetch");
    }

    [Fact]
    public void Constructor_WithNullTimeProvider_ShouldThrowArgumentNullException()
    {
        // Arrange & Act
        var action = () => new UniverseWarmService(
            _mockFetcher,
            null!,
            Options.Create(_options),
            _mockLogger);

        // Assert
        action.Should().Throw<ArgumentNullException>().WithParameterName("timeProvider");
    }

    [Fact]
    public void Constructor_WithNullOptions_ShouldThrowArgumentNullException()
    {
        // Arrange & Act
        var action = () => new UniverseWarmService(
            _mockFetcher,
            _mockTimeProvider,
            null!,
            _mockLogger);

        // Assert
        action.Should().Throw<ArgumentNullException>().WithParameterName("options");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange & Act
        var action = () => new UniverseWarmService(
            _mockFetcher,
            _mockTimeProvider,
            Options.Create(_options),
            null!);

        // Assert
        action.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    [Fact]
    public void UniverseCacheOptions_DefaultRefreshInterval_ShouldBe10Minutes()
    {
        // Arrange
        var defaultOptions = new UniverseCacheOptions();

        // Act & Assert
        defaultOptions.RefreshInterval.Should().Be(TimeSpan.FromMinutes(10));
    }

    [Fact]
    public void UniverseCacheOptions_CustomRefreshInterval_ShouldBeRespected()
    {
        // Arrange
        var customInterval = TimeSpan.FromMinutes(5);
        var options = new UniverseCacheOptions { RefreshInterval = customInterval };

        // Act & Assert
        options.RefreshInterval.Should().Be(customInterval);
    }
}
