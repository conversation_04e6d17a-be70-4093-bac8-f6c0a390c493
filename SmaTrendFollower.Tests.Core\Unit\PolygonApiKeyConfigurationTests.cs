using Microsoft.Extensions.Configuration;
using Xunit;
using FluentAssertions;

namespace SmaTrendFollower.Tests.Core.Unit;

/// <summary>
/// Tests for Polygon API key configuration fallback behavior
/// </summary>
public class PolygonApiKeyConfigurationTests
{
    [Fact]
    public void Configuration_ShouldReadPolygonApiKeyFromConfiguration()
    {
        // Arrange
        var config = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Polygon:ApiKey"] = "config-key"
            })
            .Build();

        // Act
        var result = GetPolygonApiKeyFromConfig(config);

        // Assert
        result.Should().Be("config-key");
    }

    [Fact]
    public void Configuration_ShouldFallbackToPolyApiKeyConfig()
    {
        // Arrange
        var config = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["POLY_API_KEY"] = "poly-config-key"
            })
            .Build();

        // Act
        var result = GetPolygonApiKeyFromConfig(config);

        // Assert
        result.Should().Be("poly-config-key");
    }

    [Fact]
    public void Configuration_ShouldFallbackToEnvironmentVariable()
    {
        // Arrange
        Environment.SetEnvironmentVariable("POLY_API_KEY", "env-key");

        try
        {
            var config = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>())
                .Build();

            // Act
            var result = GetPolygonApiKeyFromConfig(config);

            // Assert
            result.Should().Be("env-key");
        }
        finally
        {
            Environment.SetEnvironmentVariable("POLY_API_KEY", null);
        }
    }

    [Fact]
    public void Configuration_ShouldFallbackToPolygonApiKeyEnvironmentVariable()
    {
        // Arrange
        Environment.SetEnvironmentVariable("POLYGON_API_KEY", "polygon-env-key");

        try
        {
            var config = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>())
                .Build();

            // Act
            var result = GetPolygonApiKeyFromConfig(config);

            // Assert
            result.Should().Be("polygon-env-key");
        }
        finally
        {
            Environment.SetEnvironmentVariable("POLYGON_API_KEY", null);
        }
    }

    [Fact]
    public void Configuration_ShouldThrowWhenNoApiKeyConfigured()
    {
        // Arrange - Clear any environment variables that might be set
        var originalPolyKey = Environment.GetEnvironmentVariable("POLY_API_KEY");
        var originalPolygonKey = Environment.GetEnvironmentVariable("POLYGON_API_KEY");

        try
        {
            Environment.SetEnvironmentVariable("POLY_API_KEY", null);
            Environment.SetEnvironmentVariable("POLYGON_API_KEY", null);

            var config = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>())
                .Build();

            // Act & Assert
            var act = () => GetPolygonApiKeyFromConfig(config);
            act.Should().Throw<InvalidOperationException>()
                .WithMessage("*Polygon API key not configured*");
        }
        finally
        {
            // Restore original environment variables
            Environment.SetEnvironmentVariable("POLY_API_KEY", originalPolyKey);
            Environment.SetEnvironmentVariable("POLYGON_API_KEY", originalPolygonKey);
        }
    }

    [Fact]
    public void Configuration_ShouldPrioritizeConfigurationOverEnvironment()
    {
        // Arrange
        Environment.SetEnvironmentVariable("POLY_API_KEY", "env-key");
        Environment.SetEnvironmentVariable("POLYGON_API_KEY", "polygon-env-key");

        try
        {
            var config = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["Polygon:ApiKey"] = "config-key"
                })
                .Build();

            // Act
            var result = GetPolygonApiKeyFromConfig(config);

            // Assert
            result.Should().Be("config-key");
        }
        finally
        {
            Environment.SetEnvironmentVariable("POLY_API_KEY", null);
            Environment.SetEnvironmentVariable("POLYGON_API_KEY", null);
        }
    }

    /// <summary>
    /// Helper method that mimics the logic used in the actual services
    /// </summary>
    private static string GetPolygonApiKeyFromConfig(IConfiguration configuration)
    {
        return configuration["Polygon:ApiKey"]
               ?? configuration["POLY_API_KEY"]
               ?? Environment.GetEnvironmentVariable("POLY_API_KEY")
               ?? Environment.GetEnvironmentVariable("POLYGON_API_KEY")
               ?? throw new InvalidOperationException("Polygon API key not configured. Set Polygon:ApiKey in appsettings.json or POLY_API_KEY/POLYGON_API_KEY environment variable");
    }
}
