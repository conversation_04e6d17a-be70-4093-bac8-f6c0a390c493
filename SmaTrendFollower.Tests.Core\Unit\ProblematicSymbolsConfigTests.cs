using FluentAssertions;
using SmaTrendFollower.Configuration;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Unit;

/// <summary>
/// Tests for ProblematicSymbolsConfig to ensure proper handling of symbols that cause database caching issues
/// </summary>
public class ProblematicSymbolsConfigTests
{
    [Fact]
    public void ProblematicSymbols_ShouldContainKnownProblematicSymbols()
    {
        // Arrange & Act
        var problematicSymbols = ProblematicSymbolsConfig.ProblematicSymbols;

        // Assert
        problematicSymbols.Should().Contain("BELFB", "BELFB should be in problematic symbols list");
        problematicSymbols.Should().Contain("BATRA", "BATRA should be in problematic symbols list");
        problematicSymbols.Should().NotContain("ARKW", "ARKW should no longer be permanently blacklisted");
    }

    [Theory]
    [InlineData("BELFB", true)]
    [InlineData("BATRA", true)]
    [InlineData("belfb", true)] // Case insensitive
    [InlineData("batra", true)] // Case insensitive
    [InlineData("ARKW", false)] // ARKW should no longer be permanently blacklisted
    [InlineData("arkw", false)] // ARKW should no longer be permanently blacklisted
    [InlineData("AAPL", false)]
    [InlineData("MSFT", false)]
    [InlineData("SPY", false)]
    [InlineData("", false)]
    public void IsProblematicSymbol_ShouldReturnCorrectResult(string symbol, bool expected)
    {
        // Act
        var result = ProblematicSymbolsConfig.IsProblematicSymbol(symbol);

        // Assert
        result.Should().Be(expected, $"Symbol '{symbol}' should {(expected ? "" : "not ")}be considered problematic");
    }

    [Fact]
    public void GetProblematicSymbolsArray_ShouldReturnAllProblematicSymbols()
    {
        // Act
        var symbolsArray = ProblematicSymbolsConfig.GetProblematicSymbolsArray();

        // Assert
        symbolsArray.Should().NotBeEmpty("Should return at least the known problematic symbols");
        symbolsArray.Should().Contain("BELFB");
        symbolsArray.Should().Contain("BATRA");
        symbolsArray.Should().NotContain("ARKW", "ARKW should no longer be permanently blacklisted");
    }

    [Fact]
    public void AddProblematicSymbol_WithValidSymbol_ShouldAddToList()
    {
        // Arrange
        var newSymbol = "TEST_SYMBOL";
        
        // Ensure the symbol is not already in the list
        ProblematicSymbolsConfig.IsProblematicSymbol(newSymbol).Should().BeFalse();

        // Act
        var result = ProblematicSymbolsConfig.AddProblematicSymbol(newSymbol);

        // Assert
        result.Should().BeTrue("Should return true when adding a new symbol");
        ProblematicSymbolsConfig.IsProblematicSymbol(newSymbol).Should().BeTrue("Symbol should now be in the problematic list");
    }

    [Fact]
    public void AddProblematicSymbol_WithExistingSymbol_ShouldReturnFalse()
    {
        // Arrange
        var existingSymbol = "BELFB";

        // Act
        var result = ProblematicSymbolsConfig.AddProblematicSymbol(existingSymbol);

        // Assert
        result.Should().BeFalse("Should return false when trying to add an existing symbol");
        ProblematicSymbolsConfig.IsProblematicSymbol(existingSymbol).Should().BeTrue("Symbol should still be in the problematic list");
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public void AddProblematicSymbol_WithInvalidSymbol_ShouldReturnFalse(string invalidSymbol)
    {
        // Act
        var result = ProblematicSymbolsConfig.AddProblematicSymbol(invalidSymbol);

        // Assert
        result.Should().BeFalse("Should return false for invalid symbols");
    }

    [Fact]
    public void IsProblematicSymbol_WithNull_ShouldReturnFalse()
    {
        // Act
        var result = ProblematicSymbolsConfig.IsProblematicSymbol(null!);

        // Assert
        result.Should().BeFalse("Should return false for null symbol");
    }

    [Fact]
    public void AddProblematicSymbol_WithNull_ShouldReturnFalse()
    {
        // Act
        var result = ProblematicSymbolsConfig.AddProblematicSymbol(null!);

        // Assert
        result.Should().BeFalse("Should return false for null symbol");
    }

    [Fact]
    public void AddProblematicSymbol_ShouldHandleCaseInsensitivity()
    {
        // Arrange
        var lowerCaseSymbol = "test_case_symbol";
        var upperCaseSymbol = "TEST_CASE_SYMBOL";

        // Act
        var firstAdd = ProblematicSymbolsConfig.AddProblematicSymbol(lowerCaseSymbol);
        var secondAdd = ProblematicSymbolsConfig.AddProblematicSymbol(upperCaseSymbol);

        // Assert
        firstAdd.Should().BeTrue("Should add the symbol the first time");
        secondAdd.Should().BeFalse("Should not add the same symbol again (case insensitive)");
        
        ProblematicSymbolsConfig.IsProblematicSymbol(lowerCaseSymbol).Should().BeTrue();
        ProblematicSymbolsConfig.IsProblematicSymbol(upperCaseSymbol).Should().BeTrue();
    }

    [Fact]
    public void ProblematicSymbols_ShouldBeCaseInsensitive()
    {
        // Arrange
        var testCases = new[]
        {
            ("BELFB", "belfb"),
            ("BATRA", "batra")
        };

        // Act & Assert
        foreach (var (upper, lower) in testCases)
        {
            ProblematicSymbolsConfig.IsProblematicSymbol(upper).Should().BeTrue($"{upper} should be problematic");
            ProblematicSymbolsConfig.IsProblematicSymbol(lower).Should().BeTrue($"{lower} should be problematic (case insensitive)");
        }
    }

    [Fact]
    public void ProblematicSymbols_ShouldNotContainLegitimateSymbols()
    {
        // Arrange
        var legitimateSymbols = new[] { "BRK.A", "BRK.B", "AAPL", "MSFT", "SPY", "QQQ", "TSLA" };

        // Act & Assert
        foreach (var symbol in legitimateSymbols)
        {
            ProblematicSymbolsConfig.IsProblematicSymbol(symbol).Should().BeFalse($"{symbol} should not be considered problematic");
        }
    }

    [Fact]
    public void ProblematicSymbols_ShouldHaveMinimumExpectedCount()
    {
        // Act
        var count = ProblematicSymbolsConfig.ProblematicSymbols.Count;

        // Assert
        count.Should().BeGreaterOrEqualTo(2, "Should contain at least BELFB and BATRA");
    }
}
