using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Unit;

/// <summary>
/// Tests for TemporaryCacheExclusionService to ensure proper temporary exclusion of problematic symbols
/// </summary>
public class TemporaryCacheExclusionServiceTests
{
    private readonly IConnectionMultiplexer _mockRedis;
    private readonly IDatabase _mockDatabase;
    private readonly ILogger<TemporaryCacheExclusionService> _mockLogger;
    private readonly TemporaryCacheExclusionService _service;

    public TemporaryCacheExclusionServiceTests()
    {
        _mockRedis = Substitute.For<IConnectionMultiplexer>();
        _mockDatabase = Substitute.For<IDatabase>();
        _mockLogger = Substitute.For<ILogger<TemporaryCacheExclusionService>>();

        _mockRedis.GetDatabase(Arg.Any<int>()).Returns(_mockDatabase);

        _service = new TemporaryCacheExclusionService(_mockRedis, _mockLogger);
    }

    [Fact]
    public async Task IsExcludedAsync_WithExistingExclusion_ShouldReturnTrue()
    {
        // Arrange
        var symbol = "ARKW";
        _mockDatabase.KeyExistsAsync("cache_exclusion:ARKW").Returns(true);

        // Act
        var result = await _service.IsExcludedAsync(symbol);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsExcludedAsync_WithNonExistingExclusion_ShouldReturnFalse()
    {
        // Arrange
        var symbol = "AAPL";
        _mockDatabase.KeyExistsAsync("cache_exclusion:AAPL").Returns(false);

        // Act
        var result = await _service.IsExcludedAsync(symbol);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public async Task IsExcludedAsync_WithInvalidSymbol_ShouldReturnFalse(string invalidSymbol)
    {
        // Act
        var result = await _service.IsExcludedAsync(invalidSymbol);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsExcludedAsync_WithNull_ShouldReturnFalse()
    {
        // Act
        var result = await _service.IsExcludedAsync(null!);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task AddExclusionAsync_WithValidSymbol_ShouldSetRedisKey()
    {
        // Arrange
        var symbol = "ARKW";
        var reason = "Database caching error";
        var duration = TimeSpan.FromHours(12);

        // Act
        await _service.AddExclusionAsync(symbol, reason, duration);

        // Assert
        await _mockDatabase.Received(1).StringSetAsync(
            "cache_exclusion:ARKW",
            Arg.Any<RedisValue>(),
            duration);
    }

    [Fact]
    public async Task AddExclusionAsync_WithoutDuration_ShouldUseDefaultDuration()
    {
        // Arrange
        var symbol = "ARKW";
        var reason = "Database caching error";

        // Act
        await _service.AddExclusionAsync(symbol, reason);

        // Assert
        await _mockDatabase.Received(1).StringSetAsync(
            "cache_exclusion:ARKW",
            Arg.Any<RedisValue>(),
            TimeSpan.FromHours(24)); // Default duration
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public async Task AddExclusionAsync_WithInvalidSymbol_ShouldNotSetRedisKey(string invalidSymbol)
    {
        // Act
        await _service.AddExclusionAsync(invalidSymbol, "test reason");

        // Assert
        await _mockDatabase.DidNotReceive().StringSetAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<TimeSpan>());
    }

    [Fact]
    public async Task AddExclusionAsync_WithNull_ShouldNotSetRedisKey()
    {
        // Act
        await _service.AddExclusionAsync(null!, "test reason");

        // Assert
        await _mockDatabase.DidNotReceive().StringSetAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<TimeSpan>());
    }

    [Fact]
    public async Task RemoveExclusionAsync_WithValidSymbol_ShouldDeleteRedisKey()
    {
        // Arrange
        var symbol = "ARKW";
        _mockDatabase.KeyDeleteAsync("cache_exclusion:ARKW").Returns(true);

        // Act
        await _service.RemoveExclusionAsync(symbol);

        // Assert
        await _mockDatabase.Received(1).KeyDeleteAsync("cache_exclusion:ARKW");
    }

    [Fact]
    public async Task RemoveExclusionAsync_WithNonExistingSymbol_ShouldStillCallDelete()
    {
        // Arrange
        var symbol = "AAPL";
        _mockDatabase.KeyDeleteAsync("cache_exclusion:AAPL").Returns(false);

        // Act
        await _service.RemoveExclusionAsync(symbol);

        // Assert
        await _mockDatabase.Received(1).KeyDeleteAsync("cache_exclusion:AAPL");
    }

    [Fact]
    public async Task GetExclusionInfoAsync_WithExistingExclusion_ShouldReturnInfo()
    {
        // Arrange
        var symbol = "ARKW";
        var exclusionJson = """
        {
            "Symbol": "ARKW",
            "Reason": "Database caching error",
            "ExcludedAt": "2024-01-01T12:00:00Z",
            "ExpiresAt": "2024-01-02T12:00:00Z",
            "ExcludedBy": "system"
        }
        """;
        _mockDatabase.StringGetAsync("cache_exclusion:ARKW").Returns(exclusionJson);

        // Act
        var result = await _service.GetExclusionInfoAsync(symbol);

        // Assert
        result.Should().NotBeNull();
        result!.Symbol.Should().Be("ARKW");
        result.Reason.Should().Be("Database caching error");
        result.ExcludedBy.Should().Be("system");
    }

    [Fact]
    public async Task GetExclusionInfoAsync_WithNonExistingExclusion_ShouldReturnNull()
    {
        // Arrange
        var symbol = "AAPL";
        _mockDatabase.StringGetAsync("cache_exclusion:AAPL").Returns(RedisValue.Null);

        // Act
        var result = await _service.GetExclusionInfoAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task IsExcludedAsync_ShouldBeCaseInsensitive()
    {
        // Arrange
        var lowerSymbol = "arkw";
        var upperSymbol = "ARKW";
        
        _mockDatabase.KeyExistsAsync("cache_exclusion:ARKW").Returns(true);

        // Act
        var lowerResult = await _service.IsExcludedAsync(lowerSymbol);
        var upperResult = await _service.IsExcludedAsync(upperSymbol);

        // Assert
        lowerResult.Should().BeTrue("Should handle lowercase symbols");
        upperResult.Should().BeTrue("Should handle uppercase symbols");
        
        // Both should check the same Redis key (uppercase)
        await _mockDatabase.Received(2).KeyExistsAsync("cache_exclusion:ARKW");
    }

    [Fact]
    public async Task AddExclusionAsync_ShouldNormalizeSymbolToUppercase()
    {
        // Arrange
        var lowerSymbol = "arkw";
        var reason = "Test reason";

        // Act
        await _service.AddExclusionAsync(lowerSymbol, reason);

        // Assert
        await _mockDatabase.Received(1).StringSetAsync(
            "cache_exclusion:ARKW", // Should be normalized to uppercase
            Arg.Any<RedisValue>(),
            Arg.Any<TimeSpan>());
    }
}
