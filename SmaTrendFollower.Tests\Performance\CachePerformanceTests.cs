using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Console.Services;
using SmaTrendFollower.Console.Interfaces;
using SmaTrendFollower.Console.Models;
using Alpaca.Markets;
using System.Diagnostics;
using FluentAssertions;

namespace SmaTrendFollower.Tests.Performance;

/// <summary>
/// Performance comparison tests between different caching implementations
/// </summary>
public class CachePerformanceTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly string _testDatabasePath;

    public CachePerformanceTests()
    {
        _testDatabasePath = Path.GetTempFileName();
        
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        
        // Configure SQLite connection pooling
        services.AddSqliteConnectionPooling(config =>
        {
            config.MaxReadConnections = 10;
            config.InitialReadConnections = 3;
            config.MaxWriteConnections = 1;
            config.CommandTimeoutSeconds = 30;
            config.BusyTimeoutMs = 5000;
        });

        // Register pooled cache service
        services.AddSingleton<PooledStockBarCacheService>(provider =>
        {
            var poolFactory = provider.GetRequiredService<SqliteConnectionPoolFactory>();
            var logger = provider.GetRequiredService<ILogger<PooledStockBarCacheService>>();
            var connectionString = $"Data Source={_testDatabasePath}";
            var pool = poolFactory.GetPool(connectionString);
            return new PooledStockBarCacheService(pool, logger);
        });

        _serviceProvider = services.BuildServiceProvider();
        
        // Initialize database schema
        InitializeDatabaseSchema().GetAwaiter().GetResult();
    }

    [Fact]
    public async Task PooledCache_ShouldHandleHighConcurrencyWrites()
    {
        // Arrange
        var cacheService = _serviceProvider.GetRequiredService<PooledStockBarCacheService>();
        const int concurrentSymbols = 50;
        const int barsPerSymbol = 100;

        var testData = GenerateTestData(concurrentSymbols, barsPerSymbol);
        var stopwatch = Stopwatch.StartNew();

        // Act - Perform concurrent cache operations
        var tasks = testData.Select(async kvp =>
        {
            var symbol = kvp.Key;
            var bars = kvp.Value;
            
            await cacheService.CacheBarsAsync(symbol, "Day", bars);
            return symbol;
        });

        var results = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        results.Should().HaveCount(concurrentSymbols);
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(30000); // Should complete within 30 seconds
        
        // Verify data was cached correctly
        foreach (var kvp in testData)
        {
            var cachedBars = await cacheService.GetCachedBarsAsync(
                kvp.Key, "Day", DateTime.UtcNow.AddDays(-barsPerSymbol), DateTime.UtcNow);
            cachedBars.Should().HaveCount(barsPerSymbol);
        }

        Console.WriteLine($"Cached {concurrentSymbols} symbols with {barsPerSymbol} bars each in {stopwatch.ElapsedMilliseconds}ms");
        Console.WriteLine($"Average: {stopwatch.ElapsedMilliseconds / (double)concurrentSymbols:F2}ms per symbol");
    }

    [Fact]
    public async Task PooledCache_ShouldSupportConcurrentReads()
    {
        // Arrange
        var cacheService = _serviceProvider.GetRequiredService<PooledStockBarCacheService>();
        const int testSymbols = 10;
        const int barsPerSymbol = 50;
        const int concurrentReads = 20;

        // Pre-populate cache
        var testData = GenerateTestData(testSymbols, barsPerSymbol);
        foreach (var kvp in testData)
        {
            await cacheService.CacheBarsAsync(kvp.Key, "Day", kvp.Value);
        }

        var stopwatch = Stopwatch.StartNew();

        // Act - Perform concurrent read operations
        var readTasks = Enumerable.Range(0, concurrentReads).Select(async i =>
        {
            var symbol = $"TEST{i % testSymbols:D3}";
            var bars = await cacheService.GetCachedBarsAsync(
                symbol, "Day", DateTime.UtcNow.AddDays(-barsPerSymbol), DateTime.UtcNow);
            return bars.Count;
        });

        var results = await Task.WhenAll(readTasks);
        stopwatch.Stop();

        // Assert
        results.Should().HaveCount(concurrentReads);
        results.Should().AllSatisfy(count => count.Should().Be(barsPerSymbol));
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should be very fast

        Console.WriteLine($"Performed {concurrentReads} concurrent reads in {stopwatch.ElapsedMilliseconds}ms");
        Console.WriteLine($"Average: {stopwatch.ElapsedMilliseconds / (double)concurrentReads:F2}ms per read");
    }

    [Fact]
    public async Task PooledCache_ShouldHandleBulkOperations()
    {
        // Arrange
        var cacheService = _serviceProvider.GetRequiredService<PooledStockBarCacheService>();
        const int symbolCount = 100;
        const int barsPerSymbol = 200;

        var bulkData = new Dictionary<string, Dictionary<string, IEnumerable<IBar>>>();
        var testData = GenerateTestData(symbolCount, barsPerSymbol);
        
        foreach (var kvp in testData)
        {
            bulkData[kvp.Key] = new Dictionary<string, IEnumerable<IBar>>
            {
                ["Day"] = kvp.Value
            };
        }

        var stopwatch = Stopwatch.StartNew();

        // Act - Perform bulk cache operation
        await cacheService.CacheBarsAsync(bulkData);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(60000); // Should complete within 1 minute

        // Verify a sample of the data
        var sampleSymbol = testData.Keys.First();
        var cachedBars = await cacheService.GetCachedBarsAsync(
            sampleSymbol, "Day", DateTime.UtcNow.AddDays(-barsPerSymbol), DateTime.UtcNow);
        cachedBars.Should().HaveCount(barsPerSymbol);

        Console.WriteLine($"Bulk cached {symbolCount} symbols with {barsPerSymbol} bars each in {stopwatch.ElapsedMilliseconds}ms");
        Console.WriteLine($"Total bars: {symbolCount * barsPerSymbol:N0}");
        Console.WriteLine($"Bars per second: {(symbolCount * barsPerSymbol) / (stopwatch.ElapsedMilliseconds / 1000.0):N0}");
    }

    [Fact]
    public async Task ConnectionPool_ShouldProvideStatistics()
    {
        // Arrange
        var poolFactory = _serviceProvider.GetRequiredService<SqliteConnectionPoolFactory>();
        var cacheService = _serviceProvider.GetRequiredService<PooledStockBarCacheService>();

        // Act - Perform some operations to generate statistics
        var testData = GenerateTestData(5, 10);
        foreach (var kvp in testData)
        {
            await cacheService.CacheBarsAsync(kvp.Key, "Day", kvp.Value);
        }

        var statistics = poolFactory.GetPoolStatistics();

        // Assert
        statistics.Should().NotBeEmpty();
        var poolStat = statistics.Values.First();
        poolStat.DatabaseName.Should().NotBeNullOrEmpty();
        poolStat.TotalConnections.Should().BeGreaterThan(0);
    }

    private Dictionary<string, List<IBar>> GenerateTestData(int symbolCount, int barsPerSymbol)
    {
        var data = new Dictionary<string, List<IBar>>();
        var random = new Random(42); // Fixed seed for reproducible tests

        for (int s = 0; s < symbolCount; s++)
        {
            var symbol = $"TEST{s:D3}";
            var bars = new List<IBar>();
            var basePrice = 100m + (decimal)(random.NextDouble() * 100);

            for (int b = 0; b < barsPerSymbol; b++)
            {
                var date = DateTime.UtcNow.Date.AddDays(-barsPerSymbol + b);
                var price = basePrice + (decimal)(random.NextDouble() * 10 - 5);
                
                bars.Add(new TestBar
                {
                    TimeUtc = date,
                    Open = price,
                    High = price + (decimal)(random.NextDouble() * 2),
                    Low = price - (decimal)(random.NextDouble() * 2),
                    Close = price + (decimal)(random.NextDouble() * 2 - 1),
                    Volume = 1000000 + random.Next(500000),
                    Vwap = price,
                    TradeCount = (ulong)random.Next(1000, 10000)
                });
            }

            data[symbol] = bars;
        }

        return data;
    }

    private async Task InitializeDatabaseSchema()
    {
        var poolFactory = _serviceProvider.GetRequiredService<SqliteConnectionPoolFactory>();
        var connectionString = $"Data Source={_testDatabasePath}";
        var pool = poolFactory.GetPool(connectionString);

        using var connection = await pool.GetWriteConnectionAsync();
        
        // Create tables
        await connection.ExecuteNonQueryAsync(@"
            CREATE TABLE IF NOT EXISTS CachedStockBars (
                Id INTEGER PRIMARY KEY AUTOINCREMENT,
                Symbol TEXT NOT NULL,
                TimeFrame TEXT NOT NULL,
                TimeUtc DATETIME NOT NULL,
                Open DECIMAL NOT NULL,
                High DECIMAL NOT NULL,
                Low DECIMAL NOT NULL,
                Close DECIMAL NOT NULL,
                Volume DECIMAL NOT NULL,
                Vwap DECIMAL,
                TradeCount INTEGER,
                CachedAt DATETIME NOT NULL,
                UNIQUE(Symbol, TimeFrame, TimeUtc)
            )");

        await connection.ExecuteNonQueryAsync(@"
            CREATE TABLE IF NOT EXISTS StockCacheMetadata (
                CacheKey TEXT PRIMARY KEY,
                Symbol TEXT NOT NULL,
                TimeFrame TEXT NOT NULL,
                BarCount INTEGER NOT NULL,
                EarliestDataDate DATETIME NOT NULL,
                LatestDataDate DATETIME NOT NULL,
                LastUpdated DATETIME NOT NULL
            )");

        // Create indexes
        await connection.ExecuteNonQueryAsync(@"
            CREATE INDEX IF NOT EXISTS IX_CachedStockBars_Symbol_TimeFrame_TimeUtc 
            ON CachedStockBars(Symbol, TimeFrame, TimeUtc)");
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
        
        if (File.Exists(_testDatabasePath))
        {
            File.Delete(_testDatabasePath);
        }
    }
}

// Test implementation of IBar for performance tests
public class TestBar : IBar
{
    public DateTime TimeUtc { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public decimal Volume { get; set; }
    public decimal? Vwap { get; set; }
    public ulong? TradeCount { get; set; }
}
