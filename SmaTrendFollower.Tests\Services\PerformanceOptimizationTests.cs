using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using SmaTrendFollower.Console.Services;
using SmaTrendFollower.Console.Interfaces;
using SmaTrendFollower.Console.Models;
using FluentAssertions;
using System.Diagnostics;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Tests for performance optimization services
/// </summary>
public class PerformanceOptimizationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IStockBarCacheService> _mockCacheService;

    public PerformanceOptimizationTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockCacheService = new Mock<IStockBarCacheService>();

        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        
        // Configure services
        services.Configure<ResilienceConfiguration>(config =>
        {
            config.MaxRetryAttempts = 2;
            config.BaseDelayMs = 100;
            config.BackoffMultiplier = 2.0;
            config.CircuitBreakerFailureThreshold = 3;
            config.CircuitBreakerDurationSeconds = 5;
        });

        services.Configure<ResourceMonitoringConfiguration>(config =>
        {
            config.MonitoringIntervalSeconds = 1;
            config.EnablePeriodicLogging = false;
        });

        // Register mocks
        services.AddSingleton(_mockMarketDataService.Object);
        services.AddSingleton(_mockCacheService.Object);

        // Register services under test
        services.AddScoped<ResilientMarketDataService>();
        services.AddScoped<CacheWarmingService>();

        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task ResilientMarketDataService_ShouldRetryOnFailure()
    {
        // Arrange
        var resilientService = _serviceProvider.GetRequiredService<ResilientMarketDataService>();
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;

        // Setup mock to fail twice, then succeed
        var callCount = 0;
        _mockMarketDataService.Setup(m => m.GetStockBarsAsync(symbol, startDate, endDate))
            .Returns(() =>
            {
                callCount++;
                if (callCount <= 2)
                {
                    throw new HttpRequestException("Temporary failure");
                }
                return Task.FromResult<MarketDataResult?>(new MarketDataResult
                {
                    IsSuccess = true,
                    Items = new List<TestBar> { new TestBar { TimeUtc = DateTime.UtcNow, Close = 150m } }
                });
            });

        // Act
        var result = await resilientService.GetStockBarsAsync(symbol, startDate, endDate);

        // Assert
        result.Should().NotBeNull();
        result!.IsSuccess.Should().BeTrue();
        callCount.Should().Be(3); // 2 failures + 1 success
        _mockMarketDataService.Verify(m => m.GetStockBarsAsync(symbol, startDate, endDate), Times.Exactly(3));
    }

    [Fact]
    public async Task ResilientMarketDataService_ShouldOpenCircuitBreakerAfterThreshold()
    {
        // Arrange
        var resilientService = _serviceProvider.GetRequiredService<ResilientMarketDataService>();
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;

        // Setup mock to always fail
        _mockMarketDataService.Setup(m => m.GetStockBarsAsync(symbol, startDate, endDate))
            .ThrowsAsync(new HttpRequestException("Persistent failure"));

        // Act - Make enough calls to trigger circuit breaker
        var results = new List<MarketDataResult?>();
        for (int i = 0; i < 10; i++)
        {
            var result = await resilientService.GetStockBarsAsync(symbol, startDate, endDate);
            results.Add(result);
        }

        // Assert
        results.Should().HaveCount(10);
        results.Should().AllSatisfy(r => r.Should().NotBeNull());
        results.Should().AllSatisfy(r => r!.IsSuccess.Should().BeFalse());
        
        // Later calls should be rejected by circuit breaker (fewer actual service calls)
        var laterResults = results.Skip(5).ToList();
        laterResults.Should().Contain(r => r!.ErrorMessage.Contains("circuit breaker", StringComparison.OrdinalIgnoreCase));
    }

    [Fact]
    public async Task CacheWarmingService_ShouldTrackHitRates()
    {
        // Arrange
        var cacheWarmingService = _serviceProvider.GetRequiredService<CacheWarmingService>();

        // Act - Record some cache requests
        cacheWarmingService.RecordCacheRequest("AAPL", "Day", true);
        cacheWarmingService.RecordCacheRequest("AAPL", "Day", false);
        cacheWarmingService.RecordCacheRequest("MSFT", "Day", true);
        cacheWarmingService.RecordCacheRequest("MSFT", "Day", true);

        var report = cacheWarmingService.GetHitRateReport();

        // Assert
        report.Should().NotBeNull();
        report.TotalRequests.Should().Be(4);
        report.TotalHits.Should().Be(3);
        report.OverallHitRate.Should().Be(75.0);
        
        report.SymbolStats.Should().HaveCount(2);
        report.SymbolStats["AAPL:Day"].HitRate.Should().Be(50.0);
        report.SymbolStats["MSFT:Day"].HitRate.Should().Be(100.0);
    }

    [Fact]
    public async Task CacheWarmingService_ShouldIdentifyWarmingCandidates()
    {
        // Arrange
        var cacheWarmingService = _serviceProvider.GetRequiredService<CacheWarmingService>();

        // Record requests with low hit rates
        for (int i = 0; i < 10; i++)
        {
            cacheWarmingService.RecordCacheRequest("LOW_HIT", "Day", i < 3); // 30% hit rate
            cacheWarmingService.RecordCacheRequest("HIGH_HIT", "Day", i < 8); // 80% hit rate
        }

        // Act
        var candidates = cacheWarmingService.IdentifyWarmingCandidates();

        // Assert
        candidates.Should().Contain("LOW_HIT");
        candidates.Should().NotContain("HIGH_HIT");
    }

    [Fact]
    public async Task ResourceMonitoringService_ShouldCollectBasicStats()
    {
        // Arrange
        var config = Options.Create(new ResourceMonitoringConfiguration
        {
            MonitoringIntervalSeconds = 1,
            EnablePeriodicLogging = false
        });
        var logger = _serviceProvider.GetRequiredService<ILogger<ResourceMonitoringService>>();
        
        using var resourceService = new ResourceMonitoringService(config, logger);

        // Act - Start and let it run briefly
        var cts = new CancellationTokenSource();
        var task = resourceService.StartAsync(cts.Token);
        
        await Task.Delay(2000); // Let it run for 2 seconds
        
        cts.Cancel();
        await task;

        // Assert - Service should start and stop without errors
        task.IsCompletedSuccessfully.Should().BeTrue();
    }

    [Fact]
    public void PerformanceStats_ShouldCalculateCorrectly()
    {
        // Arrange
        var stats = new SymbolPerformanceStats
        {
            Symbol = "AAPL",
            TotalRequests = 10,
            SuccessfulRequests = 8,
            FailedRequests = 2,
            TotalLatency = TimeSpan.FromMilliseconds(5000)
        };

        // Act & Assert
        stats.SuccessRate.Should().Be(0.8);
        stats.AverageLatencyMs.Should().Be(500.0);
    }

    [Fact]
    public async Task OptimizedUniverseBuilder_ShouldHandleLargeSymbolList()
    {
        // Arrange
        var symbols = Enumerable.Range(1, 1000).Select(i => $"TEST{i:D4}").ToList();
        
        // Setup mock to return success for all symbols
        _mockMarketDataService.Setup(m => m.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(new MarketDataResult
            {
                IsSuccess = true,
                Items = new List<TestBar> 
                { 
                    new TestBar { TimeUtc = DateTime.UtcNow, Close = 100m, Volume = 1000000 }
                }
            });

        var config = new OptimizedUniverseConfig
        {
            MaxConcurrency = 10,
            BatchSize = 100,
            DelayBetweenBatchesMs = 10
        };

        var logger = _serviceProvider.GetRequiredService<ILogger<OptimizedUniverseBuilder>>();
        var builder = new OptimizedUniverseBuilder(_mockMarketDataService.Object, logger, config);

        var criteria = new UniverseFilterCriteria
        {
            MinAverageVolume = 500000,
            MinPrice = 10m,
            MaxPrice = 1000m
        };

        var stopwatch = Stopwatch.StartNew();

        // Act
        var result = await builder.BuildUniverseAsync(symbols, criteria);

        stopwatch.Stop();

        // Assert
        result.Should().NotBeNull();
        result.TotalCandidates.Should().Be(1000);
        result.QualifiedSymbols.Should().BeGreaterThan(0);
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(2)); // Should be much faster than 11+ minutes
        
        _logger.LogInformation("Universe building completed in {ElapsedMs}ms for {SymbolCount} symbols",
            stopwatch.ElapsedMilliseconds, symbols.Count);
    }

    private readonly ILogger _logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<PerformanceOptimizationTests>();

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

// Test implementations
public class TestBar : Alpaca.Markets.IBar
{
    public DateTime TimeUtc { get; set; }
    public decimal Open { get; set; } = 100m;
    public decimal High { get; set; } = 105m;
    public decimal Low { get; set; } = 95m;
    public decimal Close { get; set; } = 100m;
    public decimal Volume { get; set; } = 1000000m;
    public decimal? Vwap { get; set; }
    public ulong? TradeCount { get; set; }
}

public class MarketDataResult
{
    public bool IsSuccess { get; set; }
    public string? ErrorMessage { get; set; }
    public IEnumerable<Alpaca.Markets.IBar> Items { get; set; } = Enumerable.Empty<Alpaca.Markets.IBar>();
    public bool FromCache { get; set; }
    public bool Success => IsSuccess;
}
