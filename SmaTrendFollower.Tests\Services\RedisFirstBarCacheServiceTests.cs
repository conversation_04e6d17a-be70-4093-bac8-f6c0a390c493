using Microsoft.Extensions.Logging;
using Moq;
using StackExchange.Redis;
using SmaTrendFollower.Console.Services;
using SmaTrendFollower.Console.Interfaces;
using SmaTrendFollower.Console.Models;
using Alpaca.Markets;
using FluentAssertions;
using System.Text.Json;

namespace SmaTrendFollower.Tests.Services;

public class RedisFirstBarCacheServiceTests : IDisposable
{
    private readonly Mock<IConnectionMultiplexer> _mockRedis;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly Mock<IStockBarCacheService> _mockSqliteCache;
    private readonly Mock<ILogger<RedisFirstBarCacheService>> _mockLogger;
    private readonly RedisFirstBarCacheService _service;

    public RedisFirstBarCacheServiceTests()
    {
        _mockRedis = new Mock<IConnectionMultiplexer>();
        _mockDatabase = new Mock<IDatabase>();
        _mockSqliteCache = new Mock<IStockBarCacheService>();
        _mockLogger = new Mock<ILogger<RedisFirstBarCacheService>>();

        _mockRedis.Setup(r => r.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
               .Returns(_mockDatabase.Object);

        _service = new RedisFirstBarCacheService(_mockRedis.Object, _mockSqliteCache.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task CacheBarsAsync_ShouldWriteToRedisImmediately()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = CreateTestBars(3);

        _mockDatabase.Setup(d => d.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
                   .ReturnsAsync(true);

        // Act
        await _service.CacheBarsAsync(symbol, timeFrame, bars);

        // Assert
        _mockDatabase.Verify(d => d.StringSetAsync(
            It.Is<RedisKey>(k => k.ToString().Contains($"bars:{symbol}")),
            It.IsAny<RedisValue>(),
            It.IsAny<TimeSpan?>(),
            It.IsAny<When>(),
            It.IsAny<CommandFlags>()), Times.Once);

        _mockDatabase.Verify(d => d.StringSetAsync(
            It.Is<RedisKey>(k => k.ToString().Contains($"pending:bars:{symbol}")),
            It.IsAny<RedisValue>(),
            It.IsAny<TimeSpan?>(),
            It.IsAny<When>(),
            It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact]
    public async Task CacheBarsAsync_WhenRedisFailsShouldFallbackToSqlite()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = CreateTestBars(3);

        _mockDatabase.Setup(d => d.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
                   .ThrowsAsync(new RedisException("Redis connection failed"));

        // Act
        await _service.CacheBarsAsync(symbol, timeFrame, bars);

        // Assert
        _mockSqliteCache.Verify(s => s.CacheBarsAsync(symbol, timeFrame, bars), Times.Once);
    }

    [Fact]
    public async Task GetCachedBarsAsync_ShouldCheckRedisFirst()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow;
        var testBars = CreateTestBars(3);

        var cachedBarsData = testBars.Select(b => new CachedBarData
        {
            Symbol = symbol,
            TimeUtc = b.TimeUtc,
            Open = b.Open,
            High = b.High,
            Low = b.Low,
            Close = b.Close,
            Volume = b.Volume
        }).ToList();

        var serializedBars = JsonSerializer.Serialize(cachedBarsData);
        _mockDatabase.Setup(d => d.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
                   .ReturnsAsync(new RedisValue(serializedBars));

        // Act
        var result = await _service.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate);

        // Assert
        result.Should().HaveCount(3);
        _mockDatabase.Verify(d => d.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()), Times.Once);
        _mockSqliteCache.Verify(s => s.GetCachedBarsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()), Times.Never);
    }

    [Fact]
    public async Task GetCachedBarsAsync_WhenRedisMissShouldFallbackToSqlite()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var startDate = DateTime.UtcNow.AddDays(-5);
        var endDate = DateTime.UtcNow;
        var testBars = CreateTestBars(3);

        _mockDatabase.Setup(d => d.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
                   .ReturnsAsync(RedisValue.Null);

        _mockSqliteCache.Setup(s => s.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate))
                      .ReturnsAsync(testBars);

        // Act
        var result = await _service.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate);

        // Assert
        result.Should().HaveCount(3);
        _mockDatabase.Verify(d => d.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()), Times.Once);
        _mockSqliteCache.Verify(s => s.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate), Times.Once);
    }

    [Fact]
    public async Task GetPendingBarsAsync_ShouldReturnPendingEntries()
    {
        // Arrange
        var mockServer = new Mock<IServer>();
        var pendingKeys = new RedisKey[] { "pending:bars:AAPL:Day", "pending:bars:MSFT:Day" };
        
        _mockRedis.Setup(r => r.GetEndPoints(It.IsAny<bool>()))
               .Returns(new System.Net.EndPoint[] { new System.Net.IPEndPoint(System.Net.IPAddress.Loopback, 6379) });
        
        _mockRedis.Setup(r => r.GetServer(It.IsAny<System.Net.EndPoint>(), It.IsAny<object>()))
               .Returns(mockServer.Object);

        mockServer.Setup(s => s.Keys(It.IsAny<int>(), It.IsAny<RedisValue>(), It.IsAny<int>(), It.IsAny<long>(), It.IsAny<int>(), It.IsAny<CommandFlags>()))
                 .Returns(pendingKeys);

        var pendingData = new PendingBarData
        {
            Symbol = "AAPL",
            TimeFrame = "Day",
            QueuedAt = DateTime.UtcNow,
            BarCount = 1,
            Bars = new List<CachedBarData>
            {
                new CachedBarData { Symbol = "AAPL", TimeUtc = DateTime.UtcNow, Close = 150.0m }
            }
        };

        var serializedData = JsonSerializer.Serialize(pendingData);
        _mockDatabase.Setup(d => d.StringGetAsync(It.IsAny<RedisKey[]>(), It.IsAny<CommandFlags>()))
                   .ReturnsAsync(new RedisValue[] { serializedData, serializedData });

        // Act
        var result = await _service.GetPendingBarsAsync();

        // Assert
        result.Should().HaveCount(2);
        result.Values.Should().AllSatisfy(data => data.Symbol.Should().Be("AAPL"));
    }

    private static List<IBar> CreateTestBars(int count)
    {
        var bars = new List<IBar>();
        var baseTime = DateTime.UtcNow.Date.AddDays(-count);

        for (int i = 0; i < count; i++)
        {
            bars.Add(new TestBar
            {
                TimeUtc = baseTime.AddDays(i),
                Open = 100 + i,
                High = 105 + i,
                Low = 95 + i,
                Close = 102 + i,
                Volume = 1000000 + (i * 10000)
            });
        }

        return bars;
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}

// Test implementation of IBar
public class TestBar : IBar
{
    public DateTime TimeUtc { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public decimal Volume { get; set; }
    public decimal? Vwap { get; set; }
    public ulong? TradeCount { get; set; }
}
