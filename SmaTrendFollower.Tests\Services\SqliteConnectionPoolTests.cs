using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using SmaTrendFollower.Console.Services;
using FluentAssertions;
using System.Data;

namespace SmaTrendFollower.Tests.Services;

public class SqliteConnectionPoolTests : IDisposable
{
    private readonly Mock<ILogger<SqliteConnectionPool>> _mockLogger;
    private readonly SqlitePoolConfiguration _config;
    private readonly string _testConnectionString;
    private SqliteConnectionPool? _pool;

    public SqliteConnectionPoolTests()
    {
        _mockLogger = new Mock<ILogger<SqliteConnectionPool>>();
        _config = new SqlitePoolConfiguration
        {
            MaxReadConnections = 5,
            InitialReadConnections = 2,
            MaxWriteConnections = 1,
            CommandTimeoutSeconds = 30,
            BusyTimeoutMs = 5000
        };
        
        // Use in-memory database for testing
        _testConnectionString = "Data Source=:memory:";
    }

    [Fact]
    public async Task GetReadConnectionAsync_ShouldReturnHealthyConnection()
    {
        // Arrange
        _pool = CreatePool();

        // Act
        using var connection = await _pool.GetReadConnectionAsync();

        // Assert
        connection.Should().NotBeNull();
        connection.State.Should().Be(ConnectionState.Open);
        connection.IsHealthy().Should().BeTrue();
    }

    [Fact]
    public async Task GetWriteConnectionAsync_ShouldReturnHealthyConnection()
    {
        // Arrange
        _pool = CreatePool();

        // Act
        using var connection = await _pool.GetWriteConnectionAsync();

        // Assert
        connection.Should().NotBeNull();
        connection.State.Should().Be(ConnectionState.Open);
        connection.IsHealthy().Should().BeTrue();
        connection.GetStats().IsWrite.Should().BeTrue();
    }

    [Fact]
    public async Task GetWriteConnectionAsync_ShouldSerializeWrites()
    {
        // Arrange
        _pool = CreatePool();
        var writeStarted = new List<DateTime>();
        var writeCompleted = new List<DateTime>();

        // Act - Start multiple write operations concurrently
        var writeTasks = Enumerable.Range(0, 3).Select(async i =>
        {
            using var connection = await _pool.GetWriteConnectionAsync();
            writeStarted.Add(DateTime.UtcNow);
            
            // Simulate some work
            await Task.Delay(100);
            
            writeCompleted.Add(DateTime.UtcNow);
        });

        await Task.WhenAll(writeTasks);

        // Assert - Writes should be serialized (no overlapping)
        writeStarted.Should().HaveCount(3);
        writeCompleted.Should().HaveCount(3);
        
        // Each write should complete before the next one starts (with some tolerance)
        for (int i = 1; i < writeStarted.Count; i++)
        {
            writeStarted[i].Should().BeOnOrAfter(writeCompleted[i - 1].AddMilliseconds(-50));
        }
    }

    [Fact]
    public async Task GetReadConnectionAsync_ShouldAllowConcurrentReads()
    {
        // Arrange
        _pool = CreatePool();
        var readStarted = new List<DateTime>();
        var readCompleted = new List<DateTime>();

        // Act - Start multiple read operations concurrently
        var readTasks = Enumerable.Range(0, 3).Select(async i =>
        {
            using var connection = await _pool.GetReadConnectionAsync();
            readStarted.Add(DateTime.UtcNow);
            
            // Simulate some work
            await Task.Delay(100);
            
            readCompleted.Add(DateTime.UtcNow);
        });

        await Task.WhenAll(readTasks);

        // Assert - Reads should be concurrent (overlapping)
        readStarted.Should().HaveCount(3);
        readCompleted.Should().HaveCount(3);
        
        // All reads should start within a short time window (concurrent)
        var firstStart = readStarted.Min();
        var lastStart = readStarted.Max();
        (lastStart - firstStart).Should().BeLessThan(TimeSpan.FromMilliseconds(50));
    }

    [Fact]
    public async Task PooledConnection_ShouldSupportTransactions()
    {
        // Arrange
        _pool = CreatePool();
        
        // Create a test table first
        using (var setupConnection = await _pool.GetWriteConnectionAsync())
        {
            await setupConnection.ExecuteNonQueryAsync("CREATE TABLE TestTable (Id INTEGER PRIMARY KEY, Value TEXT)");
        }

        // Act & Assert
        using var connection = await _pool.GetWriteConnectionAsync();
        
        await connection.ExecuteInTransactionAsync(async transaction =>
        {
            await connection.ExecuteNonQueryAsync("INSERT INTO TestTable (Value) VALUES ('test1')");
            await connection.ExecuteNonQueryAsync("INSERT INTO TestTable (Value) VALUES ('test2')");
        });

        // Verify data was committed
        var count = await connection.ExecuteScalarAsync("SELECT COUNT(*) FROM TestTable");
        count.Should().Be(2L);
    }

    [Fact]
    public async Task PooledConnection_ShouldRollbackOnTransactionFailure()
    {
        // Arrange
        _pool = CreatePool();
        
        // Create a test table first
        using (var setupConnection = await _pool.GetWriteConnectionAsync())
        {
            await setupConnection.ExecuteNonQueryAsync("CREATE TABLE TestTable (Id INTEGER PRIMARY KEY, Value TEXT UNIQUE)");
        }

        // Act & Assert
        using var connection = await _pool.GetWriteConnectionAsync();
        
        var exception = await Assert.ThrowsAsync<Microsoft.Data.Sqlite.SqliteException>(async () =>
        {
            await connection.ExecuteInTransactionAsync(async transaction =>
            {
                await connection.ExecuteNonQueryAsync("INSERT INTO TestTable (Value) VALUES ('test1')");
                await connection.ExecuteNonQueryAsync("INSERT INTO TestTable (Value) VALUES ('test1')"); // Duplicate - should fail
            });
        });

        // Verify no data was committed due to rollback
        var count = await connection.ExecuteScalarAsync("SELECT COUNT(*) FROM TestTable");
        count.Should().Be(0L);
    }

    [Fact]
    public async Task ConnectionPool_ShouldReuseReadConnections()
    {
        // Arrange
        _pool = CreatePool();

        // Act - Get and return a read connection
        PooledSqliteConnection firstConnection;
        using (firstConnection = await _pool.GetReadConnectionAsync())
        {
            // Use the connection
            await firstConnection.ExecuteScalarAsync("SELECT 1");
        }

        // Get another read connection
        using var secondConnection = await _pool.GetReadConnectionAsync();

        // Assert - Should reuse the same underlying connection
        // Note: This is hard to test directly, but we can verify the pool behavior
        secondConnection.Should().NotBeNull();
        secondConnection.IsHealthy().Should().BeTrue();
    }

    [Fact]
    public void ConnectionPool_ShouldInitializeWithConfiguredReadConnections()
    {
        // Arrange & Act
        _pool = CreatePool();

        // Assert - Pool should be created successfully
        _pool.Should().NotBeNull();
        
        // Note: We can't directly test the initial connection count without exposing internal state
        // But we can verify the pool works correctly
    }

    [Fact]
    public async Task ConnectionPool_ShouldHandleHighConcurrency()
    {
        // Arrange
        _pool = CreatePool();
        const int concurrentOperations = 20;

        // Act - Perform many concurrent read operations
        var tasks = Enumerable.Range(0, concurrentOperations).Select(async i =>
        {
            using var connection = await _pool.GetReadConnectionAsync();
            var result = await connection.ExecuteScalarAsync("SELECT @value", CancellationToken.None);
            return result;
        });

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(concurrentOperations);
        results.Should().AllSatisfy(result => result.Should().NotBeNull());
    }

    private SqliteConnectionPool CreatePool()
    {
        var options = Options.Create(_config);
        return new SqliteConnectionPool(_testConnectionString, options, _mockLogger.Object);
    }

    public void Dispose()
    {
        _pool?.Dispose();
    }
}

// Extension method for testing
public static class PooledConnectionTestExtensions
{
    public static async Task<object?> ExecuteScalarAsync(this PooledSqliteConnection connection, string sql, CancellationToken cancellationToken = default)
    {
        using var command = connection.CreateCommand(sql);
        return await command.ExecuteScalarAsync(cancellationToken);
    }
}
