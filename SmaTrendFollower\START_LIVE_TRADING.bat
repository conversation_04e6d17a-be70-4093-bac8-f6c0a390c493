@echo off
echo.
echo ========================================
echo   SmaTrendFollower - Live Trading
echo ========================================
echo.
echo Starting automated trading system...
echo Account: Alpaca Live ($17,066.39)
echo Universe: 3,736 qualified symbols
echo Strategy: SMA Trend Following
echo Mode: Fully Automated (No user input)
echo.
echo Press Ctrl+C to stop trading at any time
echo.
pause

cd /d "C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower"
dotnet run --project SmaTrendFollower.Console --configuration Release

echo.
echo Trading session ended.
pause
