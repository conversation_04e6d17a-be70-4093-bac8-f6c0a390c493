# SmaTrendFollower - Live Trading Startup Script
# PowerShell version with enhanced monitoring

Write-Host "========================================" -ForegroundColor Green
Write-Host "   SmaTrendFollower - Live Trading" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 Starting automated trading system..." -ForegroundColor Yellow
Write-Host "💰 Account: Alpaca Live ($17,066.39)" -ForegroundColor Cyan
Write-Host "📊 Universe: 3,736 qualified symbols" -ForegroundColor Cyan
Write-Host "📈 Strategy: SMA Trend Following" -ForegroundColor Cyan
Write-Host "🤖 Mode: Fully Automated (No user input)" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚠️  Press Ctrl+C to stop trading at any time" -ForegroundColor Red
Write-Host ""

# Check if project exists
$projectPath = "C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower\SmaTrendFollower.Console\SmaTrendFollower.Console.csproj"
if (-not (Test-Path $projectPath)) {
    Write-Host "❌ Project not found at: $projectPath" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Project found" -ForegroundColor Green
Write-Host ""

# Prompt for confirmation
$confirmation = Read-Host "Ready to start live trading? (y/N)"
if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
    Write-Host "Trading cancelled by user" -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "🎯 Launching SmaTrendFollower..." -ForegroundColor Green
Write-Host "📅 Session started: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Change to project directory and start trading
Set-Location "C:\Users\<USER>\OneDrive\AugmentCode\SmaTrendFollower"

try {
    & dotnet run --project SmaTrendFollower.Console --configuration Release
}
catch {
    Write-Host "❌ Error starting trading system: $_" -ForegroundColor Red
}
finally {
    Write-Host ""
    Write-Host "📅 Session ended: $(Get-Date)" -ForegroundColor Gray
    Write-Host "🛑 Trading session completed" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
}
