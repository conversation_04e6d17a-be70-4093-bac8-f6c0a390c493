# SmaTrendFollower Troubleshooting Guide

## Common Issues & Solutions

### 1. Redis Connection Errors
**Error**: `RedisConnectionException: It was not possible to connect to the redis server(s)`

**Root Cause**: Redis server not running or connection string incorrect

**Solutions**:
1. Verify Redis server is running at *************:6379
2. Check network connectivity to Redis server
3. Validate Redis connection string in appsettings.json
4. Add `abortConnect=false` to connection string for retry behavior

**Prevention**: Implement Redis health checks and connection retry logic

### 2. ArgumentException Date Range Errors
**Error**: `ArgumentException: startDate must be before endDate`

**Root Cause**: Date calculation logic creating invalid ranges

**Solutions**:
1. All signal generators use: `endDate = DateTime.UtcNow.Date.AddDays(-1)`
2. All signal generators use: `startDate = endDate.AddDays(-250/-300)`
3. MarketDataService validates and adjusts invalid ranges
4. Cache services prevent startDate > endDate scenarios

**Prevention**: Comprehensive date validation guardrails already implemented

### 3. Dependency Injection Failures
**Error**: `ISlippageForecaster service not registered`

**Root Cause**: Missing service registration in DI container

**Solutions**:
1. Verify all services are registered in ServiceConfiguration.cs
2. Check for circular dependencies
3. Ensure proper service lifetime scopes (Scoped for trade cycles)
4. Validate interface implementations are properly bound

**Prevention**: Unit tests for DI container configuration

### 4. Slow Signal Processing
**Error**: Signal generation taking 3844+ seconds

**Root Cause**: Inefficient data retrieval or processing bottlenecks

**Solutions**:
1. Implement parallel processing with SemaphoreSlim rate limiting
2. Use ConcurrentBag for thread-safe collections
3. Optimize database queries (7s → 400ms performance improvement achieved)
4. Enable adaptive rate limiting and batch processing

**Prevention**: Performance monitoring and alerting thresholds

### 5. Missing Position Sizing Model
**Error**: ML position sizing model not available

**Root Cause**: Model requires real trade data to function

**Solutions**:
1. System falls back to rule-based position sizing
2. ML model will be trained after sufficient trade data is collected
3. Ensure Model/position_model.zip exists for ML mode

**Prevention**: Graceful fallback to rule-based sizing is already implemented

### 6. Database Query Issues
**Error**: Slow or failed database operations

**Root Cause**: Inefficient queries or connection problems

**Solutions**:
1. Database optimization achieved (15-20x performance improvement)
2. Implement circuit breaker patterns with Redis fallback
3. Use connection pooling and proper timeout settings
4. Monitor query performance with Prometheus metrics

**Prevention**: Database health checks and performance monitoring

### 7. Bar Caching Failures
**Error**: Cache misses or stale data

**Root Cause**: Cache invalidation or data freshness issues

**Solutions**:
1. Implement flexible staleness thresholds (18min market hours, 8h after hours)
2. Use Redis TTL settings (24h signals/universe, 7d stops, 10min synthetic VIX)
3. Enable emergency mode with relaxed staleness (1 hour)
4. Implement cache warming strategies

**Prevention**: Comprehensive caching strategy with fallback mechanisms

### 8. Alpaca Account Restrictions
**Error**: "Account is restricted to liquidation only"

**Root Cause**: Paper trading account limitations or insufficient buying power

**Solutions**:
1. Paper accounts can only buy during market hours (9:30 AM - 4:00 PM ET)
2. After hours, only liquidation is allowed (normal behavior)
3. Check account buying power and margin requirements
4. Use live account for full trading capabilities

**Prevention**: Account status monitoring and appropriate trading hour restrictions

### 9. Market Data Staleness
**Error**: Insufficient or stale market data

**Root Cause**: API rate limits or data provider issues

**Solutions**:
1. Implement VIX fallback strategy: Polygon → web scraping → synthetic VIX → halt
2. Use adaptive rate limiting (1-100 req/sec range)
3. Enable synthetic data generation for emergencies
4. Implement data freshness validation

**Prevention**: Multi-source data strategy with automatic failover

### 10. System Startup Hanging
**Error**: System hangs during initialization

**Root Cause**: Hosted services or dependency resolution issues

**Solutions**:
1. Defer trading cycle manager resolution to avoid startup hanging
2. Disable hosted services during startup (`--disable-hosted-services`)
3. Use StartupDataBootstrapper pattern for initialization
4. Implement proper cancellation token handling

**Prevention**: Staged initialization with health checks

## Diagnostic Commands

### Check System Status
```powershell
.\monitor-trading.ps1
```

### View Recent Logs
```cmd
Get-Content "Logs\sma-trend-follower-$(Get-Date -Format 'yyyyMMdd').log" -Tail 50
```

### Test Redis Connection
```cmd
redis-cli -h ************* -p 6379 ping
```

### Check Database Performance
```sql
SELECT COUNT(*) FROM Signals WHERE CreatedAt > NOW() - INTERVAL '1 hour';
```

### Validate API Credentials
```powershell
.\test-api-connections.ps1
```

## Emergency Procedures

### 1. Emergency Stop Trading
```powershell
.\emergency-stop.ps1 -ClosePositions -Force
```

### 2. Clear Redis Cache
```cmd
redis-cli -h ************* -p 6379 FLUSHDB
```

### 3. Restart Services
```powershell
Restart-Service Redis
Restart-Service PostgreSQL
```

### 4. Force System Restart
```cmd
taskkill /f /im SmaTrendFollower.Console.exe
dotnet run --project SmaTrendFollower.Console --configuration Release
```

## Monitoring & Alerts

### Key Metrics to Monitor
- Signal generation latency (< 500ms target)
- Database query performance (< 400ms target)
- Redis connection health
- API rate limit usage
- Account buying power
- Position count and exposure
- Anomaly detection z-scores

### Alert Thresholds
- Signal latency > 1 second
- Database queries > 1 second
- Redis connection failures
- API rate limit > 90%
- Account loss > 4% daily
- Z-score < -3.76 (anomaly detection)

### Discord Notifications
Automated alerts sent for:
- Trade executions
- System errors
- Performance degradation
- Account restrictions
- Emergency stops
