{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "SmaTrendFollower.Services.PolygonSymbolUniverseService": "Debug",
      "SmaTrendFollower.Services.PolygonSymbolSnapshotService": "Debug",
      "SmaTrendFollower.Services.DailyUniverseRefreshService": "Information",
      "SmaTrendFollower.Services.WebSocketSymbolSubscriptionManager": "Information",
      "SmaTrendFollower.Services.MarketScheduleCoordinatorService": "Information"
    }
  },
  
  "AllowedHosts": "*",
  
  // Enable Polygon-based universe management
  "UsePolygonUniverse": true,
  
  // Polygon Symbol Universe Configuration
  "PolygonUniverse": {
    "PageSize": 1000,
    "MaxSymbols": 0,
    "IncludedMarkets": ["stocks"],
    "IncludedTypes": ["CS", "ETF"],
    "ActiveOnly": true,
    "DelayBetweenCalls": 200,
    "CacheTtlHours": 168
  },
  
  // Polygon Snapshot Service Configuration
  "PolygonSnapshot": {
    "BatchSize": 50,
    "MaxConcurrency": 5,
    "DelayBetweenCalls": 200
  },
  
  // Daily Universe Refresh Configuration
  "UniverseRefresh": {
    "RefreshTimeUtc": "12:30:00",
    "MinPrice": 10.0,
    "MinAverageVolume": 500000,
    "MinVolatilityPercent": 1.0,
    "MaxCandidates": 200,
    "AnalysisPeriodDays": 20,
    "MinMarketCap": null,
    "CacheTtlHours": 24,
    "ExcludedExchanges": [],
    "ExcludedTypes": []
  },
  
  // WebSocket Subscription Manager Configuration
  "SubscriptionManager": {
    "SubscriptionTimeUtc": "13:25:00",
    "MaxSubscriptions": 200,
    "BatchSize": 50,
    "DelayBetweenBatches": 1000
  },
  
  // Market Schedule Coordinator Configuration
  "MarketSchedule": {
    "UniverseRefreshTimeEt": "08:30:00",
    "SubscriptionSetupTimeEt": "09:25:00",
    "TradingStartTimeEt": "09:30:00",
    "TradingEndTimeEt": "16:00:00",
    "TriggerWindowMinutes": 30
  },
  
  // Enhanced DynamicUniverseProvider Configuration
  "DynamicUniverse": {
    "UsePolygonIntegration": true,
    "FallbackToStaticSymbols": true,
    "MaxConcurrentBatches": 3,
    "BatchSize": 20,
    "DefaultCriteria": {
      "MinPrice": 10.0,
      "MinAverageVolume": 500000,
      "MinVolatilityPercent": 1.0,
      "AnalysisPeriodDays": 20,
      "MaxSymbols": 200
    }
  },
  
  // Existing Trading Configuration
  "Trading": {
    "MaxPositions": 10,
    "RiskPerTrade": 0.01,
    "MaxDailyLoss": 0.05,
    "TradingHours": {
      "Start": "09:30:00",
      "End": "16:00:00",
      "TimeZone": "Eastern Standard Time"
    }
  },
  
  // Signal Generation Configuration
  "SignalGeneration": {
    "SmaShortPeriod": 20,
    "SmaLongPeriod": 50,
    "VolumeThreshold": 1000000,
    "VolatilityThreshold": 0.02,
    "MinimumPrice": 10.0
  },
  
  // Risk Management Configuration
  "RiskManagement": {
    "MaxPositionSize": 1000,
    "StopLossPercent": 0.02,
    "TakeProfitPercent": 0.04,
    "MaxDrawdown": 0.10,
    "RiskDollarsPerTrade": 100
  },
  
  // Portfolio Gate Configuration
  "PortfolioGate": {
    "RequireSpyAboveSma200": true,
    "MaxVixLevel": 30.0,
    "MinMarketVolume": **********,
    "MarketRegimeFilter": true
  },
  
  // Market Data Configuration
  "MarketData": {
    "PrimaryProvider": "Alpaca",
    "FallbackProvider": "Polygon",
    "DataFreshnessThresholdMinutes": 18,
    "CacheEnabled": true,
    "CacheTtlMinutes": 60
  },
  
  // Polygon WebSocket Configuration
  "PolygonWebSocket": {
    "ReconnectAttempts": 5,
    "ReconnectDelayMs": 5000,
    "HeartbeatIntervalMs": 30000,
    "MaxSubscriptions": 200,
    "BatchSubscriptionSize": 50
  },
  
  // Redis Configuration
  "Redis": {
    "ConnectionString": "localhost:6379",
    "Database": 0,
    "KeyPrefix": "smatrendfollower:",
    "DefaultTtlHours": 24,
    "MaxRetries": 3,
    "RetryDelayMs": 1000
  },
  
  // Health Check Configuration
  "HealthChecks": {
    "Enabled": true,
    "Endpoints": {
      "Alpaca": "/health/alpaca",
      "Polygon": "/health/polygon",
      "Redis": "/health/redis",
      "Universe": "/health/universe"
    },
    "TimeoutSeconds": 30
  },
  
  // Performance Monitoring
  "Performance": {
    "EnableMetrics": true,
    "MetricsInterval": "00:01:00",
    "LogSlowOperations": true,
    "SlowOperationThresholdMs": 5000,
    "EnableTracing": false
  },
  
  // Notification Configuration
  "Notifications": {
    "Discord": {
      "Enabled": true,
      "WebhookUrl": null,
      "NotificationLevels": ["Error", "Warning", "Information"],
      "TradingNotifications": true,
      "SystemNotifications": true
    },
    "Email": {
      "Enabled": false,
      "SmtpServer": null,
      "SmtpPort": 587,
      "Username": null,
      "Password": null,
      "FromAddress": null,
      "ToAddresses": []
    }
  },
  
  // Development and Testing
  "Development": {
    "EnableTestMode": false,
    "MockExternalServices": false,
    "LogAllApiCalls": false,
    "BypassMarketHours": false,
    "SimulateMarketData": false
  },
  
  // Feature Flags
  "FeatureFlags": {
    "UsePolygonUniverse": true,
    "EnableAdvancedFiltering": true,
    "EnableRealTimeSubscriptions": true,
    "EnableMarketScheduleCoordination": true,
    "EnablePerformanceMonitoring": true,
    "EnableCaching": true,
    "EnableBackgroundServices": true
  }
}
