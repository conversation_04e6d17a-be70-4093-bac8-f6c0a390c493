# Clear Redis Universe Cache
Write-Host "Clearing Redis universe cache..." -ForegroundColor Cyan

$redisHost = "*************"
$redisPort = "6379"

try {
    # Connect to Redis using TCP
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect($redisHost, $redisPort)
    $stream = $tcpClient.GetStream()
    
    # Function to send Redis command
    function Send-RedisCommand {
        param($command)
        $bytes = [System.Text.Encoding]::UTF8.GetBytes("$command`r`n")
        $stream.Write($bytes, 0, $bytes.Length)
        $stream.Flush()
        
        # Read response
        $buffer = New-Object byte[] 1024
        $bytesRead = $stream.Read($buffer, 0, $buffer.Length)
        $response = [System.Text.Encoding]::UTF8.GetString($buffer, 0, $bytesRead)
        return $response
    }
    
    Write-Host "Connected to Redis at ${redisHost}:${redisPort}" -ForegroundColor Green
    
    # Select database 0
    $response = Send-RedisCommand "SELECT 0"
    Write-Host "Selected database 0" -ForegroundColor Gray
    
    # Delete universe keys
    $deletedCount = 0
    
    # Try to delete common universe keys
    $keysToDelete = @(
        "universe:candidates",
        "universe:today", 
        "universe:metadata",
        "Universe:candidates",
        "Universe:today",
        "Universe:metadata"
    )
    
    foreach ($key in $keysToDelete) {
        $response = Send-RedisCommand "DEL $key"
        if ($response -match ":1") {
            $deletedCount++
            Write-Host "Deleted: $key" -ForegroundColor Green
        } else {
            Write-Host "Key not found: $key" -ForegroundColor Gray
        }
    }
    
    # Close connection
    $stream.Close()
    $tcpClient.Close()
    
    Write-Host "Cleared $deletedCount universe-related keys from Redis" -ForegroundColor Green
    Write-Host "Universe data will be refreshed on next fetch" -ForegroundColor Cyan
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Done!" -ForegroundColor Green
