# Simple Redis Universe Cache Clearer
Write-Host "🧹 Clearing Redis universe cache..." -ForegroundColor Cyan

$redisHost = "*************"
$redisPort = "6379"

try {
    # Use .NET TCP client to send Redis commands
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect($redisHost, $redisPort)
    $stream = $tcpClient.GetStream()
    
    # Function to send Redis command
    function Send-RedisCommand {
        param($command)
        $bytes = [System.Text.Encoding]::UTF8.GetBytes("$command`r`n")
        $stream.Write($bytes, 0, $bytes.Length)
        $stream.Flush()
        
        # Read response
        $buffer = New-Object byte[] 1024
        $bytesRead = $stream.Read($buffer, 0, $buffer.Length)
        $response = [System.Text.Encoding]::UTF8.GetString($buffer, 0, $bytesRead)
        return $response
    }
    
    Write-Host "📡 Connected to Redis at ${redisHost}:${redisPort}" -ForegroundColor Green
    
    # Select database 0
    $response = Send-RedisCommand "SELECT 0"
    Write-Host "Selected database 0: $response" -ForegroundColor Gray
    
    # Delete universe keys
    $deletedCount = 0
    
    # Try to delete common universe keys
    $keysToDelete = @(
        "universe:candidates",
        "universe:today", 
        "universe:metadata",
        "Universe:candidates",
        "Universe:today",
        "Universe:metadata"
    )
    
    foreach ($key in $keysToDelete) {
        $response = Send-RedisCommand "DEL $key"
        if ($response -match ":1") {
            $deletedCount++
            Write-Host "🗑️  Deleted: $key" -ForegroundColor Green
        } else {
            Write-Host "⚪ Key not found: $key" -ForegroundColor Gray
        }
    }
    
    # Also try to get and delete any keys matching patterns
    $patterns = @("universe:*", "Universe:*")
    foreach ($pattern in $patterns) {
        $response = Send-RedisCommand "KEYS $pattern"
        if ($response -and $response -notmatch "^\*0") {
            # Parse keys from response
            $lines = $response -split "`r`n"
            foreach ($line in $lines) {
                if ($line -and $line -notmatch "^\*" -and $line -notmatch "^\+" -and $line.Trim()) {
                    $key = $line.Trim()
                    $delResponse = Send-RedisCommand "DEL $key"
                    if ($delResponse -match ":1") {
                        $deletedCount++
                        Write-Host "🗑️  Deleted: $key" -ForegroundColor Green
                    }
                }
            }
        }
    }
    
    # Close connection
    $stream.Close()
    $tcpClient.Close()
    
    Write-Host "✅ Cleared $deletedCount universe-related keys from Redis" -ForegroundColor Green
    Write-Host "🔄 Universe data will be refreshed on next fetch" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "✨ Done!" -ForegroundColor Green
