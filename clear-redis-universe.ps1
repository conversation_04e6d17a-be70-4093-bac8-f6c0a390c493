# Clear Redis Universe Cache Script
# This script clears universe-related keys from Redis to force a fresh fetch

Write-Host "🧹 Clearing Redis universe cache..." -ForegroundColor Cyan

# Redis connection details
$redisHost = "*************"
$redisPort = "6379"
$redisDatabase = "0"

try {
    # Load StackExchange.Redis assembly (if available)
    Add-Type -Path "C:\Program Files\dotnet\shared\Microsoft.NETCore.App\*\StackExchange.Redis.dll" -ErrorAction SilentlyContinue
    
    if (-not ([System.Management.Automation.PSTypeName]'StackExchange.Redis.ConnectionMultiplexer').Type) {
        Write-Host "❌ StackExchange.Redis not available. Trying alternative approach..." -ForegroundColor Yellow
        
        # Alternative: Use .NET TCP client to send Redis commands
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.Connect($redisHost, $redisPort)
        $stream = $tcpClient.GetStream()
        
        # Function to send Redis command
        function Send-RedisCommand {
            param($command)
            $bytes = [System.Text.Encoding]::UTF8.GetBytes("$command`r`n")
            $stream.Write($bytes, 0, $bytes.Length)
            $stream.Flush()
            
            # Read response
            $buffer = New-Object byte[] 1024
            $bytesRead = $stream.Read($buffer, 0, $buffer.Length)
            $response = [System.Text.Encoding]::UTF8.GetString($buffer, 0, $bytesRead)
            return $response
        }
        
        # Select database
        Write-Host "📡 Connecting to Redis at ${redisHost}:${redisPort}..." -ForegroundColor Gray
        $response = Send-RedisCommand "SELECT $redisDatabase"
        Write-Host "Database selection: $response" -ForegroundColor Gray
        
        # Get all universe keys
        Write-Host "🔍 Finding universe keys..." -ForegroundColor Yellow
        $universeKeys = @()
        
        # Check for different universe key patterns
        $keyPatterns = @("universe:*", "Universe:*", "*universe*", "*symbol*")
        
        foreach ($pattern in $keyPatterns) {
            $response = Send-RedisCommand "KEYS $pattern"
            if ($response -and $response -notmatch "^\*0") {
                # Parse the response to extract keys
                $keys = $response -split "`r`n" | Where-Object { $_ -and $_ -notmatch "^\*" -and $_ -notmatch "^\+" }
                $universeKeys += $keys
            }
        }
        
        # Remove duplicates
        $universeKeys = $universeKeys | Sort-Object -Unique
        
        Write-Host "Found $($universeKeys.Count) universe-related keys" -ForegroundColor Green
        
        # Delete each key
        $deletedCount = 0
        foreach ($key in $universeKeys) {
            if ($key -and $key.Trim()) {
                $response = Send-RedisCommand "DEL $($key.Trim())"
                if ($response -match ":1") {
                    $deletedCount++
                    Write-Host "🗑️  Deleted: $($key.Trim())" -ForegroundColor Green
                }
            }
        }
        
        # Close connection
        $stream.Close()
        $tcpClient.Close()
        
        Write-Host "✅ Cleared $deletedCount universe-related keys from Redis" -ForegroundColor Green
        Write-Host "🔄 Universe data will be refreshed on next fetch" -ForegroundColor Cyan
        
    } else {
        # Use StackExchange.Redis if available
        Write-Host "📡 Connecting to Redis using StackExchange.Redis..." -ForegroundColor Gray
        
        $connectionString = "${redisHost}:${redisPort}"
        $connection = [StackExchange.Redis.ConnectionMultiplexer]::Connect($connectionString)
        $database = $connection.GetDatabase($redisDatabase)
        $server = $connection.GetServer("${redisHost}:${redisPort}")
        
        # Get universe keys
        $universeKeys = $server.Keys($redisDatabase, "universe:*")
        $candidateKeys = $server.Keys($redisDatabase, "Universe:*")
        
        $deletedCount = 0
        
        # Delete universe keys
        foreach ($key in $universeKeys) {
            $database.KeyDelete($key)
            $deletedCount++
            Write-Host "🗑️  Deleted: $key" -ForegroundColor Green
        }
        
        # Delete candidate keys
        foreach ($key in $candidateKeys) {
            $database.KeyDelete($key)
            $deletedCount++
            Write-Host "🗑️  Deleted: $key" -ForegroundColor Green
        }
        
        $connection.Close()
        
        Write-Host "✅ Cleared $deletedCount universe-related keys from Redis" -ForegroundColor Green
        Write-Host "🔄 Universe data will be refreshed on next fetch" -ForegroundColor Cyan
    }

} catch {
    Write-Host "❌ Error clearing Redis cache: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   You may need to clear the cache manually or restart Redis" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "✨ Redis universe cache clearing completed!" -ForegroundColor Green
Write-Host "   Next time the UniverseFetcherService runs, it will fetch fresh data from Polygon API" -ForegroundColor Gray
