using StackExchange.Redis;
using System;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🧹 Clearing universe cache from Redis...");
        
        try
        {
            // Connect to Redis
            var connection = ConnectionMultiplexer.Connect("*************:6379");
            var database = connection.GetDatabase();
            
            // Clear universe cache keys
            var keys = new string[]
            {
                "universe:today",
                "universe:candidates", 
                "universe:candidates:20250717",
                "universe:20250717",
                "polygon:symbols:full",
                "Universe:Candidates:20250717"
            };
            
            int deletedCount = 0;
            foreach (var key in keys)
            {
                var deleted = await database.KeyDeleteAsync(key);
                if (deleted)
                {
                    Console.WriteLine($"✅ Deleted: {key}");
                    deletedCount++;
                }
                else
                {
                    Console.WriteLine($"⚠️  Key not found: {key}");
                }
            }
            
            connection.Close();
            
            Console.WriteLine($"🎯 Cleared {deletedCount} universe cache keys");
            Console.WriteLine("✅ Universe cache cleared! Next run will generate fresh universe.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Environment.Exit(1);
        }
    }
}
