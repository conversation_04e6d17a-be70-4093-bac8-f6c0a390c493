# Fix the regime.csv file by swapping VIX_Level and VIX_Change columns
$csv = Import-Csv 'SmaTrendFollower.Console\Model\regime.csv'

$corrected = $csv | ForEach-Object {
    [PSCustomObject]@{
        Date = $_.Date
        SPX_Ret = $_.SPX_Ret
        VIX_Level = $_.VIX_Change  # Swap: VIX_Change becomes VIX_Level
        VIX_Change = $_.VIX_Level  # Swap: VIX_Level becomes VIX_Change
        Breadth_Score = $_.Breadth_Score
        RegimeLabel = $_.RegimeLabel
    }
}

$corrected | Export-Csv 'Model\regime_corrected.csv' -NoTypeInformation
Write-Host "CSV corrected and saved to Model\regime_corrected.csv"

# Also copy to the original location
Copy-Item 'Model\regime_corrected.csv' 'Model\regime.csv' -Force
Write-Host "Corrected CSV copied to Model\regime.csv"
