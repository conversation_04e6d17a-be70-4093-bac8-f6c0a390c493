# Fix the regime.csv file by swapping VIX_Level and VIX_Change columns
$csv = Import-Csv 'SmaTrendFollower.Console\Model\regime.csv'

# Create header
$header = "Date,SPX_Ret,VIX_Level,VIX_Change,Breadth_Score,RegimeLabel"

# Create content lines
$lines = @($header)
$csv | ForEach-Object {
    $line = "$($_.Date),$($_.SPX_Ret),$($_.VIX_Change),$($_.VIX_Level),$($_.Breadth_Score),$($_.RegimeLabel)"
    $lines += $line
}

# Write to file without quotes
$lines | Out-File 'Model\regime_fixed.csv' -Encoding UTF8
Write-Host "CSV fixed and saved to Model\regime_fixed.csv"

# Copy to the training location
Copy-Item 'Model\regime_fixed.csv' 'Model\regime.csv' -Force
Write-Host "Fixed CSV copied to Model\regime.csv"
