# SmaTrendFollower AI Assistant Memories Backup
*Generated on: 2025-06-29*

## General Preferences
- User prefers .NET 8 solutions with console + xUnit test projects, DI architecture, and standardized namespaces to SmaTrendFollower.
- User has extremely high risk tolerance, is ready for immediate live trading deployment with real money, and has completed system readiness verification.
- User requires 100% production readiness (47/47 services), 100% test coverage, and 100% documentation coverage as deployment criteria.
- User prefers the trading system to run continuously until manually stopped, with faster trading cycle intervals.
- User prefers more aggressive trading with increased position sizing and daily loss limits, without manual confirmation for each trade.
- User prefers comprehensive error handling with circuit breakers for external APIs and retry policies with exponential backoff.
- User requires data freshness validation with 18-minute staleness threshold - must check data age before use, retry same source first if stale, then move to next datasource, and investigate/fix root cause of stale data to prevent business decision errors.
- User requires checking date timestamps on all fetched data to ensure freshness and prevent using stale data in trading decisions.
- User requires caching to respect data freshness - cached data can be used only while fresh, must acquire new data once it becomes stale (18-minute rule for trading data).
- User always strives for 100% production readiness and requires asking before making assumptions about which services are experimental vs production-ready.
- When in doubt about issues or discrepancies, investigate first using available tools and codebase analysis, then ask clarifying questions rather than immediately asking the user.
- User requires full implementation when asked to build something - no placeholders, stubs, or incomplete implementations. Must build complete, production-ready code immediately.
- User requires accurate production readiness reporting and expects investigation/verification when discrepancies are found between previous statements and current metrics.
- User requires thorough verification of production readiness claims and expects honest assessment when systems have issues rather than contradictory statements about readiness.
- User requires keeping all documentation up-to-date as work progresses - must update documentation immediately when making changes to maintain accuracy and prevent discrepancies.
- User plans to go live with production trading system tomorrow, indicating immediate deployment timeline.
- User has authorized starting live production trading with real money.

## Architecture & Implementation
- User prefers scoped services for trade cycles with SignalGenerator, RiskManager, PortfolioGate, TradeExecutor components.
- User prefers real-time streaming architecture: Alpaca websocket for equities trading/exit, Polygon websocket for index/volatility triggers.
- User prefers libraries: Alpaca.Markets, Skender.Stock.Indicators, Serilog for logging, DotNetEnv, and FluentAssertions/Moq for testing.
- User prefers fixing CS1998 compiler warnings (async methods without await operators) rather than leaving them as non-critical warnings.
- User prefers comprehensive end-to-end testing that validates the actual trading process and data fetching pipeline.
- User prefers Polly resilience wrapper with 3-attempt exponential backoff retry policy, 30-second circuit breaker after 5 failures, applied to named HttpClients for Polygon and Alpaca APIs with comprehensive logging.
- User prefers PolygonWebSocketManager architecture with batch subscribe in ≤500-symbol chunks, Redis subscription persistence (ws:subs:{channel} keys with 24h TTL), separate sockets for equity vs index streams, auto-reconnect with exponential backoff, 429 rate limit handling with circuit breakers, and Prometheus metrics for reconnects/subscribed symbols.
- User prefers prometheus-net.AspNetCore for observability with MetricsRegistry for trades/signals/websocket metrics, /metrics and /health endpoints, and instrumentation in SignalGenerator, TradeExecutor, PolygonWebSocketManager, and DynamicUniverseProvider components.
- User prefers parallel signal generation with SemaphoreSlim rate limiting (20 concurrent calls ≈ 80-90 req/s), ConcurrentBag for thread-safe collections, Task.WhenAll for parallel execution, and performance instrumentation with latency warnings >500ms.

## Market Data Integration
- User has Alpaca Markets levels 1-3 options + AlgoTrader Plus market data, and Polygon.io Stocks Advanced & Indices Advanced subscription.
- User provided Polygon API key: ******************************** and Alpaca paper trading credentials: key PK0AM3WB1CES3YBQPGR0, secret 2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf.
- User prefers unified market data service combining Alpaca for account/positions/fills and equity/ETF bars with Polygon fallback for throttles/history.
- User prefers getting synthetic VIX data from Alpaca.Markets instead of Polygon for the trading system.
- User prefers VIX data fallback strategy: Polygon primary -> web scraping -> synthetic VIX from Alpaca ETFs -> Polygon ETFs -> halt trading 15min and retry cycle.
- User requires accurate real-time VIX data and strongly opposes using default/fallback values for VIX in trading decisions, emphasizing data accuracy is critical.
- User strongly opposes VIX bypass implementations that contradict the established multi-step VIX fallback process with synthetic VIX calculation capabilities.
- Polygon API endpoint /v2/last/trade provides 15-minute delayed data, which impacts real-time VIX data acquisition and may require using different endpoints for live trading.
- User prefers caching index bars in SQLite with optimizations including bulk inserts, connection pooling, compression, and cache warming.
- Current VIX value is 16.76 according to Yahoo Finance.
- User prefers using web-fetch tool for VIX data retrieval instead of other web search methods.
- AI assistant previously used cached/stale data from web-fetch tool instead of real-time data for VIX values, which violates the 18-minute data freshness requirement critical for trading decisions.
- User prefers SyntheticVixService implementation with MathNet.Numerics regression training using VXX/UVXY/SVXY/SPY ETF proxies, weekly Quartz scheduling on Sundays 6PM ET, Redis weight caching, and integration into VixResolverService fallback chain after Polygon REST/WS failures.
- User requires accurate real-time VIX data and expects verification of data sources when discrepancies are found in trading system outputs.

## Trading Strategy
- For SmaTrendFollower strategy: primary trend filter requires Close > 200-day SMA AND Close > 50-day SMA to stay in broad uptrends.
- User prefers DynamicUniverseProvider implementation: weekly fetch from Polygon /v3/reference/tickers, daily filtering to top ~200 tickers (price >$10, volume >1M, volatility threshold) cached in Redis as universe:candidates, WebSocket subscription by 9:25 AM ET, signal engine start at 9:30 AM ET.
- User prefers DynamicUniverseProvider with Redis caching, filtering by price >$10, volume >1M shares, volatility >2% daily stddev.
- SignalGenerator should implement universe screening with SPY+top-500 tickers, filter by technical indicators, and rank by sixMonthReturn.
- PortfolioGate.ShouldTradeAsync() should fetch SPY bars and return false if SPY close < SPY sma200.
- RiskManager should use riskDollars = min(account.Equity * 0.01m, 1000m) for 1bps per $100k cap.
- User prefers trade execution pattern: cancel existing orders, submit Limit-on-Open Buy at lastClose * 1.002m, place GTC stop-loss at entry - 2×ATR.

## Infrastructure & Services
- User's Redis server is running at *************:6379 with no password authentication required.
- User prefers Discord notifications with bot token MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo for channel 1385057459814797383.
- User prefers Redis cache warming with specific key naming conventions and pre-market cache loading from SQLite.
- User prefers MarketRegimeService with SPY 100-day analysis to detect market regimes, cached in Redis with 24h TTL.
- User prefers Phase 5 roadmap with TickStreamService, PreMarketFilterService, BreadthService, and ExecutionQAService.
- User defined Phase 6 roadmap with TickStreamService (WebSocket T.*/Q.*/A.* channels), IndexRegimeService (I:SPX/VIX/NDX regime classification), VIXResolverService (7-level fallback: Polygon->WebSocket->Synthetic VIX->Search APIs->Redis cache <1hr->halt), BreadthMonitorService (advancing/declining tracking), and RealTimeExecutionService (dynamic limit/market execution) - all requiring 15-minute data freshness unless specified otherwise.
- User prefers Phase 7 roadmap with SlippageEstimator (model expected vs actual fill prices), TickBarBuilder (reconstruct bars from tick data for custom intervals), and SmartTradeThrottler (limit trade frequency to prevent overtrading).
- User prefers UniverseFetcherService architecture: weekly Polygon /v3/reference/tickers fetch with pagination, Redis universe:candidates caching (8-day TTL), Quartz.NET scheduled jobs for 08:30 ET daily filtering, and DynamicUniverseProvider integration replacing hard-coded symbol lists.
- User requires Redis hygiene with standardized TTLs (24h for signals/universe, 7d for stops, 10min for synthetic VIX), RedisCleanupService as IJob running daily at 2AM ET to fix orphaned keys, and all Redis SET operations must include appropriate TTL from RedisKeyTTL constants.
- User prefers AnomalyDetectorService implementation with RollingStats (200-window z-score), 3-sigma halt thresholds for spread/return anomalies, 5-minute Redis halt keys, Prometheus metrics (anomaly_events_total, anomaly_halted), WebSocket integration, and daily gauge reset job at 02:05 ET.
- User prefers QuoteVolatilityGuard implementation with 120-quote rolling window, 2-sigma threshold (vs 3-sigma in AnomalyDetector), 2-minute Redis halt keys, WebSocket quote stream integration, and coexistence with AnomalyDetector using shared halt:{symbol} keys.
- User prefers Discord alert integration using Serilog with custom DiscordSink for Warning+ level logs, DrawdownMonitor job for 5% drawdown alerts every 5 minutes, and specific logging patterns for trading system components with webhook URL configuration.
- User prefers Discord bot-token alert sink implementation with DiscordBotSink class using Bot authentication headers, Warning+ level filtering, and integration via Serilog .WriteTo.Sink() instead of webhook approach.

## Machine Learning
- User prefers ML Phase 1 implementation with Microsoft.ML and Microsoft.ML.AutoML for signal ranking using MachineLearning/ folder structure with FeatureExportService for CSV export, TrainSignalRanker console tool for AutoML training, SignalRanker for runtime prediction with 0.65 probability threshold, and weekly retraining workflow.
- User prefers automated weekly SignalRanker retraining with SignalRankerRetrainer IJob scheduled Sundays 6PM ET, hot-reload model versioning via Redis, Prometheus metrics (ml_retrain_total), and CLI manual trigger support for ML model management.
- User prefers ML Phase 2 implementation with PositionSizerService using LightGBM regression for dynamic position sizing, scheduled 30 minutes after SignalRanker retraining (Sunday 6:30 PM ET), with Redis hot-reload, Prometheus metrics, and integration into RiskManager for equity-based position calculation with 5% cap.
- User prefers ML Phase 3 Market Regime Classifier with FastForest (32 leaves, 150 trees), 4-class regime detection (Sideways/TrendingUp/TrendingDown/Panic), daily detection at 08:35 ET, weekly retraining Sunday 18:45 ET, Redis hot-reload model versioning, and specific labeling rules based on VIX levels and SPX returns.
- For ML regime classifier deployment: ensure Quartz scheduler lines are in ServiceConfiguration.cs for daily detector and weekly retrainer, verify ExportRegimeCsv() can reach historical bar tables, and ensure Model/regime_model.zip path is writable by process (especially in Docker).
- User prefers ML Phase 4 SlippageForecasterService with LightGBM regression for predicting fill slippage, fills_log database tracking, weekly retraining Sunday 18:55 ET, Redis hot-swap model versioning, and TradeExecutor integration with dynamic limit price offsets based on predicted slippage.

## Testing
- User prefers comprehensive test suite with xUnit, FluentAssertions, NSubstitute for mocking, InMemoryRedis helper for Redis testing, specific test patterns for ML hot-reload, WebSocket reconnection testing, and InternalsVisibleTo for test access to internal classes.
- User prefers InMemoryRedis helper for Redis testing, async Task test methods with await patterns, MockHttp for HTTP mocking, removing Legacy trait categories, and specific namespace organization for Guards vs Services in tests.

## CI/CD
- User prefers GitHub Actions CI pipeline with .github/workflows/build.yml, dotnet test with code coverage, self-hosted runner migration capability, and requires GitHub secrets for POLYGON_API_KEY, ALPACA_KEY_ID, ALPACA_SECRET, OPENAI_API_KEY.

## API Keys & Credentials
- Polygon API key: ********************************
- Alpaca paper trading credentials: key PK0AM3WB1CES3YBQPGR0, secret 2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf
- Discord bot token: MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo
- Discord channel: 1385057459814797383
- Redis server: *************:6379 (no password)

## Subscriptions
- Brave Search: Data for AI (Free), Data for Search (Free), Autosuggest (Free), Spellcheck (Free)
- Polygon.io: Indices Advanced, Options Starter, Stocks Advanced
- Alpaca.markets: levels 1, 2, 3 options, AlgoTrader Plus market data
- ChatGPT: Plus subscription, API tokens
- Discord: Standard subscription

---
*This backup contains all AI assistant memories for the SmaTrendFollower trading system as of 2025-06-29*
