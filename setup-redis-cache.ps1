#!/usr/bin/env pwsh
# SmaTrendFollower Redis Cache Setup Script
# Connects to Redis and creates all necessary data structures

param(
    [string]$RedisHost = "*************",
    [int]$RedisPort = 6379,
    [string]$RedisPassword = "",
    [int]$RedisDatabase = 0,
    [switch]$Force
)

# Import required modules
try {
    Import-Module StackExchange.Redis -ErrorAction Stop
} catch {
    Write-Host "❌ StackExchange.Redis module not found. Installing..." -ForegroundColor Red
    Install-Module StackExchange.Redis -Force -Scope CurrentUser
    Import-Module StackExchange.Redis
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    switch ($Color) {
        "Success" { Write-Host $Message -ForegroundColor Green }
        "Warning" { Write-Host $Message -ForegroundColor Yellow }
        "Error" { Write-Host $Message -ForegroundColor Red }
        "Info" { Write-Host $Message -ForegroundColor Cyan }
        default { Write-Host $Message -ForegroundColor White }
    }
}

function Connect-ToRedis {
    param([string]$Host, [int]$Port, [string]$Password, [int]$Database)
    
    try {
        $connectionString = "${Host}:${Port}"
        if ($Password) {
            $connectionString += ",password=${Password}"
        }
        $connectionString += ",abortConnect=false,connectTimeout=5000,syncTimeout=5000"
        
        Write-ColorOutput "🔌 Connecting to Redis at ${Host}:${Port}..." "Info"
        $redis = [StackExchange.Redis.ConnectionMultiplexer]::Connect($connectionString)
        $database = $redis.GetDatabase($Database)
        
        # Test connection
        $testResult = $database.StringSet("test:connection", "ok", [TimeSpan]::FromSeconds(10))
        if ($testResult) {
            $database.KeyDelete("test:connection")
            Write-ColorOutput "✅ Redis connection successful!" "Success"
            return @{ Connection = $redis; Database = $database }
        } else {
            throw "Connection test failed"
        }
    } catch {
        Write-ColorOutput "❌ Failed to connect to Redis: $($_.Exception.Message)" "Error"
        return $null
    }
}

function Set-UniverseData {
    param($Database)
    
    Write-ColorOutput "📊 Setting up universe data..." "Info"
    
    # Default universe symbols (top liquid stocks + ETFs)
    $universeSymbols = @(
        "SPY", "QQQ", "IWM", "VTI", "VEA", "VWO", "AGG", "TLT", "GLD", "VNQ",
        "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "BRK.B", "UNH", "JNJ",
        "V", "PG", "JPM", "HD", "MA", "DIS", "PYPL", "ADBE", "NFLX", "CRM",
        "CMCSA", "PEP", "ABT", "COST", "TMO", "AVGO", "ACN", "NKE", "MRK", "LLY",
        "DHR", "NEE", "VZ", "ABBV", "KO", "PFE", "WMT", "INTC", "CIS", "MDT"
    )
    
    # Create universe candidates
    $universeData = @{
        symbols = $universeSymbols
        generatedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        candidateCount = $universeSymbols.Count
        qualifiedCount = $universeSymbols.Count
        filterCriteria = @{
            minPrice = 10.0
            minVolume = 1000000
            minMarketCap = 1000000000
        }
    } | ConvertTo-Json -Depth 3
    
    # Set universe keys with appropriate TTLs
    $ttl = [TimeSpan]::FromHours(24)
    $result1 = $Database.StringSet("universe:candidates", $universeData, $ttl)
    $result2 = $Database.StringSet("universe:today", $universeData, $ttl)
    $result3 = $Database.StringSet("universe:filtered", $universeData, $ttl)
    
    if ($result1 -and $result2 -and $result3) {
        Write-ColorOutput "✅ Universe data created with $($universeSymbols.Count) symbols" "Success"
    } else {
        Write-ColorOutput "❌ Failed to set universe data" "Error"
    }
}

function Set-SignalFlags {
    param($Database, $Symbols)
    
    Write-ColorOutput "🚦 Setting up signal flags..." "Info"
    $today = (Get-Date).ToString("yyyy-MM-dd")
    $count = 0
    
    foreach ($symbol in $Symbols) {
        $signalFlag = @{
            symbol = $symbol
            tradingDate = $today
            signalTriggered = $false
            triggeredAt = $null
            signalStrength = $null
            metadata = $null
        } | ConvertTo-Json
        
        $key = "signal:${symbol}:$($today.Replace('-', ''))"
        $result = $Database.StringSet($key, $signalFlag, [TimeSpan]::FromHours(24))
        if ($result) { $count++ }
    }
    
    Write-ColorOutput "✅ Created $count signal flags for $today" "Success"
}

function Set-ThrottleFlags {
    param($Database, $Symbols)
    
    Write-ColorOutput "🛑 Setting up throttle flags..." "Info"
    $today = (Get-Date).ToString("yyyy-MM-dd")
    $count = 0
    
    foreach ($symbol in $Symbols) {
        $throttleFlag = @{
            symbol = $symbol
            tradingDate = $today
            isBlocked = $false
            blockReason = $null
            blockedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            blockedBy = "system"
        } | ConvertTo-Json
        
        $key = "throttle:${symbol}:$($today.Replace('-', ''))"
        $result = $Database.StringSet($key, $throttleFlag, [TimeSpan]::FromHours(24))
        if ($result) { $count++ }
    }
    
    Write-ColorOutput "✅ Created $count throttle flags for $today" "Success"
}

function Set-VixData {
    param($Database)
    
    Write-ColorOutput "📈 Setting up VIX data..." "Info"
    
    # Current VIX data (simulated)
    $vixData = @{
        value = 16.73
        timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        source = "synthetic"
        quality = "good"
    } | ConvertTo-Json
    
    $result1 = $Database.StringSet("vix:current", $vixData, [TimeSpan]::FromMinutes(10))
    $result2 = $Database.StringSet("vix:source:primary", $vixData, [TimeSpan]::FromMinutes(10))
    
    if ($result1 -and $result2) {
        Write-ColorOutput "✅ VIX data initialized (16.73)" "Success"
    } else {
        Write-ColorOutput "❌ Failed to set VIX data" "Error"
    }
}

function Set-RegimeData {
    param($Database)
    
    Write-ColorOutput "🎯 Setting up regime data..." "Info"
    
    # Market regime data
    $regimeData = @{
        regime = "Normal"
        detectedAt = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        confidence = 0.85
        vixLevel = 16.73
        spyTrend = "Bullish"
        metadata = "Auto-initialized"
    } | ConvertTo-Json
    
    $result1 = $Database.StringSet("regime:current", $regimeData, [TimeSpan]::FromHours(4))
    $result2 = $Database.StringSet("index:regime:SPY", $regimeData, [TimeSpan]::FromHours(4))
    
    if ($result1 -and $result2) {
        Write-ColorOutput "✅ Regime data initialized (Normal)" "Success"
    } else {
        Write-ColorOutput "❌ Failed to set regime data" "Error"
    }
}

function Set-HealthChecks {
    param($Database)
    
    Write-ColorOutput "🏥 Setting up health check data..." "Info"
    
    $healthData = @{
        status = "Healthy"
        lastCheck = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        services = @{
            redis = "Healthy"
            alpaca = "Healthy"
            polygon = "Healthy"
            database = "Healthy"
        }
    } | ConvertTo-Json -Depth 3
    
    $result = $Database.StringSet("health_check_system", $healthData, [TimeSpan]::FromMinutes(5))
    
    if ($result) {
        Write-ColorOutput "✅ Health check data initialized" "Success"
    } else {
        Write-ColorOutput "❌ Failed to set health check data" "Error"
    }
}

# Main execution
Write-ColorOutput "🚀 SmaTrendFollower Redis Cache Setup" "Info"
Write-ColorOutput "Target: ${RedisHost}:${RedisPort} (Database: ${RedisDatabase})" "Info"
Write-Host ""

# Connect to Redis
$redisConnection = Connect-ToRedis -Host $RedisHost -Port $RedisPort -Password $RedisPassword -Database $RedisDatabase
if (-not $redisConnection) {
    Write-ColorOutput "❌ Cannot proceed without Redis connection" "Error"
    exit 1
}

$redis = $redisConnection.Connection
$db = $redisConnection.Database

try {
    # Check if data already exists
    $existingUniverse = $db.StringGet("universe:candidates")
    if ($existingUniverse.HasValue -and -not $Force) {
        Write-ColorOutput "⚠️ Redis cache already contains data. Use -Force to overwrite." "Warning"
        $response = Read-Host "Continue anyway? (y/N)"
        if ($response -ne 'y' -and $response -ne 'Y') {
            Write-ColorOutput "❌ Setup cancelled by user" "Error"
            exit 0
        }
    }
    
    Write-Host ""
    Write-ColorOutput "📋 Setting up Redis cache structures..." "Info"
    Write-Host ""
    
    # Set up all data structures
    Set-UniverseData -Database $db
    
    # Get symbols for flags
    $universeJson = $db.StringGet("universe:candidates")
    if ($universeJson.HasValue) {
        $universeData = $universeJson | ConvertFrom-Json
        $symbols = $universeData.symbols
        
        Set-SignalFlags -Database $db -Symbols $symbols
        Set-ThrottleFlags -Database $db -Symbols $symbols
    }
    
    Set-VixData -Database $db
    Set-RegimeData -Database $db
    Set-HealthChecks -Database $db
    
    Write-Host ""
    Write-ColorOutput "🎉 Redis cache setup completed successfully!" "Success"
    Write-Host ""
    
    # Show summary
    Write-ColorOutput "📊 Cache Summary:" "Info"
    $keyCount = 0
    $keys = $db.Multiplexer.GetServer("${RedisHost}:${RedisPort}").Keys($RedisDatabase, "*")
    foreach ($key in $keys) {
        $keyCount++
    }
    Write-ColorOutput "   Total keys created: $keyCount" "Info"
    Write-ColorOutput "   Universe symbols: $($symbols.Count)" "Info"
    Write-ColorOutput "   Signal flags: $($symbols.Count)" "Info"
    Write-ColorOutput "   Throttle flags: $($symbols.Count)" "Info"
    Write-ColorOutput "   VIX data: Initialized" "Info"
    Write-ColorOutput "   Regime data: Normal" "Info"
    Write-ColorOutput "   Health checks: Healthy" "Info"
    
} catch {
    Write-ColorOutput "❌ Error during setup: $($_.Exception.Message)" "Error"
    exit 1
} finally {
    # Clean up connection
    if ($redis) {
        $redis.Dispose()
        Write-ColorOutput "🔌 Redis connection closed" "Info"
    }
}

Write-Host ""
Write-ColorOutput "✅ SmaTrendFollower Redis cache is ready for trading!" "Success"
