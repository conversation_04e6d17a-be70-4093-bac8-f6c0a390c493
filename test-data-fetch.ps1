# Test script to verify the data fetching fix is working
# This bypasses market hours checks and directly tests the cache logic

Write-Host "Testing SmaTrendFollower Data Fetching Fix..." -ForegroundColor Cyan
Write-Host "This test verifies that the cache now fetches full historical ranges instead of small gaps." -ForegroundColor Yellow

# Set environment variables
$env:POLY_API_KEY = "stffXZCR90K0YULLv7zoUMq1k4JWiyHD"
$env:ASPNETCORE_ENVIRONMENT = "LocalProd"

# Test symbols that we know had insufficient data
$testSymbols = @("AAPL", "MSFT", "GOOGL", "TSLA", "NVDA")

Write-Host "`nTesting data fetching for symbols that previously had insufficient bars..." -ForegroundColor Yellow

foreach ($symbol in $testSymbols) {
    Write-Host "`n📊 Testing $symbol..." -ForegroundColor White
    
    # Calculate date range (300 days back from yesterday)
    $endDate = (Get-Date).AddDays(-1).ToString("yyyy-MM-dd")
    $startDate = (Get-Date).AddDays(-301).ToString("yyyy-MM-dd")
    
    # Test Polygon API directly to see what data is available
    $url = "https://api.polygon.io/v2/aggs/ticker/$symbol/range/1/day/$startDate/$endDate" + "?adjusted=true" + "`&sort=asc" + "`&apikey=$env:POLY_API_KEY"
    
    try {
        Write-Host "  🔍 Fetching from Polygon API: $startDate to $endDate" -ForegroundColor Gray
        $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 30
        
        if ($response.results) {
            $barCount = $response.results.Count
            Write-Host "  ✅ Polygon returned $barCount bars for $symbol" -ForegroundColor Green
            
            if ($barCount -ge 200) {
                Write-Host "  ✅ Sufficient data for SMA200 calculation ($barCount >= 200)" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️  Insufficient data for SMA200 calculation ($barCount < 200)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  ❌ No data returned from Polygon for $symbol" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ Error fetching data for $symbol`: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 250  # Rate limiting
}

Write-Host "`n📋 Summary:" -ForegroundColor White
Write-Host "This test shows what data is available from Polygon API." -ForegroundColor Gray
Write-Host "The cache fix should now fetch the full date ranges shown above" -ForegroundColor Gray
Write-Host "instead of small 1-2 day gaps that were causing 0 bar responses." -ForegroundColor Gray

Write-Host "`n💡 Next Steps:" -ForegroundColor White
Write-Host "1. Wait for market hours (Monday 9:30 AM ET) to test the full system" -ForegroundColor Gray
Write-Host "2. Monitor logs for `'Fetching FULL range`' messages during signal generation" -ForegroundColor Gray
Write-Host "3. Verify that symbols now get 200+ bars instead of insufficient data warnings" -ForegroundColor Gray

Write-Host "`n✅ Data fetching fix verification complete!" -ForegroundColor Green
