# Simple test to verify Polygon API returns sufficient historical data
Write-Host "Testing Polygon API Historical Data Availability..." -ForegroundColor Cyan

# Set API key
$env:POLY_API_KEY = "stffXZCR90K0YULLv7zoUMq1k4JWiyHD"

# Test a few symbols
$symbols = @("AAPL", "MSFT", "GOOGL")

foreach ($symbol in $symbols) {
    Write-Host "Testing $symbol..." -ForegroundColor Yellow
    
    # Calculate date range (300 days back)
    $endDate = (Get-Date).AddDays(-1).ToString("yyyy-MM-dd")
    $startDate = (Get-Date).AddDays(-301).ToString("yyyy-MM-dd")
    
    # Build URL carefully
    $baseUrl = "https://api.polygon.io/v2/aggs/ticker/$symbol/range/1/day/$startDate/$endDate"
    $params = "?adjusted=true&sort=asc&apikey=$env:POLY_API_KEY"
    $url = $baseUrl + $params
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 30
        
        if ($response.results) {
            $count = $response.results.Count
            Write-Host "  SUCCESS: $count bars returned" -ForegroundColor Green
            
            if ($count -ge 200) {
                Write-Host "  SUFFICIENT for SMA200 calculation" -ForegroundColor Green
            } else {
                Write-Host "  INSUFFICIENT for SMA200 calculation" -ForegroundColor Red
            }
        } else {
            Write-Host "  NO DATA returned" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 300
}

Write-Host "`nTest complete. The cache fix should now fetch these full ranges instead of small gaps." -ForegroundColor Green
