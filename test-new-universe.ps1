#!/usr/bin/env pwsh

# Test New Universe Configuration Script
# Validates the updated filtering criteria and expanded universe

Write-Host "🧪 TESTING NEW UNIVERSE CONFIGURATION" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

$ErrorActionPreference = "Continue"
$testResults = @()

function Test-Component {
    param(
        [string]$Name,
        [scriptblock]$TestBlock
    )
    
    Write-Host "🔍 Testing: $Name" -ForegroundColor Yellow
    try {
        $result = & $TestBlock
        if ($result) {
            Write-Host "✅ ${Name}: PASSED" -ForegroundColor Green
            $testResults += @{Name=$Name; Status="PASSED"; Details=$result}
        } else {
            Write-Host "❌ ${Name}: FAILED" -ForegroundColor Red
            $testResults += @{Name=$Name; Status="FAILED"; Details="Test returned false"}
        }
    }
    catch {
        Write-Host "❌ ${Name}: ERROR - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{Name=$Name; Status="ERROR"; Details=$_.Exception.Message}
    }
    Write-Host ""
}

# Test 1: Build System
Test-Component "Build System" {
    $buildResult = & dotnet build SmaTrendFollower.Console --configuration Release --verbosity quiet
    return $LASTEXITCODE -eq 0
}

# Test 2: Unit Tests
Test-Component "Unit Tests" {
    $testResult = & dotnet test SmaTrendFollower.Tests.Core --verbosity quiet
    return $LASTEXITCODE -eq 0
}

# Test 3: Universe File Validation
Test-Component "Universe File" {
    if (Test-Path "universe.csv") {
        $content = Get-Content "universe.csv"
        $symbolCount = ($content | Where-Object { $_.Trim() -ne "" }).Count
        Write-Host "   Universe contains $symbolCount symbols" -ForegroundColor Gray
        return $symbolCount -gt 200  # Should have more than 200 symbols now
    }
    return $false
}

# Test 4: Configuration Validation
Test-Component "New Filter Configuration" {
    $configPath = "SmaTrendFollower.Console/appsettings.LocalProd.json"
    if (Test-Path $configPath) {
        $config = Get-Content $configPath | ConvertFrom-Json
        $universeRefresh = $config.UniverseRefresh
        $dynamicUniverse = $config.DynamicUniverse.DefaultCriteria
        
        $volumeOk = ($universeRefresh.MinAverageVolume -eq 500000) -and ($dynamicUniverse.MinAverageVolume -eq 500000)
        $volatilityOk = ($universeRefresh.MinVolatilityPercent -eq 1.0) -and ($dynamicUniverse.MinVolatilityPercent -eq 1.0)
        
        Write-Host "   MinAverageVolume: $($universeRefresh.MinAverageVolume) (should be 500000)" -ForegroundColor Gray
        Write-Host "   MinVolatilityPercent: $($universeRefresh.MinVolatilityPercent) (should be 1.0)" -ForegroundColor Gray
        
        return $volumeOk -and $volatilityOk
    }
    return $false
}

# Test 5: Sample Universe Symbols
Test-Component "Universe Quality" {
    if (Test-Path "universe.csv") {
        $symbols = Get-Content "universe.csv" | Where-Object { $_.Trim() -ne "" }
        
        # Check for key symbols that should be in our expanded universe
        $keySymbols = @("SPY", "QQQ", "AAPL", "MSFT", "NVDA", "GOOGL", "TSLA", "META")
        $foundCount = 0
        
        foreach ($symbol in $keySymbols) {
            if ($symbols -contains $symbol) {
                $foundCount++
            }
        }
        
        Write-Host "   Found $foundCount of $($keySymbols.Count) key symbols" -ForegroundColor Gray
        Write-Host "   First 10 symbols: $($symbols[0..9] -join ', ')" -ForegroundColor Gray
        Write-Host "   Last 10 symbols: $($symbols[-10..-1] -join ', ')" -ForegroundColor Gray
        
        return $foundCount -eq $keySymbols.Count
    }
    return $false
}

# Test 6: Redis Connection
Test-Component "Redis Connection" {
    try {
        $pingResult = ping -n 1 ************* 2>&1
        return $pingResult -match "Reply from"
    }
    catch {
        return $false
    }
}

# Test 7: API Configuration
Test-Component "API Configuration" {
    $configPath = "SmaTrendFollower.Console/appsettings.LocalProd.json"
    if (Test-Path $configPath) {
        $config = Get-Content $configPath | ConvertFrom-Json
        $alpacaOk = $config.Alpaca.KeyId -ne $null
        $polygonOk = $config.Polygon.ApiKey -ne $null
        
        Write-Host "   Alpaca configured: $alpacaOk" -ForegroundColor Gray
        Write-Host "   Polygon configured: $polygonOk" -ForegroundColor Gray
        
        return $alpacaOk -and $polygonOk
    }
    return $false
}

# Test 8: Safety Configuration
Test-Component "Safety Configuration" {
    $configPath = "SmaTrendFollower.Console/appsettings.LocalProd.json"
    if (Test-Path $configPath) {
        $config = Get-Content $configPath | ConvertFrom-Json
        $safety = $config.Safety
        
        $safetyOk = $safety.AllowedEnvironment -eq "Live" -and 
                   $safety.MaxDailyLoss -gt 0 -and 
                   $safety.MaxPositions -gt 0
        
        Write-Host "   Environment: $($safety.AllowedEnvironment)" -ForegroundColor Gray
        Write-Host "   Max Daily Loss: $($safety.MaxDailyLoss)" -ForegroundColor Gray
        Write-Host "   Max Positions: $($safety.MaxPositions)" -ForegroundColor Gray
        
        return $safetyOk
    }
    return $false
}

# Summary Report
Write-Host "📊 TEST SUMMARY REPORT" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan
Write-Host ""

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$errors = ($testResults | Where-Object { $_.Status -eq "ERROR" }).Count
$total = $testResults.Count

Write-Host "Total Tests: $total" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Errors: $errors" -ForegroundColor Yellow
Write-Host ""

# Detailed Results
foreach ($result in $testResults) {
    $color = switch ($result.Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "ERROR" { "Yellow" }
    }
    Write-Host "$($result.Status): $($result.Name)" -ForegroundColor $color
}

Write-Host ""

# Final Assessment
if ($failed -eq 0 -and $errors -eq 0) {
    Write-Host "🎉 ALL TESTS PASSED - NEW UNIVERSE READY!" -ForegroundColor Green
    Write-Host "✅ Updated filtering criteria validated" -ForegroundColor Green
    Write-Host "✅ Expanded universe loaded successfully" -ForegroundColor Green
    Write-Host "✅ System ready for enhanced trading tomorrow" -ForegroundColor Green
} elseif ($failed + $errors -le 1) {
    Write-Host "⚠️  MOSTLY READY - Minor issues detected" -ForegroundColor Yellow
    Write-Host "🔧 System should work but review failed tests" -ForegroundColor Yellow
} else {
    Write-Host "❌ SYSTEM NOT READY - Multiple failures detected" -ForegroundColor Red
    Write-Host "🚨 Address issues before live trading" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 New Configuration Summary:" -ForegroundColor Cyan
Write-Host "• MinAverageVolume: 1,000,000 → 500,000 shares/day" -ForegroundColor White
Write-Host "• MinVolatilityPercent: 2.0% → 1.0%" -ForegroundColor White
Write-Host "• Universe Size: 50 → 258+ symbols" -ForegroundColor White
Write-Host "• Expected Impact: More trading opportunities" -ForegroundColor White
Write-Host ""
