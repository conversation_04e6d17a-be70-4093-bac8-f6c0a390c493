#!/usr/bin/env pwsh

# Test Trading System Today - Comprehensive Validation Script
# This script tests all critical trading components without waiting for market hours

Write-Host "🧪 COMPREHENSIVE TRADING SYSTEM TEST" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

$ErrorActionPreference = "Continue"
$testResults = @()

function Test-Component {
    param(
        [string]$Name,
        [scriptblock]$TestBlock
    )
    
    Write-Host "🔍 Testing: $Name" -ForegroundColor Yellow
    try {
        $result = & $TestBlock
        if ($result) {
            Write-Host "✅ ${Name}: PASSED" -ForegroundColor Green
            $testResults += @{Name=$Name; Status="PASSED"; Details=$result}
        } else {
            Write-Host "❌ ${Name}: FAILED" -ForegroundColor Red
            $testResults += @{Name=$Name; Status="FAILED"; Details="Test returned false"}
        }
    }
    catch {
        Write-Host "❌ ${Name}: ERROR - $($_.Exception.Message)" -ForegroundColor Red
        $testResults += @{Name=$Name; Status="ERROR"; Details=$_.Exception.Message}
    }
    Write-Host ""
}

# Test 1: Build Validation
Test-Component "Build System" {
    $buildResult = & dotnet build SmaTrendFollower.Console --configuration Release --verbosity quiet
    return $LASTEXITCODE -eq 0
}

# Test 2: Unit Tests
Test-Component "Unit Tests" {
    $testResult = & dotnet test SmaTrendFollower.Tests.Core --verbosity quiet
    return $LASTEXITCODE -eq 0
}

# Test 3: Configuration Loading
Test-Component "Configuration Loading" {
    $configTest = & dotnet run --project SmaTrendFollower.Console --environment LocalProd -- --help 2>&1
    return $configTest -match "SmaTrendFollower"
}

# Test 4: Redis Connectivity
Test-Component "Redis Connection" {
    try {
        $pingResult = ping -n 1 ************* 2>&1
        return $pingResult -match "Reply from"
    }
    catch {
        return $false
    }
}

# Test 5: API Credentials Validation
Test-Component "API Credentials" {
    # Check if credentials are in config
    $configPath = "SmaTrendFollower.Console/appsettings.LocalProd.json"
    if (Test-Path $configPath) {
        $config = Get-Content $configPath | ConvertFrom-Json
        return $config.Alpaca.KeyId -and $config.Polygon.ApiKey
    }
    return $false
}

# Test 6: Safety Configuration
Test-Component "Safety Configuration" {
    $configPath = "SmaTrendFollower.Console/appsettings.LocalProd.json"
    if (Test-Path $configPath) {
        $config = Get-Content $configPath | ConvertFrom-Json
        $safety = $config.Safety
        return $safety.MaxDailyLoss -and $safety.MaxPositions -and $safety.AllowedEnvironment
    }
    return $false
}

# Test 7: Database Initialization
Test-Component "Database Files" {
    $dbFiles = @(
        "SmaTrendFollower.Console/stock_cache.db",
        "SmaTrendFollower.Console/index_cache.db"
    )
    
    foreach ($file in $dbFiles) {
        if (!(Test-Path $file)) {
            return $false
        }
    }
    return $true
}

# Test 8: Model Files
Test-Component "ML Model Files" {
    $modelFiles = @(
        "Model/regime_corrected.csv",
        "Model/regime_model_test.zip"
    )
    
    $foundFiles = 0
    foreach ($file in $modelFiles) {
        if (Test-Path $file) {
            $foundFiles++
        }
    }
    return $foundFiles -gt 0
}

# Test 9: Log Directory
Test-Component "Logging System" {
    if (!(Test-Path "logs")) {
        New-Item -ItemType Directory -Path "logs" -Force | Out-Null
    }
    return Test-Path "logs"
}

# Test 10: Dry Run Execution (Quick Test)
Test-Component "Dry Run Execution" {
    Write-Host "   Starting 30-second dry run test..." -ForegroundColor Gray
    
    # Start the process in background
    $process = Start-Process -FilePath "dotnet" -ArgumentList @(
        "run", "--project", "SmaTrendFollower.Console", 
        "--environment", "LocalProd", "--", "--dry-run"
    ) -PassThru -NoNewWindow
    
    # Wait up to 30 seconds
    $timeout = 30
    $elapsed = 0
    
    while (!$process.HasExited -and $elapsed -lt $timeout) {
        Start-Sleep -Seconds 1
        $elapsed++
        if ($elapsed % 5 -eq 0) {
            Write-Host "   ... waiting ($elapsed / $timeout seconds)" -ForegroundColor Gray
        }
    }
    
    # Kill if still running
    if (!$process.HasExited) {
        $process.Kill()
        $process.WaitForExit(5000)
        Write-Host "   Process terminated after timeout (this is expected)" -ForegroundColor Gray
        return $true  # Timeout is acceptable for this test
    }
    
    return $process.ExitCode -eq 0
}

# Summary Report
Write-Host "📊 TEST SUMMARY REPORT" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan
Write-Host ""

$passed = ($testResults | Where-Object { $_.Status -eq "PASSED" }).Count
$failed = ($testResults | Where-Object { $_.Status -eq "FAILED" }).Count
$errors = ($testResults | Where-Object { $_.Status -eq "ERROR" }).Count
$total = $testResults.Count

Write-Host "Total Tests: $total" -ForegroundColor White
Write-Host "Passed: $passed" -ForegroundColor Green
Write-Host "Failed: $failed" -ForegroundColor Red
Write-Host "Errors: $errors" -ForegroundColor Yellow
Write-Host ""

# Detailed Results
foreach ($result in $testResults) {
    $color = switch ($result.Status) {
        "PASSED" { "Green" }
        "FAILED" { "Red" }
        "ERROR" { "Yellow" }
    }
    Write-Host "$($result.Status): $($result.Name)" -ForegroundColor $color
}

Write-Host ""

# Final Assessment
if ($failed -eq 0 -and $errors -eq 0) {
    Write-Host "🎉 ALL TESTS PASSED - SYSTEM READY FOR TRADING!" -ForegroundColor Green
    Write-Host "✅ The system is fully validated and ready for live deployment tomorrow." -ForegroundColor Green
} elseif ($failed + $errors -le 2) {
    Write-Host "⚠️  MOSTLY READY - Minor issues detected" -ForegroundColor Yellow
    Write-Host "🔧 System should work but review failed tests before live trading." -ForegroundColor Yellow
} else {
    Write-Host "❌ SYSTEM NOT READY - Multiple failures detected" -ForegroundColor Red
    Write-Host "🚨 Do not proceed with live trading until issues are resolved." -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Review any failed tests above" -ForegroundColor White
Write-Host "2. If all tests passed, system is ready for tomorrow's trading" -ForegroundColor White
Write-Host "3. Start live trading with: dotnet run --project SmaTrendFollower.Console --environment LocalProd" -ForegroundColor White
Write-Host ""
