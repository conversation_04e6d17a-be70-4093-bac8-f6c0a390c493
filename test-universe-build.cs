using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Console
{
    class UniverseTestProgram
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing Universe Building with new criteria...");
            
            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.LocalProd.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            // Build services
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            services.AddSingleton<IConfiguration>(configuration);
            
            // Add required services (simplified)
            services.AddSingleton<IMarketCalendarService, MarketCalendarService>();
            
            var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILogger<UniverseTestProgram>>();
            
            try
            {
                // Test the date calculation logic
                var marketCalendar = serviceProvider.GetRequiredService<IMarketCalendarService>();
                var endDate = marketCalendar.GetLastTradingDay();
                var analysisPeriodDays = 60; // New value
                var startDate = endDate.AddDays(-analysisPeriodDays);
                
                logger.LogInformation("Date Range Test:");
                logger.LogInformation("  End Date (Last Trading Day): {EndDate:yyyy-MM-dd}", endDate);
                logger.LogInformation("  Start Date: {StartDate:yyyy-MM-dd}", startDate);
                logger.LogInformation("  Analysis Period: {Days} days", (endDate - startDate).Days);
                
                // Test filter criteria
                var dynamicUniverseConfig = configuration.GetSection("DynamicUniverse:DefaultCriteria");
                var minPrice = dynamicUniverseConfig.GetValue<decimal>("MinPrice", 5.0m);
                var minVolume = dynamicUniverseConfig.GetValue<long>("MinAverageVolume", 100_000);
                var minVolatility = dynamicUniverseConfig.GetValue<decimal>("MinVolatilityPercent", 0.5m);
                
                logger.LogInformation("Filter Criteria Test:");
                logger.LogInformation("  Min Price: ${MinPrice}", minPrice);
                logger.LogInformation("  Min Average Volume: {MinVolume:N0}", minVolume);
                logger.LogInformation("  Min Volatility: {MinVolatility}%", minVolatility);
                
                // Show what a Polygon API call would look like
                var testSymbol = "AAPL";
                var polygonUrl = $"https://api.polygon.io/v2/aggs/ticker/{testSymbol}/range/1/day/{startDate:yyyy-MM-dd}/{endDate:yyyy-MM-dd}?adjusted=true&sort=asc&limit=50000";
                logger.LogInformation("Sample Polygon API URL for {Symbol}:", testSymbol);
                logger.LogInformation("  {Url}", polygonUrl);
                
                logger.LogInformation("✅ Configuration test completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ Configuration test failed");
            }
        }
    }
}
