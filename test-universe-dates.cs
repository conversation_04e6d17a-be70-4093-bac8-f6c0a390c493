using System;

class Program
{
    static void Main()
    {
        // Simulate the date calculation logic from DynamicUniverseProvider
        var today = DateTime.UtcNow.Date;
        var analysisPeriodDays = 20; // From config
        
        Console.WriteLine($"Today (UTC): {today:yyyy-MM-dd}");
        
        // Simulate GetLastTradingDay() logic
        var endDate = GetLastTradingDay(today);
        Console.WriteLine($"End Date (Last Trading Day): {endDate:yyyy-MM-dd}");
        
        var startDate = endDate.AddDays(-analysisPeriodDays);
        Console.WriteLine($"Start Date (End - {analysisPeriodDays} days): {startDate:yyyy-MM-dd}");
        
        // Check if startDate >= endDate (the problematic condition)
        if (startDate >= endDate)
        {
            Console.WriteLine("❌ PROBLEM: startDate >= endDate!");
            startDate = endDate.AddDays(-Math.Max(analysisPeriodDays, 30));
            Console.WriteLine($"Adjusted Start Date: {startDate:yyyy-MM-dd}");
        }
        
        var daysDifference = (endDate - startDate).Days;
        Console.WriteLine($"Date range: {daysDifference} days");
        
        // Check if this is a reasonable range for getting bar data
        if (daysDifference < 10)
        {
            Console.WriteLine("⚠️ WARNING: Very short date range - may not provide enough bars for analysis");
        }
        
        // Show what the Polygon API URL would look like
        var symbol = "AAPL";
        var polygonUrl = $"https://api.polygon.io/v2/aggs/ticker/{symbol}/range/1/day/{startDate:yyyy-MM-dd}/{endDate:yyyy-MM-dd}?adjusted=true&sort=asc&limit=50000";
        Console.WriteLine($"\nPolygon API URL would be:");
        Console.WriteLine(polygonUrl);
    }
    
    static DateTime GetLastTradingDay(DateTime date)
    {
        // Simplified version of MarketCalendarService.GetLastTradingDay()
        for (int i = 1; i <= 10; i++)
        {
            var candidateDate = date.AddDays(-i);
            
            // Skip weekends (simplified - not checking holidays)
            if (candidateDate.DayOfWeek != DayOfWeek.Saturday && 
                candidateDate.DayOfWeek != DayOfWeek.Sunday)
            {
                return candidateDate;
            }
        }
        
        // Fallback
        return date.AddDays(-5);
    }
}
