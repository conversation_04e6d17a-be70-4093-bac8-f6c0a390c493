# PowerShell script to test universe refresh with new criteria
Write-Host "🔄 Testing Universe Refresh with New Criteria" -ForegroundColor Green

# Build and run the test
Write-Host "Building project..." -ForegroundColor Yellow
dotnet build SmaTrendFollower.Console --configuration Release --verbosity quiet

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build successful" -ForegroundColor Green

# Create a simple test program inline
$testCode = @"
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Console
{
    class UniverseTestProgram
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("🔄 Testing Universe Building with new criteria...");
            
            // Use the existing host builder from Program.cs
            var host = Program.CreateHostBuilder(args).Build();
            
            using var scope = host.Services.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<UniverseTestProgram>>();
            var universeRefreshService = scope.ServiceProvider.GetRequiredService<IDailyUniverseRefreshService>();
            var universeProvider = scope.ServiceProvider.GetRequiredService<IUniverseProvider>();
            
            try
            {
                // Check current status
                var status = await universeRefreshService.GetStatusAsync();
                logger.LogInformation("Current Status: Refreshing={IsRefreshing}, CandidateCount={CandidateCount}, LastRefresh={LastRefresh}", 
                    status.IsRefreshing, status.CandidateCount, status.LastRefreshTime);
                
                // Get current candidates
                var currentCandidates = await universeRefreshService.GetCurrentCandidatesAsync();
                if (currentCandidates != null)
                {
                    logger.LogInformation("Current cached candidates: {Count} symbols, generated at {GeneratedAt}", 
                        currentCandidates.CandidateCount, currentCandidates.GeneratedAt);
                }
                else
                {
                    logger.LogInformation("No cached candidates found");
                }
                
                // Trigger manual refresh
                logger.LogInformation("🔄 Triggering manual universe refresh...");
                var refreshedCandidates = await universeRefreshService.RefreshUniverseAsync();
                
                logger.LogInformation("✅ Universe refresh completed!");
                logger.LogInformation("New candidate count: {Count}", refreshedCandidates.CandidateCount);
                logger.LogInformation("Generated at: {GeneratedAt}", refreshedCandidates.GeneratedAt);
                
                // Show some sample symbols
                if (refreshedCandidates.Candidates.Any())
                {
                    var sampleSymbols = refreshedCandidates.Candidates.Take(10).Select(c => c.Symbol);
                    logger.LogInformation("Sample symbols: {Symbols}", string.Join(", ", sampleSymbols));
                }
                
                // Test the universe provider
                var universeSymbols = await universeProvider.GetSymbolsAsync();
                var universeList = universeSymbols.ToList();
                logger.LogInformation("Universe provider returned {Count} symbols", universeList.Count);
                
                logger.LogInformation("✅ Test completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "❌ Test failed");
                throw;
            }
        }
    }
}
"@

# Write the test code to a temporary file
$testFile = "TempUniverseTest.cs"
$testCode | Out-File -FilePath $testFile -Encoding UTF8

Write-Host "Running universe refresh test..." -ForegroundColor Yellow

# Compile and run the test
try {
    csc /reference:"SmaTrendFollower.Console\bin\Release\net8.0\*.dll" /out:UniverseTest.exe $testFile
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Test compiled successfully" -ForegroundColor Green
        .\UniverseTest.exe
    } else {
        Write-Host "❌ Test compilation failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error running test: $_" -ForegroundColor Red
} finally {
    # Cleanup
    if (Test-Path $testFile) { Remove-Item $testFile }
    if (Test-Path "UniverseTest.exe") { Remove-Item "UniverseTest.exe" }
}

Write-Host "🏁 Test completed" -ForegroundColor Green
