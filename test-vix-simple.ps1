#!/usr/bin/env pwsh

# Simple VIX Data Test
Write-Host "🔍 TESTING VIX DATA ACCESS" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host ""

$apiKey = "stffXZCR90K0YULLv7zoUMq1k4JWiyHD"

# Test 1: Polygon VIX Snapshot
Write-Host "📊 Test 1: Polygon VIX Snapshot API" -ForegroundColor Yellow
try {
    $url = "https://api.polygon.io/v3/snapshot?ticker=I:VIX&apikey=$apiKey"
    $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 30
    
    if ($response.results -and $response.results.Count -gt 0) {
        $vix = $response.results[0]
        Write-Host "✅ SUCCESS: VIX data retrieved" -ForegroundColor Green
        Write-Host "   Value: $($vix.value)" -ForegroundColor White
        Write-Host "   Updated: $($vix.updated)" -ForegroundColor White
        $polygonWorking = $true
    } else {
        Write-Host "⚠️  No VIX data in response" -ForegroundColor Yellow
        $polygonWorking = $false
    }
} catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    $polygonWorking = $false
}
Write-Host ""

# Test 2: Polygon VIX Historical Bars
Write-Host "📈 Test 2: Polygon VIX Historical Bars" -ForegroundColor Yellow
try {
    $today = Get-Date -Format "yyyy-MM-dd"
    $url = "https://api.polygon.io/v2/aggs/ticker/I:VIX/range/1/day/$today/$today?adjusted=true`&sort=asc`&apikey=$apiKey"
    $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 30
    
    if ($response.results -and $response.results.Count -gt 0) {
        $bar = $response.results[0]
        Write-Host "✅ SUCCESS: VIX historical data retrieved" -ForegroundColor Green
        Write-Host "   Close: $($bar.c)" -ForegroundColor White
        Write-Host "   Volume: $($bar.v)" -ForegroundColor White
        $historicalWorking = $true
    } else {
        Write-Host "⚠️  No historical VIX data available for today" -ForegroundColor Yellow
        $historicalWorking = $false
    }
} catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    $historicalWorking = $false
}
Write-Host ""

# Test 3: Web Scraping Fallback (Yahoo Finance)
Write-Host "🌐 Test 3: Web Scraping Fallback (Yahoo Finance)" -ForegroundColor Yellow
try {
    $url = "https://finance.yahoo.com/quote/%5EVIX"
    $response = Invoke-WebRequest -Uri $url -TimeoutSec 15 -UserAgent "Mozilla/5.0"
    
    # Look for VIX value in the response
    if ($response.Content -match 'data-symbol="\^VIX"[^>]*data-field="regularMarketPrice"[^>]*>([0-9.]+)') {
        $vixValue = $matches[1]
        Write-Host "✅ SUCCESS: VIX scraped from Yahoo Finance" -ForegroundColor Green
        Write-Host "   Value: $vixValue" -ForegroundColor White
        $webScrapingWorking = $true
    } elseif ($response.Content -match '"regularMarketPrice":\{"raw":([0-9.]+)') {
        $vixValue = $matches[1]
        Write-Host "✅ SUCCESS: VIX scraped from Yahoo Finance (JSON)" -ForegroundColor Green
        Write-Host "   Value: $vixValue" -ForegroundColor White
        $webScrapingWorking = $true
    } else {
        Write-Host "⚠️  Could not parse VIX value from Yahoo Finance" -ForegroundColor Yellow
        $webScrapingWorking = $false
    }
} catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    $webScrapingWorking = $false
}
Write-Host ""

# Test 4: Alternative Web Source (MarketWatch)
Write-Host "📰 Test 4: Alternative Web Source (MarketWatch)" -ForegroundColor Yellow
try {
    $url = "https://www.marketwatch.com/investing/index/vix"
    $response = Invoke-WebRequest -Uri $url -TimeoutSec 15 -UserAgent "Mozilla/5.0"
    
    # Look for VIX value in MarketWatch response
    if ($response.Content -match 'class="value"[^>]*>([0-9.]+)') {
        $vixValue = $matches[1]
        Write-Host "✅ SUCCESS: VIX scraped from MarketWatch" -ForegroundColor Green
        Write-Host "   Value: $vixValue" -ForegroundColor White
        $marketWatchWorking = $true
    } else {
        Write-Host "⚠️  Could not parse VIX value from MarketWatch" -ForegroundColor Yellow
        $marketWatchWorking = $false
    }
} catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    $marketWatchWorking = $false
}
Write-Host ""

# Summary Report
Write-Host "📋 VIX DATA ACCESS SUMMARY" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan
Write-Host ""

$workingSources = 0
if ($polygonWorking) { 
    Write-Host "✅ Polygon Snapshot API: WORKING" -ForegroundColor Green
    $workingSources++
} else {
    Write-Host "❌ Polygon Snapshot API: FAILED" -ForegroundColor Red
}

if ($historicalWorking) { 
    Write-Host "✅ Polygon Historical API: WORKING" -ForegroundColor Green
    $workingSources++
} else {
    Write-Host "❌ Polygon Historical API: FAILED" -ForegroundColor Red
}

if ($webScrapingWorking) { 
    Write-Host "✅ Yahoo Finance Scraping: WORKING" -ForegroundColor Green
    $workingSources++
} else {
    Write-Host "❌ Yahoo Finance Scraping: FAILED" -ForegroundColor Red
}

if ($marketWatchWorking) { 
    Write-Host "✅ MarketWatch Scraping: WORKING" -ForegroundColor Green
    $workingSources++
} else {
    Write-Host "❌ MarketWatch Scraping: FAILED" -ForegroundColor Red
}

Write-Host ""
Write-Host "Working Sources: $workingSources / 4" -ForegroundColor White

if ($workingSources -ge 2) {
    Write-Host "🎉 VIX DATA ACCESS: EXCELLENT" -ForegroundColor Green
    Write-Host "✅ Multiple fallback sources available" -ForegroundColor Green
    Write-Host "✅ Trading can proceed with confidence" -ForegroundColor Green
} elseif ($workingSources -eq 1) {
    Write-Host "⚠️  VIX DATA ACCESS: ADEQUATE" -ForegroundColor Yellow
    Write-Host "⚠️  Only one source working - monitor closely" -ForegroundColor Yellow
} else {
    Write-Host "❌ VIX DATA ACCESS: CRITICAL ISSUE" -ForegroundColor Red
    Write-Host "🚨 No VIX sources working - trading may halt" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 Fallback Strategy:" -ForegroundColor Cyan
Write-Host "1. Primary: Polygon API (real-time)" -ForegroundColor White
Write-Host "2. Secondary: Web scraping (Yahoo, MarketWatch, etc.)" -ForegroundColor White
Write-Host "3. Tertiary: Synthetic VIX from VXX/UVXY ETFs" -ForegroundColor White
Write-Host "4. Final: Trading halt if all sources fail" -ForegroundColor White
Write-Host ""
