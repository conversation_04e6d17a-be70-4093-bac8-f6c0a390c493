# Test Alpaca Paper Trading API directly
Write-Host "🧪 Testing Alpaca Paper Trading API" -ForegroundColor Cyan
Write-Host "=" * 50

# Paper trading credentials
$apiKey = "PK0AM3WB1CES3YBQPGR0"
$secretKey = "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf"
$baseUrl = "https://paper-api.alpaca.markets"

$headers = @{
    "APCA-API-KEY-ID" = $apiKey
    "APCA-API-SECRET-KEY" = $secretKey
    "Content-Type" = "application/json"
}

try {
    # Test 1: Get account info
    Write-Host "📊 Testing account access..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri "$baseUrl/v2/account" -Headers $headers -Method Get
    
    Write-Host "✅ Account connected: $($response.id)" -ForegroundColor Green
    Write-Host "💰 Buying Power: $([math]::Round([decimal]$response.buying_power, 2))" -ForegroundColor Green
    Write-Host "📈 Portfolio Value: $([math]::Round([decimal]$response.portfolio_value, 2))" -ForegroundColor Green
    Write-Host "🏦 Account Status: $($response.status)" -ForegroundColor Green
    Write-Host ""
    
    # Test 2: Get current positions
    Write-Host "📊 Testing positions access..." -ForegroundColor Yellow
    $positions = Invoke-RestMethod -Uri "$baseUrl/v2/positions" -Headers $headers -Method Get
    
    Write-Host "✅ Current Positions: $($positions.Count)" -ForegroundColor Green
    foreach ($pos in $positions | Select-Object -First 3) {
        $avgCost = [math]::Round([decimal]$pos.avg_cost, 2)
        Write-Host "   $($pos.symbol): $($pos.qty) shares @ $avgCost" -ForegroundColor White
    }
    Write-Host ""
    
    # Test 3: Submit a test order
    Write-Host "🚀 Testing order submission..." -ForegroundColor Yellow
    $orderData = @{
        symbol = "AAPL"
        qty = 1
        side = "buy"
        type = "market"
        time_in_force = "day"
    } | ConvertTo-Json
    
    $order = Invoke-RestMethod -Uri "$baseUrl/v2/orders" -Headers $headers -Method Post -Body $orderData
    
    Write-Host "✅ Paper trade order submitted successfully!" -ForegroundColor Green
    Write-Host "   Order ID: $($order.id)" -ForegroundColor White
    Write-Host "   Symbol: $($order.symbol)" -ForegroundColor White
    Write-Host "   Quantity: $($order.qty) shares" -ForegroundColor White
    Write-Host "   Side: $($order.side)" -ForegroundColor White
    Write-Host "   Type: $($order.type)" -ForegroundColor White
    Write-Host "   Status: $($order.status)" -ForegroundColor White
    Write-Host "   Submitted At: $($order.submitted_at)" -ForegroundColor White
    
    # Wait and check order status
    Write-Host ""
    Write-Host "⏳ Waiting 3 seconds to check order status..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    
    $updatedOrder = Invoke-RestMethod -Uri "$baseUrl/v2/orders/$($order.id)" -Headers $headers -Method Get
    Write-Host "📊 Updated Order Status: $($updatedOrder.status)" -ForegroundColor Cyan
    
    if ($updatedOrder.filled_qty -and [decimal]$updatedOrder.filled_qty -gt 0) {
        $filledQty = [decimal]$updatedOrder.filled_qty
        $avgPrice = [decimal]$updatedOrder.filled_avg_price
        $totalValue = $filledQty * $avgPrice
        Write-Host "✅ Order filled: $filledQty shares @ $([math]::Round($avgPrice, 2))" -ForegroundColor Green
        Write-Host "💰 Total Value: $([math]::Round($totalValue, 2))" -ForegroundColor Green
    } else {
        Write-Host "⏳ Order still pending - this is normal for paper trading" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "✅ All Alpaca paper trading tests passed!" -ForegroundColor Green
    Write-Host "🎯 This proves the basic trading infrastructure works!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error in Alpaca test: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}
