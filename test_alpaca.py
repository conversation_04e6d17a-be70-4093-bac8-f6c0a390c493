#!/usr/bin/env python3

import requests
import json
from datetime import datetime

def test_alpaca_paper_trading():
    """Test Alpaca paper trading API directly"""
    
    # Paper trading credentials
    api_key = "PK0AM3WB1CES3YBQPGR0"
    secret_key = "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf"
    base_url = "https://paper-api.alpaca.markets"
    
    headers = {
        "APCA-API-KEY-ID": api_key,
        "APCA-API-SECRET-KEY": secret_key,
        "Content-Type": "application/json"
    }
    
    print("🧪 Testing Alpaca Paper Trading API")
    print("=" * 50)
    
    try:
        # Test 1: Get account info
        print("📊 Testing account access...")
        response = requests.get(f"{base_url}/v2/account", headers=headers)
        
        if response.status_code == 200:
            account = response.json()
            print(f"✅ Account connected: {account['id']}")
            print(f"💰 Buying Power: ${float(account['buying_power']):,.2f}")
            print(f"📈 Portfolio Value: ${float(account['portfolio_value']):,.2f}")
            print(f"🏦 Account Status: {account['status']}")
        else:
            print(f"❌ Account access failed: {response.status_code} - {response.text}")
            return False
            
        print()
        
        # Test 2: Get current positions
        print("📊 Testing positions access...")
        response = requests.get(f"{base_url}/v2/positions", headers=headers)
        
        if response.status_code == 200:
            positions = response.json()
            print(f"✅ Current Positions: {len(positions)}")
            for pos in positions[:3]:  # Show first 3
                print(f"   {pos['symbol']}: {pos['qty']} shares @ ${float(pos['avg_cost']):,.2f}")
        else:
            print(f"❌ Positions access failed: {response.status_code} - {response.text}")
            
        print()
        
        # Test 3: Submit a test order
        print("🚀 Testing order submission...")
        order_data = {
            "symbol": "AAPL",
            "qty": 1,
            "side": "buy",
            "type": "market",
            "time_in_force": "day"
        }
        
        response = requests.post(f"{base_url}/v2/orders", 
                               headers=headers, 
                               json=order_data)
        
        if response.status_code == 201:
            order = response.json()
            print(f"✅ Paper trade order submitted successfully!")
            print(f"   Order ID: {order['id']}")
            print(f"   Symbol: {order['symbol']}")
            print(f"   Quantity: {order['qty']} shares")
            print(f"   Side: {order['side']}")
            print(f"   Type: {order['type']}")
            print(f"   Status: {order['status']}")
            print(f"   Submitted At: {order['submitted_at']}")
            
            # Wait and check order status
            import time
            print("\n⏳ Waiting 3 seconds to check order status...")
            time.sleep(3)
            
            response = requests.get(f"{base_url}/v2/orders/{order['id']}", headers=headers)
            if response.status_code == 200:
                updated_order = response.json()
                print(f"📊 Updated Order Status: {updated_order['status']}")
                
                if updated_order.get('filled_qty', '0') != '0':
                    filled_qty = float(updated_order['filled_qty'])
                    avg_price = float(updated_order.get('filled_avg_price', 0))
                    print(f"✅ Order filled: {filled_qty} shares @ ${avg_price:.2f}")
                    print(f"💰 Total Value: ${filled_qty * avg_price:.2f}")
                else:
                    print("⏳ Order still pending - this is normal for paper trading")
            
        else:
            print(f"❌ Order submission failed: {response.status_code} - {response.text}")
            return False
            
        print()
        print("✅ All Alpaca paper trading tests passed!")
        print("🎯 This proves the basic trading infrastructure works!")
        return True
        
    except Exception as e:
        print(f"❌ Error in Alpaca test: {str(e)}")
        return False

if __name__ == "__main__":
    test_alpaca_paper_trading()
