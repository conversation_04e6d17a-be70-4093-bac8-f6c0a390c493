using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using SmaTrendFollower.Console.Data;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/database_test.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();

Console.WriteLine("🔍 Testing database query with parameter logging...");

try
{
    using IHost host = Host.CreateDefaultBuilder()
        .UseSerilog()
        .ConfigureServices(services =>
        {
            // Use PostgreSQL connection strings from configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.LocalProd.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            // Add database contexts with PostgreSQL
            services.AddDbContext<StockBarCacheDbContext>(options =>
            {
                var connectionString = configuration.GetConnectionString("StockBarCache") ?? 
                    "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;";
                options.UseNpgsql(connectionString);
                options.EnableSensitiveDataLogging(true);
                options.EnableDetailedErrors(true);
                options.LogTo(Console.WriteLine, LogLevel.Information);
            });
        })
        .Build();

    using var scope = host.Services.CreateScope();
    var context = scope.ServiceProvider.GetRequiredService<StockBarCacheDbContext>();

    // Test the exact query that was failing
    Console.WriteLine("Testing GetCachedBarsAsync with various parameter combinations...");

    // Test 1: Normal parameters
    Console.WriteLine("\n=== Test 1: Normal Parameters ===");
    var symbol = "AAPL";
    var timeFrame = "Day";
    var startDate = DateTime.UtcNow.Date.AddDays(-30);
    var endDate = DateTime.UtcNow.Date.AddDays(-1);

    Console.WriteLine($"Parameters: Symbol='{symbol}', TimeFrame='{timeFrame}', StartDate='{startDate:yyyy-MM-dd}', EndDate='{endDate:yyyy-MM-dd}'");
    
    var result1 = await context.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate);
    Console.WriteLine($"✅ Test 1 passed: Found {result1.Count} bars");

    // Test 2: Empty/null parameters
    Console.WriteLine("\n=== Test 2: Empty/Null Parameters ===");
    var result2 = await context.GetCachedBarsAsync("", timeFrame, startDate, endDate);
    Console.WriteLine($"✅ Test 2 passed: Found {result2.Count} bars (expected 0)");

    var result3 = await context.GetCachedBarsAsync(symbol, "", startDate, endDate);
    Console.WriteLine($"✅ Test 3 passed: Found {result3.Count} bars (expected 0)");

    // Test 4: Invalid date range
    Console.WriteLine("\n=== Test 4: Invalid Date Range ===");
    var result4 = await context.GetCachedBarsAsync(symbol, timeFrame, endDate, startDate); // swapped dates
    Console.WriteLine($"✅ Test 4 passed: Found {result4.Count} bars (expected 0)");

    // Test 5: Future dates
    Console.WriteLine("\n=== Test 5: Future Dates ===");
    var futureStart = DateTime.UtcNow.Date.AddDays(1);
    var futureEnd = DateTime.UtcNow.Date.AddDays(2);
    var result5 = await context.GetCachedBarsAsync(symbol, timeFrame, futureStart, futureEnd);
    Console.WriteLine($"✅ Test 5 passed: Found {result5.Count} bars");

    // Test 6: Very long symbol (this might cause the original error)
    Console.WriteLine("\n=== Test 6: Long Symbol ===");
    var longSymbol = "VERY_LONG_SYMBOL_NAME_THAT_EXCEEDS_20_CHARACTERS";
    try
    {
        var result6 = await context.GetCachedBarsAsync(longSymbol, timeFrame, startDate, endDate);
        Console.WriteLine($"✅ Test 6 passed: Found {result6.Count} bars");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"⚠️ Test 6 failed as expected: {ex.Message}");
    }

    // Test 7: Special characters in parameters
    Console.WriteLine("\n=== Test 7: Special Characters ===");
    var specialSymbol = "TEST'\"\\";
    try
    {
        var result7 = await context.GetCachedBarsAsync(specialSymbol, timeFrame, startDate, endDate);
        Console.WriteLine($"✅ Test 7 passed: Found {result7.Count} bars");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"⚠️ Test 7 failed: {ex.Message}");
    }

    Console.WriteLine("\n✅ All database query tests completed successfully!");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Database test failed: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    Environment.Exit(1);
}
finally
{
    Log.CloseAndFlush();
}
