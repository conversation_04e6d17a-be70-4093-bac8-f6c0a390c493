using System;
using System.Threading.Tasks;
using Alpaca.Markets;

class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("=== LIVE ACCOUNT TEST ===");
            
            // Live credentials
            var keyId = "AKGBPW5HD8LVI5C6NJUJ";
            var secretKey = "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM";
            
            Console.WriteLine($"Using Live API Key: {keyId}");
            
            // Create live trading client
            var client = Environments.Live.GetAlpacaTradingClient(new SecretKey(keyId, secretKey));
            
            Console.WriteLine("Connecting to live account...");
            var account = await client.GetAccountAsync();
            
            Console.WriteLine("✅ Successfully connected to LIVE account!");
            Console.WriteLine($"Account ID: {account.AccountId}");
            Console.WriteLine($"Status: {account.Status}");
            Console.WriteLine($"Equity: {account.Equity:C}");
            Console.WriteLine($"Cash: {account.TradableCash:C}");
            Console.WriteLine($"Buying Power: {account.BuyingPower:C}");
            
            // Check if this is actually a live account (not paper)
            var isLive = !account.AccountId.ToString().StartsWith("PA");
            Console.WriteLine($"Account Type: {(isLive ? "LIVE" : "PAPER")}");
            
            if (isLive)
            {
                Console.WriteLine("🎉 This is a LIVE trading account with real money!");
                Console.WriteLine($"💰 Current balance: {account.Equity:C}");
            }
            else
            {
                Console.WriteLine("⚠️ This appears to be a paper trading account");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Details: {ex}");
        }
    }
}
