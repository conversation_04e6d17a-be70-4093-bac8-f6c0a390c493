using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;

// Minimal test to see what's causing the startup hang
Console.WriteLine("🧪 Testing Minimal Host Startup...");

try
{
    // Set environment variable to disable hosted services
    Environment.SetEnvironmentVariable("DISABLE_HOSTED_SERVICES", "true");
    
    Console.WriteLine("✅ Environment variable set");
    
    // Configure Serilog
    Log.Logger = new LoggerConfiguration()
        .WriteTo.Console()
        .CreateLogger();
        
    Console.WriteLine("✅ Serilog configured");
    
    // Try creating a minimal host
    Console.WriteLine("🔄 Creating minimal host...");
    
    using var host = Host.CreateDefaultBuilder()
        .UseEnvironment("LocalProd")
        .UseSerilog()
        .ConfigureHostOptions(opts => 
        {
            opts.ShutdownTimeout = TimeSpan.FromSeconds(2);
            opts.BackgroundServiceExceptionBehavior = BackgroundServiceExceptionBehavior.Ignore;
        })
        .ConfigureServices((context, services) =>
        {
            // Add minimal services only
            services.AddLogging();
        })
        .Build();
        
    Console.WriteLine("✅ Minimal host created successfully!");
    
    // Test service resolution
    using var scope = host.Services.CreateScope();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    logger.LogInformation("✅ Service resolution works!");
    
    Console.WriteLine("✅ Test completed successfully!");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Test failed: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}
finally
{
    Environment.SetEnvironmentVariable("DISABLE_HOSTED_SERVICES", null);
    Log.CloseAndFlush();
}
