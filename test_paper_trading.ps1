#!/usr/bin/env pwsh

Write-Host "🧪 Testing Paper Trading with Alpaca API" -ForegroundColor Cyan
Write-Host "=" * 50

# Set environment variables for paper trading
$env:APCA_API_ENV = "paper"
$env:ASPNETCORE_ENVIRONMENT = "LocalProd"
$env:DOTNET_ENVIRONMENT = "LocalProd"

Write-Host "📊 Environment Variables Set:" -ForegroundColor Yellow
Write-Host "   APCA_API_ENV = $env:APCA_API_ENV"
Write-Host "   ASPNETCORE_ENVIRONMENT = $env:ASPNETCORE_ENVIRONMENT"
Write-Host "   DOTNET_ENVIRONMENT = $env:DOTNET_ENVIRONMENT"
Write-Host ""

# Test 1: Direct API call using PowerShell
Write-Host "🔍 Test 1: Direct Alpaca Paper API Test" -ForegroundColor Green
Write-Host "Testing account access with paper credentials..."

$headers = @{
    'APCA-API-KEY-ID' = 'PK0AM3WB1CES3YBQPGR0'
    'APCA-API-SECRET-KEY' = '2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf'
}

try {
    $response = Invoke-RestMethod -Uri 'https://paper-api.alpaca.markets/v2/account' -Headers $headers
    Write-Host "✅ Paper account access successful!" -ForegroundColor Green
    Write-Host "   Account ID: $($response.id)"
    Write-Host "   Buying Power: $($response.buying_power)"
    Write-Host "   Portfolio Value: $($response.portfolio_value)"
    Write-Host "   Status: $($response.status)"
} catch {
    Write-Host "❌ Paper account access failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test 2: Submit a paper trade order
Write-Host "🚀 Test 2: Paper Trade Order Submission" -ForegroundColor Green
Write-Host "Submitting a test buy order for 1 share of AAPL..."

$orderData = @{
    symbol = "AAPL"
    qty = 1
    side = "buy"
    type = "market"
    time_in_force = "day"
} | ConvertTo-Json

$headers['Content-Type'] = 'application/json'

try {
    $orderResponse = Invoke-RestMethod -Uri 'https://paper-api.alpaca.markets/v2/orders' -Method Post -Headers $headers -Body $orderData
    Write-Host "✅ Paper trade order submitted successfully!" -ForegroundColor Green
    Write-Host "   Order ID: $($orderResponse.id)"
    Write-Host "   Symbol: $($orderResponse.symbol)"
    Write-Host "   Quantity: $($orderResponse.qty) shares"
    Write-Host "   Side: $($orderResponse.side)"
    Write-Host "   Type: $($orderResponse.type)"
    Write-Host "   Status: $($orderResponse.status)"
    Write-Host "   Submitted At: $($orderResponse.submitted_at)"
    
    # Wait and check order status
    Write-Host ""
    Write-Host "⏳ Waiting 3 seconds to check order status..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    
    $statusResponse = Invoke-RestMethod -Uri "https://paper-api.alpaca.markets/v2/orders/$($orderResponse.id)" -Headers $headers
    Write-Host "📊 Updated Order Status: $($statusResponse.status)" -ForegroundColor Cyan
    
    if ($statusResponse.filled_qty -and $statusResponse.filled_qty -ne "0") {
        $filledQty = [decimal]$statusResponse.filled_qty
        $avgPrice = [decimal]$statusResponse.filled_avg_price
        Write-Host "✅ Order filled: $filledQty shares @ $($avgPrice.ToString('F2'))" -ForegroundColor Green
        Write-Host "💰 Total Value: $($($filledQty * $avgPrice).ToString('F2'))" -ForegroundColor Green
    } else {
        Write-Host "⏳ Order still pending - this is normal for paper trading" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Order submission failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Try to run the SmaTrendFollower with paper trading
Write-Host "🎯 Test 3: SmaTrendFollower Paper Trading Test" -ForegroundColor Green
Write-Host "Attempting to run SmaTrendFollower with paper trading environment..."

try {
    # Build first
    Write-Host "🔨 Building SmaTrendFollower..." -ForegroundColor Yellow
    $buildResult = dotnet build SmaTrendFollower.Console --verbosity quiet
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Build successful" -ForegroundColor Green
    
    # Run with timeout
    Write-Host "🚀 Running SmaTrendFollower in single-cycle mode..." -ForegroundColor Yellow
    $job = Start-Job -ScriptBlock {
        param($envVars)
        foreach ($var in $envVars.GetEnumerator()) {
            Set-Item -Path "env:$($var.Key)" -Value $var.Value
        }
        Set-Location $using:PWD
        dotnet run --project SmaTrendFollower.Console --single-cycle 2>&1
    } -ArgumentList @{
        APCA_API_ENV = "paper"
        ASPNETCORE_ENVIRONMENT = "LocalProd"
        DOTNET_ENVIRONMENT = "LocalProd"
    }
    
    # Wait for 30 seconds max
    $timeout = 30
    $result = Wait-Job -Job $job -Timeout $timeout
    
    if ($result) {
        $output = Receive-Job -Job $job
        Write-Host "📋 SmaTrendFollower Output:" -ForegroundColor Cyan
        Write-Host $output
        
        if ($output -match "Trading cycle completed" -or $output -match "paper") {
            Write-Host "✅ SmaTrendFollower paper trading test successful!" -ForegroundColor Green
        } else {
            Write-Host "⚠️ SmaTrendFollower ran but may not have completed successfully" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⏰ SmaTrendFollower test timed out after $timeout seconds" -ForegroundColor Yellow
        Stop-Job -Job $job
    }
    
    Remove-Job -Job $job -Force
    
} catch {
    Write-Host "❌ SmaTrendFollower test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Paper Trading Tests Complete!" -ForegroundColor Cyan
Write-Host "=" * 50
