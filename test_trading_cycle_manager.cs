using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Console.Configuration;
using SmaTrendFollower.Services;

// Quick test to verify TradingCycleManager can be resolved and works
Console.WriteLine("🧪 Testing TradingCycleManager Resolution...");

try
{
    // Set environment variable to disable hosted services (like in production fix)
    Environment.SetEnvironmentVariable("DISABLE_HOSTED_SERVICES", "true");

    var host = Host.CreateDefaultBuilder()
        .UseEnvironment("LocalProd")
        .ConfigureServices((context, services) =>
        {
            // Add only the essential services needed for TradingCycleManager
            services.AddLogging();
            services.AddSingleton<ITimeProvider, SystemTimeProvider>();
            services.AddSingleton<IMarketCalendarService, MarketCalendarService>();
            services.AddSingleton<IMarketSessionGuard, MarketSessionGuard>();
            
            // Add minimal market data service dependencies
            services.AddCoreInfrastructure();
            services.AddMarketDataServices();
            services.AddMonitoringServices(context.Configuration);
            
            // Add TradingCycleManager
            services.AddScoped<ITradingCycleManager>(provider =>
            {
                var marketDataService = provider.GetRequiredService<IMarketDataService>();
                var marketSessionGuard = provider.GetRequiredService<IMarketSessionGuard>();
                var logger = provider.GetRequiredService<ILogger<TradingCycleManager>>();
                var configuration = provider.GetRequiredService<Microsoft.Extensions.Configuration.IConfiguration>();
                var config = TradingCycleConfig.FromConfiguration(configuration);
                return new TradingCycleManager(marketDataService, marketSessionGuard, logger, config);
            });
        })
        .Build();

    Console.WriteLine("✅ Host created successfully");

    using var scope = host.Services.CreateScope();
    Console.WriteLine("✅ Scope created successfully");

    // Test TradingCycleManager resolution
    Console.WriteLine("🔄 Resolving TradingCycleManager...");
    var cycleManager = scope.ServiceProvider.GetRequiredService<ITradingCycleManager>();
    Console.WriteLine("✅ TradingCycleManager resolved successfully!");

    // Test its functionality
    Console.WriteLine("🔄 Testing GetCurrentCycleIntervalAsync...");
    var interval = await cycleManager.GetCurrentCycleIntervalAsync(CancellationToken.None);
    Console.WriteLine($"✅ Cycle interval: {interval.TotalMinutes} minutes");

    Console.WriteLine("🔄 Testing GetCurrentIntervalReasonAsync...");
    var reason = await cycleManager.GetCurrentIntervalReasonAsync(CancellationToken.None);
    Console.WriteLine($"✅ Interval reason: {reason}");

    Console.WriteLine("🎉 TradingCycleManager is FULLY FUNCTIONAL!");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    Environment.Exit(1);
}
