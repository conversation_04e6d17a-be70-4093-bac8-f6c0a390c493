using System;

class Program
{
    static void Main()
    {
        Console.WriteLine("Testing timezone fixes...");
        
        var now = DateTime.UtcNow;
        Console.WriteLine($"Current UTC time: {now:yyyy-MM-dd HH:mm:ss} UTC");
        
        // Test old (incorrect) timezone
        try
        {
            var oldEasternTime = TimeZoneInfo.ConvertTimeFromUtc(now,
                TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));
            Console.WriteLine($"OLD (EST only): {oldEasternTime:yyyy-MM-dd HH:mm:ss} ET");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"OLD timezone error: {ex.Message}");
        }
        
        // Test new (correct) timezone
        try
        {
            var newEasternTime = TimeZoneInfo.ConvertTimeFromUtc(now,
                TimeZoneInfo.FindSystemTimeZoneById("US Eastern Standard Time"));
            Console.WriteLine($"NEW (EST/EDT): {newEasternTime:yyyy-MM-dd HH:mm:ss} ET");
            
            // Test market hours logic
            var marketOpen = new TimeSpan(9, 30, 0);
            var marketClose = new TimeSpan(16, 0, 0);
            var currentTime = newEasternTime.TimeOfDay;
            
            bool isMarketHours = currentTime >= marketOpen && currentTime <= marketClose;
            bool isWeekday = newEasternTime.DayOfWeek != DayOfWeek.Saturday && 
                           newEasternTime.DayOfWeek != DayOfWeek.Sunday;
            
            Console.WriteLine($"Current ET time: {currentTime}");
            Console.WriteLine($"Market hours (9:30-16:00): {isMarketHours}");
            Console.WriteLine($"Is weekday: {isWeekday}");
            Console.WriteLine($"Can trade: {isMarketHours && isWeekday}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"NEW timezone error: {ex.Message}");
        }
    }
}
