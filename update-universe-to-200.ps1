#!/usr/bin/env pwsh

# Update Universe to 200 Symbols Script
# This script expands our current 50-symbol universe to 200 high-quality symbols

Write-Host "🚀 EXPANDING UNIVERSE TO 200 SYMBOLS" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Current 50 symbols from universe.csv
$current50 = @(
    "SPY", "QQQ", "AAPL", "MSFT", "NVDA", "GOOGL", "AMZN", "TSLA", "META", "BRK.B",
    "AVGO", "LLY", "JPM", "UNH", "XOM", "V", "PG", "JNJ", "MA", "HD", 
    "CVX", "ABBV", "NFLX", "BAC", "KO", "ADBE", "WMT", "CRM", "TMO", "CSCO",
    "ACN", "LIN", "ABT", "AMD", "DHR", "VZ", "PFE", "ORCL", "NKE", "TXN",
    "DIS", "QCOM", "PM", "RTX", "SPGI", "NEE", "LOW", "INTU", "HON", "UNP"
)

# Additional 150 high-quality symbols to reach 200 total
$additional150 = @(
    # Large Cap Tech & Growth
    "GOOG", "BABA", "CRM", "NOW", "SNOW", "PLTR", "RBLX", "U", "DDOG", "CRWD",
    "ZS", "OKTA", "TWLO", "DOCU", "ZOOM", "TEAM", "WDAY", "VEEV", "SPLK", "PANW",
    
    # Semiconductors & Hardware  
    "TSM", "ASML", "AMAT", "LRCX", "KLAC", "ADI", "MRVL", "MU", "INTC", "ON",
    "MPWR", "SWKS", "QRVO", "MCHP", "TER", "ENTG", "FORM", "CRUS", "SLAB", "SITM",
    
    # Healthcare & Biotech
    "MRNA", "BNTX", "REGN", "GILD", "BIIB", "AMGN", "VRTX", "ILMN", "ISRG", "SYK",
    "BSX", "MDT", "EW", "DXCM", "HOLX", "ALGN", "IDXX", "IQV", "MTD", "TECH",
    
    # Financial Services
    "GS", "MS", "C", "WFC", "USB", "PNC", "TFC", "COF", "AXP", "BLK",
    "SCHW", "SPGI", "MCO", "ICE", "CME", "NDAQ", "CBOE", "MKTX", "VIRT", "LPLA",
    
    # Consumer & Retail
    "AMZN", "COST", "TGT", "SBUX", "MCD", "NKE", "LULU", "ULTA", "TJX", "RH",
    "ETSY", "CHWY", "CHEWY", "W", "WAYFAIR", "SHOP", "SQ", "PYPL", "V", "MA",
    
    # Industrial & Materials
    "CAT", "DE", "MMM", "GE", "BA", "LMT", "NOC", "GD", "RTX", "HON",
    "EMR", "ITW", "PH", "ROK", "DOV", "XYL", "FTV", "HUBB", "FAST", "MSA",
    
    # Energy & Utilities
    "XOM", "CVX", "COP", "EOG", "SLB", "HAL", "BKR", "OXY", "DVN", "FANG",
    "NEE", "SO", "DUK", "AEP", "EXC", "XEL", "WEC", "ES", "AWK", "ATO",
    
    # REITs & Real Estate
    "AMT", "PLD", "CCI", "EQIX", "PSA", "EXR", "AVB", "EQR", "UDR", "CPT",
    "MAA", "ESS", "AIV", "BXP", "VTR", "WELL", "PEAK", "DOC", "HR", "SLG",
    
    # ETFs & Sector Funds
    "IWM", "EFA", "EEM", "VTI", "VOO", "VEA", "VWO", "GLD", "SLV", "TLT",
    "HYG", "LQD", "IEMG", "ITOT", "IXUS", "IEFA", "IEMG", "AGG", "BND", "VTEB",
    
    # Additional Quality Names
    "ROKU", "PINS", "SNAP", "TWTR", "UBER", "LYFT", "ABNB", "COIN", "HOOD", "SOFI"
)

# Combine all symbols (remove duplicates)
$allSymbols = @()
$allSymbols += $current50
$allSymbols += $additional150

# Remove duplicates and take first 200
$uniqueSymbols = $allSymbols | Select-Object -Unique | Select-Object -First 200

Write-Host "📊 Universe Statistics:" -ForegroundColor Yellow
Write-Host "  Current symbols: $($current50.Count)" -ForegroundColor White
Write-Host "  Additional symbols: $($additional150.Count)" -ForegroundColor White  
Write-Host "  Total unique symbols: $($uniqueSymbols.Count)" -ForegroundColor White
Write-Host ""

# Create the new universe.csv content
$csvContent = $uniqueSymbols -join ", "

# Backup existing universe.csv
if (Test-Path "universe.csv") {
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    Copy-Item "universe.csv" "universe_backup_$timestamp.csv"
    Write-Host "✅ Backed up existing universe.csv to universe_backup_$timestamp.csv" -ForegroundColor Green
}

# Write new universe.csv
$csvContent | Out-File -FilePath "universe.csv" -Encoding UTF8 -NoNewline
Write-Host "✅ Updated universe.csv with $($uniqueSymbols.Count) symbols" -ForegroundColor Green

# Display first 20 symbols as preview
Write-Host ""
Write-Host "📋 First 20 symbols in new universe:" -ForegroundColor Cyan
$uniqueSymbols | Select-Object -First 20 | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
Write-Host "  ... and $($uniqueSymbols.Count - 20) more" -ForegroundColor Gray

Write-Host ""
Write-Host "🎉 UNIVERSE EXPANSION COMPLETE!" -ForegroundColor Green
Write-Host "✅ Universe now contains $($uniqueSymbols.Count) high-quality symbols" -ForegroundColor Green
Write-Host "✅ Ready for enhanced signal generation and trading opportunities" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Restart the trading system to load new universe" -ForegroundColor White
Write-Host "2. Monitor signal generation across expanded universe" -ForegroundColor White
Write-Host "3. Verify increased trading opportunities" -ForegroundColor White
Write-Host ""
